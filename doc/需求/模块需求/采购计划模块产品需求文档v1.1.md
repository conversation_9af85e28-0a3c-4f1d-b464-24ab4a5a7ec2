---
tags:
  - 需求
  - 采购计划
---
## 1. 文档信息

| 版本   | 作者   | 日期         | 备注  |
| ---- | ---- | ---------- | --- |
| V1.0 | 行稳路远 | 2025-06-18 | 首版  |
| V1.1 | 解蕾   | 2025-06-21 |     |

---

## 2. 背景与目标

### 2.1 背景
医药零售企业采购环节需兼顾合规、效率与成本。采购计划单作为采购业务的起点，决定了后续采购订单、收货、验收、入库等流程的顺畅与合规。系统需支持采购计划的自动与手动生成，满足日常运营与GSP监管要求。

### 2.2 目标
- 支持采购计划单的全流程管理（制单、查询、执行、删除等）。
- 实现采购计划的自动生成、手动录入、导入等多种制单方式。
- 严格执行GSP合规校验，保障药品采购安全。
- 支持与商品、供应商、库存、销售、系统参数等模块的数据联动。

---

## 3. 术语与定义

| 术语 | 说明 |
|------|------|
| 采购计划单 | 记录采购需求的业务单据，包含采购品种、数量、供应商等信息。 |
| GSP | 药品经营质量管理规范，行业合规标准。 |
| 在途数量 | 已下采购订单但未入库的商品数量。 |
| 日均销量 | 指定周期内商品的平均每日销量。 |
| 库存上下限 | 由系统参数和商品属性决定的库存安全区间。 |

---

## 4. 功能需求

### 4.1 采购计划单管理

#### 4.1.1 查询采购计划单
- **入口**：进退货管理-采购计划单
- **功能**：
  - 按单据编号、供应商、状态、日期等多条件查询。
  - 展示字段：单据编号、供应商编号、供应商名称、商品种类、采购内容、金额总计、操作人员、日期、状态。
  - 支持分页、导出excel。

#### 4.1.2 新增采购计划单


```mermaid
flowchart LR
    A[新增采购计划] --> B{选择制单方法}
    B -->|手动| C[选择商品]
    B -->|自动| D[系统计算需要采购的商品\n自动做好选择]
    C --> E[展示到预采购清单中]
    D --> E
    E --> F[用户确认信息]
    F --> G[保存采购计划]
    
    style A fill:#f9f,stroke:#333
    style B fill:#bbf,stroke:#333
    style G fill:#5a5,stroke:#333,color:white
```


**填写基本信息内容:**
- **抬头信息**
- **开票日期**（默认当前日期）SaveOrUpdatePurchaseOrderParam.billDate
- **操作人**,自动带出当前登录人 SaveOrUpdatePurchaseOrderParam.purchaserId
- **状态**:支持暂存（状态为"待执行"）、直接启用（状态为"已执行"）。
- **方式一：手动制单**
  - 选择商品（多选），每个商品都需要录入的数据:
	  - 采购数量
	  - 进货单价
	  - 供应商
  - 规则：
    - **供应商**：商品上一次采购计划使用的供应商,如果没有则不带出。
    - **进货单价**：根据系统参数自动赋值，有三种方式：
      1. **按最低进价生成**：取相同商品在N天内的采购计划最低的进价，N值由系统参数决定（如180天），与供应商无关。
      2. **按最新供应商生成**：取相同商品最近一次采购计划的进价，与供应商无关。
      3. **按上次进价生成**（默认）：取相同商品在同一供应商的上次采购计划的进价。
    - **在途数量**：预采购商品在N天内已执行的采购订单但未入库的数量，N为系统参数。仅展示,不可修改.
    - **库存上下限**、**日均销量**,仅展示不可修改。

- **方式二：自动生成**
  - 系统根据预设规则一键生成一条采购计划单（非定时自动执行的计划任务）。
  - 生成规则：
    - 库存上限 = 上限天数 × 日均销量
    - 库存下限 = 下限天数 × 日均销量
    - 采购计划量 = 上限库存 - 当前库存 - 在途数量
    - 日均销量 = 前N天销售数量 / (N-断货天数)
  - 生成范围：低于库存下限的商品。
	其他数据规则与手动制单一致.

- **方式三：导入**
❓需要excel模板,要注意药品与供应商等信息的准确性如何保证..先不做了
  - 支持EXCEL或文本导入采购计划明细。
  - 导入模板需提供，导入后可编辑、校验。

#### 4.1.3 编辑/删除采购计划单
- 仅"待执行"状态可编辑、删除。
- 已执行单据不可编辑、删除。

#### 4.1.4 分单规则
- 采购订单可按供应商、付款方式等分单提取。

#### 4.1.5 GSP合规校验
- 校验内容：
  - 供应商证照效期
  - 商品证照效期
  - 供应商经营范围
  - 企业经营范围及证照效期
- 校验方式：系统参数控制（1-拦截，0-不拦截不提示，-1-提示不拦截）
- 如果校验失败,责不可执行,可以暂存

#### 4.1.6 其他
- 记录操作日志，便于追溯。   -- 暂缓开发
- 支持单据打印、导出。-- 暂缓开发

---

## 5. 数据需求

### 5.1 相关参数表
- 组织参数：
  - **采购计划进货价规则**
    - 1：按最低进价生成
    - 2：按最新供应商生成
    - 3：按上次进价生成(默认)
  - **采购计划上次进阶天数**：采购计划进货价规则=(1：按最低进价生成) 时的取样天数，默认值180天
  - 采购计划在途数量天数:
- 采购计划自动生成规则
	- 上限天数
	- 下限天数
	- 上限库存
	- 前N天销售数量中的N
	- 

---

## 6. 业务流程

### 6.1 采购计划单业务流程图（Mermaid）

```mermaid
flowchart LR
    A["用户进入采购计划单模块"] --> B{"选择制单方式"}
    B --"手动制单"--> C["选择商品并填写明细"]
    B --"自动生成"--> D["系统根据库存上下限规则\n生成一条采购计划单"]
    B --"导入"--> E["导入EXCEL/文本明细"]
    C & E --> F["系统自动带出进价、供应商、在途数量等"]
    D --> G1["人工确认并可修改明细"]
    G1 --> F
    F --> G["GSP合规校验"]
    G --"校验通过"--> H["保存/暂存/启用采购计划单"]
    G --"校验不通过"--> I["提示/拦截，按系统参数处理"]
    H --> J["采购计划单进入待执行/已执行状态"]
    J --> K["后续可转采购订单"]
```

---

## 7. 权限与角色

| 角色 | 权限 |
|------|------|
| 采购员 | 新增、编辑、删除、查询采购计划单 |
| 采购经理 | 审批、查询、导出、打印 |
| 质量经理 | 证照校验、合规审核 |
| 系统管理员 | 参数配置、权限分配 |

---

## 8. 关键业务规则

- 采购计划单号自动生成，规则可配置。
- 采购计划单仅"待执行"状态可编辑、删除。
- 自动生成采购计划是指系统根据预设规则一键生成一条采购计划单，而非创建可多次自动执行的计划任务。
- 进货单价和供应商的自动带出逻辑由系统参数控制，支持三种方式。
- GSP校验不通过时，按系统参数决定是否拦截或仅提示。
- 采购计划单可按供应商、付款方式等分单转采购订单。

---

## 9. 需确认/待补充事项

1. 【需确认】GSP校验的具体字段和校验规则是否有补充？
2. 【需确认】导入模板的字段及格式要求。
