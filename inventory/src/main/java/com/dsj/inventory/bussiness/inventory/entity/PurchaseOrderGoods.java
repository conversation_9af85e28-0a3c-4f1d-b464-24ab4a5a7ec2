package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 进销存采购订单商品明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_purchase_order_goods")
@ApiModel(value="PurchaseOrderGoods", description="进销存采购订单商品明细")
public class PurchaseOrderGoods extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "采购订单Id")
    @TableField("order_id")
    private Long orderId;

    @ApiModelProperty(value = "私域商品Id")
    @TableField("organization_medicine_id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "通用名")
    @TableField("goods_name")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    @TableField("general_name")
    private String generalName;

    @ApiModelProperty(value = "商品编号")
    @TableField("goods_code")
    private String goodsCode;

    @ApiModelProperty(value = "包装单位")
    @TableField("pack_unit")
    private String packUnit;

    @ApiModelProperty(value = "规格型号")
    @TableField("specification")
    private String specification;

    @ApiModelProperty(value = "剂型")
    @TableField("drug_type")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    @TableField("manufacturer")
    private String manufacturer;

    @ApiModelProperty(value = "上市许可持有人")
    @TableField("launch_permit_holder")
    private String launchPermitHolder;

    @ApiModelProperty(value = "当前零售价（私域商品价格）")
    @TableField("price")
    private Integer price;

    @ApiModelProperty(value = "当前库存")
    @TableField("repertory")
    private Double repertory;

    @ApiModelProperty(value = "含税价（进货价）")
    @TableField("purchase_price")
    private Integer purchasePrice;

    @ApiModelProperty(value = "采购数量")
    @TableField("purchase_number")
    private Double purchaseNumber;

    @ApiModelProperty(value = "含税金额（进货总金额）")
    @TableField("total_purchase_price")
    private Integer totalPurchasePrice;

    @ApiModelProperty(value = "已收货数量", example = "0")
    @TableField("received_quantity")
    private Integer receivedQuantity;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
