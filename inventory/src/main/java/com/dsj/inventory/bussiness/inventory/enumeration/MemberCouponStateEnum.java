package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 会员优惠券状态 0未使用 1已使用 2已过期
 * <AUTHOR>
 */

@Getter
public enum MemberCouponStateEnum {

    NOT_USED(0, "未使用"),
    USED(1, "已使用"),
    EXPIRED(2, "已过期"),
    ;

    private Integer code;

    private String desc;

    MemberCouponStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (MemberCouponStateEnum a : MemberCouponStateEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
