package com.dsj.inventory.common.utils;

import com.pocky.transport.bussiness.diagnose.vo.PrescriptionVo;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.StringWriter;

/**
 * freemarker转换
 *
 * <AUTHOR>
 * @date 2022/4/10 9:13
 */
@Component
public class FreemarkerService {

    @Autowired
    private Configuration configuration;  //package freemarker.template;freemarker.template包里的类

    public String getHtml(String fileName, PrescriptionVo prescription) throws Exception {
        //指定模板所在位置
        String freemarkerPath="/templates/";
        //spring加载资源文件的ClassPathResource方法
        Resource resource = new ClassPathResource(freemarkerPath);
        //java.io.File：getAbsoluteFile是不会抛出异常的，返回：绝对路径名字符串，此处为后面模板中引入图片做准备
//        map.put("basePath", resource.getFile().getAbsoluteFile());
        //按名字获取到模板
        Template template = configuration.getTemplate(fileName);
        //执行 写
        StringWriter writer = new StringWriter();
        template.process(prescription,writer);
        writer.flush();
        writer.close();
        String html=writer.toString();
        writer.close();
        //写成一个“页面”
        return html;
    }


}
