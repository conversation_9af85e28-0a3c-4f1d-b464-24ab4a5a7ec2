package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 退货单
 * <AUTHOR>
 * @date 2023/04/13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DeliveryOrderVo", description="退货单实体")
@ToString(callSuper = true)
public class DeliveryOrderVo {

    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "入库单Id")
    private Long orderId;

    @ApiModelProperty(value = "入库单号")
    private String orderCode;

    @ApiModelProperty(value = "退货单号")
    private String code;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "处理状态 0暂存 1退货中 2已退货 3已取消")
    private Integer dealState;

    @ApiModelProperty(value = "私域供应商Id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "供应商名称")
    private String organizationSupplierName;

    @ApiModelProperty(value = "供应商编号")
    private String organizationSupplierCode;

    @ApiModelProperty(value = "私域供应商销售人员Id")
    private Long organizationSupplierMemberId;

    @ApiModelProperty(value = "私域供应商销售人员名称")
    private String organizationSupplierMemberName;

    @ApiModelProperty(value = "退货总额")
    private Integer returnTotalPrice;

    @ApiModelProperty(value = "退货成本金额")
    private Integer returnCostTotalPrice;

    @ApiModelProperty(value = "进退货差价（退货成本金额-退货总额）")
    private Integer disparityPrice;

    @ApiModelProperty(value = "退货原因")
    private String reason;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
