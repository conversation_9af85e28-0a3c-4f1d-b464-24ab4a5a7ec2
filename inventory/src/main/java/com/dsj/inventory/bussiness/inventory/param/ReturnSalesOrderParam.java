package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/04/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@Builder
@ApiModel(value = "ReturnSalesOrderParam", description = "进销存销售单退货请求参数")
public class ReturnSalesOrderParam {

    @ApiModelProperty(hidden = true,value = "销售单id")
    private Long id;

    @ApiModelProperty(value = "退货原因")
    private String returnReason;

}
