package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberCouponCountParam;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberCouponParam;
import com.dsj.inventory.bussiness.inventory.service.MemberCouponService;
import com.dsj.inventory.bussiness.inventory.vo.MemberCouponCountVo;
import com.dsj.inventory.bussiness.inventory.vo.MemberCouponVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 进销存会员优惠券 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@RestController
@Api(value = "MemberCouponController", tags = "进销存会员优惠券api")
public class MemberCouponController {

    @Autowired
    private MemberCouponService memberCouponService;

    /*获取会员优惠券列表*/
    @GetMapping("/member-coupon")
    @ApiOperation(value = "获取会员优惠券列表", notes = "获取会员优惠券列表")
    public ResponseEntity<List<MemberCouponVo>> queryMemberCouponList(QueryMemberCouponParam param){
        Page<MemberCouponVo> page = memberCouponService.queryMemberCouponPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取会员优惠券详情*/
    @GetMapping("/member-coupon/{id}")
    @ApiOperation(value = "获取会员优惠券详情", notes = "获取会员优惠券详情")
    public ResponseEntity<MemberCouponVo> queryMemberCoupon(@PathVariable Long id){
        return Res.success(memberCouponService.getMemberCouponById(id));
    }

    /*领取优惠券*/
    @PostMapping("/member-coupon/{couponId}/action-get")
    @ApiOperation(value = "领取优惠券", notes = "领取优惠券")
    public ResponseEntity<Void> getCoupon(@PathVariable Long couponId){
        memberCouponService.getCoupon(couponId);
        return Res.success();
    }

    @GetMapping("/home/<USER>")
    @ApiOperation(value = "会员优惠券数据统计", notes = "会员优惠券数据统计")
    public MemberCouponCountVo countMemberCoupon(QueryMemberCouponCountParam param){
        MemberCouponCountVo count = memberCouponService.countMemberCoupon(param);
        return count;
    }

}
