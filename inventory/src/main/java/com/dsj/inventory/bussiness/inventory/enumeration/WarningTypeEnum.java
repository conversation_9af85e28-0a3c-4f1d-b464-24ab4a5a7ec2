package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 预警类型 1缺货 2积货 3滞销 4近效期
 * <AUTHOR>
 */

@Getter
public enum WarningTypeEnum {

    LACK(1, "缺货"),
    EXCEED(2, "积货"),
    UNSALABLE(3, "滞销"),
    BECOME_DUE(4, "近效期"),
    ;

    private Integer code;

    private String desc;

    WarningTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (WarningTypeEnum a : WarningTypeEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
