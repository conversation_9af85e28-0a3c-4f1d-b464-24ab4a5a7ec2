package com.dsj.inventory.common.cache;

import com.dsj.inventory.common.lock.DistributedLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

/**
 * redis Key到期监听
 *
 * <AUTHOR>
 * @date 2022/3/22 14:56
 */
@Slf4j
@Component
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {

    public RedisKeyExpirationListener(RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }

    @Autowired
    private DistributedLock RedisDistributedLock;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        // 用户做自己的业务处理即可,注意message.toString()可以获取失效的key
        String expirationKey = message.toString();
        try {
            boolean lock = RedisDistributedLock.lock(expirationKey, 1000, 0, 0);
//            log.info("===========失效的Redis的键===========：{}",doctorWaitingCountKey);
        }catch (Exception e){
            log.error("redis KEY值失效回调处理异常：失效key:{},异常内容:{}",expirationKey, e.getMessage());
        }finally {
            RedisDistributedLock.releaseLock(expirationKey);
        }
    }
}
