package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.MemberCoupon;
import com.dsj.inventory.bussiness.inventory.entity.SalesCoupon;
import com.dsj.inventory.bussiness.inventory.enumeration.MemberCouponStateEnum;
import com.dsj.inventory.bussiness.inventory.enumeration.SalesCouponGroupEnum;
import com.dsj.inventory.bussiness.inventory.enumeration.SalesCouponModeEnum;
import com.dsj.inventory.bussiness.inventory.mapper.MemberCouponMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberCouponCountParam;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberCouponParam;
import com.dsj.inventory.bussiness.inventory.service.MemberCouponService;
import com.dsj.inventory.bussiness.inventory.service.SalesCouponGoodsService;
import com.dsj.inventory.bussiness.inventory.service.SalesCouponService;
import com.dsj.inventory.bussiness.inventory.vo.MemberCouponCountVo;
import com.dsj.inventory.bussiness.inventory.vo.MemberCouponVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesCouponGoodsVo;
import com.pocky.transport.bussiness.diagnose.enumeration.MedicineOrderEnum;
import com.pocky.transport.bussiness.diagnose.enumeration.ModuleEnum;
import com.pocky.transport.bussiness.home.entity.MedicineOrder;
import com.pocky.transport.bussiness.home.service.MedicineOrderService;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 进销存会员优惠券 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@Service
public class MemberCouponServiceImpl extends ServiceImpl<MemberCouponMapper, MemberCoupon> implements MemberCouponService {

    @Autowired
    private SalesCouponGoodsService salesCouponGoodsService;
    @Autowired
    private SalesCouponService salesCouponService;
    @Autowired
    private MedicineOrderService medicineOrderService;

    @Override
    public Page<MemberCouponVo> queryMemberCouponPage(QueryMemberCouponParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        if(CommonUtils.isNotEmpty(param.getModuleType()) && ModuleEnum.WHOLESALE.getCode().equals(param.getModuleType())){
            if(CommonUtils.isEmpty(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID))){
                throw new BizException("获取失败，登录人未选所属组织");
            }
            param.setGetOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        List<MemberCouponVo> list = this.baseMapper.queryMemberCouponPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public MemberCouponVo getMemberCouponById(Long id) {
        MemberCouponVo memberCoupon = this.baseMapper.getMemberCouponById(id);
        if(CommonUtils.isNotEmpty(memberCoupon)){
            List<SalesCouponGoodsVo> goodsList = salesCouponGoodsService.querySalesCouponGoodsList(memberCoupon.getCouponId());
            if(CommonUtils.isNotEmpty(goodsList)){
                memberCoupon.setGoodsList(goodsList);
            }
        }
        return memberCoupon;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getCoupon(Long couponId) {
        SalesCoupon coupon = salesCouponService.getById(couponId);
        if(CommonUtils.isEmpty(coupon)){
            throw new BizException("领取失败，优惠券不存在");
        }
        if(SalesCouponModeEnum.GRANT.getCode().equals(coupon.getCouponMode())){
            throw new BizException("领取失败，优惠券不是领取模式");
        }
        if(TrueEnum.TRUE.getCode().equals(coupon.getCouponState())){
            throw new BizException("领取失败，优惠券已过期");
        }
        LocalDateTime now = LocalDateTime.now();
        if(CommonUtils.isNotEmpty(coupon.getStartDate()) && now.isBefore(coupon.getStartDate())){
            throw new BizException("领取失败，优惠券未在有效期");
        }
        if(CommonUtils.isNotEmpty(coupon.getEndDate()) && now.isAfter(coupon.getEndDate())){
            throw new BizException("领取失败，优惠券未在有效期");
        }
        if(now.isBefore(coupon.getGetStartDate())){
            throw new BizException("领取失败，优惠券未到领取时间");
        }
        if(now.isAfter(coupon.getGetEndDate())){
            throw new BizException("领取失败，优惠券已过领取时间");
        }
        Long getOrganizationId = null;
        Long userId = BaseContextHandler.getUserId();
        LambdaQueryWrapper<MemberCoupon> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberCoupon::getCouponId, couponId);
        wrapper.eq(MemberCoupon::getUserId, userId);
        if (ModuleEnum.WHOLESALE.getCode().equals(coupon.getModuleType())){
            if(CommonUtils.isEmpty(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID))){
                throw new BizException("领取失败，领取人未选所属组织，无法领取该优惠券");
            }
            getOrganizationId = Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID));
            wrapper.eq(MemberCoupon::getGetOrganizationId,getOrganizationId);
        }
        Integer count = this.count(wrapper);
        if(CommonUtils.isNotEmpty(count) && count >= coupon.getAmountLimit()){
            throw new BizException("领取失败，超出优惠券每人领取数量");
        }
        if(CommonUtils.isNotEmpty(coupon.getGetAmountLimit())){
            Integer num = this.lambdaQuery().eq(MemberCoupon::getCouponId,couponId).count();
            if(CommonUtils.isNotEmpty(num) && num >= coupon.getGetAmountLimit()){
                throw new BizException("领取失败，超出优惠券领取总量");
            }
        }
        //筛选合作买家
        if(coupon.getGetGroup().equals(SalesCouponGroupEnum.COOPERATE.getCode())){
            Integer orderCount = medicineOrderService.lambdaQuery().eq(MedicineOrder::getBuyOrganizationId,getOrganizationId)
                    .eq(MedicineOrder::getModuleType,coupon.getModuleType())
                    .in(MedicineOrder::getMedicineOrderState, Arrays.asList(MedicineOrderEnum.WAITING_EVALUATE.getCode(),MedicineOrderEnum.FINISH.getCode()))
                    .count();
            if(CommonUtils.isEmpty(orderCount) || orderCount <= 0){
                throw new BizException("领取失败，该优惠券只提供给已合作买家");
            }
        }
        //领取优惠券
        MemberCoupon memberCoupon = MemberCoupon.builder().userId(userId).couponId(couponId)
                .couponState(MemberCouponStateEnum.NOT_USED.getCode()).build();
        if(CommonUtils.isNotEmpty(getOrganizationId)){
            memberCoupon.setGetOrganizationId(getOrganizationId);
        }
        //计算优惠券有效期
        if(CommonUtils.isNotEmpty(coupon.getUseDay())){
            LocalDateTime endDate = LocalDateTime.of(LocalDate.now().plusDays(coupon.getUseDay()-1), LocalTime.of(23,59,59));
            memberCoupon.setStartDate(now);
            memberCoupon.setEndDate(endDate);
        }else {
            memberCoupon.setStartDate(coupon.getStartDate());
            memberCoupon.setEndDate(coupon.getEndDate());
        }
        this.save(memberCoupon);
        //更改优惠券发放数量
        Integer num = this.lambdaQuery().eq(MemberCoupon::getCouponId,couponId).count();
        salesCouponService.lambdaUpdate().eq(SalesCoupon::getId,couponId)
                .set(SalesCoupon::getGrantAmount,num).update(new SalesCoupon());
    }

    @Override
    public MemberCouponCountVo countMemberCoupon(QueryMemberCouponCountParam param) {
        //批发商城使用，统计用户优惠券
        if(CommonUtils.isNotEmpty(BaseContextHandler.get(BaseContextConstants.MODULE_TYPE))){
            param.setModuleType(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.MODULE_TYPE)));
        }
        param.setUserId(BaseContextHandler.getUserId());
        MemberCouponCountVo count = this.baseMapper.countMemberCoupon(param);
        return count;
    }
}
