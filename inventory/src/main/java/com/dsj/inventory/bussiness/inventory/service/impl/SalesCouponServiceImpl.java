package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.Member;
import com.dsj.inventory.bussiness.inventory.entity.MemberCoupon;
import com.dsj.inventory.bussiness.inventory.entity.SalesCoupon;
import com.dsj.inventory.bussiness.inventory.entity.SalesCouponGoods;
import com.dsj.inventory.bussiness.inventory.enumeration.MemberCouponStateEnum;
import com.dsj.inventory.bussiness.inventory.enumeration.SalesCouponGroupEnum;
import com.dsj.inventory.bussiness.inventory.enumeration.SalesCouponModeEnum;
import com.dsj.inventory.bussiness.inventory.enumeration.SalesCouponStateEnum;
import com.dsj.inventory.bussiness.inventory.mapper.SalesCouponMapper;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesCouponDetailParam;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesCouponParam;
import com.dsj.inventory.bussiness.inventory.param.SaveSalesCouponParam;
import com.dsj.inventory.bussiness.inventory.service.MemberCouponService;
import com.dsj.inventory.bussiness.inventory.service.MemberService;
import com.dsj.inventory.bussiness.inventory.service.SalesCouponGoodsService;
import com.dsj.inventory.bussiness.inventory.service.SalesCouponService;
import com.dsj.inventory.bussiness.inventory.vo.SalesCouponDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesCouponGoodsVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesCouponVo;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.common.utils.DozerUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.diagnose.enumeration.MedicineOrderEnum;
import com.pocky.transport.bussiness.diagnose.enumeration.ModuleEnum;
import com.pocky.transport.bussiness.home.entity.MedicineOrder;
import com.pocky.transport.bussiness.home.service.MedicineOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 进销存销售活动商品明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@Slf4j
@Service
public class SalesCouponServiceImpl extends ServiceImpl<SalesCouponMapper, SalesCoupon> implements SalesCouponService {

    @Autowired
    private DozerUtils dozerUtils;
    @Autowired
    private SalesCouponGoodsService salesCouponGoodsService;
    @Autowired
    private MemberCouponService memberCouponService;
    @Autowired
    private MemberService memberService;
    @Autowired
    private MedicineOrderService medicineOrderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalesCouponVo saveSalesCoupon(SaveSalesCouponParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(CommonUtils.isNotEmpty(param.getId())) {
            SalesCoupon byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            //删除商品明细
            salesCouponGoodsService.lambdaUpdate().eq(SalesCouponGoods::getCouponId,param.getId())
                    .set(SalesCouponGoods::getIsDelete, TrueEnum.TRUE.getCode()).update(new SalesCouponGoods());
            if (CommonUtils.isNotEmpty(param.getIsDelete()) && TrueEnum.TRUE.getCode().equals(param.getIsDelete())){
                this.baseMapper.deleteById(param.getId());
                //删除会员手中的优惠券
                memberCouponService.lambdaUpdate().eq(MemberCoupon::getCouponId,param.getId())
                        .set(MemberCoupon::getIsDelete,TrueEnum.TRUE.getCode()).update(new MemberCoupon());
                return null;
            }
        }

        if(CommonUtils.isNotEmpty(param.getGoodsList())){
            List<Long> goodsIdList = param.getGoodsList().stream().map(g -> g.getOrganizationMedicineId()).distinct().collect(Collectors.toList());
            if(goodsIdList.size() != param.getGoodsList().size()){
                throw new BizException("操作失败，存在重复商品");
            }
        }
        if(SalesCouponModeEnum.GET.getCode().equals(param.getCouponMode()) && CommonUtils.isNotEmpty(param.getStartDate()) && CommonUtils.isNotEmpty(param.getEndDate())){
            param.setUseDay(null);
        }
        if(CommonUtils.isNotEmpty(param.getUseDay()) && param.getUseDay() < 1){
            throw new BizException("操作失败，有效天数不能小于1");
        }
        //判断优惠券开始时间和结束时间
        if(SalesCouponModeEnum.GRANT.getCode().equals(param.getCouponMode())){
            LocalDateTime startDate = LocalDateTime.of(LocalDate.now().plusDays(param.getAssertDay()), LocalTime.MIN);
            LocalDateTime endDate = LocalDateTime.of(LocalDate.now().plusDays(param.getAssertDay()+param.getUseDay()-1), LocalTime.of(23,59,59));
            param.setStartDate(startDate);
            param.setEndDate(endDate);
        }else if(SalesCouponModeEnum.GET.getCode().equals(param.getCouponMode()) && CommonUtils.isNotEmpty(param.getUseDay())){
            param.setStartDate(param.getGetStartDate());
            LocalDateTime endDate = LocalDateTime.of(param.getGetEndDate().toLocalDate().plusDays(param.getUseDay()-1), LocalTime.of(23,59,59));
            param.setEndDate(endDate);
        }
        //判断优惠券状态
        LocalDateTime now = LocalDateTime.now();
        boolean endFlag = DateUtils.dateCompare(DateUtils.format(now,DateUtils.DEFAULT_DATE_TIME_FORMAT)
                ,DateUtils.format(param.getEndDate(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
        if(endFlag){
            param.setCouponState(TrueEnum.FALSE.getCode());
        }else {
            param.setCouponState(TrueEnum.TRUE.getCode());
        }
        SalesCoupon salesCoupon = dozerUtils.map(param,SalesCoupon.class);
        this.saveOrUpdate(salesCoupon);
        if(CommonUtils.isNotEmpty(param.getGoodsList())){
            param.getGoodsList().stream().map(g -> g.setCouponId(salesCoupon.getId())).collect(Collectors.toList());
            List<SalesCouponGoods> goodsList = dozerUtils.mapList(param.getGoodsList(),SalesCouponGoods.class);
            salesCouponGoodsService.saveBatch(goodsList);
        }
        //判断是否发放优惠券
        if(SalesCouponModeEnum.GRANT.getCode().equals(salesCoupon.getCouponMode()) && salesCoupon.getAssertDay().equals(0)){
            List<Member> memberList = memberService.lambdaQuery().eq(Member::getOrganizationId,salesCoupon.getOrganizationId()).list();
            if(CommonUtils.isNotEmpty(memberList)){
                List<MemberCoupon> memberCouponList = new ArrayList<>();
                for(Member member : memberList){
                    for(int i=0; i<salesCoupon.getAmountLimit(); i++) {
                        MemberCoupon memberCoupon = MemberCoupon.builder().couponId(salesCoupon.getId())
                                .memberId(member.getId()).couponState(MemberCouponStateEnum.NOT_USED.getCode())
                                .startDate(salesCoupon.getStartDate()).endDate(salesCoupon.getEndDate()).build();
                        memberCouponList.add(memberCoupon);
                    }
                }
                memberCouponService.saveBatch(memberCouponList);
                //更新发放数量
                this.lambdaUpdate().eq(SalesCoupon::getId,salesCoupon.getId())
                        .set(SalesCoupon::getGrantAmount,memberCouponList.size()).update(new SalesCoupon());
            }
        }
        return this.getSalesCouponById(salesCoupon.getId());
    }

    @Override
    public Page<SalesCouponVo> querySalesCouponPage(QuerySalesCouponParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        if(CommonUtils.isNotEmpty(param.getModuleType()) && ModuleEnum.WHOLESALE.getCode().equals(param.getModuleType())
                && param.getGetScreen()){
            //是否筛选已合作买家
            if(CommonUtils.isEmpty(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID))){
                throw new BizException("获取失败，当前用户需要选择所属组织");
            }
            Long organizationId = Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID));
            Integer orderCount = medicineOrderService.lambdaQuery().eq(MedicineOrder::getBuyOrganizationId,organizationId)
                    .eq(MedicineOrder::getModuleType,param.getModuleType())
                    .in(MedicineOrder::getMedicineOrderState, Arrays.asList(MedicineOrderEnum.WAITING_EVALUATE.getCode(),MedicineOrderEnum.FINISH.getCode()))
                    .count();
            if(CommonUtils.isEmpty(orderCount) || orderCount <= 0){
                param.setGetGroup(SalesCouponGroupEnum.ALL.getCode());
            }
        }
        List<SalesCouponVo> list = this.baseMapper.querySalesCouponPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public SalesCouponVo getSalesCouponById(Long id) {
        SalesCouponVo vo = this.baseMapper.getSalesCouponById(id);
        if(CommonUtils.isNotEmpty(vo)){
            List<SalesCouponGoodsVo> goodsList = salesCouponGoodsService.querySalesCouponGoodsList(vo.getId());
            if(CommonUtils.isNotEmpty(goodsList)){
                vo.setGoodsList(goodsList);
            }
        }
        return vo;
    }

    @Override
    public Page<SalesCouponDetailVo> querySalesCouponDetailPage(QuerySalesCouponDetailParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        SalesCoupon coupon = this.getById(param.getCouponId());
        List<SalesCouponDetailVo> list = new ArrayList<>();
        if(CommonUtils.isEmpty(coupon)){
            throw new BizException("获取失败，优惠券不存在");
        }
        if(SalesCouponModeEnum.GRANT.getCode().equals(coupon.getCouponMode())){
            list = this.baseMapper.querySalesCouponMemberDetailPage(page, param);
        }else if(SalesCouponModeEnum.GET.getCode().equals(coupon.getCouponMode())){
            list = this.baseMapper.querySalesCouponUserDetailPage(page, param);
        }
        return page.setRecords(list);
    }

    @Override
    public void batchHandleSalesCouponState() {
        LocalDateTime startDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime endDate = LocalDateTime.of(LocalDate.now(), LocalTime.of(23,59,59));
        //批量发放生效的优惠券
        List<SalesCoupon> salesCouponList = this.lambdaQuery().eq(SalesCoupon::getGrantAmount,0)
                .eq(SalesCoupon::getCouponState,SalesCouponStateEnum.NOT_EXPIRED.getCode())
                .eq(SalesCoupon::getCouponMode,SalesCouponModeEnum.GRANT.getCode())
                .ge(SalesCoupon::getStartDate,startDate).lt(SalesCoupon::getStartDate,endDate)
                .list();
        if(CommonUtils.isNotEmpty(salesCouponList)){
            for(SalesCoupon salesCoupon : salesCouponList){
                List<Member> memberList = memberService.lambdaQuery().eq(Member::getOrganizationId,salesCoupon.getOrganizationId()).list();
                if(CommonUtils.isNotEmpty(memberList)){
                    List<MemberCoupon> memberCouponList = new ArrayList<>();
                    for(Member member : memberList){
                        for(int i=0; i<salesCoupon.getAmountLimit(); i++) {
                            MemberCoupon memberCoupon = MemberCoupon.builder().couponId(salesCoupon.getId())
                                    .memberId(member.getId()).couponState(MemberCouponStateEnum.NOT_USED.getCode())
                                    .startDate(salesCoupon.getStartDate()).endDate(salesCoupon.getEndDate()).build();
                            memberCouponList.add(memberCoupon);
                        }
                    }
                    memberCouponService.saveBatch(memberCouponList);
                    //更新发放数量
                    this.lambdaUpdate().eq(SalesCoupon::getId,salesCoupon.getId())
                            .set(SalesCoupon::getGrantAmount,memberCouponList.size()).update(new SalesCoupon());
                }
            }
        }
        log.info("{}优惠券发放完毕", DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
        //批量更新已过期
        List<SalesCoupon> expiredCouponList = this.lambdaQuery().le(SalesCoupon::getEndDate,startDate)
                .eq(SalesCoupon::getCouponState,SalesCouponStateEnum.NOT_EXPIRED.getCode()).list();
        if(CommonUtils.isNotEmpty(expiredCouponList)){
            for(SalesCoupon salesCoupon : expiredCouponList){
                this.lambdaUpdate().eq(SalesCoupon::getId,salesCoupon.getId())
                        .set(SalesCoupon::getCouponState,SalesCouponStateEnum.EXPIRED.getCode()).update(new SalesCoupon());
                List<MemberCoupon> memberCouponList = memberCouponService.lambdaQuery()
                        .eq(MemberCoupon::getCouponId,salesCoupon.getId())
                        .eq(MemberCoupon::getCouponState,MemberCouponStateEnum.NOT_USED.getCode())
                        .list();
                if(CommonUtils.isNotEmpty(memberCouponList)){
                    List<Long> memberCouponIdList = memberCouponList.stream().map(m -> m.getId()).collect(Collectors.toList());
                    memberCouponService.lambdaUpdate().in(MemberCoupon::getId,memberCouponIdList)
                            .set(MemberCoupon::getCouponState,MemberCouponStateEnum.EXPIRED.getCode()).update(new MemberCoupon());
                    this.lambdaUpdate().eq(SalesCoupon::getId,salesCoupon.getId())
                            .set(SalesCoupon::getExpireAmount,(salesCoupon.getExpireAmount() + memberCouponIdList.size()))
                            .update(new SalesCoupon());
                }
            }
        }
        log.info("{}优惠券过期状态变更完毕",DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
        //批量更细用户领取的优惠券状态
        List<MemberCoupon> memberCouponList = memberCouponService.lambdaQuery()
                .le(MemberCoupon::getEndDate,startDate)
                .eq(MemberCoupon::getCouponState,MemberCouponStateEnum.NOT_USED.getCode())
                .list();
        if(CommonUtils.isNotEmpty(memberCouponList)){
            for(MemberCoupon memberCoupon : memberCouponList){
                memberCouponService.lambdaUpdate().eq(MemberCoupon::getId,memberCoupon.getId())
                        .set(MemberCoupon::getCouponState,MemberCouponStateEnum.EXPIRED.getCode()).update(new MemberCoupon());
            }
            List<Long> couponIdList = memberCouponList.stream().map(m -> m.getCouponId()).distinct().collect(Collectors.toList());
            for(Long couponId : couponIdList){
                Integer count = memberCouponService.lambdaQuery()
                        .eq(MemberCoupon::getCouponId,couponId)
                        .eq(MemberCoupon::getCouponState,MemberCouponStateEnum.EXPIRED.getCode())
                        .count();
                this.lambdaUpdate().eq(SalesCoupon::getId,couponId)
                        .set(SalesCoupon::getExpireAmount,count)
                        .update(new SalesCoupon());
            }
        }
        log.info("{}用户领取优惠券过期状态变更完毕",DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
    }
}
