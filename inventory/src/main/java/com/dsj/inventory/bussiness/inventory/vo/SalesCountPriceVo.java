package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 销售金额统计
 * <AUTHOR>
 * @date 2023/04/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SalesCountPriceVo", description="销售金额统计")
@ToString(callSuper = true)
public class SalesCountPriceVo {

    @ApiModelProperty(value = "优惠")
    private Integer preferentialPrice;

    @ApiModelProperty(value = "抹零")
    private Integer deductPrice;

    @ApiModelProperty(value = "合计")
    private Integer totalPrice;

    @ApiModelProperty(value = "实收")
    private Integer actualPrice;

    @ApiModelProperty(value = "找零")
    private Integer giveChangePrice;

    @ApiModelProperty(value = "活动减额")
    private Integer activityReducePrice;

    @ApiModelProperty(value = "优惠券金额")
    private Integer couponPrice;

    @ApiModelProperty(value = "储值")
    private Integer storedPay;

    @ApiModelProperty(value = "积分抵扣")
    private Integer integralPay;

}
