package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 进销存退货单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_delivery_order")
@ApiModel(value="DeliveryOrder", description="进销存退货单")
public class DeliveryOrder extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织Id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "入库单Id")
    @TableField("order_id")
    private Long orderId;

    @ApiModelProperty(value = "入库单号")
    @TableField("order_code")
    private String orderCode;

    @ApiModelProperty(value = "退货单号")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "开票日期")
    @TableField("bill_date")
    private LocalDateTime billDate;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "处理状态 0暂存 1退货中 2已退货 3已取消")
    @TableField("deal_state")
    private Integer dealState;

    @ApiModelProperty(value = "私域供应商Id")
    @TableField("organization_supplier_id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "私域供应商销售人员Id")
    @TableField("organization_supplier_member_id")
    private Long organizationSupplierMemberId;

    @ApiModelProperty(value = "退货总额")
    @TableField("return_total_price")
    private Integer returnTotalPrice;

    @ApiModelProperty(value = "退货成本金额")
    @TableField("return_cost_total_price")
    private Integer returnCostTotalPrice;

    @ApiModelProperty(value = "进退货差价（退货成本金额-退货总额）")
    @TableField("disparity_price")
    private Integer disparityPrice;

    @ApiModelProperty(value = "运输方式")
    @TableField("transport_way")
    private String transportWay;

    @ApiModelProperty(value = "运输单号")
    @TableField("transport_order_code")
    private String transportOrderCode;

    @ApiModelProperty(value = "退货原因")
    @TableField("reason")
    private String reason;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
