package com.dsj.inventory.framework.config;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import springfox.bean.validators.configuration.BeanValidatorPluginsConfiguration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * Knife4j配置
 */
@Slf4j
@Configuration
@EnableSwagger2
@EnableKnife4j
@ConfigurationProperties(prefix = "pocky.knife4j")
@Data
@Import(BeanValidatorPluginsConfiguration.class)
public class Knife4jConfig implements ApplicationListener<WebServerInitializedEvent> {

    private Boolean isOpen = false;

    @Bean(value = "inventory4Knife")
    public Docket inventory4Knife() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(isOpen)
                .useDefaultResponseMessages(false)
                .apiInfo(apiInfo())
                .groupName("进销存模块")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.dsj.inventory.bussiness.inventory.controller"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("djs-inventory")
                .build();
    }

    @Override
    public void onApplicationEvent(WebServerInitializedEvent event) {

    }
}