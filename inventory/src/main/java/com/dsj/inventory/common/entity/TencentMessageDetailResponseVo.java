package com.dsj.inventory.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 腾讯云发送短信请求响应内容
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class TencentMessageDetailResponseVo {

    private String SerialNo;

    private String PhoneNumber;

    private Integer Fee;

    private String SessionContext;

    /**
     * Ok发送成功
     */
    private String Code;

    private String Message;

    private String IsoCode;

}
