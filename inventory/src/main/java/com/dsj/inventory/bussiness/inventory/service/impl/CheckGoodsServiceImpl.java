package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.CheckGoods;
import com.dsj.inventory.bussiness.inventory.mapper.CheckGoodsMapper;
import com.dsj.inventory.bussiness.inventory.service.CheckGoodsService;
import com.dsj.inventory.bussiness.inventory.vo.CheckGoodsVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 进销存盘点计划商品明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Service
public class CheckGoodsServiceImpl extends ServiceImpl<CheckGoodsMapper, CheckGoods> implements CheckGoodsService {

    @Override
    public List<CheckGoodsVo> getCheckGoodsListByCheckId(Long checkId) {
        return this.baseMapper.getCheckGoodsListByCheckId(checkId);
    }
}
