package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.CashierWorking;
import com.dsj.inventory.bussiness.inventory.mapper.CashierWorkingMapper;
import com.dsj.inventory.bussiness.inventory.service.CashierWorkingService;
import com.dsj.inventory.bussiness.inventory.vo.CashierWorkingVo;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <p>
 * 进销存收银员值班记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Service
public class CashierWorkingServiceImpl extends ServiceImpl<CashierWorkingMapper, CashierWorking> implements CashierWorkingService {

    @Autowired
    private OrganizationService organizationService;

    @Override
    public CashierWorkingVo turnCashierWorking(Long organizationId) {
        Organization organization = organizationService.getById(organizationId);
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }

        Long cashierId = BaseContextHandler.getUserId();
        LocalDateTime startDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime endDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(23,59,59));
        CashierWorking cashierWorking = this.lambdaQuery().eq(CashierWorking::getCashierId,cashierId)
                .eq(CashierWorking::getOrganizationId,organizationId)
                .ge(CashierWorking::getCreateTime, startDateTime)
                .le(CashierWorking::getCreateTime,endDateTime).one();
        Long cashierWorkingId = null;
        if(CommonUtils.isEmpty(cashierWorking)){
            CashierWorking newWorking = CashierWorking.builder().cashierId(cashierId).startDate(LocalDateTime.now())
                    .endDate(LocalDateTime.now()).organizationId(organizationId).build();
            this.save(newWorking);
            cashierWorkingId = newWorking.getId();
        }else {
            cashierWorkingId = cashierWorking.getId();
            this.lambdaUpdate().eq(CashierWorking::getId,cashierWorkingId)
                    .set(CashierWorking::getEndDate,LocalDateTime.now()).update(new CashierWorking());
        }
        return this.getCashierWorkingById(cashierWorkingId);
    }

    @Override
    public CashierWorkingVo getCashierWorkingById(Long id) {
        return this.baseMapper.getCashierWorkingById(id);
    }

}
