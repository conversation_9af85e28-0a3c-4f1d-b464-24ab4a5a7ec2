package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryInventoryParam;
import com.dsj.inventory.bussiness.inventory.param.QueryInventorySaleParam;
import com.dsj.inventory.bussiness.inventory.service.InventoryService;
import com.dsj.inventory.bussiness.inventory.vo.InventoryExportVo;
import com.dsj.inventory.bussiness.inventory.vo.InventorySaleVo;
import com.dsj.inventory.bussiness.inventory.vo.InventoryVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.EasyExcelUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 进销存商品库存 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@RestController
@Api(value = "InventoryController", tags = "进销存商品库存api")
public class InventoryController {

    @Autowired
    private InventoryService inventoryService;


    /*获取商品库存列表*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-stock"})
    @GetMapping("/inventory")
    @ApiOperation(value = "获取商品库存列表", notes = "获取商品库存列表")
    public ResponseEntity<List<InventoryVo>> queryInventoryList(QueryInventoryParam param){
        Page<InventoryVo> page = inventoryService.queryInventoryPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*商品库存导出*/
    @GetMapping("/inventory/action-export")
    @ApiOperation(value = "导出商品库存", notes = "导出商品库存")
    public ResponseEntity<Void> exportInventory(HttpServletResponse response, QueryInventoryParam param) throws IOException {
        param.setNeedExport(true);
        List<InventoryExportVo> list = inventoryService.queryInventoryList(param);
        EasyExcelUtils.exportOrdinaryExcel(response,list,"商品库存导出列表","商品库存导出列表", InventoryExportVo.class);
        return Res.success();
    }

    /*获取商品销售库存列表*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-stock","drugstore-erp-sale"})
    @GetMapping("/inventory/action-sale")
    @ApiOperation(value = "获取商品销售库存列表", notes = "获取商品销售库存列表")
    public ResponseEntity<List<InventorySaleVo>> queryInventorySaleList(QueryInventorySaleParam param){
        Page<InventorySaleVo> page = inventoryService.queryInventorySalePage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*初始化商品库存*/
    @OrgAuthMode(strict = true,strictUserMenus = false)
    @PostMapping("/inventory/action-initialize/{organizationId}")
    @ApiOperation(value = "初始化商品库存", notes = "初始化商品库存")
    public ResponseEntity<Void> importIllness(@RequestParam(value = "file") MultipartFile file, @PathVariable Long organizationId) throws IOException {
        inventoryService.initializeInventory(file,organizationId);
        return Res.success();
    }

}
