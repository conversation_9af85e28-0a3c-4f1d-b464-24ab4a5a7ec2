package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 盘点计划
 * <AUTHOR>
 * @date 2023/04/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateCheckParam", description="新增or编辑 盘点计划参数")
public class SaveOrUpdateCheckParam {

    @ApiModelProperty(hidden = true, value = "id,更新是传入")
    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(hidden = true,value = "盘点计划号")
    private String code;

    @ApiModelProperty(value = "盘点计划名称")
    private String name;

    @ApiModelProperty(value = "盘点状态：0暂存 1盘点中 2已完成")
    private Integer checkState;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "盘点货位Id，多个用逗号分割，值为1时是全部")
    private String shelfIds;

    @ApiModelProperty(value = "盘点人Id，多个用逗号分割")
    private String checkIds;

    @ApiModelProperty(value = "商品明细")
    private List<SaveOrUpdateCheckGoodsParam> goodsList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
