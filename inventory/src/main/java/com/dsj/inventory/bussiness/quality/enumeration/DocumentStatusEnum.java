package com.dsj.inventory.bussiness.quality.enumeration;

import lombok.Getter;

/**
 * 单据状态枚举
 */
@Getter
public enum DocumentStatusEnum {

    DRAFT(0, "草稿"),
    REVIEWING(1, "审核中"),
    APPROVED(2, "已审核"),
    REJECTED(3, "已驳回");

    private final Integer code;
    private final String desc;

    DocumentStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取描述
     *
     * @param code 状态码
     * @return 状态描述
     */
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return "";
        }
        for (DocumentStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status.getDesc();
            }
        }
        return "";
    }

    /**
     * 根据code获取枚举
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static DocumentStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DocumentStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 