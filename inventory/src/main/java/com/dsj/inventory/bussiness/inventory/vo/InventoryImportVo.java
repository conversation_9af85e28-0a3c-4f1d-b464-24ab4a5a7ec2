package com.dsj.inventory.bussiness.inventory.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName : InventoryImportVo
 * @Description : 商品库存导入实体
 * @Date : 2023-05-18
 */
@Data
@ApiModel(value="InventoryImportVo",description = "商品库存导入实体")
public class InventoryImportVo {

    @ApiModelProperty(value = "商品分类")
    private String goodsType;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(value = "国家医保编码")
    private String insuranceCode;

    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(value = "备案/注册证")
    private String registrationNumber;

    @ApiModelProperty(value = "医疗器械产品注册证")
    private String medicalApparatusRegistration;

    @ApiModelProperty(value = "生产许可证")
    private String producePermit;

    @ExcelProperty(value = "生产日期（2023/12/10）")
    private String produceDate;

    @ExcelProperty(value = "有效期至（2023/12/10）")
    private String userDate;

    @ExcelProperty(value = "库存数量")
    private String inventoryAmount;

    @ExcelProperty(value = "批号")
    private String batchNumber;

    @ExcelProperty(value = "货位名称")
    private String shelfName;

    @ExcelProperty(value = "批次")
    private String batch;

}
