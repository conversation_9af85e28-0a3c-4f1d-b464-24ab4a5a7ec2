package com.dsj.inventory.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.system.entity.SysDict;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统字典表 Mapper 接口
 */
@Mapper
public interface SysDictMapper extends BaseMapper<SysDict> {
    
    /**
     * 根据字典类型查询字典数据
     *
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    default List<SysDict> selectByDictType(@Param("dictType") String dictType) {
        return this.selectList(new LambdaQueryWrapper<SysDict>()
                .eq(SysDict::getDictType, dictType)
                .orderByAsc(SysDict::getId));
    }
    
    /**
     * 查询某个字典下的所有直接子字典
     *
     * @param parentId 父级字典ID
     * @return 子字典列表
     */
    default List<SysDict> selectByParentId(@Param("parentId") Long parentId) {
        return this.selectList(new LambdaQueryWrapper<SysDict>()
                .eq(SysDict::getParentId, parentId)
                .orderByAsc(SysDict::getId));
    }
    
    /**
     * 根据字典编码查询字典信息
     *
     * @param dictCode 字典编码
     * @return 字典信息
     */
    default SysDict selectByDictCode(@Param("dictCode") String dictCode) {
        return this.selectOne(new LambdaQueryWrapper<SysDict>()
                .eq(SysDict::getDictCode, dictCode)
                .last("LIMIT 1"));
    }
    
    /**
     * 分页查询字典数据
     *
     * @param page 分页对象
     * @param dictName 字典名称
     * @param dictType 字典类型
     * @return 分页结果
     */
    default IPage<SysDict> selectDictPage(Page<SysDict> page, @Param("dictName") String dictName, @Param("dictType") String dictType) {
        LambdaQueryWrapper<SysDict> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加条件，仅当参数不为空时
        if (dictType != null && !dictType.isEmpty()) {
            queryWrapper.eq(SysDict::getDictType, dictType);
        }
        
        if (dictName != null && !dictName.isEmpty()) {
            queryWrapper.like(SysDict::getDictName, dictName);
        }
        
        // 按更新时间降序排序
        queryWrapper.orderByDesc(SysDict::getUpdateTime);
        
        return this.selectPage(page, queryWrapper);
    }
}