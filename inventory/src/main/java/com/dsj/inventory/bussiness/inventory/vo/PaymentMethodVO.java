package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 组织付款方式视图对象
 */
@Data
@ApiModel(description = "组织付款方式视图对象")
public class PaymentMethodVO {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "组织ID")
    private Long organizationId;

    @ApiModelProperty(value = "付款方式编码")
    private String methodCode;

    @ApiModelProperty(value = "付款方式名称")
    private String methodName;

    @ApiModelProperty(value = "图标URL")
    private String iconUrl;

    @ApiModelProperty(value = "排序")
    private Integer sortOrder;

    @ApiModelProperty(value = "是否启用，1启用，0禁用")
    private Integer enabled;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    
    @ApiModelProperty(value = "付款方式配置项列表")
    private List<PaymentConfigVO> configList;
} 