<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.MemberCouponMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.MemberCoupon">
    <result column="id" property="id" />
        <result column="member_id" property="memberId" />
        <result column="coupon_id" property="couponId" />
        <result column="coupon_state" property="couponState" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        member_id, coupon_id, coupon_state, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="queryMemberCouponPage" resultType="com.dsj.inventory.bussiness.inventory.vo.MemberCouponVo">
        SELECT distinct jmc.id
        ,jsc.module_type moduleType
        ,jmc.member_id memberId
        ,jmc.user_id userId
        ,jmc.coupon_id couponId
        ,jmc.coupon_state couponState
        ,jsc.coupon_type couponType
        ,jsc.name
        ,jsc.coupon_condition couponCondition
        ,jsc.reduce_price reducePrice
        ,jsc.percentage
        ,jmc.start_date startDate
        ,jmc.end_date endDate
        ,jsc.goods_limit goodsLimit
        ,jmc.create_time createTime
        FROM jxc_member_coupon jmc
        LEFT JOIN jxc_sales_coupon jsc ON jsc.id = jmc.coupon_id AND jsc.is_delete = 0
        <if test="param.goodsIds != null and param.goodsIds != ''">
            LEFT JOIN jxc_sales_coupon_goods jscg ON jscg.coupon_id = jmc.coupon_id AND jscg.is_delete = 0
        </if>
        WHERE
        jmc.is_delete = 0
        <if test="param.moduleType != null">
            AND jsc.module_type = #{param.moduleType}
        </if>
        <if test="param.memberId != null and param.memberId != '' ">
            AND jmc.member_id = #{param.memberId}
        </if>
        <if test="param.userId != null and param.userId != '' ">
            AND jmc.user_id = #{param.userId}
        </if>
        <if test="param.couponId != null and param.couponId != '' ">
            AND jmc.coupon_id = #{param.couponId}
        </if>
        <if test="param.getOrganizationId != null and param.getOrganizationId != '' ">
            AND jmc.get_organization_id = #{param.getOrganizationId}
        </if>
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jsc.organization_id = #{param.organizationId}
        </if>
        <if test="param.couponState != null">
            AND jmc.coupon_state = #{param.couponState}
        </if>
        <if test="param.goodsIds != null and param.goodsIds != ''">
            AND (jscg.organization_medicine_id in
            <foreach item="item" index="index" collection="param.goodsIds.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR jsc.goods_limit = 1)
        </if>
        ORDER BY jmc.create_time desc
    </select>

    <select id="getMemberCouponById" resultType="com.dsj.inventory.bussiness.inventory.vo.MemberCouponVo">
        SELECT distinct jmc.id
        ,jsc.module_type moduleType
        ,jmc.member_id memberId
        ,jmc.user_id userId
        ,jmc.coupon_id couponId
        ,jmc.coupon_state couponState
        ,jsc.coupon_type couponType
        ,jsc.name
        ,jsc.coupon_condition couponCondition
        ,jsc.reduce_price reducePrice
        ,jsc.percentage
        ,jmc.start_date startDate
        ,jmc.end_date endDate
        ,jsc.goods_limit goodsLimit
        ,jmc.create_time createTime
        FROM jxc_member_coupon jmc
        LEFT JOIN jxc_sales_coupon jsc ON jsc.id = jmc.coupon_id AND jsc.is_delete = 0
        WHERE
        jmc.is_delete = 0
        AND jmc.id = #{id}
    </select>

    <select id="countMemberCoupon" resultType="com.dsj.inventory.bussiness.inventory.vo.MemberCouponCountVo">
        SELECT
        ifnull(sum(case when jmc.coupon_state = 0 then 1 end),0) notUseAmount,
        ifnull(sum(case when jmc.coupon_state = 1 then 1 end),0) useAmount,
        ifnull(sum(case when jmc.coupon_state = 2 and 74 then 1 end),0) expireAmount,
        count(distinct id) totalAmount
        FROM jxc_member_coupon jmc
        LEFT JOIN jxc_sales_coupon jsc ON jsc.id = jmc.coupon_id AND jsc.is_delete = 0
        WHERE
        jmc.is_delete = 0
        <if test="param.moduleType != null">
            AND jsc.module_type = #{param.moduleType}
        </if>
        <if test="param.userId != null and param.userId != '' ">
            AND jmc.user_id = #{param.userId}
        </if>
        <if test="param.memberId != null and param.memberId != '' ">
            AND jmc.member_id = #{param.memberId}
        </if>
    </select>

</mapper>
