package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.IncomingOrderGoods;
import com.dsj.inventory.bussiness.inventory.mapper.IncomingOrderGoodsMapper;
import com.dsj.inventory.bussiness.inventory.service.IncomingOrderGoodsService;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderGoodsVo;
import com.dsj.inventory.common.enumeration.TrueEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 进销存入库单商品明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Service
public class IncomingOrderGoodsServiceImpl extends ServiceImpl<IncomingOrderGoodsMapper, IncomingOrderGoods> implements IncomingOrderGoodsService {

    @Override
    public List<IncomingOrderGoodsVo> getIncomingOrderGoodsList(Long orderId) {
        return this.baseMapper.getIncomingOrderGoodsList(orderId);
    }

    @Override
    public List<IncomingOrderGoodsVo> countIncomingOrderGoods(Long organizationMedicineId) {
        return this.baseMapper.countIncomingOrderGoods(organizationMedicineId);
    }

    @Override
    public void markAsDeletedByOrderId(Long orderId) {
        this.baseMapper.markAsDeletedByOrderId(orderId);
    }
}
