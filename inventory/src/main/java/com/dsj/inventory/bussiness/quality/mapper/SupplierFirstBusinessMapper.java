package com.dsj.inventory.bussiness.quality.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.quality.entity.SupplierFirstBusinessEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 供应商首营审批表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Mapper
public interface SupplierFirstBusinessMapper extends BaseMapper<SupplierFirstBusinessEntity> {
    // 根据SupplierId和 Status查询首营记录
    default SupplierFirstBusinessEntity getSupplierFirstBusinessBySupplierIdAndStatus(Long supplierId, Integer status) {
        LambdaQueryWrapper<SupplierFirstBusinessEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierFirstBusinessEntity::getSupplierId, supplierId)
                .eq(ObjectUtil.isNotEmpty(status), SupplierFirstBusinessEntity::getStatus, status)
                .orderByDesc(SupplierFirstBusinessEntity::getApprovalSequence);
        queryWrapper.last("LIMIT 1");
        return this.selectOne(queryWrapper);
    }

    default SupplierFirstBusinessEntity findLatestBySupplierIdAndType(Long supplierId, Integer approvalType) {
        LambdaQueryWrapper<SupplierFirstBusinessEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SupplierFirstBusinessEntity::getSupplierId, supplierId)
                .eq(SupplierFirstBusinessEntity::getApprovalType, approvalType)
                .orderByDesc(SupplierFirstBusinessEntity::getApprovalSequence)
                .last("LIMIT 1");
        return this.selectOne(queryWrapper);
    }
} 