package com.dsj.inventory.bussiness.inventory.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.dsj.inventory.framework.config.easyExcel.LocalDateTimeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.time.LocalDateTime;

/**
 * 商品库存导出
 * <AUTHOR>
 * @date 2023/04/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InventoryExportVo", description="商品库存导出实体")
@ToString(callSuper = true)
@ColumnWidth(25)
@HeadRowHeight(20)
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER)//内容样式
public class InventoryExportVo {

    @ApiModelProperty(value = "商品编号")
    @ExcelProperty(value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(value = "通用名")
    @ExcelProperty(value = "通用名称")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    @ExcelProperty(value = "商品名称")
    private String generalName;

    @ApiModelProperty(value = "规格型号")
    @ExcelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "剂型")
    @ExcelProperty(value = "剂型")
    private String drugType;

    @ApiModelProperty(value = "包装单位")
    @ExcelProperty(value = "单位")
    private String packUnit;

    @ApiModelProperty(value = "库存数量")
    @ExcelProperty(value = "库存数量")
    private String inventoryAmount;

    @ApiModelProperty(value = "生产厂家")
    @ExcelProperty(value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(value = "产地")
    @ExcelProperty(value = "产地")
    private String producePlace;

    @ApiModelProperty(value = "生产日期")
    @ExcelProperty(value = "生产日期")
    private String produceDate;

    @ApiModelProperty(value = "有效期至")
    @ExcelProperty(value = "有效期至")
    private String userDate;

    @ApiModelProperty(value = "批次（入库单号）")
    @ExcelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "批号")
    @ExcelProperty(value = "商品批号")
    private String batchNumber;

    @ApiModelProperty(value = "货位名称")
    @ExcelProperty(value = "货位")
    private String shelfName;

    @ApiModelProperty(value = "售价")
    @ExcelProperty(value = "售价")
    private String offlinePrice;

    @ApiModelProperty(value = "成本价格")
    @ExcelProperty(value = "成本均价")
    private String costPrice;

    @ApiModelProperty(value = "库存金额（成本价格*库存数量）")
    @ExcelProperty(value = "库存金额")
    private String inventoryPrice;

    @ApiModelProperty(value = "会员价")
    @ExcelProperty(value = "会员价")
    private String memberPrice;

    @ApiModelProperty(value = "入库时间")
    @ExcelProperty(value = "入库时间",converter = LocalDateTimeConverter.class)
    private LocalDateTime incomingDate;

}
