package com.dsj.inventory.framework.config.easyExcel;


/**
 * 中西医转换器
 */

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.pocky.transport.bussiness.diagnose.enumeration.MedicineEnum;

public class MedicineConverter implements Converter<Integer> {

    @Override
    public Class<Integer> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
                                           GlobalConfiguration globalConfiguration) {
        if (cellData.getStringValue().equals(MedicineEnum.CMEDICINE.getDesc())){
            return MedicineEnum.CMEDICINE.getCode();
        }else if (cellData.getStringValue().equals(MedicineEnum.MEDICINE.getDesc())){
            return MedicineEnum.MEDICINE.getCode();
        }else if(cellData.getStringValue().equals(MedicineEnum.OTHER.getDesc())){
            return MedicineEnum.OTHER.getCode();
        }else {
            return MedicineEnum.MIXTURE.getCode();
        }
    }

    @Override
    public CellData<Integer> convertToExcelData(Integer value, ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        if (value.equals(MedicineEnum.CMEDICINE.getCode())){
            return new CellData<>(MedicineEnum.CMEDICINE.getDesc());
        }else if (value.equals(MedicineEnum.MEDICINE.getCode())){
            return new CellData<>(MedicineEnum.MEDICINE.getDesc());
        }else if(value.equals(MedicineEnum.OTHER.getCode())){
            return new CellData<>(MedicineEnum.OTHER.getDesc());
        }else {
            return new CellData<>(MedicineEnum.MIXTURE.getDesc());
        }
    }

}
