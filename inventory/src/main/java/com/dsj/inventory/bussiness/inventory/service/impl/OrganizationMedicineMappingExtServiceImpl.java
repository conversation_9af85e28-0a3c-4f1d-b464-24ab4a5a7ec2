package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dsj.inventory.bussiness.inventory.service.OrganizationMedicineMappingExtService;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 组织私域药品映射 扩展服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class OrganizationMedicineMappingExtServiceImpl implements OrganizationMedicineMappingExtService {

    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;

    @Override
    public boolean updateMedicineInventoryInfo(Long id, double repertory, int averageCost,
                                             LocalDateTime lastIncomingTime, boolean updateInventoryShelve,
                                             LocalDateTime lastSaleTime) {
        LambdaUpdateWrapper<OrganizationMedicineMapping> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrganizationMedicineMapping::getId, id);
        updateWrapper.set(OrganizationMedicineMapping::getMedicineRepertory, repertory);
        if (updateInventoryShelve) {
            updateWrapper.set(OrganizationMedicineMapping::getInventoryShelve, TrueEnum.TRUE.getCode());
        }
        updateWrapper.set(OrganizationMedicineMapping::getAverageCost, averageCost);
        updateWrapper.set(OrganizationMedicineMapping::getLastIncomingTime, lastIncomingTime);
        updateWrapper.set(OrganizationMedicineMapping::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(OrganizationMedicineMapping::getUpdateUser, BaseContextHandler.getUserId());
        if (lastSaleTime != null) {
            updateWrapper.set(OrganizationMedicineMapping::getLastSaleTime, lastSaleTime);
        }
        return organizationMedicineMappingService.update(new OrganizationMedicineMapping(), updateWrapper);
    }
} 