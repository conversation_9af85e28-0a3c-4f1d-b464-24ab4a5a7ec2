package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryReceiveOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateReceiveOrderParam;
import com.dsj.inventory.bussiness.inventory.service.ReceiveOrderService;
import com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.EasyExcelUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 进销存收货单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
@RestController
@Api(value = "ReceiveOrderController", tags = "进销存收货单api")
public class ReceiveOrderController {

    @Autowired
    private ReceiveOrderService receiveOrderService;

    /*新增收货单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-3"})
    @PostMapping("/receive-order")
    @ApiOperation(value = "新增收货单", notes = "新增收货单")
    public ResponseEntity<ReceiveOrderDetailVo> saveReceiveOrder(@RequestBody SaveOrUpdateReceiveOrderParam param){
        ReceiveOrderDetailVo receiveVo = receiveOrderService.saveOrUpdateReceiveOrder(param);
        return Res.success(receiveVo);
    }

    /*编辑收货单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-3"})
    @PutMapping("/receive-order/{id}")
    @ApiOperation(value = "编辑收货单", notes = "编辑收货单")
    public ResponseEntity<ReceiveOrderDetailVo> updateReceiveOrder(@PathVariable Long id, @RequestBody SaveOrUpdateReceiveOrderParam param){
        param.setId(id);
        ReceiveOrderDetailVo receiveVo = receiveOrderService.saveOrUpdateReceiveOrder(param);
        return Res.success(receiveVo);
    }

    /*删除收货单*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-3"})
    @DeleteMapping("/receive-order/{id}")
    @ApiOperation(value = "删除收货单", notes = "删除收货单")
    public ResponseEntity<Void> deleteReceiveOrder(@PathVariable Long id){
        receiveOrderService.deleteReceiveOrder(id);
        return Res.success();
    }

    /*获取收货单列表*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-3"})
    @GetMapping("/receive-order")
    @ApiOperation(value = "获取收货单列表", notes = "获取收货单列表")
    public ResponseEntity<List<ReceiveOrderVo>> queryReceiveOrderList(QueryReceiveOrderParam param){
        Page<ReceiveOrderVo> page = receiveOrderService.queryReceiveOrderPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取收货单详情*/
    @GetMapping("/receive-order/{id}")
    @ApiOperation(value = "获取收货单详情", notes = "获取收货单详情")
    public ResponseEntity<ReceiveOrderDetailVo> queryReceiveOrder(@PathVariable Long id){
        return Res.success(receiveOrderService.getReceiveOrderById(id));
    }

    /*提交收货单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-3"})
    @PostMapping("/receive-order/{id}/action-submit")
    @ApiOperation(value = "提交收货单", notes = "提交收货单")
    public ResponseEntity<Void> submitReceiveOrder(@PathVariable Long id){
        receiveOrderService.submitReceiveOrder(id);
        return Res.success();
    }

    /*收货单导出*/
    @GetMapping("/receive-order/action-export")
    @ApiOperation(value = "导出收货单", notes = "导出收货单")
    public ResponseEntity<Void> exportReceiveOrder(HttpServletResponse response, QueryReceiveOrderParam param) throws IOException {
        param.setNeedExport(true);
        List<ReceiveOrderExportVo> list = receiveOrderService.queryReceiveOrderList(param);
        EasyExcelUtils.exportOrdinaryExcel(response,list,"收货单导出列表","收货单导出列表", ReceiveOrderExportVo.class);
        return Res.success();
    }

}
