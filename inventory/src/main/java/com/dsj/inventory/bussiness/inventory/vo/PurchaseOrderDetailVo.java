package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购订单详情
 * <AUTHOR>
 * @date 2023/04/06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PurchaseOrderDetailVo", description="采购订单详情实体")
@ToString(callSuper = true)
public class PurchaseOrderDetailVo {

    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "计划Id")
    private Long planId;

    @ApiModelProperty(value = "计划单号")
    private String orderCode;

    @ApiModelProperty(value = "订单单号")
    private String code;

    @ApiModelProperty(value = "开票日期")
    private LocalDateTime billDate;

    @ApiModelProperty(value = "预计收货日期")
    private LocalDateTime expectReceiveDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "处理状态 0暂存 1待收货 2已完成")
    private Integer dealState;

    @ApiModelProperty(value = "采购员Id")
    private Long purchaserId;

    @ApiModelProperty(value = "采购员名称")
    private String purchaserName;

    @ApiModelProperty(value = "私域供应商Id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "供应商名称")
    private String organizationSupplierName;

    @ApiModelProperty(value = "供应商编号")
    private String organizationSupplierCode;

    @ApiModelProperty(value = "总金额")
    private Integer totalPrice;

    @ApiModelProperty(value = "商品种类")
    private String goodsTypes;

    @ApiModelProperty(value = "采购内容")
    private String purchaseContent;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "商品明细")
    private List<PurchaseOrderGoodsVo> goodsList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
