### 获取会员列表
GET {{baseUrl}}member
Content-Type: application/json
token: {{token}}

### 获取会员列表(分页)
GET {{baseUrl}}member?page=1&size=10
Content-Type: application/json
token: {{token}}

### 获取会员详情
GET {{baseUrl}}member/1914703028238290944
Content-Type: application/json
token: {{token}}

### 新增会员
POST {{baseUrl}}member
Content-Type: application/json
token: {{token}}

{
  "memberName": "测试会员",
  "memberPhone": "13800138000",
  "memberType": 1,
  "status": 1,
  "remark": "测试备注"
}

### 编辑会员
PUT {{baseUrl}}member/1
Content-Type: application/json
token: {{token}}

{
  "memberName": "更新会员名称",
  "memberPhone": "13800138001",
  "memberType": 1,
  "status": 1,
  "remark": "更新备注"
}

### 删除会员
DELETE {{baseUrl}}member/1
Content-Type: application/json
token: {{token}}

### 导出会员
GET {{baseUrl}}member/action-export
Content-Type: application/json
token: {{token}}