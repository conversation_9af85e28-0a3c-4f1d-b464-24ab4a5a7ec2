package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 会员
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MemberVo", description="会员实体")
@ToString(callSuper = true)
public class MemberVo {

    private Long id;

    @ApiModelProperty(value = "组织id")
    private Long organizationId;

    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    @ApiModelProperty(value = "系统账号Id")
    private Long userId;

    @ApiModelProperty(value = "会员卡号")
    private String code;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "性别1 男  2 女  0 未知")
    private Integer gender;

    @ApiModelProperty(value = "生日(yyyy-MM-dd)")
    private String birth;

    @ApiModelProperty(value = "身份证号码")
    private String card;

    @ApiModelProperty(value = "省")
    private Long province;

    @ApiModelProperty(value = "市")
    private Long city;

    @ApiModelProperty(value = "县")
    private Long county;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "余额")
    private Integer balance;

    @ApiModelProperty(value = "可用积分")
    private Integer usableIntegral;

    @ApiModelProperty(value = "累积积分")
    private Integer accrueIntegral;

    @ApiModelProperty(value = "消费密码")
    private String consumePwd;

    @ApiModelProperty(value = "是否开启安全验证 0否1是")
    private Integer safetyState;

    @ApiModelProperty(value = "办理人Id")
    private Long transactUser;

    @ApiModelProperty(value = "办理人名称")
    private String transactUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
