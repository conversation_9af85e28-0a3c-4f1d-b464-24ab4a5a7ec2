spring:
  application:
    name: dsj-inventory-test
  # 禁用外部组件自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration
      - org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration
  datasource:
    # 使用H2内存数据库进行测试
#    driver-class-name: org.h2.Driver
#    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MYSQL;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE
    driverClassName: com.p6spy.engine.spy.P6SpyDriver
    url: ***********************************************************************************************************************************************************************************************************
    username: root
    password: 123123
    # 启用 SQL 脚本初始化（兼容 Spring Boot 2.x）
    initialization-mode: always
    schema: classpath:schema.sql
    data: classpath:data.sql
    # 初始化模式设置（兼容 Spring Boot 2.5+）
    schema-username: root
    schema-password: 123123
    data-username: root
    data-password: 123123
  sql:
    init:
      mode: always
      platform: h2
      schema-locations: classpath:schema.sql
      data-locations: classpath:data.sql
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: none # 我们使用 schema.sql 创建表结构，不需要 Hibernate 自动生成
    show-sql: true
  main:
    # 允许Bean定义覆盖，避免冲突
    allow-bean-definition-overriding: true
    # 懒加载所有Bean，提高测试启动速度
    lazy-initialization: true
  
  # 不包含 application-datasource.yml
  profiles:
    include: []
    
# 关闭 Spring Boot Admin 客户端
spring.boot.admin.client.enabled: false
    
# MyBatis Plus 配置 
mybatis-plus:
  # 仅扫描项目自身的mapper文件，不扫描外部依赖
  mapper-locations: classpath:mapper/**/*Mapper.xml
  # 明确不扫描jar包中的mapper
  check-config-location: true
  # 仅指定项目自身的实体类包
  typeAliasesPackage: com.dsj.inventory.bussiness.inventory.entity
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: isDelete
      logic-delete-value: 1
      logic-not-delete-value: 0
      
# 测试环境禁用一些特性
xxl:
  job:
    enabled: false

# 使用 H2 控制台进行调试
h2:
  console:
    enabled: true
    path: /h2-console

# 提供测试环境中需要的属性值
pocky:
  server:
    # 雪花算法工作节点ID(0~31)
    workerId: 12
    # 雪花算法数据中心ID(0~31)
    dataCenterId: 25
    # 是否使用雪花算法生成编号（false表示使用Redis实现），这里的编号不是指ID，是数据中的编号，参考NumberGeneratorFactory
    numberGeneratorUseSnowflake: true
  mysql:
    driverClassName: com.p6spy.engine.spy.P6SpyDriver
    ip: localhost
    port: 3306
    database: medicine_cloud_test
    username: root
    password: 123123
  redis:
    ip: localhost
    port: 6379
    password: 
    database: 1

# 日志配置
logging:
  file:
    path: ~/logs/inventory
  level:
    com.dsj.inventory: debug
    com.baomidou.mybatisplus: debug
    org.hibernate.SQL: debug
    org.hibernate.type.descriptor.sql: trace
