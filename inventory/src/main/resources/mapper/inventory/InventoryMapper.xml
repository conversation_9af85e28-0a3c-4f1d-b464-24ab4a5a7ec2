<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.InventoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.Inventory">
    <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="organization_medicine_id" property="organizationMedicineId" />
        <result column="produce_date" property="produceDate" />
        <result column="user_date" property="userDate" />
        <result column="inventory_amount" property="inventoryAmount" />
        <result column="cost_price" property="costPrice" />
        <result column="batch_number" property="batchNumber" />
        <result column="shelf_id" property="shelfId" />
        <result column="batch" property="batch" />
        <result column="incoming_date" property="incomingDate" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        organization_id, organization_medicine_id, produce_date, user_date, inventory_amount, cost_price, batch_number, shelf_id, batch, incoming_date, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="queryInventoryPage" resultType="com.dsj.inventory.bussiness.inventory.vo.InventoryVo">
        SELECT distinct ji.id
        ,ji.organization_id organizationId
        ,ji.organization_medicine_id organizationMedicineId
        ,homm.name goodsName
        ,homm.general_name generalName
        ,homm.goods_code goodsCode
        ,homm.pack_unit packUnit
        ,homm.specification
        ,homm.drug_type drugType
        ,homm.manufacturer
        ,home.launch_permit_holder launchPermitHolder
        ,homm.produce_place producePlace
        ,homm.approval_number approvalNumber
        ,homm.bar_code barCode
        ,homm.offline_price offlinePrice
        ,homm.member_price memberPrice
        ,ji.produce_date produceDate
        ,ji.user_date userDate
        ,ji.inventory_amount inventoryAmount
        ,jiog.returned_amount returnedAmount
        ,jiog.incoming_amount incomingAmount
        ,jiog.discount_price purchasePrice
        ,(CASE WHEN orm.cost_compute = 2 THEN ji.cost_price ELSE homm.average_cost END) costPrice
        ,(CASE WHEN orm.cost_compute = 2
         THEN ROUND((ji.cost_price*ji.inventory_amount),0)
         ELSE ROUND((homm.average_cost*ji.inventory_amount),0) END) inventoryPrice
        ,ji.batch_number batchNumber
        ,ji.shelf_id shelfId
        ,js.name shelfName
        ,ji.batch
        ,ji.incoming_date incomingDate
        ,jos.name organizationSupplierName
        ,ji.create_time createTime
        FROM jxc_inventory ji
        LEFT JOIN hos_organization_medicine_mapping homm ON homm.id = ji.organization_medicine_id AND homm.is_delete = 0
        LEFT JOIN hos_organization_medicine_expand home ON home.id = homm.id AND home.is_delete = 0
        LEFT JOIN jxc_shelf js ON js.id = ji.shelf_id AND js.is_delete = 0
        LEFT JOIN jxc_incoming_order_goods jiog ON jiog.id = ji.incoming_goods_id AND jiog.is_delete = 0
        LEFT JOIN jxc_incoming_order jio ON jio.id = jiog.order_id AND jio.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jio.organization_supplier_id AND jos.is_delete = 0
        LEFT JOIN sys_org_r_module orm ON orm.organization_id = jio.organization_id AND orm.is_delete = 0
        WHERE
        ji.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND ji.organization_id = #{param.organizationId}
        </if>
        <if test="param.batch != null and param.batch != '' ">
            AND ji.batch = #{param.batch}
        </if>
        <if test="param.shelfId != null and param.shelfId != '' ">
            AND ji.shelf_id = #{param.shelfId}
        </if>
        <if test="param.shelfIds != null and param.shelfIds != '' ">
            AND ji.shelf_id in
            <foreach item="item" index="index" collection="param.shelfIds.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.goodsLike != null and param.goodsLike != '' ">
            AND (homm.goods_code like CONCAT('%',#{param.goodsLike},'%') OR homm.name like CONCAT('%',#{param.goodsLike},'%'))
        </if>
        <choose>
            <when test="param.zeroState != null and param.zeroState == 0 ">
                AND ji.inventory_amount = 0
            </when>
            <when test="param.zeroState != null and param.zeroState == 1 ">
                AND ji.inventory_amount <![CDATA[>]]> 0
            </when>
        </choose>
        GROUP BY ji.id
        ORDER BY ji.create_time desc
    </select>

    <select id="queryInventoryList" resultType="com.dsj.inventory.bussiness.inventory.vo.InventoryExportVo">
        SELECT distinct ji.id
        ,homm.name goodsName
        ,homm.general_name generalName
        ,homm.goods_code goodsCode
        ,homm.pack_unit packUnit
        ,homm.specification
        ,homm.drug_type drugType
        ,homm.manufacturer
        ,homm.produce_place producePlace
        ,ROUND(homm.offline_price/100,2) offlinePrice
        ,ROUND(homm.member_price/100,2) memberPrice
        ,DATE_FORMAT(ji.produce_date,'%Y-%m-%d') produceDate
        ,DATE_FORMAT(ji.user_date,'%Y-%m-%d') userDate
        ,ji.inventory_amount inventoryAmount
        ,(CASE WHEN orm.cost_compute = 2 THEN ROUND(ji.cost_price/100,2) ELSE ROUND(homm.average_cost/100,2) END) costPrice
        ,(CASE WHEN orm.cost_compute = 2
        THEN ROUND((ji.cost_price*ji.inventory_amount)/100,2)
        ELSE ROUND((homm.average_cost*ji.inventory_amount)/100,2) END) inventoryPrice
        ,ji.batch_number batchNumber
        ,js.name shelfName
        ,ji.batch
        ,ji.incoming_date incomingDate
        FROM jxc_inventory ji
        LEFT JOIN hos_organization_medicine_mapping homm ON homm.id = ji.organization_medicine_id AND homm.is_delete = 0
        LEFT JOIN jxc_shelf js ON js.id = ji.shelf_id AND js.is_delete = 0
        LEFT JOIN jxc_incoming_order_goods jiog ON jiog.id = ji.incoming_goods_id AND jiog.is_delete = 0
        LEFT JOIN jxc_incoming_order jio ON jio.id = jiog.order_id AND jio.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jio.organization_supplier_id AND jos.is_delete = 0
        LEFT JOIN sys_org_r_module orm ON orm.organization_id = jio.organization_id AND orm.is_delete = 0
        WHERE
        ji.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND ji.organization_id = #{param.organizationId}
        </if>
        <if test="param.shelfId != null and param.shelfId != '' ">
            AND ji.shelf_id = #{param.shelfId}
        </if>
        <if test="param.goodsLike != null and param.goodsLike != '' ">
            AND (homm.goods_code like CONCAT('%',#{param.goodsLike},'%') OR homm.name like CONCAT('%',#{param.goodsLike},'%'))
        </if>
        <choose>
            <when test="param.zeroState != null and param.zeroState == 0 ">
                AND ji.inventory_amount = 0
            </when>
            <when test="param.zeroState != null and param.zeroState == 1 ">
                AND ji.inventory_amount <![CDATA[>]]> 0
            </when>
        </choose>
        GROUP BY ji.id
        ORDER BY ji.create_time desc
    </select>

    <select id="queryInventorySalePage" resultType="com.dsj.inventory.bussiness.inventory.vo.InventorySaleVo">
        SELECT ji.organization_id organizationId
        ,ji.organization_medicine_id organizationMedicineId
        ,homm.name goodsName
        ,homm.general_name generalName
        ,homm.goods_code goodsCode
        ,homm.pack_unit packUnit
        ,homm.specification
        ,homm.drug_type drugType
        ,homm.manufacturer
        ,homm.produce_place producePlace
        ,homm.approval_number approvalNumber
        ,homm.bar_code barCode
        ,homm.offline_price offlinePrice
        ,homm.member_price memberPrice
        ,ji.produce_date produceDate
        ,ji.user_date userDate
        ,SUM(ji.inventory_amount) inventoryAmount
        ,ji.batch_number batchNumber
        ,ji.shelf_id shelfId
        ,js.name shelfName
        ,homm.effective_month effectiveMonth
        ,homm.usage_dosage usageDosage
        ,homm.special_offer specialOffer
        ,(SELECT hommf.file_path FROM hos_organization_medicine_mapping_file hommf WHERE hommf.mapping_id = ji.organization_medicine_id AND hommf.is_delete = 0 ORDER BY hommf.create_time LIMIT 1 ) medicineFilePath
        FROM jxc_inventory ji
        LEFT JOIN hos_organization_medicine_mapping homm ON homm.id = ji.organization_medicine_id AND homm.is_delete = 0
        LEFT JOIN jxc_shelf js ON js.id = ji.shelf_id AND js.is_delete = 0
        WHERE
        ji.is_delete = 0
        AND ji.inventory_amount <![CDATA[>]]> 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND ji.organization_id = #{param.organizationId}
        </if>
        <if test="param.organizationMedicineId != null and param.organizationMedicineId != '' ">
            AND ji.organization_medicine_id = #{param.organizationMedicineId}
        </if>
        <if test="param.inventoryShelve != null">
            AND homm.inventory_shelve = #{param.inventoryShelve}
        </if>
        <if test="param.goodsLike != null and param.goodsLike != '' ">
            AND (homm.name like CONCAT('%',#{param.goodsLike},'%')
                     OR homm.general_name like CONCAT('%',#{param.goodsLike},'%')
                     OR homm.approval_number like CONCAT('%',#{param.goodsLike},'%'))
        </if>
        GROUP BY ji.organization_medicine_id, ji.batch_number, ji.shelf_id
        ORDER BY ji.user_date
    </select>

    <select id="queryBecomeDueInventoryList" resultType="com.dsj.inventory.bussiness.inventory.vo.InventoryVo">
        SELECT ji.id
        ,ji.organization_medicine_id organizationMedicineId
        FROM
            jxc_inventory ji
        WHERE
            ji.is_delete = 0
          AND ji.inventory_amount <![CDATA[>]]> 0
          AND ji.organization_id = #{organizationId}
          AND (TO_DAYS(ji.user_date) - TO_DAYS(now())) <![CDATA[<=]]> #{becomeDueDay}
          AND NOT EXISTS
            (SELECT distinct jwl.inventory_id FROM jxc_warning_log jwl
              WHERE ji.id = jwl.inventory_id
              AND jwl.is_delete = 0
              AND jwl.type = 4
              AND jwl.organization_id = #{organizationId})
    </select>

</mapper>
