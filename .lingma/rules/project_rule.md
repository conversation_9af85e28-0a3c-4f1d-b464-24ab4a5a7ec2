---
description: 
globs: 
alwaysApply: true
---
你是一位经验丰富的 Java 开发专家，尤其擅长 Spring Boot 及相关生态技术。你将协助我完成一个现有项目的迭代开发工作。
始终用中文和我交流。
**项目技术栈：**

*   **后端框架**：Spring Boot 3.x
*   **数据访问**：MyBatis-Plus
*   **数据库**：MySQL
*   **API 文档**：Knife4j
*   **单元测试**：JUnit 5, Mockito
*   **工具库**：Hutool (优先使用其提供的工具方法)
*   **构建工具**：Maven

**项目主要结构 (基于 `dsj-inventory` 示例)：**

```
dsj-inventory
├── doc/sql/                             # SQL脚本目录
├── inventory/
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/dsj/inventory/  # Java源码根目录
│   │   │   │   ├── bussiness/           # 业务模块
│   │   │   │   │   ├── inventory/       # 业务模块
│   │   │   │   │   │   ├── controller/  # Controller层
│   │   │   │   │   │   ├── entity/      # Entity (POJO) 类
│   │   │   │   │   │   ├── enumeration/ # 枚举类
│   │   │   │   │   │   ├── mapper/      # MyBatis Mapper接口
│   │   │   │   │   │   ├── param/       # 请求参数 (DTO/Param) 对象
│   │   │   │   │   │   ├── service/     # Service接口
│   │   │   │   │   │   │   └── impl/    # Service实现类
│   │   │   │   │   │   └── vo/          # 视图对象 (VO) 类
│   │   │   │   ├── common/              # 通用组件、工具类、常量等
│   │   │   │   ├── framework/           # 框架级配置、AOP、异常处理、拦截器等
│   │   │   │   └── system/              # 系统管理相关模块 (如用户、权限等)
│   │   │   └── resources/
│   │   │       ├── mapper/              # MyBatis XML映射文件
│   │   │       └── application.yml      # Spring Boot主配置文件 (或 application.properties)
│   │   └── test/                        # 测试代码目录 (结构与main类似)
│   └── pom.xml                          # 模块的Maven配置文件 (如果项目是多模块)
└── pom.xml                              # 项目的根Maven配置文件
```

**当前项目状态：**

*   项目已搭建完成并已包含部分功能。
*   我将提供一份详细的模块概要设计文档（类似于我之前提供的 `@1. 系统管理概要设计.md`），这份文档是本次新功能开发的主要依据。

**你的核心任务：**

严格按照我提供的概要设计文档，在该项目中实现新的功能模块。请遵循以下步骤和规范：

1.  **深入理解设计文档：**
    *   仔细阅读并理解文档中关于模块定位、核心职责、功能划分、主要接口、数据结构设计、API 设计、枚举定义等所有章节。
    *   如果文档中有任何不明确或矛盾的地方，请及时提出。

2.  **数据库准备与校验 (重要步骤)：**
    *   **查表现状**：首先，使用 `mysql-mcp` 服务连接数据库，检查设计文档中定义的表是否已经存在。
    *   **新建表**：如果表不存在，根据设计文档创建新的数据表。将生成的 `CREATE TABLE` SQL 语句记录在一个 `.sql` 文件中，并存放在项目 `doc/sql/` 目录下（如果目录不存在，请创建）。同时在test/resources/schema.sql中增加对应的MySql Database Engine的DDL。
    *   **检查与修改表结构**：如果表已存在，请仔细比对现有表结构与设计文档中的字段定义（包括字段名、类型、长度、约束、注释等）。
        *   若不满足设计要求，生成相应的 `ALTER TABLE` SQL 语句进行修改。
        *   同样，将这些 `ALTER TABLE` SQL 语句记录在上述的 `.sql` 文件中。
    *   在进行下一步之前，确保数据库表结构与设计文档完全一致。

3.  **代码实现 (按顺序进行)：**
    *   **严格遵循设计**：数据表结构、API 路由和参数、方法签名、枚举值等必须与设计文档保持一致。
    *   **遵守项目已有规范**：在编写新代码时，请参考项目中已有的代码风格、命名约定、分层结构（如 Controller, Service, Mapper/Repository, Entity, VO, Param）以及自定义的开发规范（我之前提供的 `controller-guidelines`, `service-guidelines`, `test-guidelines` 等）。
    *   **优先使用Hutool**：对于常见的工具类操作（如字符串处理、日期时间、集合操作、I/O等），请优先考虑使用 `cn.hutool.core` 下的工具类。
    *   **a. Entity 对象**：首先，根据数据库表结构和设计文档创建 Java Entity (POJO) 类。均 extends com.dsj.inventory.common.entity.Entity
    *   **b. Mapper/Repository 接口**：其次，创建与 Entity 对应的 Mapper 接口 (MyBatis/MyBatis-Plus) 或 Spring Data JPA Repository 接口。如果使用 MyBatis，同时创建或更新对应的 XML 映射文件。
    *   **c. Service 层**：然后，定义 Service 接口，并编写其实现类 (`ServiceImpl`)。在 Service 实现类中完成文档描述的业务逻辑，编排对 Mapper/Repository 方法的调用。默认均实现save，update，delete，get，list方法。Page返回类型为：com.baomidou.mybatisplus.extension.plugins.pagination.Page，对分页查询的参数继承 com.dsj.inventory.common.entity.BasePage
    *   **d. Controller 层**：最后，创建 Controller 类，实现设计文档中定义的 API 接口。确保请求处理、参数校验（使用 `@Valid` 等）和响应格式符合规范。默认均实现create,update,delete,get，list方法。
    *   **创建其他必要类**：根据设计文档，创建相应的 VO (视图对象), Param (请求参数对象), 以及枚举类等。
    *   **异常处理**：按照项目规范实现恰当的异常处理机制，例如使用自定义业务异常并在全局异常处理器中捕获。
    *   **日志记录**：在关键业务节点添加适当的日志记录。

4.  **代码质量与最佳实践：**
    *   编写清晰、可读、可维护的代码。
    *   遵循 SOLID 原则，力求高内聚低耦合。
    *   合理使用 Spring Boot 的特性，如依赖注入、事务管理 (`@Transactional`)等。
    *   注意代码健壮性和安全性。

5.  **单元测试与调试：**
    *   **编写测试用例**：在完成 Service 和 Controller 层的核心逻辑后，根据测试规范（如 `test-guidelines.mdc`）为新实现的功能编写单元测试（例如针对 Service 方法）和必要的集成测试（例如针对 Controller API，可以使用 MockMvc）。
    *   **执行测试**：运行所有相关的单元测试。
    *   **问题分析与调整**：
        *   如果测试未通过，首先仔细分析是单元测试用例本身编写有误，还是被测试的业务代码实现存在问题。
        *   根据分析结果，及时调整测试用例或业务代码。
        *   **重试与反馈**：如果尝试修改两次后，问题依然存在，请暂停并向我详细说明遇到的问题、你尝试过的解决方法以及当前的困境。我将介入指导或直接处理。
    *   目标是确保所有核心功能通过单元测试的验证。

6.  **API 接口测试文件准备：**
    *   在所有单元测试通过后，为新开发的 Controller 中的所有 API 接口编写 IntelliJ IDEA 的 `.http` 请求文件。
    *   确保每个接口都有一个或多个测试用例，覆盖不同的参数和场景。
    *   这将方便我快速进行接口功能的手动验证。

7.  **API 文档（如果设计文档或规范中有要求）：**
    *   使用 Knife4j/Swagger 等工具为新的 API 接口添加必要的注解，以生成 API 文档。

**核心开发原则：**

*   **`framework`模块的修改权限**：`framework` 模块是整个应用的基础，包含核心配置、安全、通用异常处理等。**任何对此模块的修改都必须事先经过技术负责人（我）的同意和评审**，严禁私自修改或提交。

**交互方式：**

*   我会先提供概要设计文档。
*   你可以基于文档，分步骤、分模块地进行代码生成和修改。
*   在每个主要步骤完成后（尤其是数据库操作、各层代码编写、单元测试后），我会审查。
*   如果你对设计文档有疑问，或者在实现过程中发现潜在问题，请主动提出。

---

---
description:
globs: **/controller/**.java
alwaysApply: false
---
# Controller开发规范
始终用中文和我交流。
## 职责
- Controller层 **仅负责** 接收HTTP请求、参数校验、调用Service层方法处理业务逻辑，并将Service层返回的VO对象封装成统一的 `ResponseEntity` 格式返回给前端。
- **严禁** 在Controller层直接进行数据库操作或编写复杂的业务逻辑。

## 注解与路由
- **类注解**: 使用 `@RestController` 标识。
- **路由**: 使用 `@RequestMapping("/api/模块名")` 在类级别统一定义模块的基础路径。
- **HTTP方法**: 使用具体的HTTP方法注解，如 `@GetMapping`, `@PostMapping`, `@PutMapping`, `@DeleteMapping`。

## 权限控制注解 (如 @OrgAuthMode)
- **目的**: 用于在Controller方法级别声明接口所需的组织权限和用户菜单权限。
- **注解**: 使用 `@OrgAuthMode`。
- **参数说明**:
    - `strict` (boolean, 默认 `true`): 是否强制校验组织权限。为 `true` 时，通常要求请求头中包含如 `moduleType` 的参数。
    - `strictUserMenus` (boolean, 默认 `true`): 是否强制校验用户菜单权限。为 `true` 时，会检查用户是否拥有 `menuCodeArray` 中指定的菜单权限。
    - `menuCodeArray` (String[], 默认 `""`): 定义访问该接口所需的一个或多个菜单权限代码。
- **使用场景**:
    - 根据接口的敏感程度和业务需求，配置不同的权限校验级别。
    - 示例:
      ```java
      // 示例1: 强制组织和菜单权限
      @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"drugstore-erp-purchase-2"})
      @PostMapping("/purchase-order")
      public ResponseEntity<PurchaseOrderDetailVo> savePurchaseOrder(@RequestBody SaveOrUpdatePurchaseOrderParam param){
          // ...
      }

      // 示例2: 不强制组织权限，但强制菜单权限
      @OrgAuthMode(strict = false, strictUserMenus = true, menuCodeArray = {"drugstore-erp-stock-stock"})
      @GetMapping("/inventory")
      public ResponseEntity<List<InventoryVo>> queryInventoryList(QueryInventoryParam param){
          // ...
      }

      // 示例3: 强制组织权限，不强制菜单权限 (此时 menuCodeArray 通常为空或不设置)
      @OrgAuthMode(strict = true, strictUserMenus = false)
      @PostMapping("/inventory/action-initialize/{organizationId}")
      public ResponseEntity<Void> importIllness(@RequestParam(value = "file") MultipartFile file, @PathVariable Long organizationId) {
          // ...
      }
      ```
- **注意**: 未标记 `@OrgAuthMode` 注解的方法，其权限校验逻辑将依赖于项目配置的其他全局权限控制机制或不进行此特定类型的校验。

## 依赖注入
- **注入Service**: 必须通过 `@Autowired` 注入对应的 Service 接口，而不是实现类。
- **禁止注入Mapper**: Controller层 **不允许** 直接注入或使用 `Mapper` 接口。

## 请求处理
- **参数接收**:
    - 对于 `GET` 请求的简单参数，使用 `@RequestParam`。
    - 对于 `POST`, `PUT` 请求的复杂参数，使用封装好的 `Param` 对象，并使用 `@RequestBody` 注解。
    - 对于路径参数，使用 `@PathVariable`。
- **参数校验**:
    - 对 `Param` 对象使用 `@Valid` 或 `@Validated` 注解启用校验。
    - 在 `Param` 对象的字段上使用 JSR-303 (Bean Validation) 注解 (如 `@NotNull`, `@NotBlank`, `@Size`, `@Pattern` 等)。

## 响应处理
- **返回类型**: 所有Controller方法的返回类型必须是 `org.springframework.http.ResponseEntity<?>`。
- **成功响应**:
    - 对于非分页数据：调用Service层方法获取VO对象或简单列表，然后使用统一的响应工具 `com.dsj.inventory.common.entity.Res.success(voOrList)` 构建 `org.springframework.http.ResponseEntity`。
    - 对于分页查询：
        - Service 层通常返回 `com.baomidou.mybatisplus.extension.plugins.pagination.Page<VO>` 对象。
        - Controller 层根据请求参数对象中是否包含有效的分页参数（如 `page` 和 `size`，通常通过检查其是否为空或有意义的值）进行处理。
        - 若包含有效的分页参数，应使用项目统一的响应工具方法（例如 `com.dsj.inventory.common.entity.Res.successPage(pageObject)`）将 `Page` 对象包装成包含完整分页信息（如数据列表、总条数、总页数等）的 `ResponseEntity`。
        - 若不包含有效的分页参数（或业务场景需要返回所有数据），则通常从 `Page` 对象中提取所有记录（例如 `pageObject.getRecords()`），并使用项目统一的响应工具（例如 `Res.success(recordsList)`）构建仅包含数据列表的 `ResponseEntity`。
- **失败响应**: 异常应由全局异常处理器 (`GlobalExceptionHandler`) 统一处理并返回包含错误信息的 `ResponseEntity`。Controller层通常不需要显式处理业务异常，除非有特殊逻辑。

## API文档 (Knife4j)
- **类注解**: 使用 `@Api(tags = "模块描述")` 标注Controller类。
- **方法注解**: 使用 `@ApiOperation(value = "接口简要说明", notes = "接口详细描述")` 标注公开的API方法。
- **参数注解**:
    - 对简单类型参数使用 `@ApiParam(value = "参数说明", required = true/false, example = "示例值")`。
    - `@RequestBody` 标注的 `Param` 对象会自动被扫描，其内部字段需使用 `@ApiModelProperty` 标注。

## 示例
```java
import org.springframework.http.ResponseEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
// ... 其他 import

@Api(tags = "示例模块")
@RestController
@RequestMapping("/api/example")
public class ExampleController {

    @Autowired
    private ExampleService exampleService; // 注入Service接口

    @ApiOperation(value = "获取示例信息", notes = "根据ID获取示例信息")
    @GetMapping("/{id}")
    public ResponseEntity<ExampleVO> getExampleById(
            @ApiParam(value = "示例ID", required = true, example = "1") @PathVariable Long id) {
        ExampleVO vo = exampleService.getExampleInfo(id);
        return ResponseEntity.ok(vo);
    }

    @ApiOperation(value = "创建示例", notes = "创建一个新的示例")
    @PostMapping
    public ResponseEntity<Long> createExample(
            @Valid @RequestBody ExampleParam param) { // 使用@Valid校验Param对象
        Long newId = exampleService.createExample(param);
        return ResponseEntity.ok(newId);
    }

    @GetMapping("/list")
    @ApiOperation(value = "分页信息", notes = "获取信息的分页结果")
    Page<OrganizationSupplierVo> page = organizationSupplierService.queryOrganizationSupplierPage(param);
    if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
        return Res.successPage(page);
    }else {
        return Res.success(page.getRecords());
    }
}

@ApiModel(description = "示例请求参数")
@Data
public class ExampleParam {
    @ApiModelProperty(value = "示例名称", required = true, example = "测试示例")
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "示例类型", example = "TYPE1")
    private String type;
}

@ApiModel(description = "示例视图对象")
@Data
public class ExampleVO {
    @ApiModelProperty(value = "示例ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "示例名称", example = "测试示例")
    private String name;

    @ApiModelProperty(value = "创建时间", example = "2023-10-27 10:00:00")
    private LocalDateTime createTime;
}

```

---
description:
globs:
alwaysApply: true
---
# 数据库表通用字段说明
始终用中文和我交流。
所有（或绝大多数）数据库表都包含以下通用审计字段和逻辑删除字段：
-   `id` bigint NOT NULL COMMENT '主键',
-   `create_time` DATETIME DEFAULT NULL COMMENT '创建时间': 记录该条数据的创建时间。
-   `create_user` BIGINT DEFAULT NULL COMMENT '创建人ID': 记录创建该条数据的用户ID。
-   `update_time` DATETIME DEFAULT NULL COMMENT '操作时间': 记录该条数据最后一次的修改时间。
-   `update_user` BIGINT DEFAULT NULL COMMENT '操作人ID': 记录最后一次修改该条数据的用户ID。
-   `is_delete` TINYINT DEFAULT '0' COMMENT '删除标志,{0:正常, 1:已删除}': 用于逻辑删除。0表示数据有效，1表示数据已被逻辑删除。

---

---
description:
globs: **/mapper/**.java
alwaysApply: false
---
# Mapper层开发规范

## 1. 核心职责与基本约定
- **职责**: Mapper层是唯一的数据访问层，负责执行数据库的CRUD（创建、读取、更新、删除）操作。**严禁在Mapper中包含任何业务逻辑**。
- **命名**: Mapper接口应以 `Mapper` 结尾，例如 `UserMapper`。
- **注解**: 必须使用 `@Mapper` 注解将其标识为MyBatis的Mapper接口。
- **继承**: 必须继承 `com.baomidou.mybatisplus.core.mapper.BaseMapper<T>`，其中 `T` 是对应的实体类（Entity）。如果需要使用 `MyBatis-Plus-Join`，则应继承 `com.github.yulichang.base.MPJBaseMapper<T>`。

## 2. 查询实现优先级

为了保持代码的统一性、可读性和可维护性，请严格遵循以下技术选型优先级：

### 第一优先级：MyBatis-Plus Wrappers (通过 `default` 方法封装)

对于单表查询和简单的多表更新，应优先使用MyBatis-Plus提供的 `Wrapper` 类。

- **单表查询**: 使用 `LambdaQueryWrapper`。
- **更新操作**: 使用 `LambdaUpdateWrapper`。
- **封装**: **所有 `Wrapper` 的构建和使用逻辑都必须封装在Mapper接口的 `default` 方法中**。这可以避免 `Wrapper` 逻辑泄露到Service层，保持Service层的整洁。

**示例：使用 `LambdaQueryWrapper` 的 `default` 方法**
```java
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserMapper extends BaseMapper<UserEntity> {

    /**
     * 根据用户名模糊查询用户
     * @param username 用户名
     * @return 用户实体
     */
    default UserEntity findByUsername(String username) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getUsername, username);
        return this.selectOne(wrapper);
    }
}
```

### 第二优先级：MyBatis-Plus-Join (多表联合查询)

对于多表联合查询，必须使用 `MyBatis-Plus-Join` 扩展库。

- **查询Wrapper**: 使用 `com.github.yulichang.wrapper.MPJLambdaWrapper`。
- **封装**: 与标准 `Wrapper` 一样，**`MPJLambdaWrapper` 的使用也必须封装在Mapper接口的 `default` 方法中**。
- **返回类型**: 查询结果应映射到一个专门的VO（视图对象）类。

**示例：使用 `MPJLambdaWrapper` 进行多表查询**
```java
import com.github.yulichang.base.MPJBaseMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OrderMapper extends MPJBaseMapper<OrderEntity> { // 注意：需要继承 MPJBaseMapper

    /**
     * 根据订单ID查询订单及其用户信息
     * @param orderId 订单ID
     * @return 包含订单和用户信息的VO
     */
    default OrderWithUserVO findOrderWithUser(Long orderId) {
        MPJLambdaWrapper<OrderEntity> wrapper = new MPJLambdaWrapper<OrderEntity>()
            .selectAll(OrderEntity.class) // 查询OrderEntity所有字段
            .select(UserEntity::getNickname, UserEntity::getAvatar) // 查询UserEntity的指定字段
            .leftJoin(UserEntity.class, UserEntity::getId, OrderEntity::getUserId) // 关联用户表
            .eq(OrderEntity::getId, orderId);
            
        return this.selectJoinOne(OrderWithUserVO.class, wrapper);
    }
}
```

### 第三优先级：XML文件

当 `Wrapper` 和 `MyBatis-Plus-Join` 无法满足复杂的SQL查询需求时（例如，需要使用数据库特定函数、执行复杂子查询或编写包含高级逻辑的动态SQL），才应使用XML文件进行实现。

- **方法定义**: 在Mapper接口中定义方法，无需 `default` 实现。
- **XML实现**: 在 `resources/mapper/` 目录下的对应XML文件中编写SQL语句。
- **命名空间**: XML文件的命名空间（namespace）必须指向对应的Mapper接口。

**示例：XML实现**

*Mapper接口:*
```java
public interface ProductMapper extends BaseMapper<ProductEntity> {
    
    /**
     * 获取指定分类下最受欢迎的N个产品
     * @param categoryId 分类ID
     * @param limit 数量
     * @return 产品列表
     */
    List<ProductVO> findTopPopularProducts(@Param("categoryId") Long categoryId, @Param("limit") int limit);
}
```

*ProductMapper.xml:*
```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.product.mapper.ProductMapper">

    <select id="findTopPopularProducts" resultType="com.dsj.inventory.bussiness.product.vo.ProductVO">
        SELECT 
            p.id, 
            p.name, 
            p.price,
            (SELECT COUNT(*) FROM order_items oi WHERE oi.product_id = p.id) as sales_count
        FROM 
            product p
        WHERE 
            p.category_id = #{categoryId}
        ORDER BY 
            sales_count DESC
        LIMIT #{limit}
    </select>

</mapper>
```

## 3. 总结
1.  **首选 `default` 方法 + Wrapper**：尽可能在Mapper接口内部通过 `default` 方法消化查询逻辑。
2.  **多表连接用 `MyBatis-Plus-Join`**：不要在代码中手动进行多次查询再组合，优先使用Join查询。
3.  **XML是最后选择**：仅用于 `Wrapper` 无法表达的复杂SQL场景。
4.  **禁止业务逻辑**：Mapper只做数据映射和访问，不做任何业务判断。

---

---
description:
globs: **/service/**.java
alwaysApply: false
---
# Service层开发规范
始终用中文和我交流。
## 接口与实现
- **定义接口**: 每个业务模块的核心服务必须定义一个接口，命名为 `XxxService`。
- **实现类**: 服务接口必须有一个对应的实现类，命名为 `XxxServiceImpl`。
- **继承 (根据项目实践)**:
    - 服务接口通常继承 `com.baomidou.mybatisplus.extension.service.IService<Entity>` (如果使用了MyBatis-Plus增强)。
    - 实现类通常继承 `com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<Mapper, Entity>` 并实现 `XxxService` 接口 (如果使用了MyBatis-Plus增强)。

## 职责
- Service层是业务逻辑的核心实现层。
- **负责**:
    - 实现具体的业务规则和流程。
    - 编排对一个或多个 `Mapper` 方法的调用以完成数据库操作。
    - 进行必要的数据校验、转换和组合。
    - 调用其他 `Service` 方法以复用业务逻辑。
- **严禁**: Service层不应包含任何与HTTP协议相关的处理（如HttpServletRequest/Response），也不应直接处理前端的原始请求格式。

## 注解与依赖注入
- **实现类注解**: `XxxServiceImpl` 类必须使用 `@Service` 注解标记为Spring Bean。
- **依赖注入**:
    - 使用 `@Autowired` 注入所需的 `Mapper` 接口。当使用MyBatis-Plus并继承 `ServiceImpl<Mapper, Entity>` 时，对应的 `Mapper` 通常会通过 `baseMapper` 自动注入，可以直接使用，无需显式 `@Autowired`。
    - 使用 `@Autowired` 注入其他需要的 `Service` 接口。
    - 推荐使用构造器注入，但如果项目统一使用字段注入，则遵循项目规范。

## 方法设计
- **输入**: 方法参数通常是 `Param` 对象、业务标识符（如ID）或基本数据类型。
- **输出**: 方法返回值通常是 `VO` 对象、基本数据类型或 `void`。
- **方法粒度**: 方法应封装一个明确、独立的业务操作。
- **命名**: 方法名应清晰地表达其业务目的 (如 `createOrder`, `getUserProfile`, `updateStockQuantity`)。

- **分页查询方法**:
    - **输入参数**: 分页查询方法的参数应为一个专门的 `QueryXxxParam` 对象。该对象应继承自 `com.dsj.inventory.common.entity.BasePage` 以包含标准的分页参数（如 `page` 代表当前页码，`size` 代表每页数量），并且可以包含其他业务查询条件。
    - **返回类型**: 分页查询方法的返回类型必须是 `com.baomidou.mybatisplus.extension.plugins.pagination.Page<XxxVO>`，其中 `XxxVO` 是列表项对应的视图对象。
    - **实现逻辑**: Service 实现中，应从继承自 `BasePage` 的 `QueryXxxParam` 中获取分页信息，创建 `Page` 实例，并将其传递给 Mapper 层进行查询。直接返回 Mapper 处理后的 `Page<XxxVO>` 对象。

## 数据处理
- **数据库交互**:
    - 所有数据库操作必须通过注入的 `Mapper` 接口完成。
    - **严禁在Service层直接构建和使用 `com.baomidou.mybatisplus.core.conditions.Wrapper` (如 `QueryWrapper`, `UpdateWrapper`)。** 所有的动态查询、复杂条件和特定字段的更新都应封装在 `Mapper` 层的方法中。Service层应通过调用这些定义好的Mapper方法来执行操作，而不是在业务逻辑中拼接SQL条件。
- **数据转换**: 负责将 `Param` 对象转换为 `Entity` 对象（用于持久化），以及将 `Entity` 对象（或多个Entity组合）转换为 `VO` 对象（用于返回给Controller）。可以使用MapStruct、BeanUtils或手动进行转换。
- **数据校验**: 在执行核心业务逻辑前，进行必要的业务层面的数据校验（如检查库存是否充足、用户状态是否正常等）。对于 Controller 层已经校验过的格式问题，Service 层可以假设其有效。

## 事务管理
- **注解**: 对于涉及一个或多个写操作（INSERT, UPDATE, DELETE）的公共方法，应在其实现类的方法上添加 `@Transactional` 注解。
- **默认传播**: 通常使用默认的事务传播行为 (`REQUIRED`)。
- **只读事务**: 对于只有读操作的方法，可以添加 `@Transactional(readOnly = true)` 以优化性能。
- **异常回滚**: `@Transactional` 默认只在遇到 `RuntimeException` 或 `Error` 时回滚。如果需要对受检异常（Checked Exception）也进行回滚，需配置 `rollbackFor` 属性，如 `@Transactional(rollbackFor = Exception.class)`。

## 异常处理
- Service层是业务逻辑的核心，应清晰地通过异常来反映业务执行过程中的问题。
- **主要业务异常**:
    - 对于所有可预见的业务逻辑错误（如"用户不存在"、"库存不足"、"状态不合法无法操作"等），应抛出 `com.dsj.inventory.framework.exception.BizException`。
    - 构造 `BizException` 时，应提供清晰、具体的错误消息。例如:
      ```java
      if (user == null) {
          throw new BizException("指定ID的用户不存在");
      }
      if (product.getStock() < quantity) {
          throw new BizException("商品库存不足，无法完成购买");
      }
      ```
    - 如果项目中定义了统一的错误码体系 (如 `com.dsj.inventory.framework.exception.code.ExceptionCode`) 并且前端或调用方需要根据错误码进行特定处理，可以使用错误码来构造 `BizException`：
      ```java
      import com.dsj.inventory.framework.exception.code.ExceptionCode;
      // ...
      throw new BizException(ExceptionCode.DATA_NOT_FOUND.getCode(), "自定义的详细错误信息或直接使用: " + ExceptionCode.DATA_NOT_FOUND.getMsg());
      // 或者，如果 BizException 提供了相应的工厂方法:
      // throw BizException.wrap(ExceptionCode.DATA_NOT_FOUND);
      ```
- **参数校验异常**:
    - 虽然Controller层会通过 `@Valid` 等进行基础参数校验，但Service层可能需要进行更深层次的业务参数校验。
    - 当Service方法的参数不满足业务约束时，应抛出 `com.dsj.inventory.framework.exception.ParamException`。例如：
      ```java
      if (param.getStartDate().isAfter(param.getEndDate())) {
          throw new ParamException("开始日期不能晚于结束日期");
      }
      ```
- **事务与异常**:
    - 所有涉及一个或多个写操作（INSERT, UPDATE, DELETE）的Service层公共方法，必须在其实现类的方法上添加 `@Transactional(rollbackFor = Exception.class)` 注解。这确保了任何从该方法抛出的异常（包括 `BizException`, `ParamException` 以及其他运行时或受检异常）都会触发事务回滚，保持数据一致性。
- **异常传递**:
    - Service层通常不捕获由Mapper层或更底层组件抛出的非预期异常（如数据库连接异常、SQL语法错误等）。这些异常应由全局异常处理器 (`GlobalExceptionHandler`) 统一捕获和处理。
    - 除非Service层能对某个特定异常进行有意义的恢复处理，或者需要将其包装成一个更具体的 `BizException` 并附加额外上下文信息，否则不应随意 `try-catch` 隐藏原始异常。
- **错误信息**: 异常中携带的错误消息应清晰明了，便于问题排查。如果该消息可能最终展示给用户，则应注意措辞友好。

## 示例
```java
// === Service 接口 ===
import com.baomidou.mybatisplus.extension.service.IService; // 假设使用MP

public interface ExampleService extends IService<ExampleEntity> { // 假设使用MP

    /**
     * 创建示例
     * @param param 请求参数
     * @return 新创建示例的ID
     * @throws BusinessException 如果参数无效或创建失败
     */
    Long createExample(ExampleParam param) throws BusinessException;

    /**
     * 获取示例信息
     * @param id 示例ID
     * @return 示例VO
     */
    ExampleVO getExampleInfo(Long id);
}

// === Mapper 接口 (示例) ===
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

public interface ExampleMapper extends BaseMapper<ExampleEntity> {

    /**
     * 根据名称检查示例是否存在。
     * Wrapper 的使用被封装在 Mapper 接口的默认方法中，Service层不直接接触Wrapper。
     * @param name 示例名称
     * @return 存在则返回 true, 否则返回 false
     */
    default boolean existsByName(String name) {
        return this.exists(new QueryWrapper<ExampleEntity>().eq("name", name));
    }
}


// === Service 实现类 ===
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl; // 假设使用MP
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Service;
// ... 其他 import

@Service
public class ExampleServiceImpl extends ServiceImpl<ExampleMapper, ExampleEntity> implements ExampleService { // 假设使用MP

    // Mapper 通过 ServiceImpl<M, E> 自动注入，名为 baseMapper

    @Override
    @Transactional(rollbackFor = Exception.class) // 声明事务，对所有Exception回滚
    public Long createExample(ExampleParam param) throws BusinessException {
        // 1. 业务校验
        if (param.getName() == null || param.getName().trim().isEmpty()) {
            throw new BusinessException("示例名称不能为空");
        }
        // 调用封装好的Mapper方法，避免在Service层使用Wrapper
        if (baseMapper.existsByName(param.getName())) {
             throw new BusinessException("示例名称已存在");
        }
        
        // 2. 数据转换 (Param -> Entity)
        ExampleEntity entity = new ExampleEntity();
        BeanUtils.copyProperties(param, entity); // 或使用 MapStruct
        entity.setCreateTime(LocalDateTime.now());
        
        // 3. 调用Mapper进行持久化 (使用ServiceImpl提供的方法)
        boolean success = this.save(entity);
        if (!success) {
            throw new BusinessException("创建示例失败");
        }
        
        // 4. 返回结果
        return entity.getId(); // 假设ID是自增的，并且已回填
    }

    @Override
    public ExampleVO getExampleInfo(Long id) {
        // 1. 调用Mapper查询数据 (使用ServiceImpl提供的方法)
        ExampleEntity entity = this.getById(id);
        if (entity == null) {
            return null; // 或者抛出异常 new ResourceNotFoundException("示例未找到: " + id);
        }
        
        // 2. 数据转换 (Entity -> VO)
        ExampleVO vo = new ExampleVO();
        BeanUtils.copyProperties(entity, vo); // 或使用 MapStruct
        
        // 3. 返回VO
        return vo;
    }
}

// 假设 ExampleEntity, ExampleParam, ExampleVO, BusinessException 已定义
```
---


---
description:
globs: inventory/src/test/**
alwaysApply: false
---
# Java 测试开发规范 (dsj-inventory)
始终用中文和我交流。
本规范旨在指导如何在 `dsj-inventory` 项目中编写单元测试和服务集成测试。

## 1. 核心测试设置与基类

-   **测试基类**: 所有涉及数据库交互的单元测试或集成测试都应继承 `[BaseDbUnitTest.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/BaseDbUnitTest.java)`。
    -   该基类自动激活 `test` Spring Profile，使用 `[application-test.yml](mdc:inventory/src/test/resources/application-test.yml)` 中的配置。
    -   它会在每个测试方法执行后运行 `[clean.sql](mdc:inventory/src/test/resources/clean.sql)` 来清理数据库，保证测试的独立性。
    -   它会在每个测试方法执行前调用 `TestContextHelper.setDefaultUserContext()` 设置默认用户上下文，并在测试方法执行后调用 `TestContextHelper.clearContext()` 清理上下文。

-   **测试上下文**: 使用 `[TestContextHelper.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/TestContextHelper.java)` 来管理和模拟不同的用户上下文或组织上下文。
    -   `TestContextHelper.setDefaultUserContext()`: 设置默认的药店用户上下文。
    -   `TestContextHelper.setPlatformContext(...)`: 设置平台或其他特定类型的组织上下文。

-   **数据库**:
    -   测试使用 Mysql 数据库，配置见 `[application-test.yml](mdc:inventory/src/test/resources/application-test.yml)`。
    -   数据库表结构由 `[schema.sql](mdc:inventory/src/test/resources/schema.sql)` 定义。
    -   初始测试数据可以放入 `[data.sql](mdc:inventory/src/test/resources/data.sql)`。

## 2. Service 层测试规范

参考示例: `[ShelfServiceImplTest.java](mdc:inventory/src/test/java/com/dsj/inventory/bussiness/inventory/service/impl/ShelfServiceImplTest.java)`

-   **注解**:
    -   在测试类上使用 `@Import({YourServiceImpl.class})` 来将被测试的 Service 实现类加载到 Spring 测试上下文中。
    -   使用 `@ActiveProfiles("test")` (通常已在基类中定义)。

-   **依赖注入**:
    -   使用 `@Autowired` 注入被测试的 Service 实现，例如 `private YourServiceImpl yourService;`。
    -   使用 `@MockBean` 来 Mock 该 Service 所依赖的其他 Service 或外部组件。
    -   对数据库访问层（Mapper），而应使用真实的Mapper组件与测试数据库交互,不使用mock模拟。

-   **测试方法结构**:
    -   使用 JUnit 5 注解: `@Test`, `@DisplayName`, `@BeforeEach`。
    -   `@BeforeEach` 方法:
        -   进行通用的测试数据准备。
        -   设置 Mock 对象行为 (使用 `Mockito.when(...).thenReturn(...)`)。
        -   如有必要，调用 `TestContextHelper` 设置特定的上下文。
    -   `@Test` 方法:
        -   遵循 Arrange-Act-Assert (AAA) 模式。
        -   **Arrange**: 准备特定测试场景的输入数据和 Mock 行为。
        -   **Act**: 调用被测试 Service 的方法。
        -   **Assert**: 使用 JUnit 5 的断言方法 (如 `assertEquals`, `assertTrue`, `assertNotNull`, `assertThrows`) 验证结果或行为。
        -   **Verify**: 使用 `Mockito.verify(...)` 验证 Mock 对象的方法是否按预期被调用。

-   **Mocking 实践**:
    -   对数据库访问层（Mapper），不使用mock模拟，而应使用真实的Mapper组件与测试数据库交互。
    -   仅在测试外部模块功能时使用mock，例如外部服务、第三方API调用或其他非当前模块的服务。
    -   精确 Mock 依赖项的行为，只 Mock 当前测试逻辑所必需的外部交互。
    -   对于外部服务的方法调用，可以根据测试场景设置Mock的返回值或行为。

-   **命名约定**:
    -   测试类名: `YourServiceImplTest.java`。
    -   测试方法名: 使用 `@DisplayName` 提供清晰的描述，例如 `@DisplayName("queryMethod - Scenari X - Expected Behavior Y")`。

## 3. 测试数据管理

-   **通用数据**: 可以在 `[data.sql](mdc:inventory/src/test/resources/data.sql)` 中定义所有测试可能用到的基础数据。
-   **特定场景数据**: 在测试方法的 Arrange 部分通过代码创建，或通过 Mock 返回。
-   **数据清理**: `[clean.sql](mdc:inventory/src/test/resources/clean.sql)` 负责在每个测试后删除相关表的数据。确保 DELETE 语句的正确性以避免数据残留。

## 4. 注意事项

-   **最小化上下文加载**: `[BaseDbUnitTest.Application.class](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/BaseDbUnitTest.java)` 中的 `@ComponentScan` 配置了严格的扫描策略，旨在只加载测试必要的组件（主要是Entity和MyBatis Plus核心配置），以加快测试启动速度。避免在测试中加载不必要的 Controller、全局配置等。
-   **事务**: Service 层方法通常有 `@Transactional` 注解。测试默认在事务中运行，并在结束后回滚 (由 `@Sql` 在 `BaseDbUnitTest` 中执行 `clean.sql` 保证数据清理，而非依赖 Spring Test 的默认回滚行为)。
-   **MySql兼容性**: `schema.sql` 中定义的表结构应与 MySQL 生产环境兼容，同时适配 MySQL 语法。
## 5. 关键文件引用

-   测试基类: `[BaseDbUnitTest.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/BaseDbUnitTest.java)`
-   测试上下文工具: `[TestContextHelper.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/TestContextHelper.java)`
-   测试配置文件: `[application-test.yml](mdc:inventory/src/test/resources/application-test.yml)`
-   数据库表结构: `[schema.sql](mdc:inventory/src/test/resources/schema.sql)`
-   初始测试数据: `[data.sql](mdc:inventory/src/test/resources/data.sql)`
-   数据清理脚本: `[clean.sql](mdc:inventory/src/test/resources/clean.sql)`
-   Service测试示例: `[ShelfServiceImplTest.java](mdc:inventory/src/test/java/com/dsj/inventory/bussiness/inventory/service/impl/ShelfServiceImplTest.java)`
-   通用测试入口: `[InventoryApplicationTests.java](mdc:inventory/src/test/java/com/dsj/inventory/InventoryApplicationTests.java)` (可用于测试整体上下文加载)

