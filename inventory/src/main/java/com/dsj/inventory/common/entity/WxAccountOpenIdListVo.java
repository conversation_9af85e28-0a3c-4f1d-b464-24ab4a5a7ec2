package com.dsj.inventory.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * 微信公众号OpenId请求响应内容
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class WxAccountOpenIdListVo {

    /**
     * openIdList
     */
    private List<String> openid;

}
