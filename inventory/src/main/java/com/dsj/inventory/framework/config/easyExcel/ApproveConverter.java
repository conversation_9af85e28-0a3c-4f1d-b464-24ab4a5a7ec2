package com.dsj.inventory.framework.config.easyExcel;


/**
 * 审批状态转换器
 */

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.pocky.transport.bussiness.diagnose.enumeration.ApproveEnum;

public class ApproveConverter implements Converter<Integer> {

    @Override
    public Class<Integer> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
                                           GlobalConfiguration globalConfiguration) {
        if (cellData.getStringValue().equals(ApproveEnum.WAITING.getDesc())){
            return ApproveEnum.WAITING.getCode();
        }else if (cellData.getStringValue().equals(ApproveEnum.PASS.getDesc())){
            return ApproveEnum.PASS.getCode();
        }else {
            return ApproveEnum.REFUSE.getCode();
        }
    }

    @Override
    public CellData<Integer> convertToExcelData(Integer value, ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        if (value.equals(ApproveEnum.WAITING.getCode())){
            return new CellData<>(ApproveEnum.WAITING.getDesc());
        }else if (value.equals(ApproveEnum.PASS.getCode())){
            return new CellData<>(ApproveEnum.PASS.getDesc());
        }else {
            return new CellData<>(ApproveEnum.REFUSE.getDesc());
        }
    }

}
