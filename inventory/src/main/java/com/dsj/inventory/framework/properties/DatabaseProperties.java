package com.dsj.inventory.framework.properties;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * 客户端认证配置
 *
 * <AUTHOR>
 * @date 2018/11/20
 */
@Data
@NoArgsConstructor
@ConfigurationProperties(prefix = "pocky.server")
@EnableConfigurationProperties(DatabaseProperties.class)
@Component
public class DatabaseProperties {
    /**
     * 终端ID (0-31)      单机配置0 即可。 集群部署，根据情况每个实例自增即可。
     */
    private Long workerId = 0L;
    /**
     * 数据中心ID (0-31)   单机配置0 即可。 集群部署，根据情况每个实例自增即可。
     */
    private Long dataCenterId = 0L;
}
