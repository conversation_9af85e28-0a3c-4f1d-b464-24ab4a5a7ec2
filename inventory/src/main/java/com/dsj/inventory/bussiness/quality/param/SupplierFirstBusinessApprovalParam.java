package com.dsj.inventory.bussiness.quality.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "SupplierFirstBusinessApprovalParam", description = "供应商首营审批参数")
public class SupplierFirstBusinessApprovalParam {
    
    @ApiModelProperty(value = "首营审批记录ID", required = true)
    @NotNull(message = "首营审批记录ID不能为空")
    private Long id;
    
    @ApiModelProperty(value = "供应商ID", required = true)
    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;
    
    @ApiModelProperty(value = "审批结果: 2-通过，3-驳回", required = true)
    @NotNull(message = "审批结果不能为空")
    @Range(min = 2, max = 3, message = "审批结果无效")
    private Integer actionType;
    
    @ApiModelProperty(value = "审批意见")
    private String approvalOpinion;
    
    @ApiModelProperty(value = "驳回原因 (仅当驳回时需要)")
    private String rejectReason;
} 