package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 入库商品成本计算
 * <AUTHOR>
 * @date 2023/07/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="IncomingGoodsCostVo", description="入库商品成本计算")
public class IncomingGoodsCostVo {

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "入库数量")
    private Double incomingAmount;

    @ApiModelProperty(value = "折后金额（含税金额折后）")
    private Integer totalDiscountPrice;

    @ApiModelProperty(value = "赠送数量")
    private Double giveAmount;

    @ApiModelProperty(value = "采购数量")
    private Double purchaseNumber;

    @ApiModelProperty(value = "收货数量")
    private Double receiveAmount;

}
