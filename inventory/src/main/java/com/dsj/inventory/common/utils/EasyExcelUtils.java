package com.dsj.inventory.common.utils;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.framework.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * @ClassName : EasyExcelUtils
 * @Description : 阿里easyExcel工具类
 * @Version : V1.0
 */
@Slf4j
public class EasyExcelUtils {

    /**
     * @description : 导出普通的excel文件
     *                  文件下载并且失败的时候返回json（默认失败了会返回一个有部分数据的Excel）
     * <AUTHOR> hbw
     * @date : 2020-04-14 15:20
     * @param response
     * @param data excel数据
     * @param fileName 文件名称
     * @param sheetName sheet名称
     * @return : void
     * @throws :
     */
    public static void exportOrdinaryExcel(HttpServletResponse response, List data, String fileName, String sheetName, Class head) throws IOException {
        // 这里注意 使用swagger 会导致各种问题，请直接用浏览器或者用postman
        try {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fName + ".xlsx");

            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), head).autoCloseStream(Boolean.FALSE).sheet(sheetName)
                    .doWrite(data);

        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = new HashMap<>(5);
            map.put("status", "failure");
            map.put("message", "下载"+ fileName +"文件失败" + e.getMessage());
            response.getWriter().println(JSON.toJSONString(map));
        }

    }

    /**
     * @description : 动态头，实时生成头导出excel文件
     *                  思路是这样子的，先创建List<String>头格式的sheet仅仅写入头,然后通过table 不写入头的方式 去写入数据
     *                  文件下载并且失败的时候返回json（默认失败了会返回一个有部分数据的Excel）
     * <AUTHOR> hbw
     * @date : 2020-07-13 18:42
     * @param response
     * @param data excel数据
     * @param fileName 文件名称
     * @param sheetName sheet名称
     * @return : void
     * @throws :
     */
    public static void dynamicHeadExportExcel(HttpServletResponse response, List<List<String>> head, List data, String fileName, String sheetName) throws IOException {

        // 这里注意 使用swagger 会导致各种问题，请直接用浏览器或者用postman
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fName + ".xlsx");

            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.FALSE)
                    // .needHead(true)
                    // .excelType(ExcelTypeEnum.XLSX)
                    // 这里放入动态头
                    .head(head)
                    // easyexcel自适应列宽的一种方式
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet(sheetName)
                    // 当然这里数据也可以用 List<List<String>> 去传入
                    .doWrite(data);

        } catch (Exception e) {
            System.out.println("导出excel报错信息：" + e.getMessage());
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = new HashMap<>(5);
            map.put("status", "failure");
            map.put("message", "下载"+ fileName +"文件失败" + e.getMessage());
            response.getWriter().println(JSON.toJSONString(map));
        }

    }


    /**
     * 生成读取excel的监听
     *
     * @param consumer Lambda具体业务逻辑
     * @param threshold 阈值
     * @return
     */
    public static <T> AnalysisEventListener<T> getListenerConsumer(Consumer<List<T>> consumer, int threshold) {
        return new AnalysisEventListener<T>() {
            /**
             * 存储数据集合
             */
            private List<T> list = new LinkedList<>();

            /**
             * 遍历读取的文件
             * 这个每一条数据解析都会调用一次
             * @param t
             * @param analysisContext
             */
            @Override
            public void invoke(T t, AnalysisContext analysisContext) {

                // ReadRowHolder readRowHolder = analysisContext.readRowHolder();
                // Integer totalCount = analysisContext.getTotalCount();

                // 获取当前数据是第几行
                // Integer rowIndex = readRowHolder.getRowIndex();

                list.add(t);

                // 达到threshold了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
                if (list.size() >= threshold) {
                    consumer.accept(list);
                    list.clear();
                }

                /*if (totalCount < threshold) {
                    consumer.accept(list);
                    list.clear();
                } else {
                    // 达到threshold了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
                    if (list.size() >= threshold){
                        consumer.accept(list);
                        list.clear();
                    }
                }*/

            }

            /**
             * 所有数据解析完成 自动调用此方法
             * @param analysisContext
             */
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                if (list.size() > 0) {
                    // 这里也要保存数据，确保最后遗留的数据也存储到数据库
                    consumer.accept(list);
                }
                log.info("所有数据解析完成!");
                // 解析结束销毁不用的资源
                list.clear();
            }

            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                log.info("解析到一条头数据:{" + JSON.toJSONString(headMap) + "}");
            }

            /*@Override
            public void onException(Exception e, AnalysisContext context) {
                if (e instanceof ExcelDataConvertException) {
                    ExcelDataConvertException convertException = (ExcelDataConvertException) e;
                    String msg = "第" + convertException.getRowIndex() + "行 第" + convertException.getColumnIndex() + "列解析异常 原因：" + convertException.getMessage();
                    throw new BusinessException("555", msg);
                }
                list.clear();
            }*/

        };
    }


    /**
     * 生成读取excel的监听
     *
     * @param biConsumer Lambda具体业务逻辑
     * @param threshold 阈值
     * @param extendMap 扩展参数
     * @return
     */
    public static <T> AnalysisEventListener<T> getListenerBiConsumer(BiConsumer<List<T>, Map<String, Object>> biConsumer, int threshold, Map<String, Object> extendMap) {
        return new AnalysisEventListener<T>() {
            /**
             * 存储数据集合
             */
            private List<T> list = new LinkedList<>();

            /**
             * 存储行号的List
             */
            private List<Integer> rowIndexList = new ArrayList<>();

            /**
             * 临时数据包含错误信息返回;
             */
            private Map<String,Object> temporary = BeanUtil.copyProperties(extendMap,Map.class);


            /**
             * 遍历读取的文件
             * 这个每一条数据解析都会调用一次
             *
             * @param t
             * @param analysisContext
             */
            @Override
            public void invoke(T t, AnalysisContext analysisContext) {

                // Integer totalCount = analysisContext.getTotalCount();
                ReadRowHolder readRowHolder = analysisContext.readRowHolder();

                // 获取当前数据是第几行
                Integer rowIndex = readRowHolder.getRowIndex();

                list.add(t);
                rowIndexList.add(rowIndex + 1);

                // 达到threshold了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
                if (list.size() >= threshold) {
                    extendMap.put("rowIndexList", rowIndexList);
                    biConsumer.accept(list, extendMap);
                    list.clear();
                    //extendMap.clear();
                    rowIndexList.clear();
                }
                /*if (totalCount < threshold) {
                    consumer.accept(list);
                    list.clear();
                } else {
                    // 达到threshold了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
                    if (list.size() >= threshold){
                        consumer.accept(list);
                        list.clear();
                    }
                }*/
            }

            /**
             * 所有数据解析完成 自动调用此方法
             *
             * @param analysisContext
             */
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                if (list.size() > 0) {
                    // 这里也要保存数据，确保最后遗留的数据也存储到数据库
                    extendMap.put("rowIndexList", rowIndexList);
                    biConsumer.accept(list, extendMap);
                }
                log.info("所有数据解析完成!，共解析数据{}条",rowIndexList.size());
                // 解析结束销毁不用的资源
                StringBuffer errorMessage = (StringBuffer)extendMap.get("errorMessage");
                Boolean isAsync = (Boolean) extendMap.get("isAsync");
                if (errorMessage != null && errorMessage.length() >0){
                    if (CommonUtils.isEmpty(isAsync) || !isAsync){
                        throw new BizException("导入数据错误内容："+ errorMessage.toString());
                    }
                }
                list.clear();
                // extendMap.clear();
                rowIndexList.clear();
            }

            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                log.info("解析到一条头数据:{" + JSON.toJSONString(headMap) + "}");
            }

            /*@Override
            public void onException(Exception e, AnalysisContext context) {
                if (e instanceof ExcelDataConvertException) {
                    ExcelDataConvertException convertException = (ExcelDataConvertException) e;
                    String msg = "第" + convertException.getRowIndex() + "行 第" + convertException.getColumnIndex() + "列解析异常 原因：" + convertException.getMessage();
                    throw new BusinessException("555", msg);
                }
                list.clear();
            }*/

        };
    }

    /**
     * 根据指定字段导出数据
     *
     * @param response
     * @param colums
     * @param dataList
     * @param aClass
     */
    public static void exportExcel(HttpServletResponse response, String[] colums, List<?> dataList, Class<?> aClass,String fileName) {
        try {
            //添加要导出的字段
            Set<String> includeList = new HashSet<>();
            Collections.addAll(includeList, colums);
            //设置返回类型及编码类型
//            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
//            response.setCharacterEncoding("UTF-8");

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fName + ".xlsx");

            //写出
            EasyExcel.write(new BufferedOutputStream(response.getOutputStream()), aClass)
                    .autoCloseStream(true)
                    .excelType(ExcelTypeEnum.XLSX)
                    .includeColumnFiledNames(includeList)
                    .sheet().doWrite(dataList);
        } catch (IOException e) {
            log.error("IOException",e);
        }
    }

    /**
     * 不指定导出字段
     * 不需要导出的字段需要使用注解标记@ExcelIgnore
     * 按照实体类中注解标记顺序导出
     *
     * @param response
     * @param dataList
     * @param aClass
     */
    public static void exportExcel(HttpServletResponse response, List<?> dataList, Class<?> aClass) {
        try {
            //设置返回类型及编码类型
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setCharacterEncoding("UTF-8");
            //写出
            EasyExcel.write(new BufferedOutputStream(response.getOutputStream()), aClass)
                    .autoCloseStream(true)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet().doWrite(dataList);
        } catch (IOException e) {
            log.error("IOException",e);
        }
    }

    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    /**
     * 未指定阈值 默认每隔50条存储数据库
     */
    private static int BATCH_COUNT = 50;

    private static final String SUCCESS_COUNT_KEY = "success";

    private static final String ERROR_KEY = "excel,service";

    /**
     * 不需要返回值
     *
     * @param func Lambda具体业务逻辑
     * @return
     */
    public static <T> AnalysisEventListener<T> getListener(Function<List<T>, ResponseEntity<Object>> func) {
        return getListener(func, BATCH_COUNT, null);
    }

    /**
     * 不需要返回值
     * @param func Lambda具体业务逻辑
     * @param threshold 阈值
     * @param <T>
     * @return
     */
    public static <T> AnalysisEventListener<T> getListener(Function<List<T>, ResponseEntity<Object>> func, int threshold) {
        return getListener(func, threshold, null);
    }

    /**
     * 默认监听器阈值50
     *
     * @param func    Lambda具体业务逻辑
     * @param results 错误行返回值
     * @return
     */
    public static <T> AnalysisEventListener<T> getListener(Function<List<T>, ResponseEntity<Object>> func, Map<String, Object> results) {
        return getListener(func, BATCH_COUNT, results);
    }

    /**
     * yù
     * 自定义阈值监听器
     *
     * @param func      Lambda具体业务逻辑
     * @param results   错误行返回值
     * @param threshold 阈值
     * @return
     */
    public static <T> AnalysisEventListener<T> getListener(Function<List<T>, ResponseEntity<Object>> func, Map<String, Object> results, int threshold) {
        return getListener(func, threshold, results);
    }

    /**
     * 自定义生成读取excel的监听
     *
     * @param func      Lambda具体业务逻辑
     * @param threshold 阈值
     * @param results   错误行返回值
     * @return
     */
    public static <T> AnalysisEventListener<T> getListener(Function<List<T>, ResponseEntity<Object>> func, int threshold, Map<String, Object> results) {
        return new AnalysisEventListener<T>() {

            // 存储数据的集合
            private List<T> list = new ArrayList<T>();
            // 存储错误信息的集合
            private Set<String> sets = new LinkedHashSet<>();

            /**
             * 保存数据
             */
            private void doSave() {
                // 真正存储数据的方法
                ResponseEntity<Object> apply = func.apply(list);
                if (apply.getStatusCode() == HttpStatus.OK) {
                    // putAll可以合并两个MAP，只不过如果有相同的key那么用后面的覆盖前面的
                    results.putAll(setValue(results, SUCCESS_COUNT_KEY, list.size()));
                } else {
                    sets.add(apply.getBody().toString());
                    results.putAll(setValue(results, ERROR_KEY.split(",")[1], sets));
                }
                // 解析结束销毁不用的资源
                clear(list, sets);
            }

            /**
             * 清空集合
             * @param coll
             */
            private void clear(Collection... coll) {
                Arrays.stream(coll).forEach(Collection::clear);
            }

            /**
             * 遍历读取的文件
             * 这个每一条数据解析都会调用一次
             * @param data
             * @param context
             */
            @Override
            public void invoke(T data, AnalysisContext context) {

                // ReadRowHolder readRowHolder = context.readRowHolder();
                Integer totalCount = context.getTotalCount();

                // 获取当前数据是第几行
                // Integer rowIndex = readRowHolder.getRowIndex();

                list.add(data);

                if (list.size() >= threshold) {
                    doSave();
                }

                /*if (totalCount < threshold) {
                    doSave();
                } else {
                    // 达到threshold了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
                    if (list.size() >= threshold){
                        doSave();
                    }
                }*/
            }

            /**
             * 所有数据解析完成 自动调用此方法
             * @param context
             */
            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                if (list.size() > 0) {
                    // 这里也要保存数据，确保最后遗留的数据也存储到数据库
                    doSave();
                }
                log.info("所有数据解析完成!");
            }

            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                log.info("解析到一条头数据:{" + JSON.toJSONString(headMap) + "}");
            }

            @Override
            public void onException(Exception e, AnalysisContext context) {
                if (e instanceof ExcelDataConvertException) {
                    ExcelDataConvertException convertException = (ExcelDataConvertException) e;
                    if (results != null) {
                        sets.add("第" + convertException.getRowIndex() + "行 第" + convertException.getColumnIndex() + "列解析异常 原因：" + convertException.getMessage());
                        results.putAll(setValue(results, ERROR_KEY.split(",")[0], sets));
                    }
                }
                clear(list, sets);
            }
        };
    }

    private static Map<String, Object> setValue(Map<String, Object> map, String key, Object obj) {
        Object value = map.get(key);
        if (SUCCESS_COUNT_KEY.equals(key)) {
            if (value != null && value instanceof Integer) {
                map.put(key, (int) value + (int) (obj));
            } else {
                map.put(key, obj);
            }
        }
        if (ERROR_KEY.contains(key)) {
            Set set = (value instanceof Set) ? (Set) value : new LinkedHashSet();
            set.addAll((Set) obj);
            map.put(key, set);
        }
        return map;
    }




}
