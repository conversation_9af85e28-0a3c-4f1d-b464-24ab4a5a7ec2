package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.AcceptOrderGoods;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存验收单商品明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface AcceptOrderGoodsService extends IService<AcceptOrderGoods> {

    /**
     * 获取验收单商品明细
     * @param orderId
     * @return
     */
    List<AcceptOrderGoodsVo> getAcceptOrderGoodsList(Long orderId);

    /**
     * 根据订单ID将商品明细标记为已删除
     * @param orderId 订单ID
     */
    void markAsDeletedByOrderId(Long orderId);

    /**
     * 根据验收单ID和组织药品ID及批号查询商品明细
     * @param orderId 验收单ID
     * @param organizationMedicineId 组织药品ID
     * @return 验收单商品明细
     */
    AcceptOrderGoods getByOrderIdAndOrgMedicineId(Long orderId, Long organizationMedicineId);

}
