package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.CheckGoods;
import com.dsj.inventory.bussiness.inventory.vo.CheckGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存盘点计划商品明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface CheckGoodsMapper extends BaseMapper<CheckGoods> {

    /**
     * 获取盘点计划商品明细列表
     * @param checkId
     * @return
     */
    List<CheckGoodsVo> getCheckGoodsListByCheckId(Long checkId);

}
