package com.dsj.inventory.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Map;


/**
 * 微信小程序推送公众号消息请求内容
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class WxAccountSendRequestVo {

    /**用户微信公众号的openid*/
    private String touser;

    private String template_id;

    private String url;

    private MiniProgram miniprogram;

    private Map<String, Map<String, String>> data;

    @Data
    public static class MiniProgram {
        /**小程序APPID*/
        private String appid;
        /**跳转到小程序的页面路径*/
        private String pagepath;
    }

}
