package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 组织付款方式保存或更新参数
 */
@Data
@ApiModel(description = "组织付款方式保存或更新参数")
public class SaveOrUpdatePaymentMethodParam {

    @ApiModelProperty(value = "主键ID（更新时必填）")
    private Long id;

    @ApiModelProperty(value = "组织ID", required = true)
    @NotNull(message = "组织ID不能为空")
    private Long organizationId;

    @ApiModelProperty(value = "付款方式编码", required = true)
    @NotBlank(message = "付款方式编码不能为空")
    private String methodCode;

    @ApiModelProperty(value = "付款方式名称", required = true)
    @NotBlank(message = "付款方式名称不能为空")
    private String methodName;

    @ApiModelProperty(value = "图标URL")
    private String iconUrl;

    @ApiModelProperty(value = "排序")
    private Integer sortOrder = 0;

    @ApiModelProperty(value = "是否启用：1启用，0禁用")
    private Integer enabled = 1;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "配置项列表")
    @Valid
    private List<PaymentConfigParam> configList;
} 
 