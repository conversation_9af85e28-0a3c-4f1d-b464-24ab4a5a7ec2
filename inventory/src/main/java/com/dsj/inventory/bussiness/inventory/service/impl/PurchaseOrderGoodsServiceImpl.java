package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.PurchaseOrderGoods;
import com.dsj.inventory.bussiness.inventory.mapper.PurchaseOrderGoodsMapper;
import com.dsj.inventory.bussiness.inventory.service.PurchaseOrderGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 进销存采购订单商品明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
@Service
public class PurchaseOrderGoodsServiceImpl extends ServiceImpl<PurchaseOrderGoodsMapper, PurchaseOrderGoods> implements PurchaseOrderGoodsService {

    @Autowired
    private PurchaseOrderGoodsMapper purchaseOrderGoodsMapper;

    @Override
    public PurchaseOrderGoods getByOrderIdAndOrgMedicineId(Long orderId, Long organizationMedicineId) {
        return purchaseOrderGoodsMapper.selectByOrderIdAndOrgMedicineId(orderId, organizationMedicineId);
    }

    @Override
    public void markAsDeletedByOrderId(Long orderId) {
        purchaseOrderGoodsMapper.markAsDeletedByOrderId(orderId);
    }

    @Override
    public List<PurchaseOrderGoods> listByOrderId(Long orderId) {
        return purchaseOrderGoodsMapper.selectListByOrderId(orderId);
    }

    @Override
    public List<PurchaseOrderGoods> listByOrderIdAndNotDeleted(Long orderId) {
        return purchaseOrderGoodsMapper.selectListByOrderIdAndNotDeleted(orderId);
    }
}
