package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.PurchaseOrderGoods;
import com.dsj.inventory.common.enumeration.TrueEnum;

import java.util.List;

/**
 * <p>
 * 进销存采购订单商品明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
public interface PurchaseOrderGoodsMapper extends BaseMapper<PurchaseOrderGoods> {

    /**
     * 根据订单ID和商品ID查询商品明细
     * @param orderId 订单ID
     * @param organizationMedicineId 组织商品ID
     * @return 采购订单商品明细
     */
    default PurchaseOrderGoods selectByOrderIdAndOrgMedicineId(Long orderId, Long organizationMedicineId) {
        LambdaQueryWrapper<PurchaseOrderGoods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseOrderGoods::getOrderId, orderId)
                .eq(PurchaseOrderGoods::getOrganizationMedicineId, organizationMedicineId);
        return this.selectOne(queryWrapper);
    }
    
    /**
     * 根据订单ID将商品明细标记为已删除
     * @param orderId 订单ID
     * @return 更新记录数
     */
    default int markAsDeletedByOrderId(Long orderId) {
        LambdaUpdateWrapper<PurchaseOrderGoods> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PurchaseOrderGoods::getOrderId, orderId)
                .set(PurchaseOrderGoods::getIsDelete, TrueEnum.TRUE.getCode());
        return this.update(new PurchaseOrderGoods(), updateWrapper);
    }
    
    /**
     * 根据订单ID查询商品明细列表
     * @param orderId 订单ID
     * @return 商品明细列表
     */
    default List<PurchaseOrderGoods> selectListByOrderId(Long orderId) {
        LambdaQueryWrapper<PurchaseOrderGoods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseOrderGoods::getOrderId, orderId);
        return this.selectList(queryWrapper);
    }
    
    /**
     * 根据订单ID查询未删除的商品明细列表
     * @param orderId 订单ID
     * @return 未删除的商品明细列表
     */
    default List<PurchaseOrderGoods> selectListByOrderIdAndNotDeleted(Long orderId) {
        LambdaQueryWrapper<PurchaseOrderGoods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PurchaseOrderGoods::getOrderId, orderId)
                .eq(PurchaseOrderGoods::getIsDelete, TrueEnum.FALSE.getCode());
        return this.selectList(queryWrapper);
    }
}
