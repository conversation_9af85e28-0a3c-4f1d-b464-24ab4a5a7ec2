package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 活动规则
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SalesActivityRuleVo", description="活动规则")
@ToString(callSuper = true)
public class SalesActivityRuleVo {

    @ApiModelProperty(value = "满倍")
    private Integer multiple;

    @ApiModelProperty(value = "折数")
    private Double percentage;

    @ApiModelProperty(value = "最高折")
    private Double percentageMax;

    @ApiModelProperty(value = "满件")
    private Double amount;

    @ApiModelProperty(value = "减元")
    private Integer reducePrice;

    @ApiModelProperty(value = "特价")
    private Integer specialPrice;

    @ApiModelProperty(value = "每单限量")
    private Double limitAmount;

    @ApiModelProperty(value = "总共限量")
    private Double totalLimitAmount;

    @ApiModelProperty(value = "满元")
    private Integer spendPrice;

    @ApiModelProperty(value = "赠数")
    private Double giveAmount;

}
