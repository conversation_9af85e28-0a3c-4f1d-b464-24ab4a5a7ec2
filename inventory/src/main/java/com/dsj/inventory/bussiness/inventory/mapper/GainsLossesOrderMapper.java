package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.GainsLossesOrder;
import com.dsj.inventory.bussiness.inventory.param.QueryGainsLossesOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存损溢单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface GainsLossesOrderMapper extends BaseMapper<GainsLossesOrder> {


    /**
     * 获取损溢单分页列表
     * @param page
     * @param param
     * @return
     */
    List<GainsLossesOrderVo> queryGainsLossesOrderPage(@Param("page") Page page, @Param("param") QueryGainsLossesOrderParam param);

    /**
     * 获取损溢单详情
     * @param id
     * @return
     */
    GainsLossesOrderDetailVo getGainsLossesOrderById(Long id);

    /**
     * 导出损溢单列表
     * @param param
     * @return
     */
    List<GainsLossesOrderExportVo> queryGainsLossesOrderList(@Param("param") QueryGainsLossesOrderParam param);

    /**
     * 删除组织的损溢单
     * @param organizationId
     */
    void deleteGainsLossesOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天损溢单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getGainsLossesOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);

}
