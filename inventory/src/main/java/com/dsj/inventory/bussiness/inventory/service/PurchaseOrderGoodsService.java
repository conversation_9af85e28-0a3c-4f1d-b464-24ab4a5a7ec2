package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.PurchaseOrderGoods;
import java.util.List;

/**
 * <p>
 * 进销存采购订单商品明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
public interface PurchaseOrderGoodsService extends IService<PurchaseOrderGoods> {

    /**
     * 根据订单ID和商品ID查询商品明细
     * @param orderId 订单ID
     * @param organizationMedicineId 组织商品ID
     * @return 采购订单商品明细
     */
    PurchaseOrderGoods getByOrderIdAndOrgMedicineId(Long orderId, Long organizationMedicineId);

    /**
     * 根据订单ID将商品明细标记为已删除
     * @param orderId 订单ID
     */
    void markAsDeletedByOrderId(Long orderId);

    /**
     * 根据订单ID查询商品明细列表
     * @param orderId 订单ID
     * @return 商品明细列表
     */
    List<PurchaseOrderGoods> listByOrderId(Long orderId);

    /**
     * 根据订单ID查询未删除的商品明细列表
     * @param orderId 订单ID
     * @return 未删除的商品明细列表
     */
    List<PurchaseOrderGoods> listByOrderIdAndNotDeleted(Long orderId);
}
