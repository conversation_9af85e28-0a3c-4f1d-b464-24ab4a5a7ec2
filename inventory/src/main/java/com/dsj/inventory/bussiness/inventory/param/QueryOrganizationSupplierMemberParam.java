package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询供应商组织私域销售人员
 * <AUTHOR>
 * @date 2023/03/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QueryOrganizationSupplierMemberParam",description = "查询供应商组织私域销售人员参数")
public class QueryOrganizationSupplierMemberParam extends BasePage {

    @ApiModelProperty(value = "私域供应商Id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "销售人员名称模糊搜索")
    private String nameLike;

    @ApiModelProperty(hidden = true,value = "销售人员名称模糊搜索")
    private String createTimeOrder = "desc";

}
