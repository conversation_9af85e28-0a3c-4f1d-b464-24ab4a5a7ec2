package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.PurchaseOrder;
import com.dsj.inventory.bussiness.inventory.param.QueryPurchaseOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdatePurchaseOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.PurchaseOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchaseOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchaseOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存采购订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
public interface PurchaseOrderService extends IService<PurchaseOrder> {

    /**
     * 新增、编辑 采购订单
     * @param param
     * @return
     */
    PurchaseOrderDetailVo saveOrUpdatePurchaseOrder(SaveOrUpdatePurchaseOrderParam param);

    /**
     * 删除 采购订单
     * @param id
     */
    void deletePurchaseOrder(Long id);

    /**
     * 获取采购订单分页列表
     * @param param
     * @return
     */
    Page<PurchaseOrderVo> queryPurchaseOrderPage(QueryPurchaseOrderParam param);

    /**
     * 获取采购订单列表详情
     * @param id
     * @return
     */
    PurchaseOrderDetailVo getPurchaseOrderById(Long id);

    /**
     * 提交采购订单
     * @param id
     */
    void submitPurchaseOrder(Long id);

    /**
     * 导出采购订单列表
     * @param param
     * @return
     */
    List<PurchaseOrderExportVo> queryPurchaseOrderList(QueryPurchaseOrderParam param);

    /**
     * 删除组织的采购订单
     * @param organizationId
     */
    void deletePurchaseOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天采购订单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getPurchaseOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);

    /**
     * 根据计划ID统计采购单数量，可排除指定采购单
     * @param planId 计划ID
     * @param excludeOrderId 要排除的采购单ID (可为null)
     * @return 采购单数量
     */
    int countByPlanId(Long planId, Long excludeOrderId);

    /**
     * 更新采购订单状态
     * @param orderId 订单ID
     * @param currentState 当前状态
     * @param targetState 目标状态
     */
    void updateOrderState(Long orderId, Integer currentState, Integer targetState);

    /**
     * 收货后更新采购订单的整体状态
     * (此方法将在Task005中具体实现)
     * @param purchaseOrderId 采购订单ID
     */
    void updatePurchaseOrderStatusAfterReceipt(Long purchaseOrderId);
}
