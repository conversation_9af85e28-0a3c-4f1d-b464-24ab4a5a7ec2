package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityTask;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesActivityTaskParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateSalesActivityTaskParam;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskVo;

/**
 * <p>
 * 销售活动任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface SalesActivityTaskService extends IService<SalesActivityTask> {

    /**
     * 新增、编辑Task销售活动任务
     * @param param
     * @return
     */
    SalesActivityTaskVo saveOrUpdateSalesActivityTask(SaveOrUpdateSalesActivityTaskParam param);

    /**
     * 获取销售活动任务分页列表
     * @param param
     * @return
     */
    Page<SalesActivityTaskVo> querySalesActivityTaskPage(QuerySalesActivityTaskParam param);

    /**
     * 获取销售活动任务列表详情
     * @param id
     * @return
     */
    SalesActivityTaskVo getSalesActivityTaskById(Long id);

    /**
     * 暂停/执行销售活动任务
     * @param id
     */
    void pauseSalesActivityTask(Long id);

}
