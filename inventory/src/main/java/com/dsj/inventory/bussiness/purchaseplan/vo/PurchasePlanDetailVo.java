package com.dsj.inventory.bussiness.purchaseplan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购计划详情
 * <AUTHOR>
 * @date 2023/04/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PurchasePlanDetailVo", description="采购计划详情实体")
@ToString(callSuper = true)
public class PurchasePlanDetailVo {

    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "计划单号")
    private String code;

    @ApiModelProperty(value = "开票日期")
    private LocalDateTime billDate;

    @ApiModelProperty(value = "预计收货日期")
    private LocalDateTime expectReceiveDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "处理状态 0待执行 1已执行")
    private Integer dealState;

    @ApiModelProperty(value = "是否自动生成：0-手动创建，1-自动生成")
    private Integer autoGenerated;

    @ApiModelProperty(value = "GSP校验状态：0-未校验，1-校验通过，-1-校验不通过")
    private Integer gspCheckStatus;

    @ApiModelProperty(value = "GSP校验信息，记录校验结果详情")
    private String checkMessage;

    @ApiModelProperty(value = "私域供应商Id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "供应商名称")
    private String organizationSupplierName;

    @ApiModelProperty(value = "供应商编号")
    private String organizationSupplierCode;

    @ApiModelProperty(value = "总金额")
    private Integer totalPrice;

    @ApiModelProperty(value = "商品种类")
    private String goodsTypes;

    @ApiModelProperty(value = "采购内容")
    private String purchaseContent;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "商品明细")
    private List<PurchasePlanGoodsVo> goodsList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
