---
description: 
globs: 
alwaysApply: true
---
# DSJ-Inventory 项目 Git 提交规范

## 1. 提交消息格式

每个提交消息必须遵循以下格式：

```
<类型>(<范围>): <简短描述>

<详细描述>

<相关任务编号>
```

## 2. 类型说明

提交类型必须是以下之一：

- `feat`: 新增功能或特性
- `fix`: 修复Bug
- `docs`: 文档更新（如README、注释、API文档等）
- `style`: 代码格式调整（不影响代码功能，如空格、格式、缩进、分号等）
- `refactor`: 代码重构（既不新增功能，也不修复Bug的代码变更）
- `perf`: 性能优化
- `test`: 新增或修改测试用例
- `build`: 构建系统或外部依赖变更（Maven配置、依赖版本等）
- `ci`: CI配置文件和脚本变更
- `chore`: 不修改src或测试文件的其他变更（如更新.gitignore）
- `revert`: 回滚之前的提交

## 3. 范围说明

范围标识变更影响的模块或功能，在项目中常用的范围包括：

- `inventory`: 库存管理模块
- `system`: 系统管理模块
- `framework`: 框架级别的变更
- `common`: 通用组件和工具
- `doc`: 文档相关
- `config`: 配置相关
- `api`: API接口相关
- `security`: 安全相关
- `ui`: 用户界面相关

## 4. 简短描述规范

- 使用中文，描述清晰明了
- 不超过50个字符
- 不以标点符号结尾
- 使用第一人称现在时态（如"添加"而非"添加了"或"添加中"）

## 5. 详细描述规范

- 使用中文
- 可以使用多个段落，每个段落间空一行
- 可使用列表格式（无序列表使用"-"，有序列表使用数字加点）
- 说明代码变更的原因、解决方案以及可能的影响
- 如有必要，可引用相关文档或资料

## 6. 相关任务编号

- 使用项目管理工具（如JIRA、GitHub Issues）的任务编号
- 格式为：`相关任务: #任务编号`
- 如有多个任务，用逗号分隔：`相关任务: #123, #124`

## 7. 提交示例

### 7.1 新增功能示例

```
feat(inventory): 新增库存批量导入功能

1. 实现从Excel文件导入库存数据
2. 添加数据验证机制，确保导入数据符合系统要求
3. 记录导入日志，便于追溯操作

相关任务: #123
```

### 7.2 修复Bug示例

```
fix(system): 修复用户登录失败问题

修复了在高并发情况下用户登录可能失败的问题。
- 优化了登录流程中的锁机制
- 增加了失败重试逻辑
- 添加了详细的日志记录

相关任务: #456
```

### 7.3 重构代码示例

```
refactor(common): 重构日期工具类

将原有的日期处理方法重构为使用Hutool工具库，
提高了代码可读性和可维护性，同时修复了潜在的线程安全问题。

相关任务: #789
```

## 8. 实践建议

- **小批量提交**：每个提交专注于一个变更，便于代码审查和问题定位
- **提交前检查**：使用 `git diff` 查看变更，确保符合预期
- **撰写提交消息时**：先写类型和范围，再思考简短描述，最后补充详细描述
- **遵循团队约定**：保持团队内提交消息风格的一致性

## 9. 注意事项

- 避免在公共分支使用 `git commit --amend` 和 `git push --force`
- 合理使用 `git rebase` 保持提交历史的整洁
- 定期同步主分支的更新，减少合并冲突

