package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.Member;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateMemberParam;
import com.dsj.inventory.bussiness.inventory.vo.MemberExportVo;
import com.dsj.inventory.bussiness.inventory.vo.MemberVo;

import java.util.List;

/**
 * <p>
 * 进销存会员 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface MemberService extends IService<Member> {

    /**
     * 新增、编辑、删除 会员
     * @param param
     * @return
     */
    MemberVo saveOrUpdateMember(SaveOrUpdateMemberParam param);

    /**
     * 获取会员分页列表
     * @param param
     * @return
     */
    Page<MemberVo> queryMemberPage(QueryMemberParam param);

    /**
     * 获取会员列表详情
     * @param id
     * @return
     */
    MemberVo getMemberById(Long id);

    /**
     * 导出会员列表
     * @param param
     * @return
     */
    List<MemberExportVo> queryMemberList(QueryMemberParam param);

}
