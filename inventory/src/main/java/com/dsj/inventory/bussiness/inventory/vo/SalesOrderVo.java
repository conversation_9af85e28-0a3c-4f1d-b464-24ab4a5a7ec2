package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 销售单
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SalesOrderVo", description="销售单实体")
@ToString(callSuper = true)
public class SalesOrderVo {

    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "销售单号")
    private String code;

    @ApiModelProperty(value = "订单状态：1 挂单 2已完成 3已退单")
    private Integer orderState;

    @ApiModelProperty(value = "订单类型：1线下 2线上")
    private Integer orderType;

    @ApiModelProperty(value = "销售员Id")
    private Long saleId;

    @ApiModelProperty(value = "销售员名称")
    private String saleName;

    @ApiModelProperty(value = "收银员id")
    private Long cashierId;

    @ApiModelProperty(value = "收银员名称")
    private String cashierName;

    @ApiModelProperty(value = "支付方式")
    private String payWay;

    @ApiModelProperty(value = "优惠")
    private Integer preferentialPrice;

    @ApiModelProperty(value = "合计")
    private Integer totalPrice;

    @ApiModelProperty(value = "实收")
    private Integer actualPrice;

    @ApiModelProperty(value = "应收")
    private Integer receivablePrice;

    @ApiModelProperty(value = "商品信息")
    private String goodsInfo;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
