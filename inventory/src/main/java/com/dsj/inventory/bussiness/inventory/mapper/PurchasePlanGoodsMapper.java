package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.PurchasePlanGoods;
import com.dsj.inventory.common.enumeration.TrueEnum;

/**
 * <p>
 * 进销存采购计划单商品明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
public interface PurchasePlanGoodsMapper extends BaseMapper<PurchasePlanGoods> {
    /**
     * 根据计划ID将商品明细标记为已删除
     * @param planId 计划ID
     * @return 受影响的行数
     */
    default int markAsDeletedByPlanId(Long planId) {
        PurchasePlanGoods entity = new PurchasePlanGoods();
        entity.setIsDelete(TrueEnum.TRUE.getCode());

        LambdaUpdateWrapper<PurchasePlanGoods> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PurchasePlanGoods::getPlanId, planId);

        return this.update(entity, wrapper);
    }
}
