package com.dsj.inventory.bussiness.quality.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.quality.entity.MedicineFirstBusinessEntity;
import com.dsj.inventory.bussiness.quality.enumeration.*;
import com.dsj.inventory.bussiness.quality.mapper.MedicineFirstBusinessMapper;
import com.dsj.inventory.bussiness.quality.param.MedicineFirstBusinessApprovalParam;
import com.dsj.inventory.bussiness.quality.param.MedicineFirstBusinessCreateParam;
import com.dsj.inventory.bussiness.quality.param.MedicineFirstBusinessQueryParam;
import com.dsj.inventory.bussiness.quality.service.MedicineFirstBusinessService;
import com.dsj.inventory.bussiness.quality.vo.MedicineFirstBusinessVO;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.SystemRoleCodeEnum;
import com.dsj.inventory.common.utils.PageUtils;
import com.dsj.inventory.framework.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 药品首营审批表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Service
public class MedicineFirstBusinessServiceImpl extends ServiceImpl<MedicineFirstBusinessMapper, MedicineFirstBusinessEntity> implements MedicineFirstBusinessService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createMedicineFirstBusiness(MedicineFirstBusinessCreateParam param) {
        // 首先检查是否已经存在该药品和供应商组合的草稿状态记录
        MedicineFirstBusinessEntity existingDraft = this.baseMapper.getMedicineFirstBusinessByMedicineAndSupplierAndStatus(
                param.getOrgMedicineMappingId(), param.getSupplierId(), DocumentStatusEnum.DRAFT.getCode());

        // 如果已存在草稿，则直接返回其ID
        if (existingDraft != null) {
            return existingDraft.getId();
        }

        // 不存在草稿，创建新记录
        MedicineFirstBusinessEntity entity = new MedicineFirstBusinessEntity();

        // 设置药品和供应商相关信息
        entity.setOrgMedicineMappingId(param.getOrgMedicineMappingId());
        entity.setSupplierId(param.getSupplierId());

        // 设置默认值
        entity.setApplyTime(param.getApplyTime() != null ? param.getApplyTime() : LocalDateTime.now());
        entity.setStatus(param.getStatus() != null ? param.getStatus() : DocumentStatusEnum.DRAFT.getCode()); // 默认状态: 草稿
        entity.setApprovalStep(MedicineFirstApprovalStepEnum.PURCHASE_MANAGER_REVIEW.getCode()); // 第一步：采购经理审核
        entity.setActionType(ActionTypeEnum.CREATE.getCode()); // 操作类型为创建
        entity.setApprovalOpinion(param.getApprovalOpinion());
        entity.setRemark(param.getRemark());
        entity.setApprovalSequence(this.baseMapper.getNextApprovalSequence(param.getOrgMedicineMappingId(), param.getSupplierId()));

        this.save(entity);
        return entity.getId();
    }

    @Override
    public MedicineFirstBusinessVO getMedicineFirstBusinessById(Long id) {
        // 获取实体
        MedicineFirstBusinessEntity entity = this.getById(id);
        if (entity == null) {
            return null;
        }

        // 转换为VO
        return convertToVO(entity);
    }

    @Override
    public Page<MedicineFirstBusinessVO> queryMedicineFirstBusinessPage(MedicineFirstBusinessQueryParam param) {
        // 创建分页对象
        Page<MedicineFirstBusinessEntity> page = PageUtils.toPage(param);

        // 创建查询条件
        LambdaQueryWrapper<MedicineFirstBusinessEntity> queryWrapper = Wrappers.lambdaQuery();

        // 添加查询条件
        queryWrapper.eq(param.getOrgMedicineMappingId() != null,
                MedicineFirstBusinessEntity::getOrgMedicineMappingId, param.getOrgMedicineMappingId());
        queryWrapper.eq(param.getSupplierId() != null,
                MedicineFirstBusinessEntity::getSupplierId, param.getSupplierId());
        queryWrapper.eq(param.getStatus() != null,
                MedicineFirstBusinessEntity::getStatus, param.getStatus());
        queryWrapper.eq(param.getApprovalStep() != null,
                MedicineFirstBusinessEntity::getApprovalStep, param.getApprovalStep());

        // 排序
        queryWrapper.orderByDesc(MedicineFirstBusinessEntity::getCreateTime);

        // 执行查询
        page = this.page(page, queryWrapper);

        // 转换为VO
        return PageUtils.toPageVO(page, this::convertToVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long approveMedicineFirstBusiness(MedicineFirstBusinessApprovalParam param) {
        // 获取要审批的记录
        MedicineFirstBusinessEntity entity = this.getById(param.getId());
        if (entity == null) {
            throw new BizException("药品首营审批记录不存在，ID: " + param.getId());
        }

        // 校验药品ID是否匹配
        if (!entity.getOrgMedicineMappingId().equals(param.getOrgMedicineMappingId())) {
            throw new BizException("药品ID与审批记录不匹配");
        }

        // 校验状态
        if (!DocumentStatusEnum.REVIEWING.getCode().equals(entity.getStatus())) {
            throw new BizException("只有审核中状态的记录才能进行审批");
        }

        // 校验当前用户是否有权限进行当前步骤的审批
        validateApprovalPermission(entity.getApprovalStep(), entity.getCurrentApproverRole());

        // 创建新的审批记录
        MedicineFirstBusinessEntity newEntity = BeanUtil.copyProperties(entity, MedicineFirstBusinessEntity.class, "id", "createTime", "createUser", "updateTime", "updateUser");
        newEntity.setId(null); // 确保创建新记录
        newEntity.setActionType(param.getActionType()); // 设置操作类型（通过/驳回）
        newEntity.setApprovalOpinion(param.getApprovalOpinion()); // 设置审批意见
        newEntity.setRejectReason(param.getRejectReason()); // 设置驳回原因
        newEntity.setReviewerId(BaseContextHandler.getUserId()); // 设置审核人
        newEntity.setReviewTime(LocalDateTime.now()); // 设置审核时间
        newEntity.setApprovalSequence(this.baseMapper.getNextApprovalSequence(
                param.getOrgMedicineMappingId(), entity.getSupplierId())); // 获取新的审批序号

        // 根据审批结果设置状态和下一步
        if (ActionTypeEnum.APPROVE.getCode().equals(param.getActionType())) {
            // 审批通过
            // 根据当前审批步骤设置下一步
            if (MedicineFirstApprovalStepEnum.PURCHASE_MANAGER_REVIEW.getCode().equals(entity.getApprovalStep())) {
                // 第一步通过，进入第二步
                newEntity.setApprovalStep(MedicineFirstApprovalStepEnum.QUALITY_MANAGER_REVIEW.getCode());
                newEntity.setCurrentApproverRole(SystemRoleCodeEnum.QUALITY_MANAGER.getCode());
                newEntity.setStatus(DocumentStatusEnum.REVIEWING.getCode()); // 仍然是审核中
            } else if (MedicineFirstApprovalStepEnum.QUALITY_MANAGER_REVIEW.getCode().equals(entity.getApprovalStep())) {
                // 第二步通过，进入第三步
                newEntity.setApprovalStep(MedicineFirstApprovalStepEnum.QUALITY_SUPERVISOR_REVIEW.getCode());
                newEntity.setCurrentApproverRole(SystemRoleCodeEnum.QUALITY_SUPERVISOR.getCode());
                newEntity.setStatus(DocumentStatusEnum.REVIEWING.getCode()); // 仍然是审核中
            } else if (MedicineFirstApprovalStepEnum.QUALITY_SUPERVISOR_REVIEW.getCode().equals(entity.getApprovalStep())) {
                // 第三步通过，审批完成
                newEntity.setApprovalStep(MedicineFirstApprovalStepEnum.COMPLETED.getCode());
                newEntity.setCurrentApproverRole(null); // 没有下一个审批人
                newEntity.setStatus(DocumentStatusEnum.APPROVED.getCode()); // 状态改为已审核

                //TODO 更新药品状态为已审核通过 =》 DocumentStatusEnum.APPROVED

            }
        } else if (ActionTypeEnum.REJECT.getCode().equals(param.getActionType())) {
            // 审批驳回
            newEntity.setStatus(DocumentStatusEnum.REJECTED.getCode()); // 状态改为已驳回
            newEntity.setCurrentApproverRole(null); // 清空当前审批人角色

            //TODO 驳回状态也需要更新药品状态 =>

        }

        // 保存新的审批记录
        this.save(newEntity);
        return newEntity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long withdrawMedicineFirstBusiness(Long id) {
        // 获取要撤回的记录
        MedicineFirstBusinessEntity entity = this.getById(id);
        if (entity == null) {
            throw new BizException("药品首营审批记录不存在，ID: " + id);
        }

        // 只有创建人才能撤回
        if (!BaseContextHandler.getUserId().equals(entity.getCreateUser())) {
            throw new BizException("只有创建人才能撤回审批");
        }

        // 只有审核中状态的记录才能撤回
        if (!DocumentStatusEnum.REVIEWING.getCode().equals(entity.getStatus())) {
            throw new BizException("只有审核中状态的记录才能撤回");
        }

        // 创建新的撤回记录
        MedicineFirstBusinessEntity newEntity = BeanUtil.copyProperties(entity, MedicineFirstBusinessEntity.class, "id", "createTime", "createUser", "updateTime", "updateUser");
        newEntity.setId(null); // 确保创建新记录
        newEntity.setActionType(ActionTypeEnum.WITHDRAW.getCode()); // 设置操作类型为撤回
        newEntity.setStatus(DocumentStatusEnum.DRAFT.getCode()); // 撤回后状态改为草稿
        newEntity.setCurrentApproverRole(BaseContextHandler.get(BaseContextConstants.USER_ROLE)); // 当前审批人角色
        newEntity.setReviewerId(BaseContextHandler.getUserId()); // 设置撤回人
        newEntity.setReviewTime(LocalDateTime.now()); // 设置撤回时间
        newEntity.setApprovalSequence(this.baseMapper.getNextApprovalSequence(
                entity.getOrgMedicineMappingId(), entity.getSupplierId())); // 获取新的审批序号

        // 保存新的审批记录
        this.save(newEntity);

        //TODO 更新药品状态为草稿 =》 DocumentStatusEnum.DRAFT

        return newEntity.getId();
    }

    @Override
    public MedicineFirstBusinessVO getLatestMedicineFirstBusiness(Long orgMedicineMappingId, Long supplierId) {
        // 获取最新的审批记录
        MedicineFirstBusinessEntity entity = this.baseMapper.getLatestMedicineFirstBusiness(orgMedicineMappingId, supplierId);
        if (entity == null) {
            return null;
        }

        // 转换为VO
        return convertToVO(entity);
    }

    @Override
    public List<MedicineFirstBusinessVO> getMedicineFirstBusinessHistory(Long orgMedicineMappingId, Long supplierId) {
        // 查询所有历史记录
        LambdaQueryWrapper<MedicineFirstBusinessEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MedicineFirstBusinessEntity::getOrgMedicineMappingId, orgMedicineMappingId)
                .eq(MedicineFirstBusinessEntity::getSupplierId, supplierId)
                .orderByAsc(MedicineFirstBusinessEntity::getApprovalSequence);

        List<MedicineFirstBusinessEntity> entities = this.list(queryWrapper);

        // 转换为VO列表
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public Page<MedicineFirstBusinessVO> queryPendingApprovalPage(MedicineFirstBusinessQueryParam param) {
        // 获取当前用户角色
        String userRole = BaseContextHandler.get(BaseContextConstants.USER_ROLE);
        if (StringUtils.isEmpty(userRole)) {
            throw new BizException("未获取到当前用户角色信息");
        }

        // 创建分页对象
        Page<MedicineFirstBusinessEntity> page = PageUtils.toPage(param);

        // 创建查询条件
        LambdaQueryWrapper<MedicineFirstBusinessEntity> queryWrapper = Wrappers.lambdaQuery();

        // 只查询审核中的记录
        queryWrapper.eq(MedicineFirstBusinessEntity::getStatus, DocumentStatusEnum.REVIEWING.getCode());
        // 根据角色匹配当前审批人
        queryWrapper.eq(MedicineFirstBusinessEntity::getCurrentApproverRole, userRole);

        // 添加查询条件
        if (param.getOrgMedicineMappingId() != null) {
            queryWrapper.eq(MedicineFirstBusinessEntity::getOrgMedicineMappingId, param.getOrgMedicineMappingId());
        }
        if (param.getSupplierId() != null) {
            queryWrapper.eq(MedicineFirstBusinessEntity::getSupplierId, param.getSupplierId());
        }

        // 排序
        queryWrapper.orderByDesc(MedicineFirstBusinessEntity::getCreateTime);

        // 执行查询
        page = this.page(page, queryWrapper);

        // 转换为VO
        return PageUtils.toPageVO(page, this::convertToVO);
    }

    @Override
    public boolean checkMedicineFirstBusinessStatus(Long orgMedicineMappingId, Long supplierId) {
        // 检查药品与供应商组合是否已通过首营审批
        MedicineFirstBusinessEntity latestEntity = this.baseMapper.getMedicineFirstBusinessByMedicineAndSupplierAndStatus(
                orgMedicineMappingId, supplierId, DocumentStatusEnum.APPROVED.getCode());

        return latestEntity != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createMedicineModificationApproval(Long orgMedicineMappingId, Long supplierId) {
        // TODO: Need to get the medicine entity to check its status, assuming a service exists
        // OrganizationMedicineMapping medicine = organizationMedicineMappingService.getById(orgMedicineMappingId);
        // if (!DocumentStatusEnum.REVIEWING.getCode().equals(medicine.getStatus())) {
        //     throw new BizException("只有审核中状态的药品才能发起修改审批");
        // }

        // 检查是否已存在该药品的、正在审核中的修改审批流程
        MedicineFirstBusinessEntity existingReviewing = this.baseMapper.findLatestByMedicineAndSupplierAndType(
                orgMedicineMappingId, supplierId, ApprovalTypeEnum.MODIFICATION.getCode());

        if (existingReviewing != null && DocumentStatusEnum.REVIEWING.getCode().equals(existingReviewing.getStatus())) {
            return existingReviewing.getId(); // 避免重复创建
        }

        // 创建新的修改审批记录
        MedicineFirstBusinessEntity entity = new MedicineFirstBusinessEntity();
        entity.setOrgMedicineMappingId(orgMedicineMappingId);
        entity.setSupplierId(supplierId);
        entity.setApplyTime(LocalDateTime.now());
        entity.setStatus(DocumentStatusEnum.REVIEWING.getCode());
        entity.setApprovalStep(ModificationApprovalStepEnum.QUALITY_MANAGER_REVIEW.getCode());
        entity.setActionType(ActionTypeEnum.SUBMIT.getCode());
        entity.setApprovalSequence(this.baseMapper.getNextApprovalSequence(orgMedicineMappingId, supplierId));
        entity.setApprovalType(ApprovalTypeEnum.MODIFICATION.getCode());
        entity.setCurrentApproverRole(SystemRoleCodeEnum.QUALITY_MANAGER.getCode());
        // entity.setCreateUser(medicine.getUpdateUser()); //TODO 将修改者设置为申请人

        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long approveMedicineModification(MedicineFirstBusinessApprovalParam param) {
        MedicineFirstBusinessEntity entity = this.getById(param.getId());
        if (entity == null) {
            throw new BizException("药品修改审批记录不存在");
        }

        if (!ApprovalTypeEnum.MODIFICATION.getCode().equals(entity.getApprovalType())) {
            throw new BizException("该记录不是修改审批类型");
        }

        if (!entity.getOrgMedicineMappingId().equals(param.getOrgMedicineMappingId())) {
            throw new BizException("药品ID与审批记录不匹配");
        }

        if (!DocumentStatusEnum.REVIEWING.getCode().equals(entity.getStatus())) {
            throw new BizException("只有审核中状态的记录才能进行审批");
        }

        validateApprovalPermission(entity.getApprovalStep(), entity.getCurrentApproverRole());

        MedicineFirstBusinessEntity newEntity = BeanUtil.copyProperties(entity, MedicineFirstBusinessEntity.class, "id", "createTime", "createUser", "updateTime", "updateUser");
        newEntity.setId(null);
        newEntity.setActionType(param.getActionType());
        newEntity.setApprovalOpinion(param.getApprovalOpinion());
        newEntity.setReviewerId(BaseContextHandler.getUserId());
        newEntity.setReviewTime(LocalDateTime.now());
        newEntity.setApprovalSequence(this.baseMapper.getNextApprovalSequence(param.getOrgMedicineMappingId(), entity.getSupplierId()));

        if (ActionTypeEnum.APPROVE.getCode().equals(param.getActionType())) {
            if (ModificationApprovalStepEnum.QUALITY_MANAGER_REVIEW.getCode().equals(entity.getApprovalStep())) {
                newEntity.setApprovalStep(ModificationApprovalStepEnum.QUALITY_SUPERVISOR_REVIEW.getCode());
                newEntity.setCurrentApproverRole(SystemRoleCodeEnum.QUALITY_SUPERVISOR.getCode());
                newEntity.setStatus(DocumentStatusEnum.REVIEWING.getCode());
            } else if (ModificationApprovalStepEnum.QUALITY_SUPERVISOR_REVIEW.getCode().equals(entity.getApprovalStep())) {
                newEntity.setApprovalStep(ModificationApprovalStepEnum.COMPLETED.getCode());
                newEntity.setCurrentApproverRole(null);
                newEntity.setStatus(DocumentStatusEnum.APPROVED.getCode());
                // TODO: 更新药品状态为已审核通过
            }
        } else if (ActionTypeEnum.REJECT.getCode().equals(param.getActionType())) {
            newEntity.setStatus(DocumentStatusEnum.REJECTED.getCode());
            newEntity.setRejectReason(param.getRejectReason());
            // TODO: 更新药品状态为已驳回
        }

        this.save(newEntity);
        return newEntity.getId();
    }

    /**
     * 校验当前用户是否有权限进行当前步骤的审批
     *
     * @param approvalStep 审批步骤
     * @param requiredRole 所需角色
     */
    private void validateApprovalPermission(Integer approvalStep, String requiredRole) {
        // 获取当前登录用户角色
        String userRole = BaseContextHandler.get(BaseContextConstants.USER_ROLE);

        if (StringUtils.isEmpty(userRole) || !requiredRole.equals(userRole)) {
            throw new BizException(String.format("您没有进行当前步骤(%s)审批的权限",
                    MedicineFirstApprovalStepEnum.getDescByCode(approvalStep)));
        }
    }

    /**
     * 将实体对象转换为视图对象
     *
     * @param entity 实体对象
     * @return 视图对象
     */
    private MedicineFirstBusinessVO convertToVO(MedicineFirstBusinessEntity entity) {
        if (entity == null) {
            return null;
        }

        MedicineFirstBusinessVO vo = BeanUtil.copyProperties(entity, MedicineFirstBusinessVO.class);

        // 设置枚举描述
        if (entity.getApprovalStep() != null) {
            vo.setApprovalStepDesc(MedicineFirstApprovalStepEnum.getDescByCode(entity.getApprovalStep()));
        }

        if (entity.getActionType() != null) {
            vo.setActionTypeDesc(ActionTypeEnum.getDescByCode(entity.getActionType()));
        }

        if (entity.getStatus() != null) {
            vo.setStatusDesc(DocumentStatusEnum.getDescByCode(entity.getStatus()));
        }

        // TODO: 根据orgMedicineMappingId获取药品名称、规格等信息
        // TODO: 根据supplierId获取供应商名称
        // TODO: 根据reviewerId获取审核人姓名

        return vo;
    }
} 