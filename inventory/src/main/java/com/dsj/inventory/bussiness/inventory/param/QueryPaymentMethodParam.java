package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织付款方式查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "组织付款方式查询参数")
public class QueryPaymentMethodParam extends BasePage {

    @ApiModelProperty(value = "组织ID")
    private Long organizationId;

    @ApiModelProperty(value = "付款方式编码")
    private String methodCode;

    @ApiModelProperty(value = "付款方式名称")
    private String methodName;

    @ApiModelProperty(value = "是否启用：1启用，0禁用")
    private Integer enabled;
} 