package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityTaskCycle;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskCycleVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 销售活动任务周期信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
public interface SalesActivityTaskCycleMapper extends BaseMapper<SalesActivityTaskCycle> {

    /**
     * 获取任务周期
     * @param activityId
     * @param taskId
     * @param executeState
     * @return
     */
    List<SalesActivityTaskCycleVo> queryCycleListByActivityId(@Param("activityId") Long activityId
            ,@Param("taskId") Long taskId, @Param("executeState") Integer executeState);

}
