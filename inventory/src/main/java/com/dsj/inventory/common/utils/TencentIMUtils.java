package com.dsj.inventory.common.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pocky.transport.bussiness.diagnose.param.IMCallBackParam;
import com.dsj.inventory.common.entity.*;
import com.dsj.inventory.common.utils.CommonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.TextUtils;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.zip.Deflater;


/**
 * <AUTHOR>
 * @ClassName : TencentIMUtils
 * @Description : 腾讯IM 工具类
 * @Date :
 */
@Slf4j
@Data
@Component
public class TencentIMUtils {

    private Long SDKAPPID = 0L;

    private int EXPIRETIME = 604800;

    private String SECRETKEY = "";

    /**
     * 使用默认管理员账号
     */
    private static final String IDENTIFIER = "administrator";

    /** 记住请求URL */
    private static final String BASE_URL = "https://console.tim.qq.com/";

    /** 单聊请求 singleChat */
    private static final String SINGLE_CHAT = "v4/openim/sendmsg";

    /** 批量单聊请求 batchSingleChat */
    private static final String BATCH_SINGLE_CHAT = "v4/openim/batchsendmsg";

    /** 解散群组 */
    private static final String DESTROY_GROUP = "v4/group_open_http_svc/destroy_group";

    /** 获取群成员详细资料  */
    private static final String GET_GROUP_MEMBER_INFO = "v4/group_open_http_svc/get_group_member_info";

    /** 设置用户群资料  */
    private static final String PORTRAIT_SET = "v4/profile/portrait_set";

    /** 获取群详细资料  */
    private static final String GET_GROUP_INFO = "v4/group_open_http_svc/get_group_info";

    /** 获取 App 中的所有群组  */
    private static final String GET_APP_GROUP_LIST = "v4/group_open_http_svc/get_appid_group_list";


    /**
     * 管理员发送单聊消息
     * 文档地址 https://cloud.tencent.com/document/product/269/2282
     * toUserId ：接收人id
     * data ：消息大类
     * desc ： 消息小分类
     * ext: 定制内容
     */
    public IMResponseVo adminSendSingleMsg(Long toUserId,String data,String desc,String extensionJson){
        String userSig = genTestUserSig("administrator");
        UrlBuilder urlBuilder = UrlBuilder.of(BASE_URL, CharsetUtil.CHARSET_UTF_8);
        urlBuilder.addPath(SINGLE_CHAT);
        urlBuilder.addQuery("sdkappid",SDKAPPID.toString());
        urlBuilder.addQuery("identifier",IDENTIFIER);
        urlBuilder.addQuery("usersig",userSig);
        urlBuilder.addQuery("random", RandomUtil.randomNumbers(32));
        urlBuilder.addQuery("contenttype", "json");

        IMMsgBody.MsgContent msgContent = IMMsgBody.MsgContent.builder().Data(data).Desc(desc).Ext(extensionJson).build();
        IMMsgBody imMsgBody = IMMsgBody.builder().MsgType(IMMsgBody.TIMCustomElem).MsgContent(msgContent).build();

        IMRequestParam imRequestParam = IMRequestParam.builder().build();
        imRequestParam.setFrom_Account("1");
        imRequestParam.setTo_Account(toUserId.toString());
        imRequestParam.setMsgRandom(Integer.valueOf(RandomUtil.randomNumbers(7)));
        imRequestParam.setMsgBody(ListUtil.toList(imMsgBody));
        if(data.equals("poll")){
            imRequestParam.setSendMsgControl(Arrays.asList("NoUnread","NoLastMsg"));
        }
        String result = HttpUtil.post(urlBuilder.toString(), JSONUtil.toJsonStr(imRequestParam));
        log.info("=======管理员发起IM 单聊信息======请求链接:{}, to:{},内容:{},MsgBody:{},result:{}",urlBuilder.toString(),toUserId,desc,imMsgBody,result);
        IMResponseVo imResponseVo = JSONUtil.toBean(result, IMResponseVo.class);
        if (CommonUtils.isNotEmpty(imResponseVo) && imResponseVo.getErrorCode().intValue() == 0){
            log.info("==========={}消息发送《成功》===========",desc);
        }else {
            log.info("=========={}消息发送《失败》===========",desc);
        }
        return imResponseVo;
    }

    /**
     * 管理员批量发送消息
     * 文档地址 https://cloud.tencent.com/document/product/269/1612
     * @param toUserIds 接收人ids
     * data ：消息大类
     * desc ： 消息小分类
     * ext: 定制内容
     * @return
     */
    public IMResponseVo adminSendBatchSingleMsg(List<String> toUserIds,String data,String desc,String extensionJson){
        String userSig = genTestUserSig("administrator");
        UrlBuilder urlBuilder = UrlBuilder.of(BASE_URL, CharsetUtil.CHARSET_UTF_8);
        urlBuilder.addPath(BATCH_SINGLE_CHAT);
        urlBuilder.addQuery("sdkappid",SDKAPPID.toString());
        urlBuilder.addQuery("identifier",IDENTIFIER);
        urlBuilder.addQuery("usersig",userSig);
        urlBuilder.addQuery("random", RandomUtil.randomNumbers(32));
        urlBuilder.addQuery("contenttype", "json");

        IMMsgBody.MsgContent msgContent = IMMsgBody.MsgContent.builder().Data(data).Desc(desc).Ext(extensionJson).build();
        IMMsgBody imMsgBody = IMMsgBody.builder().MsgType(IMMsgBody.TIMCustomElem).MsgContent(msgContent).build();

        IMBatchRequestParam imRequestParam = IMBatchRequestParam.builder().build();
        imRequestParam.setFrom_Account("1");
        imRequestParam.setTo_Account(toUserIds);
        imRequestParam.setMsgRandom(Integer.valueOf(RandomUtil.randomNumbers(7)));
        imRequestParam.setMsgBody(ListUtil.toList(imMsgBody));
        String result = HttpUtil.post(urlBuilder.toString(), JSONUtil.toJsonStr(imRequestParam));
        log.info("=======管理员发起IM 群发信息======请求链接:{}, to:{},内容:{},MsgBody:{},result:{}",urlBuilder.toString(),toUserIds,desc,imMsgBody,result);
        IMResponseVo imResponseVo = JSONUtil.toBean(result, IMResponseVo.class);
        if (CommonUtils.isNotEmpty(imResponseVo) && imResponseVo.getErrorCode().intValue() == 0){
            log.info("==========={}消息发送《成功》===========",desc);
        }else {
            log.info("=========={}消息发送《失败》===========",desc);
        }
        return imResponseVo;
    }

    /**
     * 解散群组
     * 文档地址 https://cloud.tencent.com/document/product/269/1624
     * groupId ：群组id
     */
    public IMResponseVo destroyGroup(String groupId){
        String userSig = genTestUserSig("administrator");
        UrlBuilder urlBuilder = UrlBuilder.of(BASE_URL, CharsetUtil.CHARSET_UTF_8);
        urlBuilder.addPath(DESTROY_GROUP);
        urlBuilder.addQuery("sdkappid",SDKAPPID.toString());
        urlBuilder.addQuery("identifier",IDENTIFIER);
        urlBuilder.addQuery("usersig",userSig);
        urlBuilder.addQuery("random", RandomUtil.randomNumbers(32));
        urlBuilder.addQuery("contenttype", "json");

        JSONObject jsonObject = new JSONObject();
        jsonObject.set("GroupId",groupId);
        String result = HttpUtil.post(urlBuilder.toString(), JSONUtil.toJsonStr(jsonObject));
        log.info("=======管理员发起IM 解散群组请求======请求链接:{}, 群id:{},内容:{},result:{}",urlBuilder,groupId,jsonObject,result);
        return JSONUtil.toBean(result,IMResponseVo.class);
    }

    /**
     * 获取群组-成员-信息
     * 文档地址 https://cloud.tencent.com/document/product/269/1617
     * groupId ：群组id
     * memberInfoFilter ：过滤群成员信息字段
     * MemberRoleFilter ：过滤群成员角色字段
     * AppDefinedDataFilter_GroupMember ：过滤群成员自定义字段
     * limit ：一次获取数量
     * offset ： 从第几个成员开始
     */
    public IMGroupMemberResponseVo getGroupMemberInfo(String groupId, List<String> MemberInfoFilter, List<String> MemberRoleFilter, List<String> AppDefinedDataFilter_GroupMember, Integer Limit, Integer Offset){
        String userSig = genTestUserSig("administrator");
        UrlBuilder urlBuilder = UrlBuilder.of(BASE_URL, CharsetUtil.CHARSET_UTF_8);
        urlBuilder.addPath(GET_GROUP_MEMBER_INFO);
        urlBuilder.addQuery("sdkappid",SDKAPPID.toString());
        urlBuilder.addQuery("identifier",IDENTIFIER);
        urlBuilder.addQuery("usersig",userSig);
        urlBuilder.addQuery("random", RandomUtil.randomNumbers(32));
        urlBuilder.addQuery("contenttype", "json");

        JSONObject jsonObject = new JSONObject();
        jsonObject.set("GroupId",groupId);
        if (CommonUtils.isNotEmpty(MemberInfoFilter)){
            jsonObject.set("MemberInfoFilter",MemberInfoFilter);
        }
        if (CommonUtils.isNotEmpty(MemberRoleFilter)){
            jsonObject.set("MemberRoleFilter",MemberRoleFilter);
        }
        if (CommonUtils.isNotEmpty(AppDefinedDataFilter_GroupMember)){
            jsonObject.set("AppDefinedDataFilter_GroupMember",AppDefinedDataFilter_GroupMember);
        }
        if (CommonUtils.isNotEmpty(Limit)){
            jsonObject.set("Limit",Limit);
        }
        if (CommonUtils.isNotEmpty(Offset)){
            jsonObject.set("Offset",Offset);
        }
        String result = HttpUtil.post(urlBuilder.toString(), JSONUtil.toJsonStr(jsonObject));
        log.info("=======管理员发起IM 获取群组成员信息请求======请求链接:{}, 群id:{},请求内容:{},result:{}",urlBuilder,groupId,jsonObject.toString(),result);
        return JSONUtil.toBean(result,IMGroupMemberResponseVo.class);
    }

    /**
     * 获取群组详情信息
     * 文档地址 https://cloud.tencent.com/document/product/269/1617
     * groupIds ：群组ids
     * GroupBaseInfoFilter ：过滤群信息字段
     * MemberInfoFilter ：过滤群成员字段
     * AppDefinedDataFilter_Group ：过滤群自定义字段
     * AppDefinedDataFilter_GroupMember ：过滤群成员自定义字段
     */
    public IMGroupInfoResponseVo getGroupsInfo(List<String> groupIds, List<String> GroupBaseInfoFilter, List<String> MemberInfoFilter, List<String> AppDefinedDataFilter_Group,List<String> AppDefinedDataFilter_GroupMember){
        String userSig = genTestUserSig("administrator");
        UrlBuilder urlBuilder = UrlBuilder.of(BASE_URL, CharsetUtil.CHARSET_UTF_8);
        urlBuilder.addPath(GET_GROUP_INFO);
        urlBuilder.addQuery("sdkappid",SDKAPPID.toString());
        urlBuilder.addQuery("identifier",IDENTIFIER);
        urlBuilder.addQuery("usersig",userSig);
        urlBuilder.addQuery("random", RandomUtil.randomNumbers(32));
        urlBuilder.addQuery("contenttype", "json");

        JSONObject jsonObject = new JSONObject();
        jsonObject.set("GroupIdList",groupIds);
        JSONObject filterJson = new JSONObject();
        boolean needFilter = false;
        if (CommonUtils.isNotEmpty(GroupBaseInfoFilter)){
            filterJson.set("GroupBaseInfoFilter",GroupBaseInfoFilter);
            needFilter = true;
        }
        if (CommonUtils.isNotEmpty(MemberInfoFilter)){
            filterJson.set("MemberInfoFilter",MemberInfoFilter);
            needFilter = true;
        }
        if (CommonUtils.isNotEmpty(AppDefinedDataFilter_Group)){
            filterJson.set("AppDefinedDataFilter_Group",AppDefinedDataFilter_Group);
            needFilter = true;
        }
        if (CommonUtils.isNotEmpty(AppDefinedDataFilter_GroupMember)){
            filterJson.set("AppDefinedDataFilter_GroupMember",AppDefinedDataFilter_GroupMember);
            needFilter = true;
        }
        if (needFilter){
            jsonObject.set("ResponseFilter",filterJson);
        }
        String result = HttpUtil.post(urlBuilder.toString(), JSONUtil.toJsonStr(jsonObject));
        log.info("=======管理员发起IM 获取群组详情信息请求======请求链接:{}, 群ids:{},请求内容:{},result:{}",urlBuilder,groupIds,jsonObject.toString(),result);
        return JSONUtil.toBean(result,IMGroupInfoResponseVo.class);
    }

    /**
     * 获取群组列表信息
     * 文档地址 https://cloud.tencent.com/document/product/269/1614
     * GroupType ：群组分类
     * limit ：一次获取数量
     * Next ：分页信息
     */
    public IMGroupListResponseVo getGroupList(String GroupType, Integer Limit, Integer Next){
        String userSig = genTestUserSig("administrator");
        UrlBuilder urlBuilder = UrlBuilder.of(BASE_URL, CharsetUtil.CHARSET_UTF_8);
        urlBuilder.addPath(GET_APP_GROUP_LIST);
        urlBuilder.addQuery("sdkappid",SDKAPPID.toString());
        urlBuilder.addQuery("identifier",IDENTIFIER);
        urlBuilder.addQuery("usersig",userSig);
        urlBuilder.addQuery("random", RandomUtil.randomNumbers(32));
        urlBuilder.addQuery("contenttype", "json");

        JSONObject jsonObject = new JSONObject();
        JSONObject filterJson = new JSONObject();
        boolean needFilter = false;
        if (CommonUtils.isNotEmpty(GroupType)){
            jsonObject.set("GroupType",GroupType);
            needFilter = true;
        }
        if (CommonUtils.isNotEmpty(Limit)){
            jsonObject.set("Limit",Limit);
            needFilter = true;
        }
        if (CommonUtils.isNotEmpty(Next)){
            jsonObject.set("Next",Next);
            needFilter = true;
        }
        String result = HttpUtil.post(urlBuilder.toString(), JSONUtil.toJsonStr(jsonObject));
        log.info("=======管理员发起IM 获取app所有群列表组信息请求======请求链接:{},请求内容:{},result:{}",urlBuilder,jsonObject.toString(),result);
        return JSONUtil.toBean(result,IMGroupListResponseVo.class);
    }

    /**
     * 设置用户群资料
     * 文档地址 https://cloud.tencent.com/document/product/269/1640
     */
    public IMResponseVo portraitSet(String userID,String tag,Object value){
        String userSig = genTestUserSig("administrator");
        UrlBuilder urlBuilder = UrlBuilder.of(BASE_URL, CharsetUtil.CHARSET_UTF_8);
        urlBuilder.addPath(PORTRAIT_SET);
        urlBuilder.addQuery("sdkappid",SDKAPPID.toString());
        urlBuilder.addQuery("identifier",IDENTIFIER);
        urlBuilder.addQuery("usersig",userSig);
        urlBuilder.addQuery("random", RandomUtil.randomNumbers(32));
        urlBuilder.addQuery("contenttype", "json");

        IMCallBackParam.ProfileItem profileItem = IMCallBackParam.ProfileItem.builder().Tag(tag).Value(value).build();
        IMCallBackParam build = IMCallBackParam.builder().From_Account(userID).ProfileItem(Arrays.asList(profileItem)).build();

        String result = HttpUtil.post(urlBuilder.toString(), JSONUtil.toJsonStr(build));
        log.info("=======管理员发起IM 设置用户资料======请求链接:{},请求内容:{},result:{}",urlBuilder,build.toString(),result);
        return JSONUtil.toBean(result,IMResponseVo.class);
    }

    /**
     * 计算 UserSig 签名
     * 仅限 后台内部调用使用，不建议加入到 web 请求获取方式，如web请求获取 请参考官方方式
     * <p>
     * 函数内部使用 HMAC-SHA256 非对称加密算法，对 SDKAPPID、userId 和 EXPIRETIME 进行加密。
     */
    public String genTestUserSig(String userId) {
        return GenTLSSignature(SDKAPPID, userId, EXPIRETIME, null, SECRETKEY);
    }

    /**
     * 计算 UserSig 签名
     * @param sdkAppId
     * @param userId
     * @param expire
     * @param priKeyContent
     * @return
     */
    public String getUserSig(Long sdkAppId, String userId, long expire, String priKeyContent){
        return GenTLSSignature(sdkAppId, userId, expire, null, priKeyContent);
    }

    /**
     * 生成 tls 票据
     *
     * @param sdkappid      应用的 appid
     * @param userId        用户 id
     * @param expire        有效期，单位是秒
     * @param userbuf       默认填写null
     * @param priKeyContent 生成 tls 票据使用的私钥内容
     * @return 如果出错，会返回为空，或者有异常打印，成功返回有效的票据
     */
    private String GenTLSSignature(long sdkappid, String userId, long expire, byte[] userbuf, String priKeyContent) {
        if (TextUtils.isEmpty(priKeyContent)) {
            return "";
        }
        long currTime = System.currentTimeMillis() / 1000;
        JSONObject sigDoc = new JSONObject();
        sigDoc.set("TLS.ver", "2.0");
        sigDoc.set("TLS.identifier", userId);
        sigDoc.set("TLS.sdkappid", sdkappid);
        sigDoc.set("TLS.expire", expire);
        sigDoc.set("TLS.time", currTime);

        String base64UserBuf = null;
        if (null != userbuf) {
            base64UserBuf = Base64.encode(userbuf);
            sigDoc.set("TLS.userbuf", base64UserBuf);
        }
        String sig = hmacsha256(sdkappid, userId, currTime, expire, priKeyContent, base64UserBuf);
        if (sig.length() == 0) {
            return "";
        }
        sigDoc.set("TLS.sig", sig);

        Deflater compressor = new Deflater();
        compressor.setInput(sigDoc.toString().getBytes(Charset.forName("UTF-8")));
        compressor.finish();
        byte[] compressedBytes = new byte[2048];
        int compressedBytesLength = compressor.deflate(compressedBytes);
        compressor.end();
        return new String(base64EncodeUrl(Arrays.copyOfRange(compressedBytes, 0, compressedBytesLength)));
    }

    private static String hmacsha256(long sdkappid, String userId, long currTime, long expire, String priKeyContent, String base64Userbuf) {
        String contentToBeSigned = "TLS.identifier:" + userId + "\n"
                + "TLS.sdkappid:" + sdkappid + "\n"
                + "TLS.time:" + currTime + "\n"
                + "TLS.expire:" + expire + "\n";
        if (null != base64Userbuf) {
            contentToBeSigned += "TLS.userbuf:" + base64Userbuf + "\n";
        }
        try {
            byte[] byteKey = priKeyContent.getBytes("UTF-8");
            Mac hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec keySpec = new SecretKeySpec(byteKey, "HmacSHA256");
            hmac.init(keySpec);
            byte[] byteSig = hmac.doFinal(contentToBeSigned.getBytes("UTF-8"));
            return new String(Base64.encode(byteSig));
        } catch (UnsupportedEncodingException e) {
            return "";
        } catch (NoSuchAlgorithmException e) {
            return "";
        } catch (InvalidKeyException e) {
            return "";
        }
    }

    private static byte[] base64EncodeUrl(byte[] input) {
        byte[] base64 = new String(Base64.encode(input)).getBytes();
        for (int i = 0; i < base64.length; ++i){
            switch (base64[i]) {
                case '+':
                    base64[i] = '*';
                    break;
                case '/':
                    base64[i] = '-';
                    break;
                case '=':
                    base64[i] = '_';
                    break;
                default:
                    break;
            }
        }
        return base64;
    }
}
