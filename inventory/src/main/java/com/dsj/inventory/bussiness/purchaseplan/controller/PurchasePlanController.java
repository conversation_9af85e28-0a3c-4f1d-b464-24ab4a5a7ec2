package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryPurchasePlanParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdatePurchasePlanParam;
import com.dsj.inventory.bussiness.inventory.service.PurchasePlanService;
import com.dsj.inventory.bussiness.inventory.vo.PurchasePlanDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchasePlanExportVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchasePlanVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.EasyExcelUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 进销存采购计划单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@RestController
@Api(value = "PurchasePlanController", tags = "进销存采购计划单api")
public class PurchasePlanController {

    @Autowired
    private PurchasePlanService purchasePlanService;

    /*新增采购计划*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-1"})
    @PostMapping("/purchase-plan")
    @ApiOperation(value = "新增采购计划", notes = "新增采购计划")
    public ResponseEntity<PurchasePlanDetailVo> savePurchasePlan(@RequestBody SaveOrUpdatePurchasePlanParam param){
        PurchasePlanDetailVo purchasePlanVo = purchasePlanService.saveOrUpdatePurchasePlan(param);
        return Res.success(purchasePlanVo);
    }

    /*编辑采购计划*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-1"})
    @PutMapping("/purchase-plan/{id}")
    @ApiOperation(value = "编辑采购计划", notes = "编辑采购计划")
    public ResponseEntity<PurchasePlanDetailVo> updatePurchasePlan(@PathVariable Long id, @RequestBody SaveOrUpdatePurchasePlanParam param){
        param.setId(id);
        PurchasePlanDetailVo purchasePlanVo = purchasePlanService.saveOrUpdatePurchasePlan(param);
        return Res.success(purchasePlanVo);
    }

    /*删除采购计划*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-1"})
    @DeleteMapping("/purchase-plan/{id}")
    @ApiOperation(value = "删除采购计划", notes = "删除采购计划")
    public ResponseEntity<Void> deletePurchasePlan(@PathVariable Long id){
        purchasePlanService.deletePurchasePlan(id);
        return Res.success();
    }

    /*获取采购计划列表*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-1"})
    @GetMapping("/purchase-plan")
    @ApiOperation(value = "获取采购计划列表", notes = "获取采购计划列表")
    public ResponseEntity<List<PurchasePlanVo>> queryPurchasePlanList(QueryPurchasePlanParam param){
        Page<PurchasePlanVo> page = purchasePlanService.queryPurchasePlanPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取采购计划详情*/
    @GetMapping("/purchase-plan/{id}")
    @ApiOperation(value = "获取采购计划详情", notes = "获取采购计划详情")
    public ResponseEntity<PurchasePlanDetailVo> queryPurchasePlan(@PathVariable Long id){
        return Res.success(purchasePlanService.getPurchasePlanById(id));
    }

    /*启用采购计划*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-1"})
    @PostMapping("/purchase-plan/{id}/action-start")
    @ApiOperation(value = "启用采购计划", notes = "启用采购计划")
    public ResponseEntity<Void> startPurchasePlan(@PathVariable Long id){
        purchasePlanService.startPurchasePlan(id);
        return Res.success();
    }

    /*采购计划导出*/
    @GetMapping("/purchase-plan/action-export")
    @ApiOperation(value = "导出采购计划", notes = "导出采购计划")
    public ResponseEntity<Void> exportPurchasePlan(HttpServletResponse response, QueryPurchasePlanParam param) throws IOException {
        param.setNeedExport(true);
        List<PurchasePlanExportVo> list = purchasePlanService.queryPurchasePlanList(param);
        EasyExcelUtils.exportOrdinaryExcel(response,list,"采购计划导出列表","采购计划导出列表", PurchasePlanExportVo.class);
        return Res.success();
    }

}
