package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 优惠券领取对象 1所有买家 2已合作买家
 * <AUTHOR>
 */

@Getter
public enum SalesCouponGroupEnum {

    ALL(1, "所有买家"),
    COOPERATE(2, "已合作买家"),
    ;

    private Integer code;

    private String desc;

    SalesCouponGroupEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (SalesCouponGroupEnum a : SalesCouponGroupEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
