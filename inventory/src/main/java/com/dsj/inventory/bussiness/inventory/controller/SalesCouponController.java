package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesCouponDetailParam;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesCouponParam;
import com.dsj.inventory.bussiness.inventory.param.SaveSalesCouponParam;
import com.dsj.inventory.bussiness.inventory.service.SalesCouponService;
import com.dsj.inventory.bussiness.inventory.vo.SalesCouponDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesCouponVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 进销存优惠券 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@RestController
@Api(value = "SalesCouponController", tags = "进销存优惠券api")
public class SalesCouponController {

    @Autowired
    private SalesCouponService salesCouponService;

    /*新增优惠券*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-promotion-coupon","supplier-wholesale-promotion-coupon"})
    @PostMapping("/sales-coupon")
    @ApiOperation(value = "新增优惠券", notes = "新增优惠券")
    public ResponseEntity<SalesCouponVo> saveSalesCoupon(@RequestBody SaveSalesCouponParam param){
        SalesCouponVo salesCouponVo = salesCouponService.saveSalesCoupon(param);
        return Res.success(salesCouponVo);
    }

    /*删除优惠券*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-promotion-coupon","supplier-wholesale-promotion-coupon"})
    @DeleteMapping("/sales-coupon/{id}")
    @ApiOperation(value = "删除优惠券", notes = "删除优惠券")
    public ResponseEntity<Void> deleteSalesCoupon(@PathVariable Long id){
        SaveSalesCouponParam param = SaveSalesCouponParam.builder().isDelete(TrueEnum.TRUE.getCode()).id(id).build();
        salesCouponService.saveSalesCoupon(param);
        return Res.success();
    }

    /*获取优惠券列表*/
    @GetMapping("/sales-coupon")
    @ApiOperation(value = "获取优惠券列表", notes = "获取优惠券列表")
    public ResponseEntity<List<SalesCouponVo>> querySalesCouponList(QuerySalesCouponParam param){
        Page<SalesCouponVo> page = salesCouponService.querySalesCouponPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取优惠券详情*/
    @GetMapping("/sales-coupon/{id}")
    @ApiOperation(value = "获取优惠券详情", notes = "获取优惠券详情")
    public ResponseEntity<SalesCouponVo> querySalesCoupon(@PathVariable Long id){
        return Res.success(salesCouponService.getSalesCouponById(id));
    }

    /*获取优惠券明细列表*/
    @GetMapping("/sales-coupon/{id}/action-detail")
    @ApiOperation(value = "获取优惠券明细列表", notes = "获取优惠券明细列表")
    public ResponseEntity<List<SalesCouponDetailVo>> querySalesCouponDetailList(@PathVariable Long id, QuerySalesCouponDetailParam param){
        param.setCouponId(id);
        Page<SalesCouponDetailVo> page = salesCouponService.querySalesCouponDetailPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*优惠券定时器模拟*/
    @PostMapping("/sales-coupon/action-timer")
    @ApiOperation(value = "优惠券定时器模拟", notes = "优惠券定时器模拟")
    public ResponseEntity<Void> salesCouponTimer(){
        salesCouponService.batchHandleSalesCouponState();
        return Res.success();
    }

}
