package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityTaskMapping;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskMappingVo;

import java.util.List;

/**
 * <p>
 * 销售活动任务关联信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
public interface SalesActivityTaskMappingService extends IService<SalesActivityTaskMapping> {

    /**
     * 获取活动任务关联活动
     * @param taskId
     * @return
     */
    List<SalesActivityTaskMappingVo> queryActivityTaskMappingListByTaskId(Long taskId);

}
