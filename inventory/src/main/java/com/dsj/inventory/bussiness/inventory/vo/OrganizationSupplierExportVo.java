package com.dsj.inventory.bussiness.inventory.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.dsj.inventory.framework.config.easyExcel.LocalDateTimeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.time.LocalDateTime;

/**
 * 供应商组织私域导出
 * <AUTHOR>
 * @date 2023/03/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="OrganizationSupplierExportVo", description="供应商组织私域导出实体")
@ToString(callSuper = true)
@ColumnWidth(25)
@HeadRowHeight(20)
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER)//内容样式
public class OrganizationSupplierExportVo {

    @ApiModelProperty(value = "组织名称")
    @ExcelProperty(value = "组织名称")
    private String organizationName;

    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称")
    private String name;

    @ApiModelProperty(value = "供应商类别")
    @ExcelProperty(value = "供应商类别")
    private String supplierType;

    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编号")
    private String code;

    @ApiModelProperty(value = "统一社会信用代码有效期，长期为空")
    @ExcelProperty(value = "营业执照有效期",converter = LocalDateTimeConverter.class)
    private LocalDateTime creditDate;

    @ApiModelProperty(value = "创建人名称")
    @ExcelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "资质是否过期")
    @ExcelProperty(value = "资质是否过期")
    private String creditState;

    @ApiModelProperty(value = "可用状态 0：禁用 1可用")
    @ExcelProperty(value = "状态")
    private String usableState;

    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "创建时间",converter = LocalDateTimeConverter.class)
    private LocalDateTime createTime;

}
