package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织付款方式配置实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("jxc_payment_config")
public class PaymentConfig extends Entity {

    /**
     * 付款方式ID
     */
    private Long paymentMethodId;

    /**
     * 组织ID
     */
    private Long organizationId;

    /**
     * 配置项键
     */
    private String configKey;

    /**
     * 配置项值
     */
    private String configValue;

    /**
     * 配置项描述
     */
    private String description;

    /**
     * 是否启用
     */
    private Integer enabled;
} 