package com.dsj.inventory.bussiness.quality.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.quality.entity.MedicineFirstBusinessEntity;
import com.dsj.inventory.bussiness.quality.enumeration.ApprovalTypeEnum;
import com.dsj.inventory.bussiness.quality.param.MedicineFirstBusinessApprovalParam;
import com.dsj.inventory.bussiness.quality.param.MedicineFirstBusinessCreateParam;
import com.dsj.inventory.bussiness.quality.param.MedicineFirstBusinessQueryParam;
import com.dsj.inventory.bussiness.quality.service.MedicineFirstBusinessService;
import com.dsj.inventory.bussiness.quality.vo.MedicineFirstBusinessVO;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 药品首营审批表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Api(tags = "药品首营审批")
@RestController
@RequestMapping("/api/quality/medicine-first-business")
public class MedicineFirstBusinessController {

    @Autowired
    private MedicineFirstBusinessService medicineFirstBusinessService;

    @ApiOperation("创建药品首营审批")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-medicine-first"})
    @PostMapping
    public ResponseEntity<Long> createMedicineFirstBusiness(@Valid @RequestBody MedicineFirstBusinessCreateParam param) {
        Long id = medicineFirstBusinessService.createMedicineFirstBusiness(param);
        return Res.success(id);
    }

    @ApiOperation("获取药品首营审批详情")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-medicine-first"})
    @GetMapping("/{id}")
    public ResponseEntity<MedicineFirstBusinessVO> getMedicineFirstBusinessById(@PathVariable Long id) {
        MedicineFirstBusinessVO vo = medicineFirstBusinessService.getMedicineFirstBusinessById(id);
        return Res.success(vo);
    }

    @ApiOperation("分页查询药品首营审批")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-medicine-first"})
    @GetMapping("/page")
    public ResponseEntity<?> queryMedicineFirstBusinessPage(MedicineFirstBusinessQueryParam param) {
        Page<MedicineFirstBusinessVO> page = medicineFirstBusinessService.queryMedicineFirstBusinessPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())) {
            return Res.successPage(page);
        } else {
            return Res.success(page.getRecords());
        }
    }

    @ApiOperation("审批药品（首营/修改）")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-medicine-first-approve"})
    @PostMapping("/approve")
    public ResponseEntity<Long> approveMedicineFirstBusiness(@Valid @RequestBody MedicineFirstBusinessApprovalParam param) {
        MedicineFirstBusinessEntity entity = medicineFirstBusinessService.getById(param.getId());
        if (entity == null) {
            return Res.fail("审批记录不存在");
        }
        Long id;
        if (ApprovalTypeEnum.MODIFICATION.getCode().equals(entity.getApprovalType())) {
            id = medicineFirstBusinessService.approveMedicineModification(param);
        } else {
            id = medicineFirstBusinessService.approveMedicineFirstBusiness(param);
        }
        return Res.success(id);
    }

    @ApiOperation("撤回药品首营审批")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-medicine-first"})
    @PostMapping("/withdraw/{id}")
    public ResponseEntity<Long> withdrawMedicineFirstBusiness(@PathVariable Long id) {
        Long resultId = medicineFirstBusinessService.withdrawMedicineFirstBusiness(id);
        return Res.success(resultId);
    }

    @ApiOperation("获取药品首营审批最新状态")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-medicine-first"})
    @GetMapping("/latest")
    public ResponseEntity<MedicineFirstBusinessVO> getLatestMedicineFirstBusiness(
            @ApiParam(value = "药品私域ID", required = true) @RequestParam Long orgMedicineMappingId,
            @ApiParam(value = "供应商ID", required = true) @RequestParam Long supplierId) {
        MedicineFirstBusinessVO vo = medicineFirstBusinessService.getLatestMedicineFirstBusiness(orgMedicineMappingId, supplierId);
        return Res.success(vo);
    }

    @ApiOperation("获取药品首营审批历史记录")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-medicine-first"})
    @GetMapping("/history")
    public ResponseEntity<List<MedicineFirstBusinessVO>> getMedicineFirstBusinessHistory(
            @ApiParam(value = "药品私域ID", required = true) @RequestParam Long orgMedicineMappingId,
            @ApiParam(value = "供应商ID", required = true) @RequestParam Long supplierId) {
        List<MedicineFirstBusinessVO> history = medicineFirstBusinessService.getMedicineFirstBusinessHistory(orgMedicineMappingId, supplierId);
        return Res.success(history);
    }

    @ApiOperation("查询待我审批的药品首营记录")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-medicine-first"})
    @GetMapping("/pending")
    public ResponseEntity<?> queryPendingApprovalPage(MedicineFirstBusinessQueryParam param) {
        Page<MedicineFirstBusinessVO> page = medicineFirstBusinessService.queryPendingApprovalPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())) {
            return Res.successPage(page);
        } else {
            return Res.success(page.getRecords());
        }
    }

    @ApiOperation("检查药品首营状态")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-medicine-first"})
    @GetMapping("/check")
    public ResponseEntity<Boolean> checkMedicineFirstBusinessStatus(
            @ApiParam(value = "药品私域ID", required = true) @RequestParam Long orgMedicineMappingId,
            @ApiParam(value = "供应商ID", required = true) @RequestParam Long supplierId) {
        boolean passed = medicineFirstBusinessService.checkMedicineFirstBusinessStatus(orgMedicineMappingId, supplierId);
        return Res.success(passed);
    }
} 