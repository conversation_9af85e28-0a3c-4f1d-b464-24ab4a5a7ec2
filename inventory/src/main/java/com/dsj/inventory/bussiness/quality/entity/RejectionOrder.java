package com.dsj.inventory.bussiness.quality.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 拒收单主表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("jxc_rejection_order")
public class RejectionOrder extends Entity {

    /**
     * 拒收单号
     */
    private String rejectionCode;

    /**
     * 组织ID
     */
    private Long organizationId;

    /**
     * 来源单据ID
     */
    private Long sourceOrderId;

    /**
     * 来源单据号
     */
    private String sourceOrderCode;

    /**
     * 来源单据类型
     */
    private Integer sourceOrderType;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 单据状态
     */
    private Integer status;

    /**
     * 拒收总数量
     */
    private Integer totalRejectionQuantity;

    /**
     * 审核人ID
     */
    private Long auditUserId;

    /**
     * 审核人姓名
     */
    private String auditUserName;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核意见
     */
    private String auditRemark;

    /**
     * 备注
     */
    private String remark;
}