package com.dsj.inventory.feign.framework;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

public class FeignBaseUrlConfig {
    @Value("${feign.base-url}")
    private String baseUrl;
    
    @Bean
    public feign.Request.Options options() {
        return new feign.Request.Options(5000, 10000);
    }
    
    @Bean
    public feign.Contract feignContract() {
        return new feign.Contract.Default();
    }
    
    @Bean
    public feign.Client feignClient() {
        return new feign.Client.Default(null, null);
    }
    
    @Bean
    public feign.Target.HardCodedTarget<Object> target() {
        return new feign.Target.HardCodedTarget<>(Object.class, baseUrl);
    }
}
