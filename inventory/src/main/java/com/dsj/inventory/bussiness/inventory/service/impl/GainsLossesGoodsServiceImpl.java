package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.GainsLossesGoods;
import com.dsj.inventory.bussiness.inventory.mapper.GainsLossesGoodsMapper;
import com.dsj.inventory.bussiness.inventory.service.GainsLossesGoodsService;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesGoodsVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 进销存损溢单商品明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Service
public class GainsLossesGoodsServiceImpl extends ServiceImpl<GainsLossesGoodsMapper, GainsLossesGoods> implements GainsLossesGoodsService {

    @Override
    public List<GainsLossesGoodsVo> getGainsLossesGoodById(Long id) {
        return this.baseMapper.getGainsLossesGoodById(id);
    }
}
