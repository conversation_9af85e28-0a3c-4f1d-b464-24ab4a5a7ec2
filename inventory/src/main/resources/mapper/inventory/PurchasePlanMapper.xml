<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.purchaseplan.mapper.PurchasePlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.purchaseplan.entity.PurchasePlan">
    <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="code" property="code" />
        <result column="bill_date" property="billDate" />
        <result column="expect_receive_date" property="expectReceiveDate" />
        <result column="remark" property="remark" />
        <result column="deal_state" property="dealState" />
        <result column="organization_supplier_id" property="organizationSupplierId" />
        <result column="total_price" property="totalPrice" />
        <result column="goods_types" property="goodsTypes" />
        <result column="purchase_content" property="purchaseContent" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        organization_id, code, bill_date, expect_receive_date, remark, deal_state, auto_generated, gsp_check_status, check_message, organization_supplier_id, total_price, goods_types, purchase_content, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="queryPurchasePlanPage" resultType="com.dsj.inventory.bussiness.purchaseplan.vo.PurchasePlanVo">
        SELECT distinct jpp.id
        ,jpp.organization_id organizationId
        ,jpp.code
        ,DATE_FORMAT(jpp.bill_date,'%Y-%m-%d') billDate
        ,DATE_FORMAT(jpp.expect_receive_date,'%Y-%m-%d') expectReceiveDate
        ,jpp.remark
        ,jpp.deal_state dealState
        ,jpp.organization_supplier_id organizationSupplierId
        ,jos.name organizationSupplierName
        ,jos.code organizationSupplierCode
        ,jpp.total_price totalPrice
        ,jpp.goods_types goodsTypes
        ,jpp.purchase_content purchaseContent
        ,jpp.create_user createUser
        ,su.name createUserName
        ,jpp.create_time createTime
        FROM jxc_purchase_plan jpp
        LEFT JOIN sys_user su ON su.id = jpp.create_user AND su.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jpp.organization_supplier_id AND jos.is_delete = 0
        WHERE
        jpp.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jpp.organization_id = #{param.organizationId}
        </if>
        <if test="param.dealState != null ">
            AND jpp.deal_state = #{param.dealState}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jpp.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.createUserNameLike != null and param.createUserNameLike != '' ">
            AND su.name like CONCAT('%',#{param.createUserNameLike},'%')
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jpp.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        ORDER BY jpp.create_time desc
    </select>

    <select id="getPurchasePlanById" resultType="com.dsj.inventory.bussiness.purchaseplan.vo.PurchasePlanDetailVo">
        SELECT distinct jpp.id
             ,jpp.organization_id organizationId
             ,jpp.code
             ,jpp.bill_date billDate
             ,jpp.expect_receive_date expectReceiveDate
             ,jpp.remark
             ,jpp.deal_state dealState
             ,jpp.auto_generated autoGenerated
             ,jpp.gsp_check_status gspCheckStatus
             ,jpp.check_message checkMessage
             ,jpp.organization_supplier_id organizationSupplierId
             ,jos.name organizationSupplierName
             ,jos.code organizationSupplierCode
             ,jpp.total_price totalPrice
             ,jpp.goods_types goodsTypes
             ,jpp.purchase_content purchaseContent
             ,jpp.create_user createUser
             ,su.name createUserName
             ,jpp.create_time createTime
        FROM jxc_purchase_plan jpp
       LEFT JOIN sys_user su ON su.id = jpp.create_user AND su.is_delete = 0
       LEFT JOIN jxc_organization_supplier jos ON jos.id = jpp.organization_supplier_id AND jos.is_delete = 0
        WHERE
            jpp.is_delete = 0
        AND jpp.id = #{id}
    </select>

    <select id="queryPurchasePlanList" resultType="com.dsj.inventory.bussiness.purchaseplan.vo.PurchasePlanExportVo">
        SELECT distinct jpp.id
            ,jpp.code
            ,jpp.remark
            ,(CASE
            WHEN jpp.deal_state = 0 THEN '待执行'
            WHEN jpp.deal_state = 1 THEN '已执行'
            END) dealState
            ,ROUND(jpp.total_price/100,2) totalPrice
            ,jpp.goods_types goodsTypes
            ,jpp.purchase_content purchaseContent
            ,jos.name organizationSupplierName
            ,jos.code organizationSupplierCode
            ,su.name createUserName
            ,jpp.create_time createTime
        FROM jxc_purchase_plan jpp
        LEFT JOIN sys_user su ON su.id = jpp.create_user AND su.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jpp.organization_supplier_id AND jos.is_delete = 0
        WHERE
            jpp.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jpp.organization_id = #{param.organizationId}
        </if>
        <if test="param.dealState != null ">
            AND jpp.deal_state = #{param.dealState}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jpp.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.createUserNameLike != null and param.createUserNameLike != '' ">
            AND su.name like CONCAT('%',#{param.createUserNameLike},'%')
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jpp.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        ORDER BY jpp.create_time desc
    </select>

    <update id="deletePurchasePlanByOrganizationId">
        UPDATE jxc_purchase_plan jpp, jxc_purchase_plan_goods jppg
        SET jpp.is_delete = 1, jppg.is_delete = 1
        WHERE jpp.id = jppg.plan_id
        AND jpp.is_delete = 0
        AND jppg.is_delete = 0
        AND jpp.organization_id = #{organizationId}
    </update>

    <select id="getPurchasePlanCount" resultType="java.lang.Integer">
        SELECT COUNT(1) number
        FROM jxc_purchase_plan jpp
        WHERE
        DATE_FORMAT(jpp.create_time,'%Y-%m-%d') = #{day}
        <if test="organizationId != null and organizationId != '' ">
            AND jpp.organization_id = #{organizationId}
        </if>
    </select>

</mapper>
