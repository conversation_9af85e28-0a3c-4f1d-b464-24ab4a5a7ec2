package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.InventoryChangeLog;
import com.dsj.inventory.bussiness.inventory.vo.CheckGoodsVo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 进销存库存变动记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
public interface InventoryChangeLogService extends IService<InventoryChangeLog> {

    /**
     * 获取固定时间盘点商品明细
     * @param organizationId
     * @param startTime
     * @param endTime
     * @return
     */
    List<CheckGoodsVo> getDayCheckGoods(Long organizationId, LocalDateTime startTime, LocalDateTime endTime);

}
