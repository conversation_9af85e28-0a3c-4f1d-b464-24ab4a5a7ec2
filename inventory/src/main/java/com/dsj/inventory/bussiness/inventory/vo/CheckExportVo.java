package com.dsj.inventory.bussiness.inventory.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.dsj.inventory.framework.config.easyExcel.LocalDateTimeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.time.LocalDateTime;

/**
 * 盘点计划导出
 * <AUTHOR>
 * @date 2023/04/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="CheckExportVo", description="盘点计划导出实体")
@ToString(callSuper = true)
@ColumnWidth(25)
@HeadRowHeight(20)
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER)//内容样式
public class CheckExportVo {

    @ApiModelProperty(value = "盘点计划号")
    @ExcelProperty(value = "盘点计划单号")
    private String code;

    @ApiModelProperty(value = "盘点计划名称")
    @ExcelProperty(value = "计划名称")
    private String name;

    @ApiModelProperty(value = "盘点状态：0暂存 1盘点中 2已完成")
    @ExcelProperty(value = "盘点状态")
    private String checkState;

    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "盘点货位名称")
    @ExcelProperty(value = "盘点货位")
    private String shelfNames;

    @ApiModelProperty(value = "创建人名称")
    @ExcelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "盘点人名称")
    @ExcelProperty(value = "盘点人")
    private String checkNames;

    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "创建日期",converter = LocalDateTimeConverter.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "盘点时间")
    @ExcelProperty(value = "盘点日期",converter = LocalDateTimeConverter.class)
    private LocalDateTime checkDate;

}
