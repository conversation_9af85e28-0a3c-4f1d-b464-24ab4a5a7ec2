---
description: 
globs: **/mapper/**.java
alwaysApply: false
---
# Mapper层开发规范

## 1. 核心职责与基本约定
- **职责**: Mapper层是唯一的数据访问层，负责执行数据库的CRUD（创建、读取、更新、删除）操作。**严禁在Mapper中包含任何业务逻辑**。
- **命名**: Mapper接口应以 `Mapper` 结尾，例如 `UserMapper`。
- **注解**: 必须使用 `@Mapper` 注解将其标识为MyBatis的Mapper接口。
- **继承**: 必须继承 `com.baomidou.mybatisplus.core.mapper.BaseMapper<T>`，其中 `T` 是对应的实体类（Entity）。如果需要使用 `MyBatis-Plus-Join`，则应继承 `com.github.yulichang.base.MPJBaseMapper<T>`。

## 2. 查询实现优先级

为了保持代码的统一性、可读性和可维护性，请严格遵循以下技术选型优先级：

### 第一优先级：MyBatis-Plus Wrappers (通过 `default` 方法封装)

对于单表查询和简单的多表更新，应优先使用MyBatis-Plus提供的 `Wrapper` 类。

- **单表查询**: 使用 `LambdaQueryWrapper`。
- **更新操作**: 使用 `LambdaUpdateWrapper`。
- **封装**: **所有 `Wrapper` 的构建和使用逻辑都必须封装在Mapper接口的 `default` 方法中**。这可以避免 `Wrapper` 逻辑泄露到Service层，保持Service层的整洁。

**示例：使用 `LambdaQueryWrapper` 的 `default` 方法**
```java
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UserMapper extends BaseMapper<UserEntity> {

    /**
     * 根据用户名模糊查询用户
     * @param username 用户名
     * @return 用户实体
     */
    default UserEntity findByUsername(String username) {
        LambdaQueryWrapper<UserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserEntity::getUsername, username);
        return this.selectOne(wrapper);
    }
}
```

### 第二优先级：MyBatis-Plus-Join (多表联合查询)

对于多表联合查询，必须使用 `MyBatis-Plus-Join` 扩展库。

- **查询Wrapper**: 使用 `com.github.yulichang.wrapper.MPJLambdaWrapper`。
- **封装**: 与标准 `Wrapper` 一样，**`MPJLambdaWrapper` 的使用也必须封装在Mapper接口的 `default` 方法中**。
- **返回类型**: 查询结果应映射到一个专门的VO（视图对象）类。

**示例：使用 `MPJLambdaWrapper` 进行多表查询**
```java
import com.github.yulichang.base.MPJBaseMapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OrderMapper extends MPJBaseMapper<OrderEntity> { // 注意：需要继承 MPJBaseMapper

    /**
     * 根据订单ID查询订单及其用户信息
     * @param orderId 订单ID
     * @return 包含订单和用户信息的VO
     */
    default OrderWithUserVO findOrderWithUser(Long orderId) {
        MPJLambdaWrapper<OrderEntity> wrapper = new MPJLambdaWrapper<OrderEntity>()
            .selectAll(OrderEntity.class) // 查询OrderEntity所有字段
            .select(UserEntity::getNickname, UserEntity::getAvatar) // 查询UserEntity的指定字段
            .leftJoin(UserEntity.class, UserEntity::getId, OrderEntity::getUserId) // 关联用户表
            .eq(OrderEntity::getId, orderId);
            
        return this.selectJoinOne(OrderWithUserVO.class, wrapper);
    }
}
```

### 第三优先级：XML文件

当 `Wrapper` 和 `MyBatis-Plus-Join` 无法满足复杂的SQL查询需求时（例如，需要使用数据库特定函数、执行复杂子查询或编写包含高级逻辑的动态SQL），才应使用XML文件进行实现。

- **方法定义**: 在Mapper接口中定义方法，无需 `default` 实现。
- **XML实现**: 在 `resources/mapper/` 目录下的对应XML文件中编写SQL语句。
- **命名空间**: XML文件的命名空间（namespace）必须指向对应的Mapper接口。

**示例：XML实现**

*Mapper接口:*
```java
public interface ProductMapper extends BaseMapper<ProductEntity> {
    
    /**
     * 获取指定分类下最受欢迎的N个产品
     * @param categoryId 分类ID
     * @param limit 数量
     * @return 产品列表
     */
    List<ProductVO> findTopPopularProducts(@Param("categoryId") Long categoryId, @Param("limit") int limit);
}
```

*ProductMapper.xml:*
```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.product.mapper.ProductMapper">

    <select id="findTopPopularProducts" resultType="com.dsj.inventory.bussiness.product.vo.ProductVO">
        SELECT 
            p.id, 
            p.name, 
            p.price,
            (SELECT COUNT(*) FROM order_items oi WHERE oi.product_id = p.id) as sales_count
        FROM 
            product p
        WHERE 
            p.category_id = #{categoryId}
        ORDER BY 
            sales_count DESC
        LIMIT #{limit}
    </select>

</mapper>
```

## 3. 总结
1.  **首选 `default` 方法 + Wrapper**：尽可能在Mapper接口内部通过 `default` 方法消化查询逻辑。
2.  **多表连接用 `MyBatis-Plus-Join`**：不要在代码中手动进行多次查询再组合，优先使用Join查询。
3.  **XML是最后选择**：仅用于 `Wrapper` 无法表达的复杂SQL场景。
4.  **禁止业务逻辑**：Mapper只做数据映射和访问，不做任何业务判断。

