package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.CashierWorking;
import com.dsj.inventory.bussiness.inventory.vo.CashierWorkingVo;

/**
 * <p>
 * 进销存收银员值班记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface CashierWorkingMapper extends BaseMapper<CashierWorking> {

    /**
     * 获取存收银员值班记录详情
     * @param id
     * @return
     */
    CashierWorkingVo getCashierWorkingById(Long id);

}
