package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 销售单商品明细
 * <AUTHOR>
 * @date 2023/04/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateSalesGoodsParam", description="新增or编辑 销售单商品明细参数")
public class SaveOrUpdateSalesGoodsParam {

    @ApiModelProperty(hidden = true,value = "销售单Id")
    private Long orderId;

    @ApiModelProperty(value = "销售员Id")
    private Long saleId;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(hidden = true,value = "通用名")
    private String goodsName;

    @ApiModelProperty(hidden = true,value = "商品名称")
    private String generalName;

    @ApiModelProperty(hidden = true,value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(hidden = true,value = "包装单位")
    private String packUnit;

    @ApiModelProperty(hidden = true,value = "规格型号")
    private String specification;

    @ApiModelProperty(hidden = true,value = "剂型")
    private String drugType;

    @ApiModelProperty(hidden = true,value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(hidden = true,value = "用法用量")
    private String usageDosage;

    @ApiModelProperty(hidden = true,value = "条形码")
    private String barCode;

    @ApiModelProperty(hidden = true,value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(hidden = true,value = "生产日期")
    private LocalDateTime produceDate;

    @ApiModelProperty(hidden = true,value = "有效期至")
    private LocalDateTime userDate;

    @ApiModelProperty(value = "批号")
    private String batchNumber;

    @ApiModelProperty(value = "货位Id")
    private Long shelfId;

    @ApiModelProperty(value = "销售数量")
    private Double saleAmount;

    @ApiModelProperty(value = "赠送数量")
    private Double giveAmount = 0.00;

    @ApiModelProperty(value = "单价")
    private Integer price;

    @ApiModelProperty(value = "折后单价")
    private Integer discountPrice;

    @ApiModelProperty(value = "优惠")
    private Integer preferentialPrice;

    @ApiModelProperty(value = "总计（销售数量*折后单价）")
    private Integer totalPrice;

    @ApiModelProperty(hidden = true,value = "零售价（私域商品价格）")
    private Integer salePrice;

    @ApiModelProperty(hidden = true,value = "会员价")
    private Integer memberPrice;

    @ApiModelProperty(value = "单品活动Id")
    private Long singleActivityId;

    @ApiModelProperty(value = "会员活动Id")
    private Long memberActivityId;

    @ApiModelProperty(hidden = true,value = "特价商品 0否1是")
    private Integer specialOffer;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
