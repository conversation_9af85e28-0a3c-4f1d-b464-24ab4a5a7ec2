package com.dsj.inventory.bussiness.inventory.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.dsj.inventory.framework.config.easyExcel.LocalDateTimeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.time.LocalDateTime;

/**
 * 采购计划导出
 * <AUTHOR>
 * @date 2023/04/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PurchasePlanExportVo", description="采购计划导出实体")
@ToString(callSuper = true)
@ColumnWidth(25)
@HeadRowHeight(20)
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER)//内容样式
public class PurchasePlanExportVo {

    @ApiModelProperty(value = "计划单号")
    @ExcelProperty(value = "计划单号")
    private String code;

    @ApiModelProperty(value = "处理状态 0待执行 1已执行")
    @ExcelProperty(value = "处理状态")
    private String dealState;

    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称")
    private String organizationSupplierName;

    @ApiModelProperty(value = "供应商编号")
    @ExcelProperty(value = "供应商编号")
    private String organizationSupplierCode;

    @ApiModelProperty(value = "创建人名称")
    @ExcelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "商品种类")
    @ExcelProperty(value = "商品种类")
    private String goodsTypes;

    @ApiModelProperty(value = "采购内容")
    @ExcelProperty(value = "采购内容")
    private String purchaseContent;

    @ApiModelProperty(value = "总金额")
    @ExcelProperty(value = "金额总计")
    private String totalPrice;

    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "创建时间",converter = LocalDateTimeConverter.class)
    private LocalDateTime createTime;

}
