package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.PurchasePlan;
import com.dsj.inventory.bussiness.inventory.param.QueryPurchasePlanParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdatePurchasePlanParam;
import com.dsj.inventory.bussiness.inventory.vo.PurchasePlanDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchasePlanExportVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchasePlanVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存采购计划单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
public interface PurchasePlanService extends IService<PurchasePlan> {

    /**
     * 新增、编辑、采购计划
     * @param param
     * @return
     */
    PurchasePlanDetailVo saveOrUpdatePurchasePlan(SaveOrUpdatePurchasePlanParam param);

    /**
     * 删除 采购计划
     * @param id
     */
    void deletePurchasePlan(Long id);

    /**
     * 获取采购计划分页列表
     * @param param
     * @return
     */
    Page<PurchasePlanVo> queryPurchasePlanPage(QueryPurchasePlanParam param);

    /**
     * 获取采购计划列表详情
     * @param id
     * @return
     */
    PurchasePlanDetailVo getPurchasePlanById(Long id);

    /**
     * 启用采购计划
     * @param id
     */
    void startPurchasePlan(Long id);

    /**
     * 导出采购计划列表
     * @param param
     * @return
     */
    List<PurchasePlanExportVo> queryPurchasePlanList(QueryPurchasePlanParam param);

    /**
     * 删除组织的采购计划
     * @param organizationId
     */
    void deletePurchasePlanByOrganizationId(Long organizationId);

    /**
     * 统计组织当天采购计划数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getPurchasePlanCount(@Param("organizationId") Long organizationId, @Param("day") String day);

    /**
     * 更新采购计划状态为已执行
     * @param planId 采购计划ID
     */
    void updateStateToExecuted(Long planId);

}
