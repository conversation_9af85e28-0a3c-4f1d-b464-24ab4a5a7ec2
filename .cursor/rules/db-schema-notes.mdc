---
description: 
globs: 
alwaysApply: true
---
# 数据库表通用字段说明
始终用中文和我交流。
所有（或绝大多数）数据库表都包含以下通用审计字段和逻辑删除字段：
-   `id` bigint NOT NULL COMMENT '主键',
-   `create_time` DATETIME DEFAULT NULL COMMENT '创建时间': 记录该条数据的创建时间。
-   `create_user` BIGINT DEFAULT NULL COMMENT '创建人ID': 记录创建该条数据的用户ID。
-   `update_time` DATETIME DEFAULT NULL COMMENT '操作时间': 记录该条数据最后一次的修改时间。
-   `update_user` BIGINT DEFAULT NULL COMMENT '操作人ID': 记录最后一次修改该条数据的用户ID。
-   `is_delete` TINYINT DEFAULT '0' COMMENT '删除标志,{0:正常, 1:已删除}': 用于逻辑删除。0表示数据有效，1表示数据已被逻辑删除。