<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.ShelfMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.Shelf">
    <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="name" property="name" />
        <result column="parent_id" property="parentId" />
        <result column="level_ids" property="levelIds" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        organization_id, name, parent_id, level_ids, type, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="queryShelfPage" resultType="com.dsj.inventory.bussiness.inventory.vo.ShelfVo">
        SELECT distinct js.id
        ,js.organization_id organizationId
        ,js.name
        ,js.parent_id parentId
        ,js.level_ids levelIds
        ,js.type
        ,js.level
        ,js.delete_flag deleteFlag
        ,ifnull(COUNT(distinct ji.organization_medicine_id),0) goodsNumber
        ,js.create_user createUser
        ,su.name createUserName
        ,js.create_time createTime
        FROM jxc_shelf js
        LEFT JOIN sys_user su ON su.id = js.create_user AND su.is_delete = 0
        LEFT JOIN jxc_inventory ji ON ji.shelf_id = js.id AND ji.is_delete = 0
        WHERE
        js.is_delete = 0
        <if test="param.type != null ">
            AND js.type = #{param.type}
        </if>
        <if test="param.level != null ">
            AND js.level = #{param.level}
        </if>
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND js.organization_id = #{param.organizationId}
        </if>
        <if test="param.parentId != null and param.parentId != '' ">
            AND js.parent_id = #{param.parentId}
        </if>
        <if test="param.nameLike != null and param.nameLike != '' ">
            AND js.name like CONCAT('%',#{param.nameLike},'%')
        </if>
        GROUP BY js.id
        ORDER BY js.create_time
    </select>

    <select id="queryInitializationOrganization" resultType="com.pocky.transport.bussiness.diagnose.vo.OrganizationVo">
        SELECT distinct do.id,
                        do.name
        FROM di_organization do
        WHERE
            do.is_delete = 0
          AND do.type = 3
          AND NOT EXISTS
            (SELECT distinct js.organization_id FROM jxc_shelf js
            WHERE do.id = js.organization_id
            AND js.is_delete = 0
            AND js.type = 1)
    </select>

</mapper>
