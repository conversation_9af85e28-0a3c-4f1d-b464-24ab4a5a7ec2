# 采购计划功能增强实施方案

## 📋 1. 开发规划

### 🎯 开发优先级规划

#### 第一阶段：基础功能完善 (3-4天)
1. **参数配置管理** - 基于OrganizationDict实现采购计划参数管理
2. **数据模型扩展** - 扩展必要字段支持新功能  
3. **进货单价策略服务** - 实现三种价格生成策略

#### 第二阶段：核心业务功能 (4-5天)
1. **自动生成采购计划** - 实现基于库存规则的自动生成
2. **供应商选择策略** - 智能推荐供应商逻辑
3. **库存相关计算** - 在途数量、日均销量计算

#### 第三阶段：校验和导入功能 (3-4天)
1. **GSP合规校验** - 实现证照和经营范围校验
2. **Excel导入功能** - 支持批量导入采购计划
3. **API接口完善** - 扩展控制器接口

#### 第四阶段：测试优化 (2-3天)
1. **单元测试编写**
2. **集成测试验证**
3. **性能调优**

**总计：12-16天**

---

## 📊 2. 现状分析

### ✅ 已实现功能
- 基础CRUD操作（新增、编辑、删除、查询）
- 单号自动生成（JH格式）
- 供应商拆分逻辑
- 状态管理（待执行/已执行）
- 权限控制
- Excel导出

### 🆕 需要新增功能
1. **自动生成采购计划**
2. **进货单价生成策略**
3. **GSP合规校验**
4. **Excel/文本导入**
5. **在途数量统计**
6. **增强查询功能**

### 📋 现有数据库表结构分析

#### jxc_purchase_plan 表字段：
- ✅ 基础字段完整（id, organization_id, code, bill_date等）
- ✅ 业务字段基本满足（deal_state, organization_supplier_id, total_price等）

#### jxc_purchase_plan_goods 表字段：
- ✅ 商品基本信息完整
- ✅ 价格和数量字段齐全
- ❌ **缺少新功能需要的关键字段**

---

## 🛠️ 3. 参数配置设计

### 3.1 参数配置基于OrganizationDict实现

```java
// PurchasePlanParamConstants.java - 参数常量定义
public class PurchasePlanParamConstants {
    
    // 字典类型
    public static final String DICT_TYPE_PURCHASE_PLAN = "PURCHASE_PLAN_CONFIG";
    
    // 进货单价策略参数
    public static final String PRICE_STRATEGY = "PRICE_STRATEGY";
    public static final String PRICE_STRATEGY_DESC = "进货单价生成策略：1-最低进价，2-最新供应商，3-上次进价";
    public static final String PRICE_STRATEGY_DEFAULT = "3";
    
    // 最低进价取样天数
    public static final String LOWEST_PRICE_DAYS = "LOWEST_PRICE_DAYS";
    public static final String LOWEST_PRICE_DAYS_DESC = "最低进价取样天数";
    public static final String LOWEST_PRICE_DAYS_DEFAULT = "180";
    
    // 在途数量统计天数
    public static final String IN_TRANSIT_DAYS = "IN_TRANSIT_DAYS";
    public static final String IN_TRANSIT_DAYS_DESC = "在途数量统计天数";
    public static final String IN_TRANSIT_DAYS_DEFAULT = "30";
    
    // 日均销量统计天数
    public static final String SALES_STAT_DAYS = "SALES_STAT_DAYS";
    public static final String SALES_STAT_DAYS_DESC = "日均销量统计天数";
    public static final String SALES_STAT_DAYS_DEFAULT = "30";
    
    // 库存上限天数
    public static final String UPPER_LIMIT_DAYS = "UPPER_LIMIT_DAYS";
    public static final String UPPER_LIMIT_DAYS_DESC = "库存上限天数";
    public static final String UPPER_LIMIT_DAYS_DEFAULT = "7";
    
    // 库存下限天数
    public static final String LOWER_LIMIT_DAYS = "LOWER_LIMIT_DAYS";
    public static final String LOWER_LIMIT_DAYS_DESC = "库存下限天数";
    public static final String LOWER_LIMIT_DAYS_DEFAULT = "3";
    
    // GSP校验模式
    public static final String GSP_CHECK_MODE = "GSP_CHECK_MODE";
    public static final String GSP_CHECK_MODE_DESC = "GSP校验模式：1-拦截，0-不拦截不提示，-1-提示不拦截";
    public static final String GSP_CHECK_MODE_DEFAULT = "1";
}
```

### 3.2 数据库扩展脚本

```sql
-- 扩展采购计划表字段
ALTER TABLE jxc_purchase_plan 
ADD COLUMN auto_generated TINYINT DEFAULT 0 COMMENT '是否自动生成：0-手动，1-自动' AFTER deal_state,
ADD COLUMN gsp_check_status TINYINT DEFAULT 0 COMMENT 'GSP校验状态：0-未校验，1-通过，-1-不通过' AFTER auto_generated,
ADD COLUMN check_message VARCHAR(500) DEFAULT NULL COMMENT 'GSP校验信息' AFTER gsp_check_status;

-- 扩展采购计划商品明细表字段
ALTER TABLE jxc_purchase_plan_goods 
ADD COLUMN organization_supplier_id BIGINT DEFAULT NULL COMMENT '供应商ID' AFTER organization_medicine_id,
ADD COLUMN in_transit_quantity DOUBLE DEFAULT 0 COMMENT '在途数量' AFTER repertory,
ADD COLUMN daily_avg_sales DOUBLE DEFAULT 0 COMMENT '日均销量' AFTER in_transit_quantity,
ADD COLUMN stock_upper_limit DOUBLE DEFAULT 0 COMMENT '库存上限' AFTER daily_avg_sales,
ADD COLUMN stock_lower_limit DOUBLE DEFAULT 0 COMMENT '库存下限' AFTER stock_upper_limit,
ADD COLUMN recommend_quantity DOUBLE DEFAULT 0 COMMENT '推荐采购数量' AFTER stock_lower_limit,
ADD COLUMN price_strategy TINYINT DEFAULT 3 COMMENT '价格策略：1-最低价，2-最新供应商，3-上次进价' AFTER recommend_quantity;

-- 添加索引优化查询性能
CREATE INDEX idx_purchase_plan_org_date ON jxc_purchase_plan(organization_id, bill_date);
CREATE INDEX idx_purchase_plan_goods_medicine ON jxc_purchase_plan_goods(organization_medicine_id, organization_supplier_id);
CREATE INDEX idx_purchase_plan_auto_generated ON jxc_purchase_plan(auto_generated, deal_state);
```

---

## 🔧 4. 核心业务服务接口设计

### 4.1 参数管理服务

```java
/**
 * 采购计划参数管理服务
 */
@Service
public interface PurchasePlanParameterService {
    
    /**
     * 获取参数值（整型）
     */
    Integer getIntParameter(Long organizationId, String paramCode);
    
    /**
     * 获取参数值（字符串）
     */
    String getStringParameter(Long organizationId, String paramCode);
    
    /**
     * 初始化组织采购计划参数
     */
    void initDefaultParameters(Long organizationId);
    
    /**
     * 更新参数值
     */
    void updateParameter(Long organizationId, String paramCode, String paramValue);
}
```

### 4.2 进货单价策略服务

```java
/**
 * 进货单价策略服务
 */
@Service
public interface PurchasePriceStrategyService {
    
    /**
     * 根据策略获取进货单价
     * @param organizationMedicineId 私域商品ID
     * @param organizationSupplierId 供应商ID（可为空）
     * @param organizationId 组织ID
     * @return 进货单价（分）
     */
    Integer getPurchasePrice(Long organizationMedicineId, Long organizationSupplierId, Long organizationId);
    
    /**
     * 按最低进价策略获取价格
     */
    Integer getPriceByLowest(Long organizationMedicineId, Integer days, Long organizationId);
    
    /**
     * 按最新供应商策略获取价格
     */
    Integer getPriceByLatestSupplier(Long organizationMedicineId, Long organizationId);
    
    /**
     * 按上次进价策略获取价格
     */
    Integer getPriceByLastPrice(Long organizationMedicineId, Long organizationSupplierId, Long organizationId);
    
    /**
     * 获取商品最近使用的供应商
     */
    Long getLastUsedSupplier(Long organizationMedicineId, Long organizationId);
}
```

### 4.3 库存计算服务

```java
/**
 * 库存相关计算服务
 */
@Service
public interface InventoryCalculationService {
    
    /**
     * 计算在途数量
     * @param organizationMedicineId 私域商品ID
     * @param days 统计天数
     * @param organizationId 组织ID
     * @return 在途数量
     */
    Double calculateInTransitQuantity(Long organizationMedicineId, Integer days, Long organizationId);
    
    /**
     * 计算日均销量
     * @param organizationMedicineId 私域商品ID
     * @param days 统计天数
     * @param organizationId 组织ID
     * @return 日均销量
     */
    Double calculateDailyAvgSales(Long organizationMedicineId, Integer days, Long organizationId);
    
    /**
     * 计算库存上限
     */
    Double calculateStockUpperLimit(Double dailyAvgSales, Integer upperLimitDays);
    
    /**
     * 计算库存下限
     */
    Double calculateStockLowerLimit(Double dailyAvgSales, Integer lowerLimitDays);
    
    /**
     * 计算推荐采购数量
     */
    Double calculateRecommendQuantity(Double upperLimit, Double currentStock, Double inTransitQuantity);
    
    /**
     * 获取商品当前库存
     */
    Double getCurrentStock(Long organizationMedicineId, Long organizationId);
}
```

### 4.4 自动生成采购计划服务

```java
/**
 * 自动生成采购计划服务
 */
@Service
public interface AutoGeneratePurchasePlanService {
    
    /**
     * 自动生成采购计划
     * @param organizationId 组织ID
     * @return 生成的采购计划详情
     */
    PurchasePlanDetailVo autoGeneratePurchasePlan(Long organizationId);
    
    /**
     * 获取需要采购的商品列表（库存低于下限的商品）
     */
    List<AutoGenerateGoodsInfo> getLowStockGoods(Long organizationId);
    
    /**
     * 批量计算采购计划明细
     */
    List<PurchasePlanGoodsVo> calculatePurchasePlanGoods(List<Long> organizationMedicineIds, Long organizationId);
}

/**
 * 自动生成商品信息DTO
 */
@Data
public class AutoGenerateGoodsInfo {
    private Long organizationMedicineId;
    private String goodsName;
    private String goodsCode;
    private Double currentStock;
    private Double dailyAvgSales;
    private Double stockLowerLimit;
    private Double inTransitQuantity;
    private Double recommendQuantity;
    private Long recommendedSupplierId;
    private Integer recommendedPrice;
}
```

### 4.5 GSP合规校验服务

```java
/**
 * GSP合规校验服务
 */
@Service
public interface GspComplianceService {
    
    /**
     * 采购计划GSP校验
     * @param planId 采购计划ID
     * @return 校验结果
     */
    GspCheckResult validatePurchasePlan(Long planId);
    
    /**
     * 批量GSP校验采购计划明细
     */
    Map<Long, GspCheckResult> batchValidatePlanGoods(List<Long> planGoodsIds);
    
    /**
     * 校验供应商资质
     */
    GspCheckResult validateSupplierQualification(Long organizationSupplierId);
    
    /**
     * 校验商品资质
     */
    GspCheckResult validateMedicineQualification(Long organizationMedicineId);
    
    /**
     * 校验经营范围匹配
     */
    GspCheckResult validateBusinessScope(Long organizationSupplierId, Long organizationMedicineId);
}

/**
 * GSP校验结果
 */
@Data
public class GspCheckResult {
    private Boolean passed;          // 校验是否通过
    private String message;          // 校验信息
    private List<String> errorDetails; // 错误详情
    private Integer checkMode;       // 校验模式：1-拦截，0-不拦截不提示，-1-提示不拦截
}
```

---

## 🎯 5. 实施细节

### 阶段一：参数配置管理（3-4天）

#### 5.1 参数管理服务实现
```java
// PurchasePlanParameterServiceImpl.java - 基于OrganizationDict的参数管理服务
@Service
@Slf4j
public class PurchasePlanParameterServiceImpl implements PurchasePlanParameterService {
    
    @Autowired
    private OrganizationDictService organizationDictService;
    
    @Override
    public Integer getIntParameter(Long organizationId, String paramCode) {
        String value = getStringParameter(organizationId, paramCode);
        return value != null ? Integer.valueOf(value) : getDefaultValue(paramCode);
    }
    
    @Override
    public String getStringParameter(Long organizationId, String paramCode) {
        OrganizationDictVO dict = organizationDictService.getByOrgTypeAndCode(
            organizationId, PurchasePlanParamConstants.DICT_TYPE_PURCHASE_PLAN, paramCode);
        return dict != null ? dict.getDictValue() : getDefaultStringValue(paramCode);
    }
    
    @Override
    public void initDefaultParameters(Long organizationId) {
        // 初始化所有默认参数
        Map<String, String> defaultParams = getDefaultParameters();
        for (Map.Entry<String, String> entry : defaultParams.entrySet()) {
            SaveOrUpdateOrganizationDictParam param = new SaveOrUpdateOrganizationDictParam();
            param.setOrganizationId(organizationId);
            param.setDictType(PurchasePlanParamConstants.DICT_TYPE_PURCHASE_PLAN);
            param.setDictCode(entry.getKey());
            param.setDictValue(entry.getValue());
            param.setDictName(getParamDescription(entry.getKey()));
            organizationDictService.saveOrUpdateOrganizationDict(param);
        }
    }
    
    private Map<String, String> getDefaultParameters() {
        Map<String, String> params = new HashMap<>();
        params.put(PurchasePlanParamConstants.PRICE_STRATEGY, PurchasePlanParamConstants.PRICE_STRATEGY_DEFAULT);
        params.put(PurchasePlanParamConstants.LOWEST_PRICE_DAYS, PurchasePlanParamConstants.LOWEST_PRICE_DAYS_DEFAULT);
        params.put(PurchasePlanParamConstants.IN_TRANSIT_DAYS, PurchasePlanParamConstants.IN_TRANSIT_DAYS_DEFAULT);
        params.put(PurchasePlanParamConstants.SALES_STAT_DAYS, PurchasePlanParamConstants.SALES_STAT_DAYS_DEFAULT);
        params.put(PurchasePlanParamConstants.UPPER_LIMIT_DAYS, PurchasePlanParamConstants.UPPER_LIMIT_DAYS_DEFAULT);
        params.put(PurchasePlanParamConstants.LOWER_LIMIT_DAYS, PurchasePlanParamConstants.LOWER_LIMIT_DAYS_DEFAULT);
        params.put(PurchasePlanParamConstants.GSP_CHECK_MODE, PurchasePlanParamConstants.GSP_CHECK_MODE_DEFAULT);
        return params;
    }
}
```

#### 5.2 扩展Entity字段
```java
// 扩展PurchasePlan.java
@ApiModelProperty(value = "是否自动生成：0-手动，1-自动")
@TableField("auto_generated")
private Integer autoGenerated;

@ApiModelProperty(value = "GSP校验状态：0-未校验，1-通过，-1-不通过")
@TableField("gsp_check_status") 
private Integer gspCheckStatus;

@ApiModelProperty(value = "GSP校验信息")
@TableField("check_message")
private String checkMessage;

// 扩展PurchasePlanGoods.java
@ApiModelProperty(value = "供应商ID")
@TableField("organization_supplier_id")
private Long organizationSupplierId;

@ApiModelProperty(value = "在途数量")
@TableField("in_transit_quantity")
private Double inTransitQuantity;

@ApiModelProperty(value = "日均销量")
@TableField("daily_avg_sales")
private Double dailyAvgSales;

@ApiModelProperty(value = "库存上限")
@TableField("stock_upper_limit")
private Double stockUpperLimit;

@ApiModelProperty(value = "库存下限")
@TableField("stock_lower_limit")
private Double stockLowerLimit;

@ApiModelProperty(value = "推荐采购数量")
@TableField("recommend_quantity")
private Double recommendQuantity;

@ApiModelProperty(value = "价格策略：1-最低价，2-最新供应商，3-上次进价")
@TableField("price_strategy")
private Integer priceStrategy;
```

### 阶段二：核心业务逻辑（4-5天）

#### 5.3 自动生成算法核心逻辑
```java
// 核心算法实现
@Override
public PurchasePlanDetailVo autoGeneratePurchasePlan(Long organizationId) {
    // 1. 获取参数配置
    Integer upperLimitDays = parameterService.getIntParameter(organizationId, UPPER_LIMIT_DAYS);
    Integer lowerLimitDays = parameterService.getIntParameter(organizationId, LOWER_LIMIT_DAYS);
    Integer salesStatDays = parameterService.getIntParameter(organizationId, SALES_STAT_DAYS);
    Integer inTransitDays = parameterService.getIntParameter(organizationId, IN_TRANSIT_DAYS);
    
    // 2. 获取所有商品的库存情况
    List<AutoGenerateGoodsInfo> lowStockGoods = getLowStockGoods(organizationId);
    
    // 3. 计算每个商品的采购计划量
    List<PurchasePlanGoodsVo> planGoods = new ArrayList<>();
    for (AutoGenerateGoodsInfo goodsInfo : lowStockGoods) {
        // 计算日均销量
        Double dailyAvgSales = inventoryCalculationService.calculateDailyAvgSales(
            goodsInfo.getOrganizationMedicineId(), salesStatDays, organizationId);
        
        // 计算库存上下限
        Double upperLimit = inventoryCalculationService.calculateStockUpperLimit(dailyAvgSales, upperLimitDays);
        Double lowerLimit = inventoryCalculationService.calculateStockLowerLimit(dailyAvgSales, lowerLimitDays);
        
        // 计算在途数量
        Double inTransitQuantity = inventoryCalculationService.calculateInTransitQuantity(
            goodsInfo.getOrganizationMedicineId(), inTransitDays, organizationId);
        
        // 计算推荐采购数量
        Double recommendQuantity = inventoryCalculationService.calculateRecommendQuantity(
            upperLimit, goodsInfo.getCurrentStock(), inTransitQuantity);
        
        if (recommendQuantity > 0) {
            // 获取推荐供应商和价格
            Long supplierId = priceStrategyService.getLastUsedSupplier(
                goodsInfo.getOrganizationMedicineId(), organizationId);
            Integer price = priceStrategyService.getPurchasePrice(
                goodsInfo.getOrganizationMedicineId(), supplierId, organizationId);
            
            // 构建采购计划明细
            PurchasePlanGoodsVo planGood = buildPlanGoods(goodsInfo, supplierId, price, 
                recommendQuantity, dailyAvgSales, upperLimit, lowerLimit, inTransitQuantity);
            planGoods.add(planGood);
        }
    }
    
    // 4. 按供应商分组生成采购计划
    return generatePurchasePlanBySupplier(planGoods, organizationId);
}
```

### 阶段三：API接口扩展（3-4天）

#### 5.4 扩展PurchasePlanController
```java
// 新增自动生成接口
@PostMapping("/purchase-plan/action-auto-generate")
@ApiOperation(value = "自动生成采购计划", notes = "根据库存上下限规则自动生成采购计划")
@OrgAuthMode(strict = true)
public ResponseEntity<PurchasePlanDetailVo> autoGeneratePurchasePlan() {
    Long organizationId = BaseContextHandler.get(BaseContextConstants.USER_ORG_ID, Long.class);
    PurchasePlanDetailVo result = autoGeneratePurchasePlanService.autoGeneratePurchasePlan(organizationId);
    return ResponseEntity.ok(result);
}

// 新增GSP校验接口
@PostMapping("/purchase-plan/{id}/action-gsp-check")
@ApiOperation(value = "GSP合规校验", notes = "对采购计划进行GSP合规校验")
@OrgAuthMode(strict = true)
public ResponseEntity<GspCheckResult> gspCheck(@PathVariable Long id) {
    GspCheckResult result = gspComplianceService.validatePurchasePlan(id);
    return ResponseEntity.ok(result);
}

// 新增导入接口
@PostMapping("/purchase-plan/action-import")
@ApiOperation(value = "导入采购计划", notes = "通过Excel导入采购计划明细")
@OrgAuthMode(strict = true)
public ResponseEntity<PurchasePlanDetailVo> importPurchasePlan(
    @RequestParam("file") MultipartFile file) {
    Long organizationId = BaseContextHandler.get(BaseContextConstants.USER_ORG_ID, Long.class);
    PurchasePlanDetailVo result = purchasePlanImportService.importPurchasePlan(file, organizationId);
    return ResponseEntity.ok(result);
}
```

### 阶段四：测试验证（2-3天）

#### 5.5 单元测试示例
```java
@Test
void testAutoGeneratePurchasePlan() {
    // 1. 准备测试数据
    Long organizationId = 1001L;
    
    // 2. 模拟参数配置
    when(parameterService.getIntParameter(organizationId, UPPER_LIMIT_DAYS)).thenReturn(7);
    when(parameterService.getIntParameter(organizationId, LOWER_LIMIT_DAYS)).thenReturn(3);
    
    // 3. 模拟库存数据
    List<AutoGenerateGoodsInfo> mockGoods = createMockLowStockGoods();
    when(autoGenerateService.getLowStockGoods(organizationId)).thenReturn(mockGoods);
    
    // 4. 执行测试
    PurchasePlanDetailVo result = autoGenerateService.autoGeneratePurchasePlan(organizationId);
    
    // 5. 验证结果
    assertThat(result).isNotNull();
    assertThat(result.getAutoGenerated()).isEqualTo(1);
    assertThat(result.getPurchasePlanGoodsVoList()).isNotEmpty();
}
```

---

## 📅 6. 时间安排

| 阶段 | 内容 | 预计时间 | 负责人 |
|------|------|----------|--------|
| 阶段一 | 参数配置管理 | 3-4天 | 后端开发 |
| 阶段二 | 核心业务逻辑 | 4-5天 | 后端开发 |
| 阶段三 | API接口扩展 | 3-4天 | 后端开发 |
| 阶段四 | 测试验证 | 2-3天 | 测试团队 |

**总计：12-16天**

---

## 🔧 7. 技术要点

### 7.1 自动生成算法
```java
// 核心算法逻辑
库存上限 = 上限天数 × 日均销量
库存下限 = 下限天数 × 日均销量
采购计划量 = 上限库存 - 当前库存 - 在途数量
日均销量 = 前N天销售数量 / (N - 断货天数)
```

### 7.2 性能优化
- 批量查询优化
- 缓存策略
- 异步处理

### 7.3 数据一致性
- 事务管理
- 分布式锁
- 数据校验

---

## ⚠️ 8. 风险控制

### 8.1 技术风险
- 大数据量查询性能
- 并发操作数据一致性
- 第三方接口依赖

### 8.2 业务风险
- GSP校验规则变更
- 库存计算准确性
- 用户操作习惯改变

### 8.3 应对措施
- 充分的单元测试和集成测试
- 灰度发布策略
- 回滚方案准备
- 用户培训和文档

---

## 📋 9. 验收标准

### 9.1 功能验收
- [ ] 自动生成采购计划功能正常
- [ ] 三种价格策略正确实现
- [ ] GSP校验功能完整
- [ ] 导入功能稳定可用
- [ ] 查询功能增强完成

### 9.2 性能验收
- [ ] 自动生成响应时间 < 5秒
- [ ] 导入1000条数据 < 30秒
- [ ] 查询响应时间 < 2秒

### 9.3 质量验收
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试通过率 100%
- [ ] 代码审查通过
- [ ] 文档完整

---

## 🚀 10. 后续规划

### 10.1 功能扩展
- 采购计划模板管理
- 智能推荐算法优化
- 移动端支持

### 10.2 系统优化
- 微服务拆分
- 消息队列引入
- 监控告警完善

---

## 📝 11. 关键设计亮点

### ✅ 设计优势
- **复用现有架构**：基于OrganizationDict服务管理参数，避免重复造轮子
- **模块化设计**：每个服务职责单一，便于测试和维护
- **数据库优化**：添加必要字段和索引，提升查询性能
- **业务逻辑清晰**：自动生成算法和GSP校验规则明确定义

### 🎯 接下来的开发重点
1. **第一阶段**：实现参数管理和数据模型扩展
2. **第二阶段**：开发核心业务逻辑服务
3. **第三阶段**：完善GSP校验和导入功能
4. **第四阶段**：测试和性能优化

这个规划为采购计划功能的开发提供了清晰的路线图和技术方案。