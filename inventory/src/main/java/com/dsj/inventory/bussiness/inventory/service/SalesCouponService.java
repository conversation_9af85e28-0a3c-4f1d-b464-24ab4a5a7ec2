package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.SalesCoupon;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesCouponDetailParam;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesCouponParam;
import com.dsj.inventory.bussiness.inventory.param.SaveSalesCouponParam;
import com.dsj.inventory.bussiness.inventory.vo.SalesCouponDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesCouponVo;

/**
 * <p>
 * 进销存优惠券 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface SalesCouponService extends IService<SalesCoupon> {

    /**
     * 新增、删除 优惠券
     * @param param
     * @return
     */
    SalesCouponVo saveSalesCoupon(SaveSalesCouponParam param);

    /**
     * 获取优惠券分页列表
     * @param param
     * @return
     */
    Page<SalesCouponVo> querySalesCouponPage(QuerySalesCouponParam param);

    /**
     * 获取优惠券列表详情
     * @param id
     * @return
     */
    SalesCouponVo getSalesCouponById(Long id);

    /**
     * 获取优惠券明细列表
     * @param param
     * @return
     */
    Page<SalesCouponDetailVo> querySalesCouponDetailPage(QuerySalesCouponDetailParam param);

    /**
     * 批量处理优惠券状态
     */
    void batchHandleSalesCouponState();

}
