
package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.MemberCoupon;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberCouponCountParam;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberCouponParam;
import com.dsj.inventory.bussiness.inventory.vo.MemberCouponCountVo;
import com.dsj.inventory.bussiness.inventory.vo.MemberCouponVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存会员优惠券 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface MemberCouponMapper extends BaseMapper<MemberCoupon> {

    /**
     * 获取会员优惠券分页列表
     * @param page
     * @param param
     * @return
     */
    List<MemberCouponVo> queryMemberCouponPage(@Param("page") Page page, @Param("param") QueryMemberCouponParam param);

    /**
     * 获取会员优惠券详情
     * @param id
     * @return
     */
    MemberCouponVo getMemberCouponById(Long id);

    /**
     * 会员优惠券统计
     * @param param
     * @return
     */
    MemberCouponCountVo countMemberCoupon(@Param("param") QueryMemberCouponCountParam param);

}
