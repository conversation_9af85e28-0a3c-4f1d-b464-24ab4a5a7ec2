package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询供应商组织私域
 * <AUTHOR>
 * @date 2023/03/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QueryOrganizationSupplierParam",description = "查询供应商组织私域参数")
public class QueryOrganizationSupplierParam extends BasePage {

    @ApiModelProperty(value = "供应商名称模糊搜索")
    private String nameLike;

    @ApiModelProperty(value = "供应商类别，字典")
    private String supplierType;

    @ApiModelProperty(value = "可用状态 0：禁用 1可用")
    private Integer usableState;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "组织名称模糊搜索")
    private String organizationNameLike;

    @ApiModelProperty(value = "组织分类 医院1 医药公司2  药房3")
    private Integer organizationType;

    @ApiModelProperty(value = "公域供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "单据编号")
    private String documentNumber;

    @ApiModelProperty(value = "单据状态")
    private Integer documentStatus;
}
