package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 损溢单类型 1损 2溢
 * <AUTHOR>
 */

@Getter
public enum GainsLossesOrderTypeEnum {

    Losses(1, "损"),
    Gains(2, "溢"),
    ;

    private Integer code;

    private String desc;

    GainsLossesOrderTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (GainsLossesOrderTypeEnum a : GainsLossesOrderTypeEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
