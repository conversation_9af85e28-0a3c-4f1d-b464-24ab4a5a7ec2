package com.dsj.inventory.framework.annotations;

import java.lang.annotation.*;

/**
 * 校验组织权限
 * 只在方法上使用
 */
@Documented
@Inherited
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface OrgAuthMode {
    //是否强制校验组织权限,强制校验组织权限时  header中必传 moduleType
    boolean strict() default true;

    //是否强制校验人员菜单权限
    boolean strictUserMenus() default true;

    //对应权限code
    String[] menuCodeArray() default "";
}