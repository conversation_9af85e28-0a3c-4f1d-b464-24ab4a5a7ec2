package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationDict;
import com.dsj.inventory.bussiness.inventory.param.QueryOrganizationDictParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateOrganizationDictParam;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationDictVO;

import java.util.List;

/**
 * 组织字典参数 Service 接口
 */
public interface OrganizationDictService extends IService<OrganizationDict> {

    /**
     * 保存或更新组织字典参数
     *
     * @param param 保存或更新参数
     * @return 组织字典参数ID
     */
    Long saveOrUpdateOrganizationDict(SaveOrUpdateOrganizationDictParam param);

    /**
     * 删除组织字典参数
     *
     * @param id 组织字典参数ID
     * @return 是否成功
     */
    Boolean deleteOrganizationDict(Long id);

    /**
     * 获取组织字典参数
     *
     * @param id 组织字典参数ID
     * @return 组织字典参数视图对象
     */
    OrganizationDictVO getOrganizationDict(Long id);

    /**
     * 分页查询组织字典参数
     *
     * @param param 查询参数
     * @return 分页结果
     */
    Page<OrganizationDictVO> queryOrganizationDictPage(QueryOrganizationDictParam param);

    /**
     * 根据组织ID和字典类型查询字典列表
     *
     * @param organizationId 组织ID
     * @param dictType 字典类型
     * @return 字典列表
     */
    List<OrganizationDictVO> listByOrgAndType(Long organizationId, String dictType);

    /**
     * 根据组织ID、字典类型和编码查询字典
     *
     * @param organizationId 组织ID
     * @param dictType 字典类型
     * @param dictCode 字典编码
     * @return 字典视图对象
     */
    OrganizationDictVO getByOrgTypeAndCode(Long organizationId, String dictType, String dictCode);
} 