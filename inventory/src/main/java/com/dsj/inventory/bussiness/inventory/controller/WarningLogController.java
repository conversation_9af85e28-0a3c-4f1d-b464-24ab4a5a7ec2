package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryWarningLogParam;
import com.dsj.inventory.bussiness.inventory.service.WarningLogService;
import com.dsj.inventory.bussiness.inventory.vo.WarningLogVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 进销存预警记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@RestController
@Api(value = "WarningLogController", tags = "进销存预警记录api")
public class WarningLogController {

    @Autowired
    private WarningLogService warningLogService;


    /*获取预警记录列表*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-warning"})
    @GetMapping("/warning-log")
    @ApiOperation(value = "获取预警记录列表", notes = "获取预警记录列表")
    public ResponseEntity<List<WarningLogVo>> queryWarningLogList(QueryWarningLogParam param){
        Page<WarningLogVo> page = warningLogService.queryWarningLogPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*预警记录定时器模拟*/
    @PostMapping("/warning-log/action-timer")
    @ApiOperation(value = "预警记录定时器模拟", notes = "预警记录定时器模拟")
    public ResponseEntity<Void> warningLogTimer(){
        warningLogService.batchHandleWarningLog();
        return Res.success();
    }


}
