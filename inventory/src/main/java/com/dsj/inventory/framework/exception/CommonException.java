package com.dsj.inventory.framework.exception;

import com.dsj.inventory.framework.exception.BaseCheckedException;

/**
 * 非业务异常
 * 用于在处理非业务逻辑时，进行抛出的异常。
 *
 * <AUTHOR>
 * @version 1.0
 * @see Exception
 */
public class CommonException extends BaseCheckedException {


    public CommonException(int code, String message) {
        super(code, message);
    }

    public CommonException(int code, String format, Object... args) {
        super(code, String.format(format, args));
        this.code = code;
        this.message = String.format(format, args);
    }

    public com.dsj.inventory.framework.exception.CommonException wrap(int code, String format, Object... args) {
        return new com.dsj.inventory.framework.exception.CommonException(code, format, args);
    }

    @Override
    public String toString() {
        return "BizException [message=" + message + ", code=" + code + "]";
    }
}
