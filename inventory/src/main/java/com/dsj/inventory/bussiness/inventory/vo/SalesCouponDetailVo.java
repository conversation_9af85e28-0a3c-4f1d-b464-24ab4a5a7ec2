package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 优惠券明细
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SalesCouponDetailVo", description="优惠券明细")
@ToString(callSuper = true)
public class SalesCouponDetailVo {

    private Long id;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "优惠券状态 0未使用 1已使用 2已过期")
    private Integer couponState;

    @ApiModelProperty(value = "优惠券开始时间")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "优惠券结束时间")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "领取优惠券组织名称")
    private String getOrganizationName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
