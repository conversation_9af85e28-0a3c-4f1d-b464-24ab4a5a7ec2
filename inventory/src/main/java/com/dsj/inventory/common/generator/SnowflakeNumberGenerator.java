package com.dsj.inventory.common.generator;

import cn.hutool.core.util.IdUtil;

/**
 * 基于雪花算法（Snowflake）的编号生成器
 * 利用hutool工具包中的雪花算法实现，保证多服务器环境下编号的唯一性
 */
public class SnowflakeNumberGenerator extends AbstractNumberGenerator {

    private final cn.hutool.core.lang.Snowflake snowflake;
    private final int sequenceLength;

    /**
     * 构造方法
     *
     * @param type         编号类型
     * @param workerId     工作机器ID (0~31)
     * @param dataCenterId 数据中心ID (0~31)
     * @param sequenceLength 序列号长度，用于前导零填充
     */
    public SnowflakeNumberGenerator(NumberType type, long workerId, long dataCenterId, int sequenceLength) {
        super(type);
        // 使用hutool的雪花算法实现，设置工作节点ID和数据中心ID
        this.snowflake = IdUtil.getSnowflake(workerId, dataCenterId);
        this.sequenceLength = sequenceLength;
    }

    /**
     * 构造方法，默认序列号长度为10
     *
     * @param type         编号类型
     * @param workerId     工作机器ID (0~31)
     * @param dataCenterId 数据中心ID (0~31)
     */
    public SnowflakeNumberGenerator(NumberType type, long workerId, long dataCenterId) {
        this(type, workerId, dataCenterId, 10);
    }

    @Override
    protected String getSequencePart() {
        // 获取雪花算法生成的ID
        long snowflakeId = snowflake.nextId();
        // 使用雪花ID的后N位作为序列号，根据sequenceLength确定
        long maxValue = (long)Math.pow(10, sequenceLength);
        // 取模确保不超过指定长度
        return String.format("%0" + sequenceLength + "d", snowflakeId % maxValue);
    }
} 