package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 进销存退货单商品明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_delivery_order_goods")
@ApiModel(value="DeliveryOrderGoods", description="进销存退货单商品明细")
public class DeliveryOrderGoods extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "退货单Id")
    @TableField("order_id")
    private Long orderId;

    @ApiModelProperty(value = "库存Id")
    @TableField("inventory_id")
    private Long inventoryId;

    @ApiModelProperty(value = "私域商品Id")
    @TableField("organization_medicine_id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "通用名")
    @TableField("goods_name")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    @TableField("general_name")
    private String generalName;

    @ApiModelProperty(value = "商品编号")
    @TableField("goods_code")
    private String goodsCode;

    @ApiModelProperty(value = "包装单位")
    @TableField("pack_unit")
    private String packUnit;

    @ApiModelProperty(value = "规格型号")
    @TableField("specification")
    private String specification;

    @ApiModelProperty(value = "剂型")
    @TableField("drug_type")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    @TableField("manufacturer")
    private String manufacturer;

    @ApiModelProperty(value = "上市许可持有人")
    @TableField("launch_permit_holder")
    private String launchPermitHolder;

    @ApiModelProperty(value = "产地")
    @TableField("produce_place")
    private String producePlace;

    @ApiModelProperty(value = "批准文号")
    @TableField("approval_number")
    private String approvalNumber;

    @ApiModelProperty(value = "生产日期")
    @TableField("produce_date")
    private LocalDateTime produceDate;

    @ApiModelProperty(value = "有效期至")
    @TableField("user_date")
    private LocalDateTime userDate;

    @ApiModelProperty(value = "批号")
    @TableField("batch_number")
    private String batchNumber;

    @ApiModelProperty(value = "货位Id")
    @TableField("shelf_id")
    private Long shelfId;

    @ApiModelProperty(value = "批次（入库单号）")
    @TableField("batch")
    private String batch;

    @ApiModelProperty(value = "入库时的商品明细Id")
    @TableField("incoming_goods_id")
    private Long incomingGoodsId;

    @ApiModelProperty(value = "原单数量")
    @TableField("incoming_amount")
    private Double incomingAmount;

    @ApiModelProperty(value = "库存数量")
    @TableField("inventory_amount")
    private Double inventoryAmount;

    @ApiModelProperty(value = "已退货数量")
    @TableField("returned_amount")
    private Double returnedAmount;

    @ApiModelProperty(value = "退货数量")
    @TableField("return_amount")
    private Double returnAmount;

    @ApiModelProperty(value = "赠送数量")
    @TableField("give_amount")
    private Double giveAmount;

    @ApiModelProperty(value = "出库含税价（退货单价）")
    @TableField("return_price")
    private Integer returnPrice;

    @ApiModelProperty(value = "含税金额（出库含税价*退货数量）")
    @TableField("return_total_price")
    private Integer returnTotalPrice;

    @ApiModelProperty(value = "出库成本均价（当时的进货价）")
    @TableField("cost_price")
    private Integer costPrice;

    @ApiModelProperty(value = "退货成本金额（出库成本均价*退货数量）")
    @TableField("return_cost_total_price")
    private Integer returnCostTotalPrice;

    @ApiModelProperty(value = "进退货差价（退货成本金额-含税金额）")
    @TableField("disparity_price")
    private Integer disparityPrice;

    @ApiModelProperty(value = "退货原因")
    @TableField("reason")
    private String reason;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
