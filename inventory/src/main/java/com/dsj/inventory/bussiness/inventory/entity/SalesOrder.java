package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 进销存销售单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_sales_order")
@ApiModel(value="SalesOrder", description="进销存销售单")
public class SalesOrder extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织Id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "销售单号")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "销售员Id")
    @TableField("sale_id")
    private Long saleId;

    @ApiModelProperty(value = "订单状态：1 挂单 2已完成 3已退单")
    @TableField("order_state")
    private Integer orderState;

    @ApiModelProperty(value = "订单类型：1线下 2线上")
    @TableField("order_type")
    private Integer orderType;

    @ApiModelProperty(value = "线上订单Id")
    @TableField("online_order_id")
    private Long onlineOrderId;

    @ApiModelProperty(value = "退单时间")
    @TableField("return_date")
    private LocalDateTime returnDate;

    @ApiModelProperty(value = "退单原因")
    @TableField("return_reason")
    private String returnReason;

    @ApiModelProperty(value = "挂单时间")
    @TableField("hang_date")
    private LocalDateTime hangDate;

    @ApiModelProperty(value = "挂单备注")
    @TableField("hang_remark")
    private String hangRemark;

    @ApiModelProperty(value = "收银员id")
    @TableField("cashier_id")
    private Long cashierId;

    @ApiModelProperty(value = "应收")
    @TableField("receivable_price")
    private Integer receivablePrice;

    @ApiModelProperty(value = "优惠")
    @TableField("preferential_price")
    private Integer preferentialPrice;

    @ApiModelProperty(value = "抹零")
    @TableField("deduct_price")
    private Integer deductPrice;

    @ApiModelProperty(value = "合计")
    @TableField("total_price")
    private Integer totalPrice;

    @ApiModelProperty(value = "实收")
    @TableField("actual_price")
    private Integer actualPrice;

    @ApiModelProperty(value = "找零")
    @TableField("give_change_price")
    private Integer giveChangePrice;

    @ApiModelProperty(value = "商品数量")
    @TableField("goods_amount")
    private Integer goodsAmount;

    @ApiModelProperty(value = "本次积分")
    @TableField("integral")
    private Integer integral;

    @ApiModelProperty(value = "扣除积分")
    @TableField("deduct_integral")
    private Integer deductIntegral;

    @ApiModelProperty(value = "关联处方类型 1常规处方  2外部处方（拍照处方）")
    @TableField("prescription_type")
    private Integer prescriptionType;

    @ApiModelProperty(value = "处方Id，逗号分隔")
    @TableField("prescription_ids")
    private String prescriptionIds;

    @ApiModelProperty(value = "最后结算时间")
    @TableField("settlement_date")
    private LocalDateTime settlementDate;

    @ApiModelProperty(value = "会员Id")
    @TableField("member_id")
    private Long memberId;

    @ApiModelProperty(value = "活动Id")
    @TableField("activity_id")
    private Long activityId;

    @ApiModelProperty(value = "活动减额")
    @TableField("activity_reduce_price")
    private Integer activityReducePrice;

    @ApiModelProperty(value = "优惠券Id")
    @TableField("coupon_id")
    private Long couponId;

    @ApiModelProperty(value = "优惠券金额")
    @TableField("coupon_price")
    private Integer couponPrice;

    @ApiModelProperty(value = "支付方式")
    @TableField("pay_way")
    private String payWay;

    @ApiModelProperty(value = "支付信息，json字符串")
    @TableField("pay_info")
    private String payInfo;

    @ApiModelProperty(value = "储值")
    @TableField("stored_pay")
    private Integer storedPay;

    @ApiModelProperty(value = "积分抵扣")
    @TableField("integral_pay")
    private Integer integralPay;

    @ApiModelProperty(value = "商品信息")
    @TableField("goods_info")
    private String goodsInfo;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
