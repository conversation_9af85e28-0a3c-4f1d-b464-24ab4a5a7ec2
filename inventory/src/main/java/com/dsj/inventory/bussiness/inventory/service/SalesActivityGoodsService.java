package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityGoods;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存销售活动商品明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface SalesActivityGoodsService extends IService<SalesActivityGoods> {

    /**
     * 获取销售活动商品
     * @param activityId
     * @return
     */
    List<SalesActivityGoodsVo> querySalesActivityGoodsList(Long activityId);

}
