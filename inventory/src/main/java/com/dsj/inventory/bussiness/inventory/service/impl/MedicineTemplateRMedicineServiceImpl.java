package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.MedicineTemplateRMedicine;
import com.dsj.inventory.bussiness.inventory.mapper.MedicineTemplateRMedicineMapper;
import com.dsj.inventory.bussiness.inventory.service.MedicineTemplateRMedicineService;
import com.dsj.inventory.bussiness.inventory.vo.MedicineTemplateMedicineVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 药品拉取模板药品信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@Service
public class MedicineTemplateRMedicineServiceImpl extends ServiceImpl<MedicineTemplateRMedicineMapper, MedicineTemplateRMedicine> implements MedicineTemplateRMedicineService {

    @Override
    public List<MedicineTemplateMedicineVo> getMedicineTemplateMedicineList(Long templateId) {
        return this.baseMapper.getMedicineTemplateMedicineList(templateId);
    }
}
