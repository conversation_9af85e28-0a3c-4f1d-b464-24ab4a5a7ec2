package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationSupplier;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationSupplierMember;
import com.dsj.inventory.bussiness.inventory.enumeration.EnterpriseTypeEnum;
import com.dsj.inventory.bussiness.inventory.mapper.OrganizationSupplierMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryOrganizationSupplierParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateOrganizationSupplierParam;
import com.dsj.inventory.bussiness.inventory.service.OrganizationSupplierMemberService;
import com.dsj.inventory.bussiness.inventory.service.OrganizationSupplierService;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationSupplierExportVo;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationSupplierVo;
import com.dsj.inventory.bussiness.quality.enumeration.DocumentStatusEnum;
import com.dsj.inventory.bussiness.quality.service.SupplierFirstBusinessService;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.auth.entity.Log;
import com.pocky.transport.bussiness.auth.enumeration.LogModuleEnum;
import com.pocky.transport.bussiness.auth.enumeration.LogTypeEnum;
import com.pocky.transport.bussiness.auth.service.ILogService;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.param.DisableParam;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 供应商组织私域 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Service
public class OrganizationSupplierServiceImpl extends ServiceImpl<OrganizationSupplierMapper, OrganizationSupplier> implements OrganizationSupplierService {

    @Autowired
    private ILogService logService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationSupplierMemberService organizationSupplierMemberService;
    @Autowired
    private com.dsj.inventory.common.generator.NumberService numberService;
    @Autowired
    private SupplierFirstBusinessService supplierFirstBusinessService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrganizationSupplierVo saveOrUpdateOrganizationSupplier(SaveOrUpdateOrganizationSupplierParam param) {
        if (CommonUtils.isEmpty(param)) {
            throw new ParamException("参数异常");
        }
        Organization organization = organizationService.getById(param.getOrganizationId());
        if (CommonUtils.isEmpty(organization)) {
            throw new BizException("组织不存在");
        }
        if (CommonUtils.isEmpty(organization.getInventoryDate())) {
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        String type = LogTypeEnum.INSERT.getCode();
        // 是否是添加供应商
        Boolean isSave = false;
        if (CommonUtils.isNotEmpty(param.getId())) {
            // 修改供应商信息
            OrganizationSupplier byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            type = LogTypeEnum.UPDATE.getCode();
            if (CommonUtils.isEmpty(param.getCreditDate()) && CommonUtils.isNotEmpty(byId.getCreditDate())) {
                LambdaUpdateWrapper<OrganizationSupplier> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(OrganizationSupplier::getId, byId.getId());
                updateWrapper.set(OrganizationSupplier::getCreditDate, null);
                this.update(updateWrapper);
            }
        } else {
            isSave = true;
            // 新增供应商
            //默认初始化状态为可用
            param.setUsableState(TrueEnum.TRUE.getCode());
            //默认设置单据状态为审核中
            param.setDocumentStatus(DocumentStatusEnum.REVIEWING.getCode());

            // 新增供应商时的额外设置
            // 1. 生成供应商首营编号
            String documentNumber = numberService.generateSupplierFirstDocumentNumber();
            param.setDocumentNumber(documentNumber);

            // 2. 填报部门是当前登录用户所属部门
            String department = BaseContextHandler.get(BaseContextConstants.USER_ROLE);
            param.setReportingDepartment(department);

            // 3. 单据状态在上面已设置为审核中

            // 4. 填报日期是当前系统时间
            param.setFilingDate(LocalDateTime.now());

            // 5. 审批日期为空 (默认为null，无需设置)

            // 6. 转正日期为空 (默认为null，无需设置)
        }
        param.setOrganizationType(organization.getType());

        if (!checkCode(param.getId(), param.getCreditCode(), organization.getId())) {
            throw new BizException("操作失败，统一社会信用代码重复");
        }
        if (!checkName(param.getId(), param.getName(), organization.getId())) {
            throw new BizException("操作失败，名称重复");
        }

        OrganizationSupplier organizationSupplier = new OrganizationSupplier();
        cn.hutool.core.bean.BeanUtil.copyProperties(param, organizationSupplier);
        this.saveOrUpdate(organizationSupplier);
        if (isSave) {
            // 创建供应商首营
            supplierFirstBusinessService.createSupplierFirstBusiness(organizationSupplier);
        }
        //增加系统操作日志
        logService.save(Log.builder().moduleName(LogModuleEnum.SUPPLIER.getCode()).type(type)
                .logInfo(organization.getName() + LogTypeEnum.getDesc(type) + "供应商" + param.getName()).build());
        return this.getOrganizationSupplierById(organizationSupplier.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrganizationSupplier(Long id) {
        OrganizationSupplier byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        this.baseMapper.deleteById(id);
        //删除销售人员
        organizationSupplierMemberService.lambdaUpdate().eq(OrganizationSupplierMember::getOrganizationSupplierId, id)
                .set(OrganizationSupplierMember::getIsDelete, TrueEnum.TRUE.getCode()).update(new OrganizationSupplierMember());
        Organization organization = organizationService.getById(byId.getOrganizationId());
        //增加系统操作日志
        logService.save(Log.builder().moduleName(LogModuleEnum.SUPPLIER.getCode()).type(LogTypeEnum.DELETE.getCode())
                .logInfo(organization.getName() + LogTypeEnum.DELETE.getDesc() + "供应商" + byId.getName()).build());
    }

    /**
     * @author: baiyiwen
     * @description: 校验 社会信用代码
     * @date: 2023/3/21
     */
    public Boolean checkCode(Long id, String creditCode, Long organizationId) {
        LambdaQueryWrapper<OrganizationSupplier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrganizationSupplier::getCreditCode, creditCode);
        wrapper.eq(OrganizationSupplier::getOrganizationId, organizationId);
        if (CommonUtils.isNotEmpty(id)) {
            wrapper.ne(OrganizationSupplier::getId, id);
        }
        return this.count(wrapper) < 1;
    }

    /**
     * @author: baiyiwen
     * @description: 校验名称
     * @date: 2023/3/21
     */
    public Boolean checkName(Long id, String name, Long organizationId) {
        LambdaQueryWrapper<OrganizationSupplier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrganizationSupplier::getName, name);
        wrapper.eq(OrganizationSupplier::getOrganizationId, organizationId);
        if (CommonUtils.isNotEmpty(id)) {
            wrapper.ne(OrganizationSupplier::getId, id);
        }
        return this.count(wrapper) < 1;
    }

    @Override
    public Page<OrganizationSupplierVo> queryOrganizationSupplierPage(QueryOrganizationSupplierParam param) {
        if (CommonUtils.isEmpty(param)) {
            throw new ParamException("参数异常");
        }
        if (!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))) {
            throw new BizException("组织无权限");
        } else if (OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))) {
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        Page page = new Page<>(param.getPage(), param.getSize());
        List<OrganizationSupplierVo> list = this.baseMapper.queryOrganizationSupplierPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public OrganizationSupplierVo getOrganizationSupplierById(Long id) {
        OrganizationSupplierVo vo = this.baseMapper.getOrganizationSupplierById(id);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableOrganizationSupplier(DisableParam param) {
        List<Long> ids = param.getIds();
        if (CommonUtils.isNotEmpty(ids)) {
            ids.forEach(id -> {
                OrganizationSupplier byId = this.getById(id);
                this.lambdaUpdate().eq(OrganizationSupplier::getId, id)
                        .set(OrganizationSupplier::getUsableState, param.getUsableState()).update(new OrganizationSupplier());
                Organization organization = organizationService.getById(byId.getOrganizationId());
                //增加系统操作日志
                String type = LogTypeEnum.ENABLE.getCode();
                if (TrueEnum.FALSE.getCode().equals(param.getUsableState())) {
                    type = LogTypeEnum.DISABLE.getCode();
                }
                logService.save(Log.builder().moduleName(LogModuleEnum.SUPPLIER.getCode()).type(type)
                        .logInfo(organization.getName() + LogTypeEnum.getDesc(type) + "供应商" + byId.getName()).build());
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindOrganizationSupplier(String serialNo, Long organizationId) {
        if (CommonUtils.isEmpty(serialNo) || CommonUtils.isEmpty(organizationId)) {
            throw new BizException("操作失败，缺少必填参数");
        }
        Organization organization = organizationService.getById(organizationId);
        if (CommonUtils.isEmpty(organization)) {
            throw new BizException("组织不存在");
        }
        if (CommonUtils.isEmpty(organization.getInventoryDate())) {
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        //获取公域供应商数据
        List<Organization> supplierList = organizationService.lambdaQuery().eq(Organization::getSerialNo, serialNo).eq(Organization::getUsableState, TrueEnum.TRUE.getCode())
                .eq(Organization::getType, OrganizationEnum.SUPPLIER_ORG.getCode()).list();
        if (CommonUtils.isEmpty(supplierList)) {
            throw new BizException("操作失败，此邀请码未找到符合条件的供应商");
        } else if (supplierList.size() > 1) {
            throw new BizException("操作失败，此邀请码存在多个供应商");
        }
        Organization supplier = organizationService.getById(supplierList.get(0).getId());

        List<OrganizationSupplier> organizationSupplierList = this.lambdaQuery()
                .eq(OrganizationSupplier::getOrganizationId, organizationId)
                .eq(OrganizationSupplier::getSupplierId, supplier.getId()).list();
        if (CommonUtils.isNotEmpty(organizationSupplierList)) {
            throw new BizException("操作失败，供应商已绑定");
        }
        if (!checkCode(null, supplier.getCreditCode(), organization.getId())) {
            throw new BizException("操作失败，统一信用代码重复");
        }
        if (!checkName(null, supplier.getName(), organization.getId())) {
            throw new BizException("操作失败，名称重复");
        }

        OrganizationSupplier organizationSupplier = new OrganizationSupplier();
        cn.hutool.core.bean.BeanUtil.copyProperties(supplier, organizationSupplier);
        organizationSupplier.setId(null);
        organizationSupplier.setOrganizationId(organization.getId());
        organizationSupplier.setOrganizationType(organization.getType());
        organizationSupplier.setSupplierId(supplier.getId());
        //默认设置单据状态为审核中
        organizationSupplier.setDocumentStatus(DocumentStatusEnum.REVIEWING.getCode());
        this.save(organizationSupplier);
        //增加系统操作日志
        logService.save(Log.builder().moduleName(LogModuleEnum.SUPPLIER.getCode()).type(LogTypeEnum.INSERT.getCode())
                .logInfo(organization.getName() + "绑定供应商" + organizationSupplier.getName()).build());

    }

    @Override
    public List<OrganizationSupplierExportVo> queryOrganizationSupplierExportList(QueryOrganizationSupplierParam param) {
        if (!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))) {
            throw new BizException("组织无权限");
        } else if (OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))) {
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        return this.baseMapper.queryOrganizationSupplierExportList(param);
    }

    /**
     * 更新 审批状态（documentStatus）
     *
     * @param id
     * @param status
     */
    @Override
    public void updateApprovalStatus(Long id, DocumentStatusEnum status) {
        OrganizationSupplier organizationSupplier = this.getById(id);
        if (CommonUtils.isEmpty(organizationSupplier)) {
            throw new BizException("操作失败，供应商不存在");
        }
        organizationSupplier.setDocumentStatus(status.getCode());
        this.updateById(organizationSupplier);
    }

    @Override
    public String getOrganizationSupplierNameById(Long id) {
        OrganizationSupplier organizationSupplier = baseMapper.selectById(id);
        if (organizationSupplier != null) {
            return organizationSupplier.getName();
        }
        return null;
    }
}
