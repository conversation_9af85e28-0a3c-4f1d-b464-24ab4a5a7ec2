package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivity;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityGoods;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityTaskDate;
import com.dsj.inventory.bussiness.inventory.enumeration.SalesActivityStateEnum;
import com.dsj.inventory.bussiness.inventory.mapper.SalesActivityMapper;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesActivityParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateSalesActivityParam;
import com.dsj.inventory.bussiness.inventory.service.SalesActivityGoodsService;
import com.dsj.inventory.bussiness.inventory.service.SalesActivityService;
import com.dsj.inventory.bussiness.inventory.service.SalesActivityTaskCycleService;
import com.dsj.inventory.bussiness.inventory.service.SalesActivityTaskDateService;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityGoodsVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskCycleVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityVo;
import com.dsj.inventory.common.constant.CacheKey;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.entity.StrPool;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.common.utils.DozerUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.enumeration.MedicineEnum;
import com.pocky.transport.bussiness.diagnose.enumeration.ModuleEnum;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.hospital.entity.BusinessAreaMedicine;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.service.BusinessAreaMedicineService;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import com.dsj.inventory.common.cache.RedisRepositoryImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 进销存销售活动 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@Slf4j
@Service
public class SalesActivityServiceImpl extends ServiceImpl<SalesActivityMapper, SalesActivity> implements SalesActivityService {

    @Autowired
    private DozerUtils dozerUtils;
    @Autowired
    private SalesActivityGoodsService salesActivityGoodsService;
    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private BusinessAreaMedicineService businessAreaMedicineService;
    @Autowired
    private SalesActivityTaskDateService salesActivityTaskDateService;
    @Autowired
    private SalesActivityTaskCycleService salesActivityTaskCycleService;
    @Autowired
    private RedisRepositoryImpl redisRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalesActivityVo saveOrUpdateSalesActivity(SaveOrUpdateSalesActivityParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(CommonUtils.isNotEmpty(param.getId())) {
            SalesActivity byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            //删除商品明细
            salesActivityGoodsService.lambdaUpdate().eq(SalesActivityGoods::getActivityId,param.getId())
                    .set(SalesActivityGoods::getIsDelete, TrueEnum.TRUE.getCode()).update(new SalesActivityGoods());
            if (CommonUtils.isNotEmpty(param.getIsDelete()) && TrueEnum.TRUE.getCode().equals(param.getIsDelete())){
                this.baseMapper.deleteById(param.getId());
                return null;
            }
        }

        if(CommonUtils.isNotEmpty(param.getGoodsList())){
            List<Long> goodsIdList = param.getGoodsList().stream().map(g -> g.getOrganizationMedicineId()).distinct().collect(Collectors.toList());
            if(goodsIdList.size() != param.getGoodsList().size()){
                throw new BizException("操作失败，存在重复商品");
            }
            if(ModuleEnum.SHOPPING.getCode().equals(param.getModuleType())){
                //判断是否包含中药
                Integer count = organizationMedicineMappingService.lambdaQuery()
                        .in(OrganizationMedicineMapping::getId,goodsIdList)
                        .eq(OrganizationMedicineMapping::getMedicineType, MedicineEnum.CMEDICINE.getCode())
                        .count();
                if(count > 0){
                    throw new BizException("操作失败，中药类型商品无法参与线上商城活动");
                }
            }
        }
        //判断活动状态
        LocalDateTime now = LocalDateTime.now();
        boolean startFlag = DateUtils.dateCompare(DateUtils.format(now,DateUtils.DEFAULT_DATE_TIME_FORMAT)
                ,DateUtils.format(param.getStartDate(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
        if(startFlag){
            param.setActivityState(SalesActivityStateEnum.NOT_STARTED.getCode());
            param.setAssertState(TrueEnum.FALSE.getCode());
        }else {
            boolean endFlag = DateUtils.dateCompare(DateUtils.format(now,DateUtils.DEFAULT_DATE_TIME_FORMAT)
                    ,DateUtils.format(param.getEndDate(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
            if(endFlag){
                param.setActivityState(SalesActivityStateEnum.IN_PROGRESS.getCode());
                param.setAssertState(TrueEnum.TRUE.getCode());
            }else {
                param.setActivityState(SalesActivityStateEnum.FINISH.getCode());
                param.setAssertState(TrueEnum.FALSE.getCode());
            }
        }
        SalesActivity salesActivity = dozerUtils.map(param,SalesActivity.class);
        this.saveOrUpdate(salesActivity);
        if(CommonUtils.isNotEmpty(param.getGoodsList())){
            param.getGoodsList().stream().map(g -> g.setActivityId(salesActivity.getId())).collect(Collectors.toList());
            List<SalesActivityGoods> goodsList = dozerUtils.mapList(param.getGoodsList(),SalesActivityGoods.class);
            salesActivityGoodsService.saveBatch(goodsList);
        }
        return this.getSalesActivityById(salesActivity.getId());
    }

    @Override
    public Page<SalesActivityVo> querySalesActivityPage(QuerySalesActivityParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<SalesActivityVo> list = this.baseMapper.querySalesActivityPage(page, param);
        if(CommonUtils.isNotEmpty(param.getContainGoodsList()) && TrueEnum.TRUE.getCode().equals(param.getContainGoodsList())){
            for(SalesActivityVo vo : list){
                List<SalesActivityGoods> goodsList = salesActivityGoodsService.lambdaQuery()
                        .eq(SalesActivityGoods::getActivityId,vo.getId())
                        .select(SalesActivityGoods::getId,SalesActivityGoods::getOrganizationMedicineId,SalesActivityGoods::getSalesAmount).list();
                if(CommonUtils.isNotEmpty(goodsList)){
                    vo.setGoodsList(dozerUtils.mapList(goodsList,SalesActivityGoodsVo.class));
                }
            }

        }
        return page.setRecords(list);
    }

    @Override
    public SalesActivityVo getSalesActivityById(Long id) {
        SalesActivityVo vo = this.baseMapper.getSalesActivityById(id);
        if(CommonUtils.isNotEmpty(vo)){
            List<SalesActivityGoodsVo> goodsList = salesActivityGoodsService.querySalesActivityGoodsList(vo.getId());
            if(CommonUtils.isNotEmpty(goodsList)){
                if(CommonUtils.isNotEmpty(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID))){
                    Organization organization = organizationService.getById(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
                    if(CommonUtils.isNotEmpty(organization) && (CommonUtils.isNotEmpty(organization.getCounty()) || CommonUtils.isNotEmpty(organization.getCity()) || CommonUtils.isNotEmpty(organization.getProvince()))){
                        for(SalesActivityGoodsVo goods : goodsList){
                            //显示批发价格
//                            BusinessAreaMedicine businessAreaMedicine = businessAreaMedicineService.queryMedicinePrice(goods.getOrganizationMedicineId(),organization.getCounty(),organization.getCity(),organization.getProvince());
//                            if(CommonUtils.isNotEmpty(businessAreaMedicine)){
//                                if(OrganizationEnum.HOSPITAL.getCode().equals(organization.getType()) || OrganizationEnum.SUPPLIER_ORG.getCode().equals(organization.getType())){
//                                    goods.setWholesalePrice(businessAreaMedicine.getMonomerPrice());
//                                }else if(OrganizationEnum.COMPANY.getCode().equals(organization.getType())){
//                                    goods.setWholesalePrice(businessAreaMedicine.getChainPrice());
//                                }else if(OrganizationEnum.DRUGSTORE.getCode().equals(organization.getType())){
//                                    if(CommonUtils.isEmpty(organization.getParentId())){
//                                        goods.setWholesalePrice(businessAreaMedicine.getMonomerPrice());
//                                    }else {
//                                        goods.setWholesalePrice(businessAreaMedicine.getChainPrice());
//                                    }
//                                }
//                            }
                        }
                    }
                }
                vo.setGoodsList(goodsList);
            }
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pauseSalesActivity(Long id) {
        SalesActivity salesActivity = this.getById(id);
        if(CommonUtils.isEmpty(salesActivity)){
            throw new BizException("操作失败，活动不存在");
        }
        if(SalesActivityStateEnum.IN_PROGRESS.getCode().equals(salesActivity.getActivityState())){
            //暂停活动
            this.lambdaUpdate().set(SalesActivity::getActivityState, SalesActivityStateEnum.PAUSE.getCode())
                    .set(SalesActivity::getAssertState,TrueEnum.FALSE.getCode())
                    .eq(SalesActivity::getId,id).update(new SalesActivity());
            //删除任务redis
            redisRepository.del(CacheKey.ACTIVITY_TASK_ASSERT_STATE + StrPool.COLON + id);
        }else if(SalesActivityStateEnum.PAUSE.getCode().equals(salesActivity.getActivityState())){
            Integer assertState = TrueEnum.TRUE.getCode();
            //判断活动生效状态
            int dayOfWeek = LocalDate.now().getDayOfWeek().getValue();
            List<SalesActivityTaskCycleVo> allCycleList = salesActivityTaskCycleService
                    .queryCycleListByActivityId(id,null,TrueEnum.TRUE.getCode());
            if(CommonUtils.isNotEmpty(allCycleList)){
                assertState = TrueEnum.FALSE.getCode();
                for(SalesActivityTaskCycleVo cycle : allCycleList){
                    if(dayOfWeek == cycle.getWeek()){
                        int nowBefore = LocalTime.now().compareTo(DateUtils.LocalTimeFormat(cycle.getStartTime(),null));
                        int nowAfter = LocalTime.now().compareTo(DateUtils.LocalTimeFormat(cycle.getEndTime(),null));
                        if(nowBefore >= 0 && nowAfter < 0){
                            //当前时间存在任务周期
                            assertState = TrueEnum.TRUE.getCode();
                            break;
                        }
                    }
                }
            }
            //启动活动
            this.lambdaUpdate().set(SalesActivity::getActivityState, SalesActivityStateEnum.IN_PROGRESS.getCode())
                    .set(SalesActivity::getAssertState,assertState)
                    .eq(SalesActivity::getId,id).update(new SalesActivity());
            if(CommonUtils.isNotEmpty(allCycleList)) {
                SalesActivityTaskDate taskDate = salesActivityTaskDateService.lambdaQuery()
                        .eq(SalesActivityTaskDate::getActivityId, id)
                        .gt(SalesActivityTaskDate::getTaskDate, LocalDateTime.now())
                        .orderByAsc(SalesActivityTaskDate::getTaskDate)
                        .last("limit 1").one();
                if (CommonUtils.isNotEmpty(taskDate)) {
                    //更新活动下一次生效状态变更时间
                    long seconds = LocalDateTime.now().until(taskDate.getTaskDate(), ChronoUnit.SECONDS);
                    redisRepository.setExpire(CacheKey.ACTIVITY_TASK_ASSERT_STATE + StrPool.COLON + id, taskDate.getAssertState(), seconds);
                }
            }
        }
    }

    @Override
    public void stopSalesActivity(Long id) {
        this.lambdaUpdate().set(SalesActivity::getActivityState, SalesActivityStateEnum.FINISH.getCode())
                .set(SalesActivity::getAssertState,TrueEnum.FALSE.getCode())
                .eq(SalesActivity::getId,id).update(new SalesActivity());
    }

    @Override
    public void batchHandleSalesActivityState() {
        LocalDateTime now = LocalDateTime.now();
        //批量更新未开始--->进行中
        this.lambdaUpdate().set(SalesActivity::getActivityState, SalesActivityStateEnum.IN_PROGRESS.getCode())
                .set(SalesActivity::getAssertState,TrueEnum.TRUE.getCode())
                .eq(SalesActivity::getActivityState,SalesActivityStateEnum.NOT_STARTED.getCode())
                .le(SalesActivity::getStartDate,now)
                .update(new SalesActivity());
        log.info("{}销售活动开启状态变更完毕",DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
        //批量更新已结束
        this.lambdaUpdate().set(SalesActivity::getActivityState, SalesActivityStateEnum.FINISH.getCode())
                .set(SalesActivity::getAssertState,TrueEnum.FALSE.getCode())
                .ne(SalesActivity::getActivityState, SalesActivityStateEnum.FINISH.getCode())
                .le(SalesActivity::getEndDate,now)
                .update(new SalesActivity());
        log.info("{}销售活动结束状态变更完毕",DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
        //处理进行中活动因为任务产生的生效状态redis变更
//        List<SalesActivity> activityList = this.lambdaQuery().eq(SalesActivity::getModuleType,ModuleEnum.WHOLESALE.getCode())
//                .eq(SalesActivity::getActivityState, SalesActivityStateEnum.IN_PROGRESS.getCode()).list();
//        if(CommonUtils.isNotEmpty(activityList)){
//            for(SalesActivity activity : activityList){
//                SalesActivityTaskDate taskDate = salesActivityTaskDateService.lambdaQuery()
//                        .eq(SalesActivityTaskDate::getActivityId,activity.getId())
//                        .gt(SalesActivityTaskDate::getTaskDate,LocalDateTime.now())
//                        .orderByAsc(SalesActivityTaskDate::getTaskDate)
//                        .last("limit 1").one();
//                if(CommonUtils.isNotEmpty(taskDate)){
//                    //更新活动下一次生效状态变更时间
//                    long seconds = LocalDateTime.now().until(taskDate.getTaskDate(), ChronoUnit.SECONDS);
//                    redisRepository.setExpire(CacheKey.ACTIVITY_TASK_ASSERT_STATE + StrPool.COLON + activity.getId(), taskDate.getAssertState(), seconds);
//                }
//            }
//        }
    }
}
