package com.dsj.inventory.common.generator;

/**
 * 编号类型枚举
 * 定义系统中所有的编号类型
 */
public enum NumberType {
    /**
     * 供应商首营单据编号
     */
    SUPPLIER_FIRST_DOCUMENT("SF", "供应商首营单据编号"),

    /**
     * 供应商编号（审核通过后分配）
     */
    SUPPLIER_CODE("SC", "供应商编号"),

    /**
     * 采购订单编号
     */
    PURCHASE_ORDER("PO", "采购订单编号"),

    /**
     * 采购计划编号
     */
    PURCHASE_PLAN("PP", "采购计划编号"),

    /**
     * 退货编号
     */
    RETURN_ORDER("RO", "退货编号"),

    /**
     * 拒收单编号
     */
    REJECTION_ORDER("JS", "拒收单编号"),

    /**
     * 批号
     */
    BATCH_NUMBER("PC", "批号(入库单)");

    /**
     * 编号前缀
     */
    private final String prefix;
    
    /**
     * 编号类型描述
     */
    private final String description;

    NumberType(String prefix, String description) {
        this.prefix = prefix;
        this.description = description;
    }

    public String getPrefix() {
        return prefix;
    }

    public String getDescription() {
        return description;
    }
} 