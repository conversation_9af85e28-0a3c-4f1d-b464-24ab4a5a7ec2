package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 进销存入库单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_incoming_order")
@ApiModel(value="IncomingOrder", description="进销存入库单")
public class IncomingOrder extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织Id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "验收单Id")
    @TableField("order_id")
    private Long orderId;

    @ApiModelProperty(value = "验收单号")
    @TableField("order_code")
    private String orderCode;

    @ApiModelProperty(value = "入库单号")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "开票日期")
    @TableField("bill_date")
    private LocalDateTime billDate;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "处理状态 0暂存 1已完成")
    @TableField("deal_state")
    private Integer dealState;

    @ApiModelProperty(value = "采购员Id")
    @TableField("purchaser_id")
    private Long purchaserId;

    @ApiModelProperty(value = "验收员Id")
    @TableField("accept_id")
    private Long acceptId;

    @ApiModelProperty(value = "入库员Id")
    @TableField("incoming_id")
    private Long incomingId;

    @ApiModelProperty(value = "私域供应商Id")
    @TableField("organization_supplier_id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "总金额")
    @TableField("total_price")
    private Integer totalPrice;

    @ApiModelProperty(value = "折后金额")
    @TableField("discount_total_price")
    private Integer discountTotalPrice;

    @ApiModelProperty(value = "承运单位")
    @TableField("carrier")
    private String carrier;

    @ApiModelProperty(value = "起运地址")
    @TableField("start_address")
    private String startAddress;

    @ApiModelProperty(value = "起运时间")
    @TableField("start_date")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "承运方式")
    @TableField("transport_way")
    private String transportWay;

    @ApiModelProperty(value = "运输工具")
    @TableField("transport_tool")
    private String transportTool;

    @ApiModelProperty(value = "发票日期")
    @TableField("invoice_date")
    private LocalDateTime invoiceDate;

    @ApiModelProperty(value = "发票代码")
    @TableField("invoice_code")
    private String invoiceCode;

    @ApiModelProperty(value = "发票金额")
    @TableField("invoice_price")
    private Integer invoicePrice;

    @ApiModelProperty(value = "发票号")
    @TableField("invoice_number")
    private String invoiceNumber;

    @ApiModelProperty(value = "收货运单号")
    @TableField("receive_order_number")
    private String receiveOrderNumber;

    @ApiModelProperty(value = "随货同行单日期")
    @TableField("follow_goods_order_date")
    private LocalDateTime followGoodsOrderDate;

    @ApiModelProperty(value = "到货日期")
    @TableField("arrival_date")
    private LocalDateTime arrivalDate;

    @ApiModelProperty(value = "到货温度")
    @TableField("arrival_temp")
    private String arrivalTemp;

    @ApiModelProperty(value = "运输温湿度记录")
    @TableField("transport_humidity_record")
    private String transportHumidityRecord;

    @ApiModelProperty(value = "是否冷藏 0否1是")
    @TableField("refrigerate_state")
    private Integer refrigerateState;

    @ApiModelProperty(value = "批发企业业务员编号")
    @TableField("wholesaler_staff_number")
    private String wholesalerStaffNumber;

    @ApiModelProperty(value = "发票照片")
    @TableField("invoice_path")
    private String invoicePath;

    @ApiModelProperty(value = "随货同行单照片")
    @TableField("follow_goods_order_path")
    private String followGoodsOrderPath;

    @ApiModelProperty(value = "质检报告照片")
    @TableField("check_report_path")
    private String checkReportPath;

    @ApiModelProperty(value = "商品种类")
    @TableField("goods_types")
    private String goodsTypes;

    @ApiModelProperty(value = "采购内容")
    @TableField("purchase_content")
    private String purchaseContent;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
