package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 药品拉取模板药品信息
 * <AUTHOR>
 * @date 2023/03/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MedicineTemplateMedicineVo", description="药品拉取模板药品信息实体")
@ToString(callSuper = true)
public class MedicineTemplateMedicineVo {


    @ApiModelProperty(value = "商品id")
    private Long medicineId;

    @ApiModelProperty(value = "通用名称")
    private String name;

    @ApiModelProperty(value = "包装单位(西)")
    private String packUnit;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "剂型")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(value = "国家医保编码")
    private String insuranceCode;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
