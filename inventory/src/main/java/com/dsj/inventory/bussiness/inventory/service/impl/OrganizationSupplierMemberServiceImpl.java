package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationSupplier;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationSupplierMember;
import com.dsj.inventory.bussiness.inventory.mapper.OrganizationSupplierMemberMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryOrganizationSupplierMemberParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateOrganizationSupplierMemberParam;
import com.dsj.inventory.bussiness.inventory.service.OrganizationSupplierMemberService;
import com.dsj.inventory.bussiness.inventory.service.OrganizationSupplierService;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationSupplierMemberVo;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import cn.hutool.core.bean.BeanUtil;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.auth.entity.Log;
import com.pocky.transport.bussiness.auth.enumeration.LogModuleEnum;
import com.pocky.transport.bussiness.auth.enumeration.LogTypeEnum;
import com.pocky.transport.bussiness.auth.service.ILogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商组织私域销售人员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-24
 */
@Service
public class OrganizationSupplierMemberServiceImpl extends ServiceImpl<OrganizationSupplierMemberMapper, OrganizationSupplierMember> implements OrganizationSupplierMemberService {

    @Autowired
    private ILogService logService;
    @Autowired
    private OrganizationSupplierService organizationSupplierService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrganizationSupplierMemberVo saveOrUpdateOrganizationSupplierMember(SaveOrUpdateOrganizationSupplierMemberParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        String type = LogTypeEnum.INSERT.getCode();
        if(CommonUtils.isNotEmpty(param.getId())) {
            OrganizationSupplierMember byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            type = LogTypeEnum.UPDATE.getCode();
            if (CommonUtils.isNotEmpty(param.getIsDelete()) && TrueEnum.TRUE.getCode().equals(param.getIsDelete())){
                this.baseMapper.deleteById(param.getId());
                OrganizationSupplier organizationSupplier = organizationSupplierService.getById(byId.getOrganizationSupplierId());
                //增加系统操作日志
                logService.save(Log.builder().moduleName(LogModuleEnum.SUPPLIER.getCode()).type(LogTypeEnum.DELETE.getCode())
                        .logInfo(organizationSupplier.getName() + LogTypeEnum.DELETE.getDesc() + "销售人员" + byId.getName()).build());
                return null;
            }

        }
        if(CommonUtils.isEmpty(param.getOrganizationSupplierId())){
            throw new BizException("操作失败，未获取到供应商Id");
        }
        OrganizationSupplier organizationSupplier = organizationSupplierService.getById(param.getOrganizationSupplierId());
        if(CommonUtils.isEmpty(organizationSupplier)){
            throw new BizException("操作失败，供应商不存在");
        }

        OrganizationSupplierMember organizationSupplierMember = BeanUtil.toBean(param, OrganizationSupplierMember.class);
        this.saveOrUpdate(organizationSupplierMember);
        //增加系统操作日志
        logService.save(Log.builder().moduleName(LogModuleEnum.SUPPLIER.getCode()).type(type)
                .logInfo(organizationSupplier.getName() + LogTypeEnum.getDesc(type) + "销售人员" + param.getName()).build());
        return BeanUtil.toBean(this.getById(organizationSupplierMember.getId()), OrganizationSupplierMemberVo.class);
    }

    @Override
    public Page<OrganizationSupplierMemberVo> queryOrganizationSupplierMemberPage(QueryOrganizationSupplierMemberParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<OrganizationSupplierMemberVo> list = this.baseMapper.queryOrganizationSupplierMemberPage(page, param);
        return page.setRecords(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<OrganizationSupplierMemberVo> batchChangeOrganizationSupplierMember(Long organizationSupplierId, List<SaveOrUpdateOrganizationSupplierMemberParam> paramList) {
        if(CommonUtils.isEmpty(organizationSupplierId)){
            throw new BizException("操作失败，供应商Id不存在");
        }
        OrganizationSupplier organizationSupplier = organizationSupplierService.getById(organizationSupplierId);
        if(CommonUtils.isEmpty(organizationSupplier)){
            throw new BizException("操作失败，供应商不存在");
        }
        Page page = new Page<>(1,Integer.MAX_VALUE);
        QueryOrganizationSupplierMemberParam queryParam = QueryOrganizationSupplierMemberParam.builder()
                .organizationSupplierId(organizationSupplierId).createTimeOrder("asc").build();
        //原有销售人员
        List<OrganizationSupplierMember> oldMemberList = this.lambdaQuery().eq(OrganizationSupplierMember::getOrganizationSupplierId,organizationSupplierId).list();
        if(CommonUtils.isEmpty(paramList) && CommonUtils.isNotEmpty(oldMemberList)){
            this.lambdaUpdate().eq(OrganizationSupplierMember::getOrganizationSupplierId,organizationSupplierId)
                    .set(OrganizationSupplierMember::getIsDelete,TrueEnum.TRUE.getCode()).update(new OrganizationSupplierMember());
            //增加系统操作日志
            String names = String.join(",",oldMemberList.stream().map(m -> m.getName()).collect(Collectors.toList()));
            logService.save(Log.builder().moduleName(LogModuleEnum.SUPPLIER.getCode()).type(LogTypeEnum.DELETE.getCode())
                    .logInfo(organizationSupplier.getName() + LogTypeEnum.DELETE.getDesc() + "销售人员" + names).build());
            return this.baseMapper.queryOrganizationSupplierMemberPage(page, queryParam);
        }
        if(CommonUtils.isNotEmpty(paramList)){
            paramList.stream().forEach(p -> p.setOrganizationSupplierId(organizationSupplierId));
            //变动的销售人员
            List<OrganizationSupplierMember> newMemberList = paramList.stream()
                                                                   .map(item -> BeanUtil.toBean(item, OrganizationSupplierMember.class))
                                                                   .collect(Collectors.toList());
            List<OrganizationSupplierMember> updateList = newMemberList.stream().filter(m -> CommonUtils.isNotEmpty(m.getId())).collect(Collectors.toList());
            List<OrganizationSupplierMember> saveList = newMemberList.stream().filter(m -> CommonUtils.isEmpty(m.getId())).collect(Collectors.toList());
            if(CommonUtils.isNotEmpty(updateList)){
                //删除不存在的销售人员
                List<OrganizationSupplierMember> deleteList = oldMemberList.stream().filter(oldMember -> updateList.stream()
                        .allMatch(updateMember -> !oldMember.getId().equals(updateMember.getId()))).collect(Collectors.toList());
                if(CommonUtils.isNotEmpty(deleteList)){
                    this.lambdaUpdate().in(OrganizationSupplierMember::getId,deleteList.stream().map(m -> m.getId()).collect(Collectors.toList()))
                            .set(OrganizationSupplierMember::getIsDelete,TrueEnum.TRUE.getCode()).update(new OrganizationSupplierMember());
                    //增加系统操作日志
                    String names = String.join(",",deleteList.stream().map(m -> m.getName()).collect(Collectors.toList()));
                    logService.save(Log.builder().moduleName(LogModuleEnum.SUPPLIER.getCode()).type(LogTypeEnum.DELETE.getCode())
                            .logInfo(organizationSupplier.getName() + LogTypeEnum.DELETE.getDesc() + "销售人员" + names).build());
                }
            }
            this.saveOrUpdateBatch(newMemberList);
            if(CommonUtils.isNotEmpty(saveList)){
                //增加系统操作日志
                String names = String.join(",",saveList.stream().map(m -> m.getName()).collect(Collectors.toList()));
                logService.save(Log.builder().moduleName(LogModuleEnum.SUPPLIER.getCode()).type(LogTypeEnum.INSERT.getCode())
                        .logInfo(organizationSupplier.getName() + LogTypeEnum.INSERT.getDesc() + "销售人员" + names).build());
            }
            if(CommonUtils.isNotEmpty(updateList)){
                //增加系统操作日志
                String names = String.join(",",updateList.stream().map(m -> m.getName()).collect(Collectors.toList()));
                logService.save(Log.builder().moduleName(LogModuleEnum.SUPPLIER.getCode()).type(LogTypeEnum.UPDATE.getCode())
                        .logInfo(organizationSupplier.getName() + LogTypeEnum.UPDATE.getDesc() + "销售人员" + names).build());
            }
        }
        return this.baseMapper.queryOrganizationSupplierMemberPage(page, queryParam);
    }

}
