package com.dsj.inventory.framework.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 忽略token 配置类
 * <p>
 *
 * <AUTHOR>
 * @date 2021年4月22日20:56:31
 */
@Data
@Configuration
@ConfigurationProperties(prefix = com.dsj.inventory.framework.properties.SwitchProperties.PREFIX)
public class SwitchProperties {

    public static final String PREFIX = "pocky.switch";

    private Boolean giveFreeNumberSwitch = false;

    private Boolean adminNeedVerify = false;

}
