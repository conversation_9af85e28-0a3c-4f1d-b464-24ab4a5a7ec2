package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 会员充值记录
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MemberRechargeRecordVo", description="会员充值记录实体")
@ToString(callSuper = true)
public class MemberRechargeRecordVo {

    private Long id;

    @ApiModelProperty(value = "组织id")
    private Long organizationId;

    @ApiModelProperty(value = "会员Id")
    private Long memberId;

    @ApiModelProperty(value = "支付方式 1现金")
    private Integer payWay;

    @ApiModelProperty(value = "充值类型 1金额")
    private Integer rechargeType;

    @ApiModelProperty(value = "充值金额")
    private Integer rechargePrice;

    @ApiModelProperty(value = "赠送金额")
    private Integer givePrice;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
