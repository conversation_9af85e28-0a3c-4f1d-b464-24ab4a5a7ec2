package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationDict;
import com.dsj.inventory.bussiness.inventory.mapper.OrganizationDictMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryOrganizationDictParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateOrganizationDictParam;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationDictVO;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.test.core.BaseDbUnitTest;
import com.dsj.inventory.framework.test.core.TestContextHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

import java.util.List;

@Import({OrganizationDictServiceImpl.class})
@DisplayName("OrganizationDictService 单元测试")
class OrganizationDictServiceImplTest extends BaseDbUnitTest {

    @Autowired
    private OrganizationDictServiceImpl organizationDictService;

    @Autowired
    private OrganizationDictMapper organizationDictMapper;

    private final Long testUserId = 1L;
    private final Long testOrganizationId = 100L;

    @BeforeEach
    void setUp() {
        // 设置测试上下文
        TestContextHelper.setDefaultUserContext();
    }

    @Test
    @DisplayName("saveOrUpdateOrganizationDict - 创建新字典项 - 成功")
    void testSaveOrganizationDict() {
        // Arrange
        SaveOrUpdateOrganizationDictParam param = new SaveOrUpdateOrganizationDictParam();
        param.setOrganizationId(testOrganizationId);
        param.setDictName("测试字典");
        param.setDictCode("TEST_DICT_SAVE");
        param.setDictType("TEST_TYPE");
        param.setDictValue("测试值");

        // Act
        Long resultId = organizationDictService.saveOrUpdateOrganizationDict(param);

        // Assert
        Assertions.assertNotNull(resultId);
        OrganizationDict savedDict = organizationDictMapper.selectById(resultId);
        Assertions.assertNotNull(savedDict);
        Assertions.assertEquals(param.getDictName(), savedDict.getDictName());
        Assertions.assertEquals(param.getDictCode(), savedDict.getDictCode());
    }

    @Test
    @DisplayName("saveOrUpdateOrganizationDict - 已存在相同组织下同类型同编码的字典项 - 抛出异常")
    void testSaveOrganizationDictWithDuplicateDict() {
        // Arrange
        OrganizationDict existingDict = createOrganizationDict(null, testOrganizationId, "已存在字典", "DUPLICATE_DICT", "TEST_TYPE");
        existingDict.setDictValue("已存在值");
        organizationDictMapper.insert(existingDict);

        SaveOrUpdateOrganizationDictParam param = new SaveOrUpdateOrganizationDictParam();
        param.setOrganizationId(testOrganizationId);
        param.setDictName("测试字典");
        param.setDictCode("DUPLICATE_DICT");
        param.setDictType("TEST_TYPE");
        param.setDictValue("测试值");

        // Act & Assert
        Assertions.assertThrows(BizException.class, () -> {
            organizationDictService.saveOrUpdateOrganizationDict(param);
        });
    }

    @Test
    @DisplayName("saveOrUpdateOrganizationDict - 更新字典项 - 成功")
    void testUpdateOrganizationDict() {
        // Arrange
        OrganizationDict initialDict = createOrganizationDict(null, testOrganizationId, "初始字典", "UPDATE_DICT_CODE", "TEST_TYPE");
        initialDict.setDictValue("初始值");
        organizationDictMapper.insert(initialDict);
        Assertions.assertNotNull(initialDict.getId());

        SaveOrUpdateOrganizationDictParam param = new SaveOrUpdateOrganizationDictParam();
        param.setId(initialDict.getId());
        param.setOrganizationId(testOrganizationId);
        param.setDictName("测试字典-更新");
        param.setDictCode("UPDATE_DICT_CODE");
        param.setDictType("TEST_TYPE");
        param.setDictValue("测试值-更新");

        // Act
        Long resultId = organizationDictService.saveOrUpdateOrganizationDict(param);

        // Assert
        Assertions.assertEquals(initialDict.getId(), resultId);
        OrganizationDict updatedDict = organizationDictMapper.selectById(resultId);
        Assertions.assertNotNull(updatedDict);
        Assertions.assertEquals(param.getDictName(), updatedDict.getDictName());
        Assertions.assertEquals(param.getDictValue(), updatedDict.getDictValue());
    }

    @Test
    @DisplayName("deleteOrganizationDict - 删除不存在子项的字典项 - 成功")
    void testDeleteOrganizationDict() {
        // Arrange
        OrganizationDict dictToDelete = createOrganizationDict(null, testOrganizationId, "待删除字典", "DELETE_DICT_CODE", "TEST_TYPE");
        organizationDictMapper.insert(dictToDelete);
        Long id = dictToDelete.getId();
        Assertions.assertNotNull(id);

        // Act
        Boolean result = organizationDictService.deleteOrganizationDict(id);

        // Assert
        Assertions.assertTrue(result);
        OrganizationDict deletedDict = organizationDictMapper.selectById(id);
        Assertions.assertNull(deletedDict);
    }

    @Test
    @DisplayName("deleteOrganizationDict - 删除不存在的字典项 - 抛出异常")
    void testDeleteOrganizationDictNotExist() {
        // Arrange
        Long id = 999L;

        // Act & Assert
        Assertions.assertThrows(BizException.class, () -> {
            organizationDictService.deleteOrganizationDict(id);
        });
    }

    @Test
    @DisplayName("deleteOrganizationDict - 删除存在子项的字典项 - 抛出异常")
    void testDeleteOrganizationDictWithChildren() {
        // Arrange
        OrganizationDict parentDict = createOrganizationDict(null, testOrganizationId, "父字典", "PARENT_DICT_CODE", "TEST_TYPE");
        organizationDictMapper.insert(parentDict);
        Long parentId = parentDict.getId();
        Assertions.assertNotNull(parentId);

        OrganizationDict childDict = createOrganizationDict(null, testOrganizationId, "子字典", "CHILD_DICT_CODE", "TEST_TYPE");
        childDict.setParentId(parentId);
        organizationDictMapper.insert(childDict);
        Assertions.assertNotNull(childDict.getId());

        // Act & Assert
        BizException exception = Assertions.assertThrows(BizException.class, () -> {
            organizationDictService.deleteOrganizationDict(parentId);
        });
        Assertions.assertEquals("存在子字典项，请先删除子项", exception.getMessage());
    }

    @Test
    @DisplayName("getOrganizationDict - 获取存在的字典项 - 返回VO")
    void testGetOrganizationDict() {
        // Arrange
        OrganizationDict dictToFind = createOrganizationDict(null, testOrganizationId, "查询字典", "GET_DICT_CODE", "TEST_TYPE");
        organizationDictMapper.insert(dictToFind);
        Long id = dictToFind.getId();
        Assertions.assertNotNull(id);

        // Act
        OrganizationDictVO result = organizationDictService.getOrganizationDict(id);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertEquals(dictToFind.getId(), result.getId());
        Assertions.assertEquals(dictToFind.getDictName(), result.getDictName());
        Assertions.assertEquals(dictToFind.getDictCode(), result.getDictCode());
        Assertions.assertEquals(dictToFind.getDictType(), result.getDictType());
    }

    @Test
    @DisplayName("getOrganizationDict - 获取不存在的字典项 - 返回null")
    void testGetOrganizationDictNotExist() {
        // Arrange
        Long id = 999L;

        // Act
        OrganizationDictVO result = organizationDictService.getOrganizationDict(id);

        // Assert
        Assertions.assertNull(result);
    }

    @Test
    @DisplayName("queryOrganizationDictPage - 分页查询字典项 - 返回分页结果")
    void testQueryOrganizationDictPage() {
        // Arrange
        QueryOrganizationDictParam param = new QueryOrganizationDictParam();
        param.setPage(1);
        param.setSize(10);
        param.setOrganizationId(testOrganizationId);
        param.setDictType("PAGE_TEST_TYPE");

        OrganizationDict dict1 = createOrganizationDict(null, testOrganizationId, "分页字典1", "PAGE_DICT_1", "PAGE_TEST_TYPE");
        organizationDictMapper.insert(dict1);
        OrganizationDict dict2 = createOrganizationDict(null, testOrganizationId, "分页字典2", "PAGE_DICT_2", "PAGE_TEST_TYPE");
        organizationDictMapper.insert(dict2);
        organizationDictMapper.insert(createOrganizationDict(null, testOrganizationId + 1, "其他分页字典", "PAGE_DICT_OTHER_ORG", "PAGE_TEST_TYPE"));
        organizationDictMapper.insert(createOrganizationDict(null, testOrganizationId, "其他类型分页字典", "PAGE_DICT_OTHER_TYPE", "OTHER_TYPE"));

        // Act
        Page<OrganizationDictVO> result = organizationDictService.queryOrganizationDictPage(param);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertEquals(2, result.getTotal());
        Assertions.assertEquals(2, result.getRecords().size());

        List<Long> resultIds = result.getRecords().stream().map(OrganizationDictVO::getId).collect(java.util.stream.Collectors.toList());
        Assertions.assertTrue(resultIds.contains(dict1.getId()));
        Assertions.assertTrue(resultIds.contains(dict2.getId()));
    }

    @Test
    @DisplayName("listByOrgAndType - 根据组织ID和字典类型查询列表 - 返回列表")
    void testListByOrgAndType() {
        // Arrange
        Long organizationId = testOrganizationId;
        String dictType = "LIST_TEST_TYPE";

        OrganizationDict dict1 = createOrganizationDict(null, organizationId, "列表字典1", "LIST_DICT_1", dictType);
        organizationDictMapper.insert(dict1);
        OrganizationDict dict2 = createOrganizationDict(null, organizationId, "列表字典2", "LIST_DICT_2", dictType);
        organizationDictMapper.insert(dict2);
        organizationDictMapper.insert(createOrganizationDict(null, organizationId + 1, "其他列表字典", "LIST_DICT_OTHER_ORG", dictType));
        organizationDictMapper.insert(createOrganizationDict(null, organizationId, "其他类型列表字典", "LIST_DICT_OTHER_TYPE", "OTHER_TYPE"));

        // Act
        List<OrganizationDictVO> result = organizationDictService.listByOrgAndType(organizationId, dictType);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertEquals(2, result.size()); // Should be 2 records for the given orgId and dictType
        // Ensure the correct dictionaries are returned, their order might depend on the default order in the service/mapper
        List<Long> resultIds = result.stream().map(OrganizationDictVO::getId).collect(java.util.stream.Collectors.toList());
        Assertions.assertTrue(resultIds.contains(dict1.getId()));
        Assertions.assertTrue(resultIds.contains(dict2.getId()));

        // More specific checks if order is guaranteed
        // For example, if ordered by ID:
        // Assuming dict1.getId() < dict2.getId() if they are inserted in that order and ID is auto-incremented.
        // Or if the service explicitly orders them. The service uses orderByAsc(OrganizationDict::getId).
        OrganizationDictVO vo1 = result.stream().filter(vo -> vo.getId().equals(dict1.getId())).findFirst().orElse(null);
        OrganizationDictVO vo2 = result.stream().filter(vo -> vo.getId().equals(dict2.getId())).findFirst().orElse(null);

        Assertions.assertNotNull(vo1);
        Assertions.assertEquals(dict1.getDictName(), vo1.getDictName());
        Assertions.assertNotNull(vo2);
        Assertions.assertEquals(dict2.getDictName(), vo2.getDictName());
    }

    @Test
    @DisplayName("getByOrgTypeAndCode - 根据组织ID、字典类型和编码查询 - 返回VO")
    void testGetByOrgTypeAndCode() {
        // Arrange
        Long organizationId = testOrganizationId;
        String dictType = "GET_CODE_TYPE";
        String dictCode = "GET_CODE_TARGET";

        OrganizationDict targetDict = createOrganizationDict(null, organizationId, "目标字典", dictCode, dictType);
        organizationDictMapper.insert(targetDict);
        organizationDictMapper.insert(createOrganizationDict(null, organizationId, "其他编码字典", "GET_CODE_OTHER", dictType));

        // Act
        OrganizationDictVO result = organizationDictService.getByOrgTypeAndCode(organizationId, dictType, dictCode);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertEquals(targetDict.getId(), result.getId());
        Assertions.assertEquals(targetDict.getDictName(), result.getDictName());
        Assertions.assertEquals(targetDict.getDictCode(), result.getDictCode());
        Assertions.assertEquals(targetDict.getDictType(), result.getDictType());
    }

    private OrganizationDict createOrganizationDict(Long id, Long organizationId, String name, String code, String type) {
        OrganizationDict dict = new OrganizationDict();
        dict.setId(id);
        dict.setOrganizationId(organizationId);
        dict.setDictName(name);
        dict.setDictCode(code);
        dict.setDictType(type);
        return dict;
    }
} 