package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 销售活动
 * <AUTHOR>
 * @date 2023/04/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateSalesActivityParam", description="新增or编辑 销售活动参数")
public class SaveOrUpdateSalesActivityParam {

    @ApiModelProperty(hidden = true, value = "id,更新是传入")
    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "模块分类，1 云药快 ；2 外部；3 在线医院；4 商城；5进销存")
    private Integer moduleType;

    @ApiModelProperty(value = "活动类别，1单品活动 2会员日活动 3整单活动")
    private Integer activityCategory;

    @ApiModelProperty(value = "活动类型 1逢倍折扣 2满件减 3单品特价 4满元减元")
    private Integer activityType;

    @ApiModelProperty(hidden = true,value = "活动状态 0未开始 1进行中 2已暂停 3已结束")
    private Integer activityState;

    @ApiModelProperty(hidden = true,value = "生效状态 0未生效 1生效中")
    private Integer assertState;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "活动开始时间")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "活动规则json")
    private String rule;

    @ApiModelProperty(value = "适用人群，0全部，其余数字为会员等级")
    private Integer applyTo;

    @ApiModelProperty(value = "商品明细")
    private List<SaveOrUpdateSalesActivityGoodsParam> goodsList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
