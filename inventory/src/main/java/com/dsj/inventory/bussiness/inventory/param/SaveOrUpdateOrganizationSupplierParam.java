package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 供应商组织私域
 * <AUTHOR>
 * @date 2023/03/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateOrganizationSupplierParam", description="新增or编辑 供应商组织私域参数")
public class SaveOrUpdateOrganizationSupplierParam {

    @ApiModelProperty(hidden = true, value = "id,更新是传入")
    private Long id;

    @ApiModelProperty(value = "供应商名称")
    private String name;

    @ApiModelProperty(value = "供应商编码")
    private String code;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(hidden = true,value = "组织分类 医院1 医药公司2  药房3")
    private Integer organizationType;

    @ApiModelProperty(value = "企业类型,{1:生产企业(GMP), 2:经营企业(GSP)}")
    private Integer enterpriseType;

    @ApiModelProperty(value = "注册日期")
    private LocalDateTime regDate;

    @ApiModelProperty(value = "供应商类别，字典")
    private String supplierType;

    @ApiModelProperty(value = "省")
    private Long province;

    @ApiModelProperty(value = "市")
    private Long city;

    @ApiModelProperty(value = "县")
    private Long county;

    @ApiModelProperty(value = "详细地址")
    private String detailedAddress;

    @ApiModelProperty(value = "法定代表人姓名")
    private String legalRepresentative;

    @ApiModelProperty(value = "开户银行")
    private String bank;

    @ApiModelProperty(value = "银行账号")
    private String bankAccount;

    @ApiModelProperty(value = "开户户名")
    private String accountName;

    @ApiModelProperty(value = "经营范围，多个逗号分割，字典")
    private String businessScope;

    @ApiModelProperty(value = "负责人姓名")
    private String principal;

    @ApiModelProperty(value = "联系电话")
    private String telephone;

    @ApiModelProperty(hidden = true,value = "可用状态 0：禁用 1可用")
    private Integer usableState;

    @ApiModelProperty(value = "统一社会信用代码（唯一）")
    private String creditCode;

    @ApiModelProperty(value = "统一社会信用代码有效期，长期为空")
    private LocalDateTime creditDate;

    @ApiModelProperty(value = "营业执照发证机关")
    private String businessPermitOrgan;

    @ApiModelProperty(value = "营业执照照片")
    private String businessPermitPath;

    @ApiModelProperty(value = "药品生产许可证")
    private String medicineProducePermit;

    @ApiModelProperty(value = "药品生产许可证注册日期")
    private LocalDateTime medicineProducePermitDate;

    @ApiModelProperty(value = "药品生产许可证照片")
    private String medicineProducePermitPath;

    @ApiModelProperty(value = "食品经营许可证")
    private String foodManagePermit;

    @ApiModelProperty(value = "食品经营许可证注册日期")
    private LocalDateTime foodManagePermitDate;

    @ApiModelProperty(value = "食品经营许可证照片")
    private String foodManagePermitPath;

    @ApiModelProperty(value = "药品经营许可证")
    private String medicineManagePermit;

    @ApiModelProperty(value = "药品经营许可证发证机关")
    private String medicineManagePermitOrgan;

    @ApiModelProperty(value = "药品经营许可证注册日期")
    private LocalDateTime medicineManagePermitRegDate;

    @ApiModelProperty(value = "药品经营许可证有效期")
    private LocalDateTime medicineManagePermitUseDate;

    @ApiModelProperty(value = "药品经营许可证照片")
    private String medicineManagePermitPath;

    @ApiModelProperty(value = "药品经营许可范围，多个逗号分割，字典")
    private String medicineBusinessScope;

    @ApiModelProperty(value = "卫生许可证")
    private String hygienePermit;

    @ApiModelProperty(value = "卫生许可证注册日期")
    private LocalDateTime hygienePermitDate;

    @ApiModelProperty(value = "卫生许可证照片")
    private String hygienePermitPath;

    @ApiModelProperty(value = "质量保证协议")
    private String qualityAssuranceAgreement;

    @ApiModelProperty(value = "质量保证协议注册日期")
    private LocalDateTime qualityAssuranceAgreementDate;

    @ApiModelProperty(value = "质量保证协议照片")
    private String qualityAssuranceAgreementPath;

    @ApiModelProperty(value = "GSP证书")
    private String gsp;

    @ApiModelProperty(value = "GSP发证机关")
    private String gspOrgan;

    @ApiModelProperty(value = "GSP注册日期")
    private LocalDateTime gspRegDate;

    @ApiModelProperty(value = "GSP有效期")
    private LocalDateTime gspUseDate;

    @ApiModelProperty(value = "GSP照片")
    private String gspPath;

    @ApiModelProperty(value = "GMP证书")
    private String gmp;

    @ApiModelProperty(value = "GMP注册日期")
    private LocalDateTime gmpDate;

    @ApiModelProperty(value = "GMP照片")
    private String gmpPath;

    @ApiModelProperty(value = "食品流通许可证")
    private String foodCirculatePermit;

    @ApiModelProperty(value = "食品流通许可证注册日期")
    private LocalDateTime foodCirculatePermitDate;

    @ApiModelProperty(value = "食品流通许可证照片")
    private String foodCirculatePermitPath;

    @ApiModelProperty(value = "医疗器械经营许可证")
    private String medicalApparatusManagePermit;

    @ApiModelProperty(value = "医疗器械经营许可证注册日期")
    private LocalDateTime medicalApparatusManagePermitDate;

    @ApiModelProperty(value = "医疗器械经营许可证照片")
    private String medicalApparatusManagePermitPath;

    @ApiModelProperty(value = "二类器械备案凭证")
    private String secondApparatusPermit;

    @ApiModelProperty(value = "二类器械备案凭证注册日期")
    private LocalDateTime secondApparatusPermitDate;

    @ApiModelProperty(value = "二类器械备案凭证照片")
    private String secondApparatusPermitPath;

    @ApiModelProperty(value = "单据编号")
    private String documentNumber;

    @ApiModelProperty(value = "填报部门")
    private String reportingDepartment;

    @ApiModelProperty(value = "单据状态")
    private Integer documentStatus;

    @ApiModelProperty(value = "填报日期")
    private LocalDateTime filingDate;

    @ApiModelProperty(value = "审批日期")
    private LocalDateTime approvalDate;

    @ApiModelProperty(value = "转正日期")
    private LocalDateTime regularDate;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
