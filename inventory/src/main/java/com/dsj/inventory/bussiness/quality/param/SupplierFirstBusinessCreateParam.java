package com.dsj.inventory.bussiness.quality.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "SupplierFirstBusinessCreateParam", description = "供应商首营审批创建参数")
public class SupplierFirstBusinessCreateParam {

    @ApiModelProperty(value = "关联 jxc_organization_supplier 表ID，表示审批的供应商", required = true)
    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;

    @ApiModelProperty(value = "申请时间 (不填则默认为当前时间)")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "审批状态 (不填则默认为0-待审批)")
    private Integer status;

    @ApiModelProperty(value = "审批意见")
    private String approvalOpinion;

    @ApiModelProperty(value = "备注")
    private String remark;
} 