package com.dsj.inventory.common.context;

/**
 * 常量工具类
 *
 * <AUTHOR>
 * @date 2018/12/21
 */
public class BaseContextConstants {

    /**
     * JWT中封装的 用户id
     */
    public static final String JWT_KEY_USER_ID = "userid";
    /**
     * JWT中封装的 用户名称
     */
    public static final String JWT_KEY_NAME = "name";
    /**
     * JWT中封装的 用户类型
     */
    public static final String JWT_KEY_USER_TYPE = "userType";
    /**
     * JWT中封装的 token 类型
     */
    public static final String JWT_KEY_TOKEN_TYPE = "token_type";
    /**
     * JWT中封装的 用户账号
     */
    public static final String JWT_KEY_ACCOUNT = "account";

    /**
     * JWT中封装的 客户端id
     */
    public static final String JWT_KEY_CLIENT_ID = "clientId";

    /**
     * JWT中封装的 客户端id
     */
    public static final String JWT_KEY_CLIENT_NAME = "client_name";
    /**
     * 外部认证请求头类型
     */
    public static final String EXTERNAL_BEARER_HEADER_KEY = "external_token";

    /**
     * JWT token 签名
     */
    public static final String JWT_SIGN_KEY = "pocky";

    /**
     * JWT中封装的 租户编码
     */
    public static final String JWT_KEY_TENANT = "tenant";
    /**
     * 刷新 Token
     */
    public static final String REFRESH_TOKEN_KEY = "refresh_token";

    /**
     * User信息 认证请求头
     */
    public static final String BEARER_HEADER_KEY = "token";
    /**
     * User信息 认证请求头前缀
     */
    public static final String BEARER_HEADER_PREFIX = "Bearer ";

    /**
     * User信息 认证请求头前缀
     */
    public static final String BEARER_HEADER_PREFIX_EXT = "Bearer%20";

    /**
     * Client信息认证请求头
     */
    public static final String BASIC_HEADER_KEY = "Authorization";

    /**
     * Client信息认证请求头前缀
     */
    public static final String BASIC_HEADER_PREFIX = "Basic ";

    /**
     * Client信息认证请求头前缀
     */
    public static final String BASIC_HEADER_PREFIX_EXT = "Basic%20";

    public static final String APPLY_KEY = "apply_key";


    /**
     * 是否boot项目
     */
    public static final String IS_BOOT = "boot";

    /**
     * 日志链路追踪id信息头
     */
    public static final String TRACE_ID_HEADER = "x-trace-header";
    /**
     * 日志链路追踪id日志标志
     */
    public static final String LOG_TRACE_ID = "trace";

    /**
     * 租户 编码
     */
//    @Deprecated
//    public static final String TENANT = JWT_KEY_TENANT;

    /**
     * token
     */
    @Deprecated
    public static final String TOKEN_NAME = BEARER_HEADER_KEY;

    /**
     * 灰度发布版本号
     */
    public static final String GRAY_VERSION = "grayversion";

    /**
     * 用户redis token key
     */
    public static final String USER_TOKEN_KEY = "user:token:user:";

    /**
     * 用户id redis token value
     */
    public static final String USER_ID_TOKEN_KEY = "user:token:userId:";
    /**
     * 三方redis token key
     */
    public static final String CLIENT_TOKEN_KEY = "user:token:client:";

    /**
     * 三方idredis token key
     */
    public static final String CLIENT_ID_TOKEN_KEY = "user:token:clientId:";

    /**
     * 用户
     */
    public static final String USER_OPEN_ID = "user:openId:";

    /**
     * 用户当前生效组织id
     */
    public static final String USER_ORG_ID = "user:orgId:";

    /**
     * 用户当前生效组织类型
     */
    public static final String USER_ORG_TYPE = "user:orgType:";

    /**
     * 用户当前生操作角度
     */
    public static final String USER_ROLE = "userRole";

    /**
     * 用户当前生操作角度
     */
    public static final String MODULE_TYPE = "moduleType";

    /**
     * 暖心医生用户绑定视频卡 redis value
     */
    public static final String WARM_CARD_USER_KEY = "warm:card:user:";

    /**
     * 暖心医生视频卡是否被绑定 redis value
     */
    public static final String WARM_CARD_ID_KEY = "warm:card:id:";

    /**
     * 腾讯云录制房间Id
     */
    public static final String TENCENT_CLOUD_VIDEO_ID_KEY = "tencent:cloud:video:roomId:";

    /**
     * 组织应用的appSecretId
     */
//    public static final String EXTRA_APP_SECRET_ID = "extra:appSecretId";
    /**
     * 组织应用的appSecretId
     */
    public static final String EXTRA_APP_APP_ID = "extra:appId";
}
