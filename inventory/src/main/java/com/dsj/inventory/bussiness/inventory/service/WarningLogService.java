package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.WarningLog;
import com.dsj.inventory.bussiness.inventory.param.QueryWarningLogParam;
import com.dsj.inventory.bussiness.inventory.vo.WarningLogVo;

/**
 * <p>
 * 进销存预警记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface WarningLogService extends IService<WarningLog> {

    /**
     * 获取预警记录分页列表
     * @param param
     * @return
     */
    Page<WarningLogVo> queryWarningLogPage(QueryWarningLogParam param);

    /**
     * 批量处理预警记录--生成滞销预警、近效期预警；消除近效期预警
     */
    void batchHandleWarningLog();
    
    /**
     * 根据类型和商品信息查找预警记录
     * 
     * @param type 预警类型
     * @param organizationId 组织ID
     * @param organizationMedicineId 商品ID
     * @return 预警记录，如果不存在返回null
     */
    WarningLog findWarningLogByTypeAndMedicine(Integer type, Long organizationId, Long organizationMedicineId);
    
    /**
     * 将预警记录标记为已删除
     * 
     * @param id 预警记录ID
     * @return 是否标记成功
     */
    boolean markWarningLogAsDeleted(Long id);
}
