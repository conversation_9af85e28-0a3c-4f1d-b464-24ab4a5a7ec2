package com.dsj.inventory.bussiness.inventory.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.*;
import com.dsj.inventory.bussiness.inventory.enumeration.*;
import com.dsj.inventory.bussiness.inventory.mapper.SalesOrderMapper;
import com.dsj.inventory.bussiness.inventory.param.*;
import com.dsj.inventory.bussiness.inventory.service.*;
import com.dsj.inventory.bussiness.inventory.vo.*;
import com.dsj.inventory.common.utils.DozerUtils;
import com.pocky.transport.bussiness.auth.entity.OrgRModule;
import com.pocky.transport.bussiness.auth.enumeration.RoleEnum;
import com.pocky.transport.bussiness.auth.service.OrgRModuleService;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.entity.UserROrganization;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.diagnose.service.UserROrganizationService;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import com.pocky.transport.bussiness.hospital.vo.OrganizationMedicineMappingVo;
import com.dsj.inventory.common.constant.BaseConstant;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.common.utils.TencentIMUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 进销存销售单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Slf4j
@Service
public class SalesOrderServiceImpl extends ServiceImpl<SalesOrderMapper, SalesOrder> implements SalesOrderService {

    @Autowired
    private DozerUtils dozerUtils;
    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @Autowired
    private SalesGoodsService salesGoodsService;
    @Autowired
    private InventoryService inventoryService;
    @Autowired
    private InventoryChangeLogService inventoryChangeLogService;
    @Autowired
    private CashierWorkingService cashierWorkingService;
    @Autowired
    private WarningLogService warningLogService;
    @Autowired
    private SalesActivityService salesActivityService;
    @Autowired
    private SalesCouponService salesCouponService;
    @Autowired
    private MemberCouponService memberCouponService;
    @Autowired
    private MemberService memberService;
    @Autowired
    private SalesActivityGoodsService salesActivityGoodsService;
    @Autowired
    private OrgRModuleService orgRModuleService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private TencentIMUtils tencentIMUtils;
    @Autowired
    private UserROrganizationService userROrganizationService;
    @Autowired
    private ShelfService shelfService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalesOrderDetailVo saveOrUpdateSalesOrder(SaveOrUpdateSalesOrderParam param) {
        try {
            if (CommonUtils.isEmpty(param)){
                throw new ParamException("参数异常");
            }
            Organization organization = organizationService.getById(param.getOrganizationId());
            if(CommonUtils.isEmpty(organization)){
                throw new BizException("组织不存在");
            }
            if(CommonUtils.isEmpty(organization.getInventoryDate())){
                throw new BizException("进销存有效期未设置");
            }
            boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
            if (before) {
                throw new BizException("进销存有效期已过期");
            }
            if(CommonUtils.isNotEmpty(param.getId())) {
                SalesOrder byId = this.getById(param.getId());
                if (CommonUtils.isEmpty(byId)) {
                    throw new BizException("操作失败，数据不存在");
                }
                //删除商品明细
                salesGoodsService.lambdaUpdate().eq(SalesGoods::getOrderId,param.getId())
                        .set(SalesGoods::getIsDelete, TrueEnum.TRUE.getCode()).update(new SalesGoods());
            }else {
                //生成销售单号：XS+年后两位+月日四位+药房识别码六位+三位
                String day = DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_FORMAT);
                String dayStr = day.replaceAll("-", "").substring(2,8);
                Integer count = this.getSalesOrderCount(organization.getId(),day);
                count = count + 1;
                param.setCode("XS" + dayStr + organization.getSerialNo() + String.format("%05d",count));
            }

            if(CommonUtils.isEmpty(param.getGoodsList())){
                throw new BizException("操作失败，无商品信息");
            }
            StringBuilder goodsInfo = new StringBuilder();
            //应收：零售价*销售数量
            BigDecimal receivablePrice = BigDecimal.valueOf(0);
            //合计：销售数量*折后金额
            BigDecimal totalPrice = BigDecimal.valueOf(0);
            //实收：销售单最后的金额
            BigDecimal actualPrice = BigDecimal.valueOf(0);
            List<SaveOrUpdateSalesGoodsParam> repeatList = param.getGoodsList().stream().collect(Collectors.collectingAndThen
                    (Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(
                            g -> g.getOrganizationMedicineId() + ";" + g.getBatchNumber() + ";" + g.getShelfId()))), ArrayList::new));
            if(repeatList.size() != param.getGoodsList().size()){
                throw new BizException("操作失败，存在重复商品");
            }
            //获取组织权限
            Integer salesDayLimit = null;
            OrgRModule orgRModule = orgRModuleService.lambdaQuery().eq(OrgRModule::getOrganizationId,param.getOrganizationId()).one();
            if(CommonUtils.isNotEmpty(orgRModule) && CommonUtils.isNotEmpty(orgRModule.getSalesDayLimit())){
                salesDayLimit = orgRModule.getSalesDayLimit();
            }
            //是否发送缺货预警消息
            boolean lackFlag = false;
            List<InventoryChangeLog> inventoryChangeLogList = new ArrayList<>();
            for(SaveOrUpdateSalesGoodsParam goodsParam : param.getGoodsList()){
                OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(goodsParam.getOrganizationMedicineId());
                if(CommonUtils.isEmpty(organizationMedicine)){
                    throw new BizException("操作失败，商品Id"+goodsParam.getOrganizationMedicineId()+"不存在");
                }
                List<Inventory> inventoryList = inventoryService.lambdaQuery()
                        .eq(Inventory::getOrganizationId,param.getOrganizationId())
                        .eq(Inventory::getOrganizationMedicineId,goodsParam.getOrganizationMedicineId())
                        .eq(Inventory::getBatchNumber,goodsParam.getBatchNumber())
                        .eq(Inventory::getShelfId,goodsParam.getShelfId())
                        .gt(Inventory::getInventoryAmount,0.00)
                        .orderByAsc(Inventory::getUserDate, Inventory::getIncomingDate)
                        .list();
                if(CommonUtils.isEmpty(inventoryList) && inventoryList.size() < 1){
                    throw new BizException("操作失败，"+organizationMedicine.getName()+"无库存");
                }
                BigDecimal saleRealAmount = BigDecimal.valueOf(goodsParam.getSaleAmount()).add(BigDecimal.valueOf(goodsParam.getGiveAmount()));
                for (Inventory inventory : inventoryList) {
                    if(saleRealAmount.doubleValue() <= 0){
                        break;
                    }
                    BigDecimal inventoryAmount = BigDecimal.valueOf(0.00);
                    BigDecimal changeAmount = saleRealAmount;
                    if(inventory.getInventoryAmount() <= saleRealAmount.doubleValue()){
                        saleRealAmount = saleRealAmount.subtract(BigDecimal.valueOf(inventory.getInventoryAmount()));
                        changeAmount = BigDecimal.valueOf(inventory.getInventoryAmount());
                    }else {
                        inventoryAmount = BigDecimal.valueOf(inventory.getInventoryAmount()).subtract(saleRealAmount);
                        saleRealAmount = BigDecimal.valueOf(0.00);
                    }
                    if(SalesOrderStateEnum.COMPLETED.getCode().equals(param.getOrderState())) {
                        inventoryService.lambdaUpdate().eq(Inventory::getId, inventory.getId())
                                .set(Inventory::getInventoryAmount, inventoryAmount.doubleValue())
                                .update(new Inventory());
                        //库存变动记录
                        InventoryChangeLog inventoryChangeLog = InventoryChangeLog.builder()
                                .organizationId(param.getOrganizationId())
                                .organizationMedicineId(goodsParam.getOrganizationMedicineId()).inventoryId(inventory.getId())
                                .orderType(InventoryOrderTypeEnum.SALES.getCode())
                                .changeType(InventoryChangeTypeEnum.DECREASE.getCode()).changeAmount(changeAmount.doubleValue())
                                .batchNumber(inventory.getBatchNumber()).shelfId(inventory.getShelfId()).batch(inventory.getBatch())
                                .costPrice(inventory.getCostPrice()).executeId(BaseContextHandler.getUserId())
                                .build();
                        inventoryChangeLogList.add(inventoryChangeLog);
                    }
                }
                if(saleRealAmount.doubleValue() > 0) {
                    Shelf shelf = shelfService.getById(goodsParam.getShelfId());
                    throw new BizException("操作失败，"+organizationMedicine.getName()+"批号："+goodsParam.getBatchNumber()+"，货位："+shelf.getName()+"库存不足");
                }
                if(SalesOrderStateEnum.COMPLETED.getCode().equals(param.getOrderState())) {
                    BigDecimal medicineRepertory = BigDecimal.valueOf(organizationMedicine.getMedicineRepertory())
                            .subtract(BigDecimal.valueOf(goodsParam.getSaleAmount())).subtract(BigDecimal.valueOf(goodsParam.getGiveAmount()));
                    organizationMedicineMappingService.lambdaUpdate().eq(OrganizationMedicineMapping::getId,organizationMedicine.getId())
                            .set(OrganizationMedicineMapping::getMedicineRepertory,medicineRepertory.doubleValue())
                            .set(OrganizationMedicineMapping::getLastSaleTime,LocalDateTime.now())
                            .update(new OrganizationMedicineMapping());
                    //处理库存预警
                    if(CommonUtils.isNotEmpty(organizationMedicine.getRepertoryLowerLimit())){
                        //缺货
                        WarningLog lowerWarningLog = warningLogService.lambdaQuery()
                                .eq(WarningLog::getType, WarningTypeEnum.LACK.getCode())
                                .eq(WarningLog::getOrganizationId,param.getOrganizationId())
                                .eq(WarningLog::getOrganizationMedicineId,organizationMedicine.getId()).one();
                        //库存下限
                        Integer lowerLimit = organizationMedicine.getRepertoryLowerLimit();
                        if(medicineRepertory.doubleValue() < lowerLimit && CommonUtils.isEmpty(lowerWarningLog)){
                            //新增缺货预警
                            WarningLog warningLog = WarningLog.builder().organizationId(param.getOrganizationId())
                                    .organizationMedicineId(organizationMedicine.getId())
                                    .type(WarningTypeEnum.LACK.getCode()).build();
                            warningLogService.save(warningLog);
                            lackFlag = true;
                        }
                    }
                    if(CommonUtils.isNotEmpty(organizationMedicine.getRepertoryUpperLimit())){
                        //积货
                        WarningLog upperWarningLog = warningLogService.lambdaQuery()
                                .eq(WarningLog::getType,WarningTypeEnum.EXCEED.getCode())
                                .eq(WarningLog::getOrganizationId,param.getOrganizationId())
                                .eq(WarningLog::getOrganizationMedicineId,organizationMedicine.getId()).one();
                        //库存上限
                        Integer upperLimit = organizationMedicine.getRepertoryUpperLimit();
                        if(medicineRepertory.doubleValue() < upperLimit && CommonUtils.isNotEmpty(upperWarningLog)){
                            //删除预警记录
                            warningLogService.lambdaUpdate().eq(WarningLog::getId,upperWarningLog.getId())
                                    .set(WarningLog::getIsDelete,TrueEnum.TRUE.getCode()).update(new WarningLog());
                        }
                    }
                    if(CommonUtils.isNotEmpty(salesDayLimit)){
                        //滞销
                        WarningLog unsalableWarningLog = warningLogService.lambdaQuery()
                                .eq(WarningLog::getType,WarningTypeEnum.UNSALABLE.getCode())
                                .eq(WarningLog::getOrganizationId,param.getOrganizationId())
                                .eq(WarningLog::getOrganizationMedicineId,organizationMedicine.getId()).one();
                        if(CommonUtils.isNotEmpty(unsalableWarningLog)){
                            //删除预警记录
                            warningLogService.lambdaUpdate().eq(WarningLog::getId,unsalableWarningLog.getId())
                                    .set(WarningLog::getIsDelete,TrueEnum.TRUE.getCode()).update(new WarningLog());
                        }
                    }
                }
                goodsParam.setGoodsName(organizationMedicine.getName());
                goodsParam.setGeneralName(organizationMedicine.getGeneralName());
                goodsParam.setGoodsCode(organizationMedicine.getGoodsCode());
                goodsParam.setPackUnit(organizationMedicine.getPackUnit());
                goodsParam.setSpecification(organizationMedicine.getSpecification());
                goodsParam.setDrugType(organizationMedicine.getDrugType());
                goodsParam.setManufacturer(organizationMedicine.getManufacturer());
                goodsParam.setUsageDosage(organizationMedicine.getUsageDosage());
                goodsParam.setBarCode(organizationMedicine.getBarCode());
                goodsParam.setApprovalNumber(organizationMedicine.getApprovalNumber());
                goodsParam.setSalePrice(organizationMedicine.getOfflinePrice());
                goodsParam.setMemberPrice(organizationMedicine.getMemberPrice());
                goodsParam.setProduceDate(inventoryList.get(0).getProduceDate());
                goodsParam.setUserDate(inventoryList.get(0).getUserDate());
                BigDecimal goodsReceivablePrice = BigDecimal.valueOf(goodsParam.getSalePrice())
                        .multiply(BigDecimal.valueOf(goodsParam.getSaleAmount()));
                goodsReceivablePrice = goodsReceivablePrice.setScale(0,BigDecimal.ROUND_HALF_UP);
                receivablePrice = receivablePrice.add(goodsReceivablePrice);
                BigDecimal goodsTotalPrice = BigDecimal.valueOf(goodsParam.getDiscountPrice())
                        .multiply(BigDecimal.valueOf(goodsParam.getSaleAmount()));
                goodsTotalPrice = goodsTotalPrice.setScale(0,BigDecimal.ROUND_HALF_UP);
                totalPrice = totalPrice.add(goodsTotalPrice);
                goodsParam.setSpecialOffer(organizationMedicine.getSpecialOffer());
            }
            //处理单品活动、会员日活动
            List<SalesActivityGoodsParam> goodsList = dozerUtils.mapList(param.getGoodsList(),SalesActivityGoodsParam.class);
            goodsList = goodsList.stream().collect(Collectors.groupingBy(g -> g.getOrganizationMedicineId())).values().stream().map(d -> {
                SalesActivityGoodsParam goods = d.get(0);
                goods.setSaleAmount(d.stream().mapToDouble(SalesActivityGoodsParam::getSaleAmount).sum());
                goods.setGiveAmount(d.stream().mapToDouble(SalesActivityGoodsParam::getGiveAmount).sum());
                goods.setTotalPrice(d.stream().mapToInt(SalesActivityGoodsParam::getTotalPrice).sum());
                return goods;
            }).collect(Collectors.toList());
            boolean specialOfferFlag = false;
            boolean singleActivityFlag = false;
            boolean memberActivityFlag = false;
            String today = Calendar.getInstance().get(Calendar.DATE)+"";
            for(SalesActivityGoodsParam goods : goodsList){
                if(!specialOfferFlag && TrueEnum.TRUE.getCode().equals(goods.getSpecialOffer())){
                    specialOfferFlag = true;
                }
                BigDecimal goodsDiscountPrice = BigDecimal.valueOf(goods.getDiscountPrice());
                BigDecimal goodsTotalPrice = BigDecimal.valueOf(goods.getPrice()).multiply(BigDecimal.valueOf(goods.getSaleAmount()));
                //单品活动
                if(CommonUtils.isNotEmpty(goods.getSingleActivityId())){
                    if(TrueEnum.TRUE.getCode().equals(goods.getSpecialOffer())
                            && TrueEnum.FALSE.getCode().equals(orgRModule.getSpecialSingleActivityState())){
                        throw new BizException("操作失败，" + goods.getGoodsName() + "特价商品不能参与单品活动");
                    }
                    SalesActivityVo salesActivity = salesActivityService.getSalesActivityById(goods.getSingleActivityId());
                    if(CommonUtils.isEmpty(salesActivity)){
                        throw new BizException("操作失败，活动不存在");
                    }
                    if(!SalesActivityStateEnum.IN_PROGRESS.getCode().equals(salesActivity.getActivityState())){
                        throw new BizException("操作失败，活动未处于进行中");
                    }
                    if(TrueEnum.FALSE.getCode().equals(salesActivity.getAssertState())){
                        throw new BizException("操作失败，活动未生效");
                    }
                    if(!SalesActivityCategoryEnum.SINGLE.getCode().equals(salesActivity.getActivityCategory())){
                        throw new BizException("操作失败，活动不是单品活动");
                    }
                    if(salesActivity.getApplyTo() > 0 && CommonUtils.isEmpty(param.getMemberId())){
                        throw new BizException("操作失败，非会员不可参与此活动");
                    }
                    List<SalesActivityRuleVo> ruleList = JSONArray.parseArray(salesActivity.getRule(),SalesActivityRuleVo.class);
                    for(SalesActivityGoodsVo activityGoods : salesActivity.getGoodsList()){
                        if(goods.getOrganizationMedicineId().equals(activityGoods.getOrganizationMedicineId())){
                            if(SalesActivityTypeEnum.ACTIVITY_1.getCode().equals(salesActivity.getActivityType())){
                                //每逢倍折
                                SalesActivityRuleVo rule = ruleList.get(0);
                                //达到几倍
                                int num = (int)(goods.getSaleAmount()/rule.getMultiple());
                                if(num == 0){
                                    throw new BizException("操作失败，" + goods.getGoodsName() + "不满足每逢倍折活动条件");
                                }
                                //几折
                                BigDecimal percentage = BigDecimal.valueOf(rule.getPercentage());
                                for(int i=1;i<num;i++){
                                    percentage = percentage.multiply(BigDecimal.valueOf(rule.getPercentage()));
                                    percentage = percentage.divide(BigDecimal.valueOf(100.00),2,BigDecimal.ROUND_HALF_UP);
                                }
                                if(percentage.doubleValue() < rule.getPercentageMax()){
                                    percentage = BigDecimal.valueOf(rule.getPercentageMax());
                                }
                                goodsDiscountPrice = BigDecimal.valueOf(goods.getPrice()).multiply(percentage);
                                goodsDiscountPrice = goodsDiscountPrice.divide(BigDecimal.valueOf(100),0,BigDecimal.ROUND_HALF_UP);
                                goodsTotalPrice = goodsDiscountPrice.multiply(BigDecimal.valueOf(goods.getSaleAmount()));
                            }else if(SalesActivityTypeEnum.ACTIVITY_2.getCode().equals(salesActivity.getActivityType())){
                                //满件减元
                                ruleList = ruleList.stream().sorted(Comparator.comparing(SalesActivityRuleVo::getAmount).reversed()).collect(Collectors.toList());
                                boolean flag = false;
                                for(SalesActivityRuleVo rule : ruleList){
                                    if(goods.getSaleAmount() >= rule.getAmount()){
                                        goodsTotalPrice = goodsTotalPrice.subtract(BigDecimal.valueOf(rule.getReducePrice()));
                                        goodsDiscountPrice = goodsTotalPrice.divide(BigDecimal.valueOf(goods.getSaleAmount()),0,BigDecimal.ROUND_HALF_UP);
                                        flag = true;
                                        break;
                                    }
                                }
                                if(!flag){
                                    throw new BizException("操作失败，" + goods.getGoodsName() + "不满足满件减元活动条件");
                                }
                            }else if(SalesActivityTypeEnum.ACTIVITY_3.getCode().equals(salesActivity.getActivityType())){
                                //单品特价
                                SalesActivityRuleVo rule = ruleList.get(0);
                                BigDecimal specialAmount = BigDecimal.valueOf(goods.getSaleAmount());
                                if(CommonUtils.isNotEmpty(rule.getLimitAmount())){
                                    if(specialAmount.doubleValue() > rule.getLimitAmount()){
                                        specialAmount = BigDecimal.valueOf(rule.getLimitAmount());
                                    }
                                }
                                if(CommonUtils.isNotEmpty(rule.getTotalLimitAmount())){
                                    BigDecimal surplusAmount = BigDecimal.valueOf(rule.getTotalLimitAmount())
                                            .subtract(BigDecimal.valueOf(activityGoods.getSalesAmount()));
                                    if(surplusAmount.doubleValue() <= 0){
                                        throw new BizException("操作失败，" + goods.getGoodsName() + "不满足活动条件，单品特价已售罄");
                                    }
                                    if(specialAmount.doubleValue() > surplusAmount.doubleValue()){
                                        specialAmount = surplusAmount;
                                    }
                                }
                                BigDecimal oldPrice = BigDecimal.valueOf(goods.getPrice()).multiply(specialAmount);
                                BigDecimal newPrice = BigDecimal.valueOf(rule.getSpecialPrice()).multiply(specialAmount);
                                goodsTotalPrice = goodsTotalPrice.subtract(oldPrice).add(newPrice);
                                goodsDiscountPrice = goodsTotalPrice.divide(BigDecimal.valueOf(goods.getSaleAmount()),0,BigDecimal.ROUND_HALF_UP);
                                if(SalesOrderStateEnum.COMPLETED.getCode().equals(param.getOrderState())) {
                                    Double salesAmount = BigDecimal.valueOf(activityGoods.getSalesAmount()).add(specialAmount).doubleValue();
                                    salesActivityGoodsService.lambdaUpdate().eq(SalesActivityGoods::getId, activityGoods.getId())
                                            .set(SalesActivityGoods::getSalesAmount, salesAmount).update(new SalesActivityGoods());
                                }
                            }else if(SalesActivityTypeEnum.ACTIVITY_4.getCode().equals(salesActivity.getActivityType())){
                                //满元减元
                                ruleList = ruleList.stream().sorted(Comparator.comparing(SalesActivityRuleVo::getSpendPrice).reversed()).collect(Collectors.toList());
                                boolean flag = false;
                                for(SalesActivityRuleVo rule : ruleList){
                                    if(goodsTotalPrice.intValue() >= rule.getSpendPrice()){
                                        goodsTotalPrice = goodsTotalPrice.subtract(BigDecimal.valueOf(rule.getReducePrice()));
                                        goodsDiscountPrice = goodsTotalPrice.divide(BigDecimal.valueOf(goods.getSaleAmount()),0,BigDecimal.ROUND_HALF_UP);
                                        flag = true;
                                        break;
                                    }
                                }
                                if(!flag){
                                    throw new BizException("操作失败，" + goods.getGoodsName() + "不满足满元减元活动条件");
                                }
                            }else if(SalesActivityTypeEnum.ACTIVITY_5.getCode().equals(salesActivity.getActivityType())){
                                //满件赠件
                                SalesActivityRuleVo rule = ruleList.get(0);
                                if(goods.getSaleAmount() < rule.getAmount()){
                                    throw new BizException("操作失败，" + goods.getGoodsName() + "不满足满件赠件活动条件");
                                }
                            }
                            singleActivityFlag = true;
                            break;
                        }
                    }
                    if(!singleActivityFlag){
                        throw new BizException("操作失败，" + goods.getGoodsName() + "不满足单品活动条件");
                    }
                }
                //会员活动
                if(CommonUtils.isNotEmpty(goods.getMemberActivityId())){
                    if(CommonUtils.isEmpty(orgRModule.getMemberDay())){
                        throw new BizException("操作失败，会员日未设置");
                    }
                    boolean memberDayFlag = false;
                    String[] memberDays = orgRModule.getMemberDay().split(",");
                    for(String memberDay : memberDays){
                        if(today.equals(memberDay)){
                            memberDayFlag = true;
                            break;
                        }
                    }
                    if(!memberDayFlag){
                        throw new BizException("操作失败，今天不是会员日");
                    }
                    if(TrueEnum.FALSE.getCode().equals(orgRModule.getSingleMemberActivityState())
                            && CommonUtils.isNotEmpty(goods.getSingleActivityId())){
                        throw new BizException("操作失败，" + goods.getGoodsName() + "单品活动和会员日活动不可同时参与");
                    }
                    if(TrueEnum.TRUE.getCode().equals(goods.getSpecialOffer())
                            && TrueEnum.FALSE.getCode().equals(orgRModule.getSpecialMemberActivityState())){
                        throw new BizException("操作失败，" + goods.getGoodsName() + "特价商品不能参与会员日活动");
                    }
                    SalesActivityVo salesActivity = salesActivityService.getSalesActivityById(goods.getMemberActivityId());
                    if(CommonUtils.isEmpty(salesActivity)){
                        throw new BizException("操作失败，活动不存在");
                    }
                    if(!SalesActivityStateEnum.IN_PROGRESS.getCode().equals(salesActivity.getActivityState())){
                        throw new BizException("操作失败，活动未处于进行中");
                    }
                    if(TrueEnum.FALSE.getCode().equals(salesActivity.getAssertState())){
                        throw new BizException("操作失败，活动未生效");
                    }
                    if(!SalesActivityCategoryEnum.MEMBER.getCode().equals(salesActivity.getActivityCategory())){
                        throw new BizException("操作失败，活动不是会员日活动");
                    }
                    if(salesActivity.getApplyTo() > 0 && CommonUtils.isEmpty(param.getMemberId())){
                        throw new BizException("操作失败，非会员不可参与此活动");
                    }
                    List<SalesActivityRuleVo> ruleList = JSONArray.parseArray(salesActivity.getRule(),SalesActivityRuleVo.class);
                    for(SalesActivityGoodsVo activityGoods : salesActivity.getGoodsList()){
                        if(goods.getOrganizationMedicineId().equals(activityGoods.getOrganizationMedicineId())){
                            if(SalesActivityTypeEnum.ACTIVITY_2.getCode().equals(salesActivity.getActivityType())){
                                //满件减
                                ruleList = ruleList.stream().sorted(Comparator.comparing(SalesActivityRuleVo::getAmount).reversed()).collect(Collectors.toList());
                                boolean flag = false;
                                for(SalesActivityRuleVo rule : ruleList){
                                    if(goods.getSaleAmount() >= rule.getAmount()){
                                        goodsTotalPrice = goodsTotalPrice.subtract(BigDecimal.valueOf(rule.getReducePrice()));
                                        goodsDiscountPrice = goodsTotalPrice.divide(BigDecimal.valueOf(goods.getSaleAmount()),0,BigDecimal.ROUND_HALF_UP);
                                        flag = true;
                                        break;
                                    }
                                }
                                if(!flag){
                                    throw new BizException("操作失败，" + goods.getGoodsName() + "不满足活动条件");
                                }
                            }
                            memberActivityFlag = true;
                            break;
                        }
                    }
                    if(!memberActivityFlag){
                        throw new BizException("操作失败，" + goods.getGoodsName() + "不满足会员日活动条件");
                    }
                }
                goodsDiscountPrice = goodsDiscountPrice.setScale(0,BigDecimal.ROUND_HALF_UP);
                if(goodsDiscountPrice.intValue() != goods.getDiscountPrice()){
                    throw new BizException("操作失败，" + goods.getGoodsName() + "折后金额计算不一致："+goodsDiscountPrice.intValue());
                }
                goodsInfo.append(goods.getGoodsName()+"*"+goods.getSaleAmount()+goods.getPackUnit()+",");
                if(CommonUtils.isNotEmpty(goods.getGiveAmount()) && goods.getGiveAmount() > 0.00){
                    goodsInfo.append("赠品*"+goods.getGiveAmount()+goods.getPackUnit()+",");
                }
            }
            param.setGoodsInfo(goodsInfo.substring(0,goodsInfo.length()-1));
            if(receivablePrice.intValue() != param.getReceivablePrice()){
                throw new BizException("操作失败，应收金额计算不一致："+receivablePrice.intValue());
            }
            param.setReceivablePrice(receivablePrice.intValue());
            if(totalPrice.intValue() != param.getTotalPrice()){
                throw new BizException("操作失败，合计金额计算不一致："+totalPrice.intValue());
            }
            param.setTotalPrice(totalPrice.intValue());
            actualPrice = totalPrice;
            //优惠：当前单品折扣掉的金额总计
            Integer preferentialPrice = receivablePrice.subtract(totalPrice).intValue();
            if(!preferentialPrice.equals(param.getPreferentialPrice())){
                throw new BizException("操作失败，优惠金额计算不一致："+preferentialPrice);
            }
            param.setPreferentialPrice(preferentialPrice);
            if(CommonUtils.isEmpty(param.getSaleId())){
                param.setSaleId(BaseContextHandler.getUserId());
            }
            if(CommonUtils.isEmpty(param.getCashierId())){
                param.setCashierId(BaseContextHandler.getUserId());
            }
            //计算活动减额
            if(CommonUtils.isNotEmpty(param.getActivityId())){
                if(TrueEnum.FALSE.getCode().equals(orgRModule.getSingleWholeActivityState()) && singleActivityFlag){
                    throw new BizException("操作失败，单品活动和整单活动不可同时参与");
                }
                if(TrueEnum.FALSE.getCode().equals(orgRModule.getMemberWholeActivityState()) && memberActivityFlag){
                    throw new BizException("操作失败，会员日活动和整单活动不可同时参与");
                }
                if(specialOfferFlag && TrueEnum.FALSE.getCode().equals(orgRModule.getSpecialMemberActivityState())){
                    throw new BizException("操作失败，特价商品不能参与整单活动");
                }
                SalesActivityVo salesActivity = salesActivityService.getSalesActivityById(param.getActivityId());
                if(CommonUtils.isEmpty(salesActivity)){
                    throw new BizException("操作失败，活动不存在");
                }
                if(!SalesActivityStateEnum.IN_PROGRESS.getCode().equals(salesActivity.getActivityState())){
                    throw new BizException("操作失败，活动未处于进行中");
                }
                if(TrueEnum.FALSE.getCode().equals(salesActivity.getAssertState())){
                    throw new BizException("操作失败，活动未生效");
                }
                if(!SalesActivityCategoryEnum.WHOLE.getCode().equals(salesActivity.getActivityCategory())){
                    throw new BizException("操作失败，活动不是整单活动");
                }
                if(salesActivity.getApplyTo() > 0 && CommonUtils.isEmpty(param.getMemberId())){
                    throw new BizException("操作失败，非会员不可参与此活动");
                }
                List<SalesActivityRuleVo> ruleList = JSONArray.parseArray(salesActivity.getRule(),SalesActivityRuleVo.class);
                BigDecimal oldActualPrice = actualPrice;
                if(SalesActivityTypeEnum.ACTIVITY_4.getCode().equals(salesActivity.getActivityType())){
                    //满元减元
                    ruleList = ruleList.stream().sorted(Comparator.comparing(SalesActivityRuleVo::getSpendPrice).reversed()).collect(Collectors.toList());
                    boolean flag = false;
                    for(SalesActivityRuleVo rule : ruleList){
                        if(actualPrice.intValue() >= rule.getSpendPrice()){
                            actualPrice = actualPrice.subtract(BigDecimal.valueOf(rule.getReducePrice()));
                            flag = true;
                            break;
                        }
                    }
                    if(!flag){
                        throw new BizException("操作失败，不满足满元减元活动条件");
                    }
                }
                Integer activityReducePrice = oldActualPrice.subtract(actualPrice).intValue();
                if(!activityReducePrice.equals(param.getActivityReducePrice())){
                    throw new BizException("操作失败，活动减额计算不一致");
                }
                param.setActivityReducePrice(activityReducePrice);
            }
            //计算优惠券金额
            if(CommonUtils.isNotEmpty(param.getCouponId())){
                if(CommonUtils.isEmpty(param.getMemberId())){
                    throw new BizException("操作失败，非会员不可使用优惠券");
                }
                Integer couponPrice = 0;
                MemberCoupon memberCoupon = memberCouponService.getById(param.getCouponId());
                if(CommonUtils.isEmpty(memberCoupon)){
                    throw new BizException("操作失败，优惠券不存在");
                }
                if(!MemberCouponStateEnum.NOT_USED.getCode().equals(memberCoupon.getCouponState())){
                    throw new BizException("操作失败，优惠券无法使用");
                }
                SalesCouponVo salesCoupon = salesCouponService.getSalesCouponById(memberCoupon.getCouponId());
                if(CommonUtils.isEmpty(salesCoupon)){
                    throw new BizException("操作失败，优惠券不存在");
                }
                if(!SalesCouponStateEnum.NOT_EXPIRED.getCode().equals(salesCoupon.getCouponState())){
                    throw new BizException("操作失败，优惠券无法使用");
                }
                SalesCouponConditionVo condition =  JSONUtil.toBean(salesCoupon.getCouponCondition(), SalesCouponConditionVo.class);
                if(TrueEnum.FALSE.getCode().equals(salesCoupon.getGoodsLimit())){
                    BigDecimal goodsPrice = BigDecimal.valueOf(0);
                    List<SalesCouponGoodsVo> couponGoodsList = salesCoupon.getGoodsList();
                    for(SalesActivityGoodsParam goods : goodsList){
                        for(SalesCouponGoodsVo couponGoods : couponGoodsList){
                            if(goods.getOrganizationMedicineId().equals(couponGoods.getOrganizationMedicineId())){
                                goodsPrice = goodsPrice.add(BigDecimal.valueOf(goods.getTotalPrice()));
                                break;
                            }
                        }
                    }
                    if(goodsPrice.intValue() < condition.getSalesPrice()){
                        throw new BizException("操作失败，未到达优惠券使用条件");
                    }
                    if(SalesCouponTypeEnum.VOUCHER.getCode().equals(salesCoupon.getCouponType())){
                        //代金券
                        actualPrice = actualPrice.subtract(BigDecimal.valueOf(salesCoupon.getReducePrice()));
                        couponPrice = salesCoupon.getReducePrice();
                    }else if(SalesCouponTypeEnum.COUPON.getCode().equals(salesCoupon.getCouponType())){
                        //折扣券
                        couponPrice = actualPrice.intValue();
                        actualPrice = actualPrice.subtract(goodsPrice);
                        BigDecimal percentage = BigDecimal.valueOf(salesCoupon.getPercentage()).multiply(BigDecimal.valueOf(10));
                        goodsPrice = goodsPrice.multiply(percentage);
                        goodsPrice = goodsPrice.divide(BigDecimal.valueOf(100),0,BigDecimal.ROUND_HALF_UP);
                        actualPrice = actualPrice.add(goodsPrice);
                        couponPrice = BigDecimal.valueOf(couponPrice).subtract(actualPrice).intValue();
                    }
                }else {
                    if(actualPrice.intValue() < condition.getSalesPrice()){
                        throw new BizException("操作失败，未到达优惠券使用条件");
                    }
                    if(SalesCouponTypeEnum.VOUCHER.getCode().equals(salesCoupon.getCouponType())){
                        //代金券
                        actualPrice = actualPrice.subtract(BigDecimal.valueOf(salesCoupon.getReducePrice()));
                        couponPrice = salesCoupon.getReducePrice();
                    }else if(SalesCouponTypeEnum.COUPON.getCode().equals(salesCoupon.getCouponType())){
                        //折扣券
                        couponPrice = actualPrice.intValue();
                        BigDecimal percentage = BigDecimal.valueOf(salesCoupon.getPercentage()).multiply(BigDecimal.valueOf(10));
                        actualPrice = actualPrice.multiply(percentage);
                        actualPrice = actualPrice.divide(BigDecimal.valueOf(100),0,BigDecimal.ROUND_HALF_UP);
                        couponPrice = BigDecimal.valueOf(couponPrice).subtract(actualPrice).intValue();
                    }
                }
                if(!couponPrice.equals(param.getCouponPrice())){
                    throw new BizException("操作失败，优惠券金额计算不一致："+couponPrice);
                }
                if(SalesOrderStateEnum.COMPLETED.getCode().equals(param.getOrderState())) {
                    //修改会员优惠券使用状态
                    memberCouponService.lambdaUpdate().eq(MemberCoupon::getId, memberCoupon.getId())
                            .set(MemberCoupon::getCouponState, MemberCouponStateEnum.USED.getCode()).update(new MemberCoupon());
                    //修改优惠券使用数量
                    Integer useAmount = memberCouponService.lambdaQuery().eq(MemberCoupon::getCouponId, memberCoupon.getCouponId())
                            .eq(MemberCoupon::getCouponState, MemberCouponStateEnum.USED.getCode()).count();
                    salesCouponService.lambdaUpdate().eq(SalesCoupon::getId, salesCoupon.getId())
                            .set(SalesCoupon::getUseAmount, useAmount).update(new SalesCoupon());
                }
            }
            BigDecimal integral = BigDecimal.valueOf(0);
            if(SalesOrderStateEnum.COMPLETED.getCode().equals(param.getOrderState())) {
                param.setSettlementDate(LocalDateTime.now());
                //支付方式
                StringBuilder payWay = new StringBuilder();
                if(CommonUtils.isNotEmpty(param.getPayInfo())){
                    List<SalesPayInfoVo> payInfoList = JSONArray.parseArray(param.getPayInfo(),SalesPayInfoVo.class);
                    List<String> payWayList = payInfoList.stream().filter(p -> p.getPayPrice() > 0).map(p -> p.getPayWay()).collect(Collectors.toList());
                    if(CommonUtils.isNotEmpty(payWayList) && payWayList.size() > 0){
                        payWay.append(String.join("，",payWayList) + "，");
                    }
                }
                if(param.getStoredPay() > 0){
                    payWay.append("储值，");
                }
                if(param.getIntegralPay() > 0){
                    payWay.append("积分抵扣，");
                }
                if(CommonUtils.isEmpty(payWay) && payWay.length() < 1){
                    throw new BizException("操作失败，没有支付信息");
                }
                param.setPayWay(payWay.substring(0,payWay.length()-1));
                if(CommonUtils.isNotEmpty(param.getDeductPrice())){
                    actualPrice = actualPrice.subtract(BigDecimal.valueOf(param.getDeductPrice()));
                }
                if(actualPrice.intValue() != param.getActualPrice()){
                    throw new BizException("操作失败，实收计算不一致："+actualPrice.intValue());
                }
                param.setActualPrice(actualPrice.intValue());
                //计算本次积分
                if(CommonUtils.isNotEmpty(param.getMemberId())){
                    BigDecimal specialPrice = BigDecimal.valueOf(0);
                    BigDecimal ordinaryPrice = BigDecimal.valueOf(0);
                    for(SalesActivityGoodsParam goods : goodsList) {
                        if(TrueEnum.TRUE.getCode().equals(goods.getSpecialOffer())){
                            specialPrice = specialPrice.add(BigDecimal.valueOf(goods.getTotalPrice()));
                        }else {
                            ordinaryPrice = ordinaryPrice.add(BigDecimal.valueOf(goods.getTotalPrice()));
                        }
                    }
                    if(specialPrice.intValue() > 0){
                        BigDecimal priceRatio = specialPrice.divide(totalPrice,4,BigDecimal.ROUND_HALF_UP);
                        BigDecimal goodsIntegral = actualPrice.multiply(priceRatio);
                        BigDecimal integralPrice = BigDecimal.valueOf(orgRModule.getSpecialIntegral())
                                .divide(BigDecimal.valueOf(orgRModule.getSpecialIntegralMoney()),2,BigDecimal.ROUND_HALF_UP);
                        goodsIntegral = goodsIntegral.multiply(integralPrice);
                        if(CommonUtils.isNotEmpty(orgRModule.getMemberDay()) && TrueEnum.TRUE.getCode().equals(orgRModule.getMemberDaySpecialState())){
                            String[] memberDays = orgRModule.getMemberDay().split(",");
                            for(String memberDay : memberDays){
                                if(today.equals(memberDay)){
                                    goodsIntegral = goodsIntegral.multiply(BigDecimal.valueOf(orgRModule.getMemberDayIntegralMultiple()));
                                    break;
                                }
                            }
                        }
                        integral = integral.add(goodsIntegral);
                    }
                    if(ordinaryPrice.intValue() > 0){
                        BigDecimal priceRatio = ordinaryPrice.divide(totalPrice,4,BigDecimal.ROUND_HALF_UP);
                        BigDecimal goodsIntegral = actualPrice.multiply(priceRatio);
                        BigDecimal integralPrice = BigDecimal.valueOf(orgRModule.getOrdinaryIntegral())
                                .divide(BigDecimal.valueOf(orgRModule.getOrdinaryIntegralMoney()),2,BigDecimal.ROUND_HALF_UP);
                        goodsIntegral = goodsIntegral.multiply(integralPrice);
                        if(CommonUtils.isNotEmpty(orgRModule.getMemberDay())){
                            String[] memberDays = orgRModule.getMemberDay().split(",");
                            for(String memberDay : memberDays){
                                if(today.equals(memberDay)){
                                    goodsIntegral = goodsIntegral.multiply(BigDecimal.valueOf(orgRModule.getMemberDayIntegralMultiple()));
                                    break;
                                }
                            }
                        }
                        integral = integral.add(goodsIntegral);
                    }
                    integral = integral.setScale(0,BigDecimal.ROUND_DOWN);
                    if(integral.intValue() != param.getIntegral()){
                        throw new BizException("操作失败，本次积分计算不一致："+integral.intValue());
                    }
                }
            }else if(SalesOrderStateEnum.HANG.getCode().equals(param.getOrderState())){
                param.setHangDate(LocalDateTime.now());
            }
            SalesOrder salesOrder = dozerUtils.map(param,SalesOrder.class);
            this.saveOrUpdate(salesOrder);
            if(CommonUtils.isNotEmpty(param.getGoodsList())){
                param.getGoodsList().stream().map(g -> g.setOrderId(salesOrder.getId())).collect(Collectors.toList());
                List<SalesGoods> salesGoodsList = dozerUtils.mapList(param.getGoodsList(),SalesGoods.class);
                salesGoodsService.saveBatch(salesGoodsList);
            }
            if(SalesOrderStateEnum.COMPLETED.getCode().equals(param.getOrderState())){
                if(CommonUtils.isNotEmpty(salesOrder.getMemberId())){
                    //计算会员积分
                    BigDecimal integralRatio = BigDecimal.valueOf(orgRModule.getIntegralPayIntegral())
                            .divide(BigDecimal.valueOf(orgRModule.getIntegralPayMoney()),2,BigDecimal.ROUND_HALF_UP);
                    BigDecimal deductIntegral = BigDecimal.valueOf(salesOrder.getIntegralPay()).multiply(integralRatio);
                    deductIntegral = deductIntegral.setScale(0,BigDecimal.ROUND_HALF_UP);
                    if(deductIntegral.intValue() != param.getDeductIntegral()){
                        throw new BizException("操作失败，抵扣积分计算不一致："+deductIntegral.intValue());
                    }
                    Member member = memberService.getById(salesOrder.getMemberId());
                    if(CommonUtils.isEmpty(member)){
                        throw new BizException("操作失败，会员不存在");
                    }
                    if(deductIntegral.intValue() > member.getUsableIntegral()){
                        throw new BizException("操作失败，会员可用积分小于抵扣积分");
                    }
                    Integer balance = BigDecimal.valueOf(member.getBalance()).subtract(BigDecimal.valueOf(salesOrder.getStoredPay())).intValue();
                    Integer usableIntegral = BigDecimal.valueOf(member.getUsableIntegral()).subtract(deductIntegral).add(integral).intValue();
                    Integer accrueIntegral = BigDecimal.valueOf(member.getAccrueIntegral()).add(integral).intValue();
                    memberService.lambdaUpdate().eq(Member::getId,member.getId()).set(Member::getBalance,balance)
                            .set(Member::getUsableIntegral,usableIntegral).set(Member::getAccrueIntegral,accrueIntegral)
                            .update(new Member());
                }
                if(CommonUtils.isNotEmpty(inventoryChangeLogList)){
                    inventoryChangeLogList.stream().map(l -> l.setOrderId(salesOrder.getId())).collect(Collectors.toList());
                    inventoryChangeLogService.saveBatch(inventoryChangeLogList);
                }
                //更新收银员值班记录
                this.renewCashierWorking(salesOrder.getOrganizationId(),SalesOrderStateEnum.COMPLETED.getCode(),salesOrder.getPayInfo());
                //发送预警消息
                if(lackFlag){
                    log.info("============向商家发送缺货预警消息============");
                    List<UserROrganization> uroList = userROrganizationService.lambdaQuery()
                            .eq(UserROrganization::getOrganizationId,param.getOrganizationId()).list();
                    if (CommonUtils.isNotEmpty(uroList)){
                        List<String> userIdList = uroList.stream().map(s -> s.getUserId().toString()).collect(Collectors.toList());
                        tencentIMUtils.adminSendBatchSingleMsg(userIdList, BaseConstant.IM_DATE_INVENTORY_WARNING,BaseConstant.IM_DESC_INVENTORY_LACK,"");
                    }
                }
            }
            if(CommonUtils.isNotEmpty(param.getId())) {
                //发IM消息
                cn.hutool.json.JSONObject imJson = new cn.hutool.json.JSONObject();
                imJson.set("type", "salesOrderKey");
                imJson.set("id", param.getId());
                imJson.set("data", param.getId());
                tencentIMUtils.adminSendSingleMsg(salesOrder.getCashierId(), BaseConstant.IM_DATE_POLL, "salesOrder-" + salesOrder.getId(), imJson.toString());
            }
            return this.getSalesOrderById(salesOrder.getId());
        }catch (Exception e){
            if(CommonUtils.isNotEmpty(param.getId())) {
                //发IM消息
                cn.hutool.json.JSONObject imJson = new cn.hutool.json.JSONObject();
                imJson.set("type", "salesOrderKeyError");
                imJson.set("id", param.getId());
                imJson.set("data", e.getMessage());
                tencentIMUtils.adminSendSingleMsg(param.getCashierId(), BaseConstant.IM_DATE_POLL, "salesOrder-" + param.getId() + "_error", imJson.toString());
            }
            throw new BizException(e.getMessage());
        }
    }

    @Override
    public void deleteSalesOrder(Long id) {
        SalesOrder byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        if(!SalesOrderStateEnum.HANG.getCode().equals(byId.getOrderState())){
            throw new BizException("操作失败，非挂单状态不允许删除");
        }
        //删除商品明细
        salesGoodsService.lambdaUpdate().eq(SalesGoods::getOrderId,id)
                .set(SalesGoods::getIsDelete, TrueEnum.TRUE.getCode()).update(new SalesGoods());
        this.baseMapper.deleteById(id);
    }

    /**
     * 更新收银员值班记录
     * @param organizationId
     * @param orderState
     * @param payInfo
     */
    private void renewCashierWorking(Long organizationId,Integer orderState,String payInfo){
        Long cashierId = BaseContextHandler.getUserId();
        LocalDateTime startDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime endDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(23,59,59));
        CashierWorking cashierWorking = cashierWorkingService.lambdaQuery().eq(CashierWorking::getCashierId,cashierId)
                .eq(CashierWorking::getOrganizationId,organizationId)
                .ge(CashierWorking::getCreateTime, startDateTime)
                .le(CashierWorking::getCreateTime,endDateTime).one();
        CashierWorking cashierWorkingUpdate = new CashierWorking();
        if(SalesOrderStateEnum.COMPLETED.getCode().equals(orderState)){
            //统计销售信息
            SalesCountPriceVo countVo = this.baseMapper.countSalesPrice(cashierId);
            cashierWorkingUpdate = dozerUtils.map(countVo,CashierWorking.class);
            if(CommonUtils.isNotEmpty(payInfo)){
                if(CommonUtils.isEmpty(cashierWorking) || CommonUtils.isEmpty(cashierWorking.getPayInfo())){
                    cashierWorkingUpdate.setPayInfo(payInfo);
                }else {
                    List<SalesPayInfoVo> payInfoList = JSONArray.parseArray(payInfo,SalesPayInfoVo.class);
                    List<SalesPayInfoVo> cashierPayInfo = JSONArray.parseArray(cashierWorking.getPayInfo(),SalesPayInfoVo.class);
                    cashierPayInfo.addAll(payInfoList);
                    cashierPayInfo = cashierPayInfo.stream().collect(Collectors.groupingBy(p -> p.getPayWay())).values().stream().map(d -> {
                        SalesPayInfoVo payInfoVo = d.get(0);
                        payInfoVo.setPayWay(d.get(0).getPayWay());
                        payInfoVo.setPayPrice(d.stream().mapToInt(SalesPayInfoVo::getPayPrice).sum());
                        return payInfoVo;
                    }).collect(Collectors.toList());
                    cashierWorkingUpdate.setPayInfo(JSON.toJSONString(cashierPayInfo));
                }
            }
        }else if(SalesOrderStateEnum.RETURN.getCode().equals(orderState)){
            //统计退款信息
            Integer returnPrice = this.baseMapper.countReturnPrice(cashierId);
            cashierWorkingUpdate.setReturnPrice(returnPrice);
        }
        if(CommonUtils.isNotEmpty(cashierWorking)){
            cashierWorkingUpdate.setId(cashierWorking.getId());
        }else {
            cashierWorkingUpdate.setOrganizationId(organizationId);
            cashierWorkingUpdate.setCashierId(cashierId);
            cashierWorkingUpdate.setStartDate(LocalDateTime.now());
        }
        cashierWorkingUpdate.setEndDate(LocalDateTime.now());
        cashierWorkingService.saveOrUpdate(cashierWorkingUpdate);
    }

    @Override
    public Page<SalesOrderDetailVo> querySalesOrderPage(QuerySalesOrderParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(!RoleEnum.USER.getCode().equals(BaseContextHandler.get(BaseContextConstants.USER_ROLE))){
            if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
               && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
                throw new BizException("组织无权限");
            }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
                param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
            }
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<SalesOrderDetailVo> list = this.baseMapper.querySalesOrderPage(page, param);
        if(CommonUtils.isNotEmpty(list) && TrueEnum.TRUE.getCode().equals(param.getContainGoodsList())){
            for(SalesOrderDetailVo salesOrder : list){
                List<SalesGoodsVo> goodsList = salesGoodsService.getSalesGoodsListByOrderId(salesOrder.getId());
                if(CommonUtils.isNotEmpty(goodsList)){
                    salesOrder.setGoodsList(goodsList);
                }
            }
        }
        return page.setRecords(list);
    }

    @Override
    public SalesOrderDetailVo getSalesOrderById(Long id) {
        SalesOrderDetailVo vo = this.baseMapper.getSalesOrderById(id);
        if(CommonUtils.isNotEmpty(vo)){
            List<SalesGoodsVo> goodsList = salesGoodsService.getSalesGoodsListByOrderId(vo.getId());
            if(CommonUtils.isNotEmpty(goodsList)){
                vo.setGoodsList(goodsList);
            }
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void returnSalesOrder(ReturnSalesOrderParam param) {
        SalesOrder byId = this.getById(param.getId());
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        Organization organization = organizationService.getById(byId.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        if(!SalesOrderStateEnum.COMPLETED.getCode().equals(byId.getOrderState())){
            throw new BizException("操作失败，未结算订单无法退款");
        }
        //根据库存变动记录，归还库存
        List<InventoryChangeLog> inventoryChangeLogList = inventoryChangeLogService.lambdaQuery()
                .eq(InventoryChangeLog::getOrderId,byId.getId())
                .eq(InventoryChangeLog::getOrderType,InventoryOrderTypeEnum.SALES.getCode())
                .eq(InventoryChangeLog::getChangeType,InventoryChangeTypeEnum.DECREASE.getCode()).list();
        if(CommonUtils.isEmpty(inventoryChangeLogList)){
            throw new BizException("操作失败，未查询到库存变动记录，无法退货");
        }
        //是否发送积货预警消息
        boolean exceedFlag = false;
        for(InventoryChangeLog inventoryChange : inventoryChangeLogList){
            //商品详情库存
            OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(inventoryChange.getOrganizationMedicineId());
            if(CommonUtils.isEmpty(organizationMedicine)){
                throw new BizException("操作失败，未查询到商品详情，无法退货");
            }
            BigDecimal medicineRepertory = BigDecimal.valueOf(organizationMedicine.getMedicineRepertory())
                    .add(BigDecimal.valueOf(inventoryChange.getChangeAmount()));
            organizationMedicineMappingService.lambdaUpdate().eq(OrganizationMedicineMapping::getId,organizationMedicine.getId())
                    .set(OrganizationMedicineMapping::getMedicineRepertory,medicineRepertory.doubleValue())
                    .update(new OrganizationMedicineMapping());
            //处理库存预警
            if(CommonUtils.isNotEmpty(organizationMedicine.getRepertoryLowerLimit())){
                //缺货
                WarningLog lowerWarningLog = warningLogService.lambdaQuery()
                        .eq(WarningLog::getType, WarningTypeEnum.LACK.getCode())
                        .eq(WarningLog::getOrganizationId,byId.getOrganizationId())
                        .eq(WarningLog::getOrganizationMedicineId,organizationMedicine.getId()).one();
                //库存下限
                Integer lowerLimit = organizationMedicine.getRepertoryLowerLimit();
                if(medicineRepertory.doubleValue() > lowerLimit && CommonUtils.isNotEmpty(lowerWarningLog)){
                    //删除预警记录
                    warningLogService.lambdaUpdate().eq(WarningLog::getId,lowerWarningLog.getId())
                            .set(WarningLog::getIsDelete,TrueEnum.TRUE.getCode()).update(new WarningLog());
                }
            }
            if(CommonUtils.isNotEmpty(organizationMedicine.getRepertoryUpperLimit())){
                //积货
                WarningLog upperWarningLog = warningLogService.lambdaQuery()
                        .eq(WarningLog::getType,WarningTypeEnum.EXCEED.getCode())
                        .eq(WarningLog::getOrganizationId,byId.getOrganizationId())
                        .eq(WarningLog::getOrganizationMedicineId,organizationMedicine.getId()).one();
                //库存上限
                Integer upperLimit = organizationMedicine.getRepertoryUpperLimit();
                if(medicineRepertory.doubleValue() > upperLimit && CommonUtils.isEmpty(upperWarningLog)){
                    //新增积货预警
                    WarningLog warningLog = WarningLog.builder().organizationId(byId.getOrganizationId())
                            .organizationMedicineId(organizationMedicine.getId())
                            .type(WarningTypeEnum.EXCEED.getCode()).build();
                    warningLogService.save(warningLog);
                    exceedFlag = true;
                }
            }
            //商品库存列表库存
            Inventory inventory = inventoryService.getById(inventoryChange.getInventoryId());
            if(CommonUtils.isEmpty(inventory)){
                throw new BizException("操作失败，未查询到商品库存，无法退货");
            }
            BigDecimal inventoryAmount = BigDecimal.valueOf(inventory.getInventoryAmount())
                    .add(BigDecimal.valueOf(inventoryChange.getChangeAmount()));
            inventoryService.lambdaUpdate().eq(Inventory::getId, inventory.getId())
                    .set(Inventory::getInventoryAmount, inventoryAmount.doubleValue())
                    .update(new Inventory());
            //库存变动记录
            InventoryChangeLog inventoryChangeLog = InventoryChangeLog.builder()
                    .organizationId(inventoryChange.getOrganizationId())
                    .organizationMedicineId(inventoryChange.getOrganizationMedicineId()).inventoryId(inventory.getId())
                    .orderId(byId.getId()).orderType(InventoryOrderTypeEnum.SALES.getCode())
                    .changeType(InventoryChangeTypeEnum.INCREASE.getCode()).changeAmount(inventoryChange.getChangeAmount())
                    .batchNumber(inventory.getBatchNumber()).shelfId(inventory.getShelfId()).batch(inventory.getBatch())
                    .costPrice(inventory.getCostPrice()).executeId(BaseContextHandler.getUserId())
                    .build();
            inventoryChangeLogService.save(inventoryChangeLog);
        }
        this.lambdaUpdate().eq(SalesOrder::getId,byId.getId())
                .set(SalesOrder::getReturnDate,LocalDateTime.now())
                .set(SalesOrder::getReturnReason,param.getReturnReason())
                .set(SalesOrder::getOrderState,SalesOrderStateEnum.RETURN.getCode())
                .update(new SalesOrder());
        if(CommonUtils.isNotEmpty(byId.getCouponId())){
            //归还优惠券
            LocalDateTime endDate = LocalDateTime.of(LocalDate.now(), LocalTime.of(23,59,59));
            MemberCoupon memberCoupon = memberCouponService.getById(byId.getCouponId());
            if(CommonUtils.isNotEmpty(memberCoupon)){
                SalesCoupon salesCoupon = salesCouponService.getById(memberCoupon.getCouponId());
                SalesCoupon updateCoupon = new SalesCoupon();
                updateCoupon.setId(salesCoupon.getId());
                updateCoupon.setUseAmount(salesCoupon.getUseAmount() - 1);
                Integer couponState = MemberCouponStateEnum.NOT_USED.getCode();
                boolean endFlag = DateUtils.dateCompare(DateUtils.format(memberCoupon.getEndDate(),DateUtils.DEFAULT_DATE_TIME_FORMAT)
                        ,DateUtils.format(endDate,DateUtils.DEFAULT_DATE_TIME_FORMAT));
                if(endFlag){
                    couponState = MemberCouponStateEnum.EXPIRED.getCode();
                    updateCoupon.setExpireAmount(salesCoupon.getExpireAmount() + 1);
                }
                //修改会员优惠券使用状态
                memberCouponService.lambdaUpdate().eq(MemberCoupon::getId,memberCoupon.getId())
                        .set(MemberCoupon::getCouponState,couponState).update(new MemberCoupon());
                //修改优惠券使用数量
                salesCouponService.updateById(updateCoupon);
            }
        }
        if(CommonUtils.isNotEmpty(byId.getMemberId())){
            //会员储值金额、积分抵现；扣除新增的积分
            Member member = memberService.getById(byId.getMemberId());
            if(CommonUtils.isEmpty(member)){
                throw new BizException("操作失败，会员不存在");
            }
            Integer balance = BigDecimal.valueOf(member.getBalance()).add(BigDecimal.valueOf(byId.getStoredPay())).intValue();
            Integer usableIntegral = BigDecimal.valueOf(member.getUsableIntegral()).add(BigDecimal.valueOf(byId.getDeductIntegral()))
                    .subtract(BigDecimal.valueOf(byId.getIntegral())).intValue();
            if (usableIntegral < 0){
                usableIntegral = 0;
            }
            Integer accrueIntegral = BigDecimal.valueOf(member.getAccrueIntegral()).subtract(BigDecimal.valueOf(byId.getIntegral())).intValue();
            memberService.lambdaUpdate().eq(Member::getId,member.getId()).set(Member::getBalance,balance)
                    .set(Member::getUsableIntegral,usableIntegral).set(Member::getAccrueIntegral,accrueIntegral)
                    .update(new Member());
        }
        //变更收银员值班记录
        this.renewCashierWorking(byId.getOrganizationId(),SalesOrderStateEnum.RETURN.getCode(),null);
        //发送预警消息
        if(exceedFlag){
            log.info("============向商家发送积货预警消息============");
            List<UserROrganization> uroList = userROrganizationService.lambdaQuery()
                    .eq(UserROrganization::getOrganizationId,byId.getOrganizationId()).list();
            if (CommonUtils.isNotEmpty(uroList)){
                List<String> userIdList = uroList.stream().map(s -> s.getUserId().toString()).collect(Collectors.toList());
                tencentIMUtils.adminSendBatchSingleMsg(userIdList, BaseConstant.IM_DATE_INVENTORY_WARNING,BaseConstant.IM_DESC_INVENTORY_EXCEED,"");
            }
        }
    }

    @Override
    public List<SalesOrderExportVo> querySalesOrderList(QuerySalesOrderParam param) {
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        return this.baseMapper.querySalesOrderList(param);
    }

    @Override
    public void clearSalesOrder() {
        LocalDateTime startDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        this.lambdaUpdate().eq(SalesOrder::getOrderState,SalesOrderStateEnum.HANG.getCode())
                .lt(SalesOrder::getCreateTime,startDateTime)
                .set(SalesOrder::getIsDelete,TrueEnum.TRUE.getCode()).update(new SalesOrder());
        log.info("{}销售挂单清理完毕",DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
    }

    @Override
    public void deleteSalesOrderByOrganizationId(Long organizationId) {
        this.baseMapper.deleteSalesOrderByOrganizationId(organizationId);
    }

    @Override
    public Integer getSalesOrderCount(Long organizationId, String day) {
        return this.baseMapper.getSalesOrderCount(organizationId, day);
    }
}
