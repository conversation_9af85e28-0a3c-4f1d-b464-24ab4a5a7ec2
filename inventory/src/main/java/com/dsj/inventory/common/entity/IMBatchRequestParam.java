package com.dsj.inventory.common.entity;

import com.dsj.inventory.common.entity.IMMsgBody;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * IM 请求参数 json内容 群发
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class IMBatchRequestParam {

    /**
     * 选填
     * 1：把消息同步到 From_Account 在线终端和漫游上；
     * 2：消息不同步至 From_Account；若不填写默认情况下会将消息存 From_Account 漫游
     */
    private Integer SyncOtherMachine;

    /**
     * 必填
     * 消息发送方 UserID（用于指定发送消息方帐号
     */
    private String From_Account;

    /**
     * 必填
     * 消息接收方 UserID 多个
     */
    private List<String> To_Account;

    /**
     * 选填
     * 消息离线保存时长（单位：秒），最长为7天（604800秒）
     * 若设置该字段为0，则消息只发在线用户，不保存离线
     * 若设置该字段超过7天（604800秒），仍只保存7天
     * 若不设置该字段，则默认保存7天
     */
    private Integer MsgLifeTime;

    /**
     * 选填
     * 消息序列号（32位无符号整数），后台会根据该字段去重及进行同秒内消息的排序，详细规则请看本接口的功能说明。
     * 若不填该字段，则由后台填入随机数
     */
    private Integer MsgSeq;

    /**
     * 必填
     * 消息随机数（32位无符号整数），后台用于同一秒内的消息去重。请确保该字段填的是随机
     */
    private Integer MsgRandom;

    /**
     * 选填
     * 	消息回调禁止开关，只对本条消息有效，
     * 	ForbidBeforeSendMsgCallback 表示禁止发消息前回调，
     * 	ForbidAfterSendMsgCallback 表示禁止发消息后回调
     */
    private List<String> ForbidCallbackControl;

    /**
     * 选填
     *  消息发送控制选项，是一个 String 数组，只对本条消息有效。
     * "NoUnread"表示该条消息不计入未读数。
     * "NoLastMsg"表示该条消息不更新会话列表。
     * "WithMuteNotifications"表示该条消息的接收方对发送方设置的免打扰选项生效（默认不生效）。
     * 示例："SendMsgControl": ["NoUnread","NoLastMsg","WithMuteNotifications"]
     */
    private List<String> SendMsgControl;

    /**
     * 必填
     * 消息内容
     */
    private List<IMMsgBody> MsgBody;

    /**
     * 选填
     * 消息自定义数据（云端保存，会发送到对端，程序卸载重装后还能拉取到）
     */
    private String CloudCustomData;

    /**
     * 选填
     * 离线推送信息配置
     */
    private Object OfflinePushInfo;

    /**
     * 选填
     * 该条消息是否需要已读回执，0为不需要，1为需要，默认为0
     */
    private Integer IsNeedReadReceipt;

}
