package com.dsj.inventory.bussiness.inventory.controller;


import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pocky.transport.bussiness.diagnose.param.DisableParam;
import com.dsj.inventory.bussiness.inventory.param.QueryMedicineTemplateParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateMedicineTemplateParam;
import com.dsj.inventory.bussiness.inventory.service.MedicineTemplateService;
import com.dsj.inventory.bussiness.inventory.vo.MedicineTemplateVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 药品拉取模板 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@RestController
@Api(value = "MedicineTemplateController", tags = "药品拉取模板api")
public class MedicineTemplateController {

    @Autowired
    private MedicineTemplateService medicineTemplateService;

    /*新增药品拉取模板*/
    @PostMapping("/medicine-template")
    @ApiOperation(value = "新增药品拉取模板", notes = "新增药品拉取模板")
    public ResponseEntity<MedicineTemplateVo> saveMedicineTemplate(@RequestBody SaveOrUpdateMedicineTemplateParam param){
        MedicineTemplateVo medicineTemplateVo = medicineTemplateService.saveOrUpdateMedicineTemplate(param);
        return Res.success(medicineTemplateVo);
    }

    /*编辑药品拉取模板*/
    @PutMapping("/medicine-template/{id}")
    @ApiOperation(value = "编辑药品拉取模板", notes = "编辑药品拉取模板")
    public ResponseEntity<MedicineTemplateVo> updateMedicineTemplate(@PathVariable Long id, @RequestBody SaveOrUpdateMedicineTemplateParam param){
        param.setId(id);
        MedicineTemplateVo medicineTemplateVo = medicineTemplateService.saveOrUpdateMedicineTemplate(param);
        return Res.success(medicineTemplateVo);
    }

    /*删除药品拉取模板*/
    @DeleteMapping("/medicine-template/{id}")
    @ApiOperation(value = "删除药品拉取模板", notes = "删除药品拉取模板")
    public ResponseEntity<Void> deleteMedicineTemplate(@PathVariable Long id){
        SaveOrUpdateMedicineTemplateParam param = SaveOrUpdateMedicineTemplateParam.builder().isDelete(TrueEnum.TRUE.getCode()).id(id).build();
        medicineTemplateService.saveOrUpdateMedicineTemplate(param);
        return Res.success();
    }

    /*获取药品拉取模板列表*/
    @GetMapping("/medicine-template")
    @ApiOperation(value = "获取药品拉取模板列表", notes = "获取药品拉取模板列表")
    public ResponseEntity<List<MedicineTemplateVo>> queryMedicineTemplateList(QueryMedicineTemplateParam param){
        Page<MedicineTemplateVo> page = medicineTemplateService.queryMedicineTemplatePage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取药品拉取模板详情*/
    @GetMapping("/medicine-template/{id}")
    @ApiOperation(value = "获取药品拉取模板详情", notes = "获取药品拉取模板详情")
    public ResponseEntity<MedicineTemplateVo> queryMedicineTemplate(@PathVariable Long id){
        return Res.success(medicineTemplateService.getMedicineTemplateById(id));
    }

    /*变更药品拉取模板状态 --待启用或开启*/
    @PostMapping("/medicine-template/{id}/action-disable")
    @ApiOperation(value = "变更药品拉取模板状态", notes = "变更药品拉取模板待启用状态 --待启用或开启")
    public ResponseEntity<Void> disableMedicineTemplate(@PathVariable Long id, @RequestBody DisableParam param){
        param.setIds(ListUtil.toList(id));
        medicineTemplateService.disableMedicineTemplate(param);
        return Res.success();
    }

}
