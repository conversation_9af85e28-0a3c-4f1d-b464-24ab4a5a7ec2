package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 组织付款方式配置视图对象
 */
@Data
@ApiModel(description = "组织付款方式配置视图对象")
public class PaymentConfigVO {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "付款方式ID")
    private Long paymentMethodId;

    @ApiModelProperty(value = "组织ID")
    private Long organizationId;

    @ApiModelProperty(value = "配置项键")
    private String configKey;

    @ApiModelProperty(value = "配置项值")
    private String configValue;

    @ApiModelProperty(value = "配置项描述")
    private String description;

    @ApiModelProperty(value = "是否启用，1启用，0禁用")
    private Integer enabled;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
} 