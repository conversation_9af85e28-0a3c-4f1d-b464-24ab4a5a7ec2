package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 进销存销售活动信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_sales_activity")
@ApiModel(value="SalesActivity", description="进销存销售活动信息")
public class SalesActivity extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "模块分类，1 云药快 ；2 外部；3 在线医院；4 商城；5进销存")
    @TableField("module_type")
    private Integer moduleType;

    @ApiModelProperty(value = "活动类别，1单品活动 2会员日活动 3整单活动")
    @TableField("activity_category")
    private Integer activityCategory;

    @ApiModelProperty(value = "活动类型 1逢倍折扣 2满件减 3单品特价 4满元减元")
    @TableField("activity_type")
    private Integer activityType;

    @ApiModelProperty(value = "活动状态 0未开始 1进行中 2已暂停 3已结束")
    @TableField("activity_state")
    private Integer activityState;

    @ApiModelProperty(value = "生效状态 0未生效 1生效中")
    @TableField("assert_state")
    private Integer assertState;

    @ApiModelProperty(value = "活动名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "活动开始时间")
    @TableField("start_date")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "活动结束时间")
    @TableField("end_date")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "活动规则json")
    @TableField("rule")
    private String rule;

    @ApiModelProperty(value = "适用人群，0全部，其余数字为会员等级")
    @TableField("apply_to")
    private Integer applyTo;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}