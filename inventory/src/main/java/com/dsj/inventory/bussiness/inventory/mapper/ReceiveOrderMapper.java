package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.ReceiveOrder;
import com.dsj.inventory.bussiness.inventory.enumeration.ReceiveOrderStateEnum;
import com.dsj.inventory.bussiness.inventory.param.QueryReceiveOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存收货单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
public interface ReceiveOrderMapper extends BaseMapper<ReceiveOrder> {

    /**
     * 获取收货单分页列表
     * @param page
     * @param param
     * @return
     */
    List<ReceiveOrderVo> queryReceiveOrderPage(@Param("page") Page page, @Param("param") QueryReceiveOrderParam param);

    /**
     * 获取收货单详情
     * @param id
     * @return
     */
    ReceiveOrderDetailVo getReceiveOrderById(Long id);

    /**
     * 导出收货单列表
     * @param param
     * @return
     */
    List<ReceiveOrderExportVo> queryReceiveOrderList(@Param("param") QueryReceiveOrderParam param);

    /**
     * 删除组织的收货单
     * @param organizationId
     */
    void deleteReceiveOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天收货单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getReceiveOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);
    
    /**
     * 检查采购订单是否已经绑定到其他收货单
     * @param orderId 采购订单ID
     * @param excludeReceiveOrderId 需要排除的收货单ID（可为null）
     * @return 绑定计数
     */
    default int countByOrderIdExcludeReceiveOrderId(Long orderId, Long excludeReceiveOrderId) {
        LambdaQueryWrapper<ReceiveOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReceiveOrder::getOrderId, orderId);
        if (excludeReceiveOrderId != null) {
            wrapper.ne(ReceiveOrder::getId, excludeReceiveOrderId);
        }
        return this.selectCount(wrapper);
    }
    
    /**
     * 更新收货单状态
     * @param id 收货单ID
     * @param state 目标状态
     * @return 是否更新成功
     */
    default boolean updateStateById(Long id, Integer state) {
        LambdaUpdateWrapper<ReceiveOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ReceiveOrder::getId, id);
        updateWrapper.set(ReceiveOrder::getDealState, state);
        return update(null, updateWrapper) > 0;
    }
    
    /**
     * 将已审核状态的收货单更新为已完成状态
     * @param orderId 收货单ID
     * @return 是否更新成功
     */
    default boolean updateReviewedToCompleted(Long orderId) {
        LambdaUpdateWrapper<ReceiveOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ReceiveOrder::getId, orderId);
        updateWrapper.eq(ReceiveOrder::getDealState, ReceiveOrderStateEnum.REVIEWED.getCode());
        updateWrapper.set(ReceiveOrder::getDealState, ReceiveOrderStateEnum.COMPLETED.getCode());
        return update(null, updateWrapper) > 0;
    }
}
