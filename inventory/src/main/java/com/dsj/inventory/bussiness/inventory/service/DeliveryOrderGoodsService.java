package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.DeliveryOrderGoods;
import com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存退货单商品明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface DeliveryOrderGoodsService extends IService<DeliveryOrderGoods> {

    /**
     * 获取退货单商品明细
     * @param orderId
     * @return
     */
    List<DeliveryOrderGoodsVo> getDeliveryOrderGoodsByOrderId(Long orderId);

}
