package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.SalesOrder;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesOrderParam;
import com.dsj.inventory.bussiness.inventory.param.ReturnSalesOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateSalesOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.SalesOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesOrderExportVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存销售单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface SalesOrderService extends IService<SalesOrder> {

    /**
     * 新增、编辑 销售单
     * @param param
     * @return
     */
    SalesOrderDetailVo saveOrUpdateSalesOrder(SaveOrUpdateSalesOrderParam param);

    /**
     * 删除 销售单
     * @param id
     */
    void deleteSalesOrder(Long id);

    /**
     * 获取销售单分页列表
     * @param param
     * @return
     */
    Page<SalesOrderDetailVo> querySalesOrderPage(QuerySalesOrderParam param);

    /**
     * 获取销售单列表详情
     * @param id
     * @return
     */
    SalesOrderDetailVo getSalesOrderById(Long id);

    /**
     * 销售单退货
     * @param param
     */
    void returnSalesOrder(ReturnSalesOrderParam param);

    /**
     * 导出销售单列表
     * @param param
     * @return
     */
    List<SalesOrderExportVo> querySalesOrderList(QuerySalesOrderParam param);

    /**
     * 清理挂单
     */
    void clearSalesOrder();

    /**
     * 删除组织的销售单
     * @param organizationId
     */
    void deleteSalesOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天销售单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getSalesOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);

}
