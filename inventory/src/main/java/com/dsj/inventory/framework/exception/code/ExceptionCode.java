package com.dsj.inventory.framework.exception.code;


import com.dsj.inventory.framework.exception.code.BaseExceptionCode;

/**
 * 全局错误码 10000-15000
 * <p>
 * 预警异常编码    范围： 30000~34999
 * 标准服务异常编码 范围：35000~39999
 * 邮件服务异常编码 范围：40000~44999
 * 短信服务异常编码 范围：45000~49999
 * 权限服务异常编码 范围：50000-59999
 * 文件服务异常编码 范围：60000~64999
 * 日志服务异常编码 范围：65000~69999
 * 消息服务异常编码 范围：70000~74999
 * 开发者平台异常编码 范围：75000~79999
 * 搜索服务异常编码 范围：80000-84999
 * 共享交换异常编码 范围：85000-89999
 * 移动终端平台 异常码 范围：90000-94999
 * <p>
 * 安全保障平台    范围：        95000-99999
 * 软硬件平台 异常编码 范围：    100000-104999
 * 运维服务平台 异常编码 范围：  105000-109999
 * 统一监管平台异常 编码 范围：  110000-114999
 * 认证方面的异常编码  范围：115000-115999
 *
 * <AUTHOR>
 * @createTime 2017-12-13 16:22
 */
public enum ExceptionCode implements BaseExceptionCode {

    //系统相关 start
    SUCCESS(0, "成功"),
    SYSTEM_BUSY(-1, "系统繁忙~请稍后再试~"),
    SYSTEM_TIMEOUT(-2, "系统维护中~请稍后再试~"),
    PARAM_EX(-3, "参数类型解析异常"),
    SQL_EX(-4, "运行SQL出现异常"),
    NULL_POINT_EX(-5, "空指针异常"),
    ILLEGALA_ARGUMENT_EX(-6, "无效参数异常"),
    MEDIA_TYPE_EX(-7, "请求类型异常"),
    LOAD_RESOURCES_ERROR(-8, "加载资源出错"),
    BASE_VALID_PARAM(-9, "统一验证参数异常"),
    OPERATION_EX(-10, "操作异常"),
    SERVICE_MAPPER_ERROR(-11, "Mapper类转换异常"),
    CAPTCHA_ERROR(-12, "验证码校验失败"),
    LIVE_NAME_REPEAT(90006, "该直播间名称已经被占用，请换一个试试！"),
    VIDEO_UPLOAD_TENCENT_FAIL(90007, "短视频上传到腾讯接口失败！"),
    VIDEO_NAME_REPEAT(90008, "该短视频名称已经被占用，请换一个试试！"),
    MEMBER_ID_NE(90009, "会员id不同，无法进行操作！"),
    ORDER_STATUS_ERROR(90010, "只有待付款的订单才能取消！"),
    PRODUCT_STOCK_ZERO(90011, "商品库存不足，无法下单！"),
    PROFIT_PERCENT_UNEXIST(90012,"平台未设置分成比例，请联系平台管理员设置！"),
    BADPRICE_ORDER_ERROR(90013, "非法的订单数据，无法下单！"),
    BADPRICE_ACUTION_ORDER_ERROR(90014, "出价小于或等于目前最高出价，无法下单！"),
    ACUTION_OVER_ERROR(90015, "该拍卖已经结束，无法下单！"),

    OK(200, "OK"),
    BAD_REQUEST(400, "错误的请求"),
    /**
     * {@code 401 Unauthorized}.
     *
     * @see <a href="http://tools.ietf.org/html/rfc7235#section-3.1">HTTP/1.1: Authentication, section 3.1</a>
     */
    UNAUTHORIZED(401, "未经授权"),
    /**
     * {@code 404 Not Found}.
     *
     * @see <a href="http://tools.ietf.org/html/rfc7231#section-6.5.4">HTTP/1.1: Semantics and Content, section 6.5.4</a>
     */
    NOT_FOUND(404, "没有找到资源"),
    METHOD_NOT_ALLOWED(405, "不支持当前请求类型"),

    TOO_MANY_REQUESTS(429, "请求超过次数限制"),
    INTERNAL_SERVER_ERROR(500, "内部服务错误"),
    BAD_GATEWAY(502, "网关错误"),
    GATEWAY_TIMEOUT(504, "网关超时"),
    //系统相关 end

    REQUIRED_FILE_PARAM_EX(1001, "请求中必须至少包含一个有效文件"),

    DATA_SAVE_ERROR(2000, "新增数据失败"),
    DATA_UPDATE_ERROR(2001, "修改数据失败"),
    TOO_MUCH_DATA_ERROR(2002, "批量新增数据过多"),
    //jwt token 相关 start

    JWT_BASIC_INVALID(401, "无效的基本身份验证令牌"),
    JWT_TOKEN_EXPIRED(401, "会话超时，请重新登录"),
    JWT_SIGNATURE(401, "不合法的token，请认真比对 token 的签名"),
    JWT_ILLEGAL_ARGUMENT(401, "缺少token参数"),
    JWT_GEN_TOKEN_FAIL(401, "生成token失败"),
    JWT_PARSER_TOKEN_FAIL(401, "解析用户身份错误，请重新登录！"),
    JWT_USER_INVALID(40006, "用户名或密码错误"),
    JWT_USER_ENABLED(403, "用户已经被禁用！"),
    JWT_OFFLINE(40008, "您已在另一个设备登录！"),
    AUTH_CODE_INVALID(90002, "授权码已失效，请换一个新的！"),
    AUTH_CODE_USED(90003, "授权码已经使用过了，请换一个新的！")
    //jwt token 相关 end

    ;

    private int code;
    private String msg;

    ExceptionCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }


    public com.dsj.inventory.framework.exception.code.ExceptionCode build(String msg, Object... param) {
        this.msg = String.format(msg, param);
        return this;
    }

    public com.dsj.inventory.framework.exception.code.ExceptionCode param(Object... param) {
        msg = String.format(msg, param);
        return this;
    }
}
