package com.dsj.inventory.bussiness.inventory.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.CashierWorking;
import com.dsj.inventory.bussiness.inventory.entity.Member;
import com.dsj.inventory.bussiness.inventory.entity.MemberCoupon;
import com.dsj.inventory.bussiness.inventory.entity.MemberRechargeRecord;
import com.dsj.inventory.bussiness.inventory.mapper.MemberMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateMemberParam;
import com.dsj.inventory.bussiness.inventory.service.CashierWorkingService;
import com.dsj.inventory.bussiness.inventory.service.MemberCouponService;
import com.dsj.inventory.bussiness.inventory.service.MemberRechargeRecordService;
import com.dsj.inventory.bussiness.inventory.service.MemberService;
import com.dsj.inventory.bussiness.inventory.vo.MemberExportVo;
import com.dsj.inventory.bussiness.inventory.vo.MemberVo;
import com.dsj.inventory.common.constant.BaseConstant;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.common.utils.DozerUtils;
import com.dsj.inventory.common.utils.TencentIMUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.auth.entity.User;
import com.pocky.transport.bussiness.auth.enumeration.RoleEnum;
import com.pocky.transport.bussiness.auth.service.IUserService;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <p>
 * 进销存会员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@Service
public class MemberServiceImpl extends ServiceImpl<MemberMapper, Member> implements MemberService {

    @Autowired
    private DozerUtils dozerUtils;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private IUserService userService;
    @Autowired
    private MemberRechargeRecordService memberRechargeRecordService;
    @Autowired
    private CashierWorkingService cashierWorkingService;
    @Autowired
    private MemberCouponService memberCouponService;
    @Autowired
    private TencentIMUtils tencentIMUtils;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberVo saveOrUpdateMember(SaveOrUpdateMemberParam param) {
        try {
            if (CommonUtils.isEmpty(param)){
                throw new ParamException("参数异常");
            }
            if(CommonUtils.isNotEmpty(param.getId())) {
                Member byId = this.getById(param.getId());
                if (CommonUtils.isEmpty(byId)) {
                    throw new BizException("操作失败，数据不存在");
                }
                if (CommonUtils.isNotEmpty(param.getIsDelete()) && TrueEnum.TRUE.getCode().equals(param.getIsDelete())){
                    this.baseMapper.deleteById(param.getId());
                    //删除会员充值记录
                    memberRechargeRecordService.lambdaUpdate().eq(MemberRechargeRecord::getMemberId,param.getId())
                            .set(MemberRechargeRecord::getIsDelete,TrueEnum.TRUE.getCode()).update(new MemberRechargeRecord());
                    //删除会员手中的优惠券
                    memberCouponService.lambdaUpdate().eq(MemberCoupon::getMemberId,param.getId())
                            .set(MemberCoupon::getIsDelete,TrueEnum.TRUE.getCode()).update(new MemberCoupon());
                    return null;
                }
                //会员积分不能编辑
                param.setUsableIntegral(null);
                //办理人不能修改
                param.setTransactUser(null);
            }else {
                //会员卡号：药房标识码+手机号后4位+年月日6位+随机3位数字加英文
                Organization organization = organizationService.getById(param.getOrganizationId());
                String day = DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_SHORT_DATE_FORMAT);
                String dayStr = day.replaceAll("-", "");
                String code = organization.getSerialNo() + param.getPhone().substring(param.getPhone().length()-4,param.getPhone().length()-1)
                        + dayStr + RandomUtil.randomStringUpper(3);
                param.setCode(code);
                if(CommonUtils.isEmpty(param.getTransactUser())){
                    param.setTransactUser(BaseContextHandler.getUserId());
                }
            }

            //判断手机号在药房是否已绑定会员
            if(!checkPhone(param.getId(),param.getPhone(),param.getOrganizationId())){
                throw new BizException("操作失败，手机号已存在会员");
            }
            User user = userService.lambdaQuery().eq(User::getPhone,param.getPhone()).one();
            if(CommonUtils.isEmpty(user)){
                //新增用户
                user = dozerUtils.map(param,User.class);
                user.setPwd(BaseConstant.INITIAL_PWD);
                user.setPwdExpirationDate(LocalDateTime.now().plusDays(BaseConstant.PWD_EXPIRATION_DAYS));
                userService.save(user);
            }
            param.setUserId(user.getId());
            Member member = dozerUtils.map(param,Member.class);
            this.saveOrUpdate(member);
            if(CommonUtils.isEmpty(param.getId()) && CommonUtils.isNotEmpty(param.getRechargePrice())){
                MemberRechargeRecord memberRechargeRecord = dozerUtils.map(param,MemberRechargeRecord.class);
                memberRechargeRecord.setMemberId(member.getId());
                memberRechargeRecordService.save(memberRechargeRecord);
                //更改会员储值金额
                if(CommonUtils.isEmpty(memberRechargeRecord.getRechargePrice())){
                    memberRechargeRecord.setRechargePrice(0);
                }
                if(CommonUtils.isEmpty(memberRechargeRecord.getGivePrice())){
                    memberRechargeRecord.setGivePrice(0);
                }
                BigDecimal rechargePrice = BigDecimal.valueOf(memberRechargeRecord.getRechargePrice())
                        .add(BigDecimal.valueOf(memberRechargeRecord.getGivePrice()));
                BigDecimal balance = BigDecimal.valueOf(0).add(rechargePrice);
                this.lambdaUpdate().eq(Member::getId,member.getId()).set(Member::getBalance,balance.intValue()).update(new Member());
                //更改营业员交班记录
                Long cashierId = param.getTransactUser();
                LocalDateTime startDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
                LocalDateTime endDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(23,59,59));
                CashierWorking cashierWorking = cashierWorkingService.lambdaQuery().eq(CashierWorking::getCashierId,cashierId)
                        .eq(CashierWorking::getOrganizationId,param.getOrganizationId())
                        .ge(CashierWorking::getCreateTime, startDateTime)
                        .le(CashierWorking::getCreateTime,endDateTime).one();
                if(CommonUtils.isEmpty(cashierWorking)){
                    CashierWorking newWorking = CashierWorking.builder().cashierId(cashierId)
                            .startDate(LocalDateTime.now()).endDate(LocalDateTime.now())
                            .organizationId(param.getOrganizationId()).rechargePrice(rechargePrice.intValue())
                            .rechargeRealPrice(memberRechargeRecord.getRechargePrice())
                            .rechargeGivePrice(memberRechargeRecord.getGivePrice()).build();
                    cashierWorkingService.save(newWorking);
                }else {
                    cashierWorkingService.lambdaUpdate().eq(CashierWorking::getId,cashierWorking.getId())
                            .set(CashierWorking::getEndDate,LocalDateTime.now())
                            .set(CashierWorking::getRechargePrice, BigDecimal.valueOf(cashierWorking.getRechargePrice()).add(rechargePrice))
                            .set(CashierWorking::getRechargeRealPrice,BigDecimal.valueOf(cashierWorking.getRechargeRealPrice()).add(BigDecimal.valueOf(memberRechargeRecord.getRechargePrice())))
                            .set(CashierWorking::getRechargeGivePrice,BigDecimal.valueOf(cashierWorking.getRechargeGivePrice()).add(BigDecimal.valueOf(memberRechargeRecord.getGivePrice())))
                            .update(new CashierWorking());
                }
            }
            if(CommonUtils.isEmpty(param.getId())){
                //发IM消息
                cn.hutool.json.JSONObject imJson = new cn.hutool.json.JSONObject();
                imJson.set("type","memberKey");
                imJson.set("id",param.getTransactUser());
                imJson.set("data",member.getId());
                tencentIMUtils.adminSendSingleMsg(param.getTransactUser(),BaseConstant.IM_DATE_POLL,"member-" + param.getTransactUser() , imJson.toString());
            }
            return this.getMemberById(member.getId());
        }catch (Exception e){
            if(CommonUtils.isEmpty(param.getId())) {
                //发IM消息
                cn.hutool.json.JSONObject imJson = new cn.hutool.json.JSONObject();
                imJson.set("type", "memberKeyError");
                imJson.set("id", param.getTransactUser());
                imJson.set("data", e.getMessage());
                tencentIMUtils.adminSendSingleMsg(param.getTransactUser(), BaseConstant.IM_DATE_POLL, "member-" + param.getTransactUser() + "_error", imJson.toString());
            }
            throw new BizException(e.getMessage());
        }
    }

    /**
     * 判断手机号是否已绑定会员
     * @param id
     * @param phone
     * @param organizationId
     * @return
     */
    private Boolean checkPhone(Long id,String phone,Long organizationId){
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Member::getPhone, phone);
        wrapper.eq(Member::getOrganizationId, organizationId);
        if (CommonUtils.isNotEmpty(id)){
            wrapper.ne(Member::getId,id);
        }
        return this.count(wrapper) < 1;
    }

    @Override
    public Page<MemberVo> queryMemberPage(QueryMemberParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(RoleEnum.USER.getCode().equals(BaseContextHandler.get(BaseContextConstants.USER_ROLE))){
            param.setUserId(BaseContextHandler.getUserId());
        }else if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        } else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<MemberVo> list = this.baseMapper.queryMemberPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public MemberVo getMemberById(Long id) {
        return this.baseMapper.getMemberById(id);
    }

    @Override
    public List<MemberExportVo> queryMemberList(QueryMemberParam param) {
        if(RoleEnum.USER.getCode().equals(BaseContextHandler.get(BaseContextConstants.USER_ROLE))){
            param.setUserId(BaseContextHandler.getUserId());
        }else if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        } else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        return this.baseMapper.queryMemberList(param);
    }
}
