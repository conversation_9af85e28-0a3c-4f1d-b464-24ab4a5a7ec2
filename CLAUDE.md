# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 常用命令

### 构建和运行
```bash
# 项目根目录构建
mvn clean compile

# 模块构建
cd inventory && mvn clean compile

# 运行应用
mvn spring-boot:run -pl inventory

# 打包
mvn clean package -DskipTests

# 运行JAR包
java -jar inventory/target/inventory-1.0.0.jar
```

### 测试
```bash
# 运行所有测试
mvn test

# 运行特定模块测试
mvn test -pl inventory

# 运行特定测试类
mvn test -Dtest=InventoryApplicationTests

# 运行特定测试方法
mvn test -Dtest=NumberGeneratorFactoryTest#testRedisNumberGenerator
```

### 数据库相关
```bash
# 查看数据库架构更新脚本
cat doc/sql/update.sql

# 测试环境使用H2数据库，架构文件位于
cat inventory/src/test/resources/schema.sql
```

## 高级代码架构

### 整体架构
本项目是基于Spring Boot 2.3.9的微服务库存管理系统，采用分层架构：

- **业务层**: `bussiness/` - 按业务模块划分
  - `inventory/` - 核心库存管理业务
  - `quality/` - 质量管理业务 
  - `timeTask/` - 定时任务
- **通用层**: `common/` - 跨业务的通用组件
- **框架层**: `framework/` - 基础设施和配置
- **系统层**: `system/` - 系统级功能

### 关键设计模式

#### 分层架构 (Controller-Service-Mapper)
```
Controller → Service → ServiceImpl → Mapper → Database
   ↓           ↓          ↓           ↓
 Param →     Param  →   Entity   →  SQL
   ↓           ↓          ↓           ↓
  VO   ←      VO    ←   Entity   ←  Result
```

#### 数据传输对象模式
- **Param**: 请求参数对象 (Controller → Service)
- **VO**: 视图对象 (Service → Controller → 前端)
- **Entity**: 实体对象 (数据库映射)

#### 统一响应格式
- 成功响应: `Res.success(data)` 或 `Res.successPage(page)`
- 错误响应: 通过全局异常处理器自动处理

### 核心组件

#### 权限控制
- `@OrgAuthMode`: 组织和菜单权限控制注解
- 支持组织权限(`strict`)和菜单权限(`strictUserMenus`)的灵活配置

#### 分布式组件
- **缓存**: Redis + `CacheRepository` 封装
- **分布式锁**: `RedisDistributedLock`
- **编号生成**: `NumberGeneratorFactory` (支持Redis和Snowflake两种策略)

#### 数据访问
- MyBatis-Plus增强
- 实体类继承 `com.dsj.inventory.common.entity.Entity`
- 分页参数继承 `com.dsj.inventory.common.entity.BasePage`
- Mapper接口继承 `BaseMapper<Entity>`
- Service继承 `IService<Entity>` 和 `ServiceImpl<Mapper, Entity>`

#### 异常处理
- `BizException`: 业务异常
- `ParamException`: 参数异常  
- `DefaultGlobalExceptionHandler`: 统一异常处理

### 业务模块结构

每个业务模块遵循标准结构：
```
bussiness/模块名/
├── controller/     # API控制器
├── service/        # 服务接口
│   └── impl/       # 服务实现
├── mapper/         # 数据访问层
├── entity/         # 实体类
├── param/          # 请求参数
├── vo/             # 视图对象
└── enumeration/    # 枚举定义
```

### 开发规范要点

#### Controller层
- 必须使用 `@OrgAuthMode` 进行权限控制
- 返回类型统一为 `ResponseEntity<?>`
- 禁止直接注入Mapper，只能注入Service
- 使用 `@Valid` 进行参数校验

#### Service层 
- 禁止在Service层使用Wrapper，所有动态查询封装在Mapper层
- 事务方法使用 `@Transactional(rollbackFor = Exception.class)`
- 业务异常抛出 `BizException`，参数异常抛出 `ParamException`

#### 测试
- 测试配置文件: `application-test.yml`
- 使用本地的Mysql数据库进行单元测试
- 架构脚本: `inventory/src/test/resources/schema.sql`

### 技术栈关键信息
- Spring Boot 2.3.9.RELEASE + Spring Cloud Hoxton.SR6
- MyBatis-Plus 3.3.1
- MySQL 8.0.19 (生产) / MySQL (测试)
- Redis缓存和分布式锁
- Knife4j API文档
- XXL-Job定时任务
- Hutool工具库 (优先使用)
- P6Spy SQL监控

### 配置文件层次
- `application.yml`: 主配置
- `application-datasource.yml`: 数据源配置
- `application-home.yml`: 环境特定配置
- `application-test.yml`: 测试环境配置