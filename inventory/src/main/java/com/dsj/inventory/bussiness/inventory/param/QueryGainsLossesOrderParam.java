package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询损溢单
 * <AUTHOR>
 * @date 2023/04/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QueryGainsLossesOrderParam",description = "查询损溢单参数")
public class QueryGainsLossesOrderParam extends BasePage {

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "损溢类型 1损2溢")
    private Integer type;

    @ApiModelProperty(value = "损溢单号模糊搜索")
    private String codeLike;

    @ApiModelProperty(value = "创建人模糊搜索")
    private String createUserNameLike;

    @ApiModelProperty(value = "创建时间，查询条件--起始时间")
    private String createStart;

    @ApiModelProperty(value = "创建时间，查询条件--结束时间")
    private String createEnd;

}
