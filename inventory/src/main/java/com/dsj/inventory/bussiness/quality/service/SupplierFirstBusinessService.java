package com.dsj.inventory.bussiness.quality.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationSupplier;
import com.dsj.inventory.bussiness.quality.entity.SupplierFirstBusinessEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.quality.param.SupplierFirstBusinessCreateParam;
import com.dsj.inventory.bussiness.quality.param.SupplierFirstBusinessApprovalParam;
import com.dsj.inventory.bussiness.quality.param.SupplierFirstBusinessQueryParam;
import com.dsj.inventory.bussiness.quality.param.SupplierFirstBusinessUpdateParam;
import com.dsj.inventory.bussiness.quality.vo.SupplierFirstBusinessVO;

import java.util.List;

/**
 * <p>
 * 供应商首营审批表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface SupplierFirstBusinessService extends IService<SupplierFirstBusinessEntity> {

    /**
     * 创建供应商首营审批
     * 如果已经存在该供应商的草稿记录，则直接返回现有记录ID
     *
     * @param supplier 供应商实体
     * @return 主键ID
     */
    Long createSupplierFirstBusiness(OrganizationSupplier supplier);

    /**
     * 获取供应商首营审批详情
     *
     * @param id 主键ID
     * @return 详情VO
     */
    SupplierFirstBusinessVO getSupplierFirstBusinessById(Long id);

    /**
     * 分页查询供应商首营审批
     *
     * @param param 查询参数
     * @return 分页结果
     */
    Page<SupplierFirstBusinessVO> querySupplierFirstBusinessPage(SupplierFirstBusinessQueryParam param);

    /**
     * 审批（通过/驳回）
     *
     * @param param 审批参数
     * @return 审批后的记录ID
     */
    Long approveSupplierFirstBusiness(SupplierFirstBusinessApprovalParam param);

    /**
     * 撤回供应商首营审批
     *
     * @param supplierId 供应商ID
     * @return 撤回后的记录ID
     */
    Long withdrawSupplierFirstBusiness(Long supplierId);

    /**
     * 获取供应商首营审批最新状态
     *
     * @param supplierId 供应商ID
     * @return 详情VO
     */
    SupplierFirstBusinessVO getLatestSupplierFirstBusiness(Long supplierId);

    /**
     * 获取供应商首营审批历史记录
     *
     * @param supplierId 供应商ID
     * @return 历史记录列表
     */
    List<SupplierFirstBusinessVO> getSupplierFirstBusinessHistory(Long supplierId);

    /**
     * 查询待我审批的供应商首营记录
     *
     * @param param 查询参数
     * @return 分页结果
     */
    Page<SupplierFirstBusinessVO> queryPendingApprovalPage(SupplierFirstBusinessQueryParam param);

    /**
     * 为待审核的供应商创建修改审批流程
     *
     * @param supplier 状态为"审核中"的供应商实体
     * @return 主键ID
     */
    Long createSupplierModificationApproval(OrganizationSupplier supplier);

    /**
     * 审批供应商修改（通过/驳回）
     *
     * @param param 审批参数
     * @return 审批后的记录ID
     */
    Long approveSupplierModification(SupplierFirstBusinessApprovalParam param);
} 