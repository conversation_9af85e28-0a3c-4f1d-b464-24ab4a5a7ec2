package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 验收单处理状态 0暂存 1已验收 2已完成
 * <AUTHOR>
 */

@Getter
public enum AcceptOrderStateEnum {

    TEMPORARY(0, "暂存"),
    ACCEPTED(1, "已验收"),
    COMPLETED(2, "已完成"),
    ;

    private Integer code;

    private String desc;

    AcceptOrderStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (AcceptOrderStateEnum a : AcceptOrderStateEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
