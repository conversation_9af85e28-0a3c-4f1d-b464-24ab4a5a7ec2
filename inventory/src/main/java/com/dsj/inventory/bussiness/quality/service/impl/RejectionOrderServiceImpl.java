package com.dsj.inventory.bussiness.quality.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.quality.entity.RejectionOrder;
import com.dsj.inventory.bussiness.quality.entity.RejectionOrderGoods;
import com.dsj.inventory.bussiness.quality.enumeration.DocumentStatusEnum;
import com.dsj.inventory.bussiness.quality.mapper.RejectionOrderMapper;
import com.dsj.inventory.bussiness.quality.param.RejectionAuditParam;
import com.dsj.inventory.bussiness.quality.param.RejectionCreateParam;
import com.dsj.inventory.bussiness.quality.param.RejectedGoodsItem;
import com.dsj.inventory.bussiness.quality.param.RejectionOrderQueryParam;
import com.dsj.inventory.bussiness.quality.service.RejectionOrderGoodsService;
import com.dsj.inventory.bussiness.quality.service.RejectionOrderService;
import com.dsj.inventory.bussiness.quality.vo.RejectionOrderGoodsVO;
import com.dsj.inventory.bussiness.quality.vo.RejectionOrderVO;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.generator.NumberService;
import com.dsj.inventory.common.generator.NumberType;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 拒收单主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Service
public class RejectionOrderServiceImpl extends ServiceImpl<RejectionOrderMapper, RejectionOrder> implements RejectionOrderService {
    @Autowired
    private NumberService numberService;
    @Autowired
    private RejectionOrderGoodsService rejectionOrderGoodsService;

    @Override
    public Page<RejectionOrderVO> queryPage(RejectionOrderQueryParam param) {
        // 1. 调用Mapper层封装好的方法进行查询
        Page<RejectionOrder> page = baseMapper.selectPage(param);

        // 2. 转换结果为 Page<RejectionOrderVO>
        Page<RejectionOrderVO> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        if (CollUtil.isEmpty(page.getRecords())) {
            return voPage;
        }
        List<RejectionOrderVO> voRecords = page.getRecords().stream().map(order -> {
            RejectionOrderVO vo = BeanUtil.copyProperties(order, RejectionOrderVO.class);
            // TODO: 后续可根据 createUser ID 查询用户真实姓名并设置到 createUserName
            // TODO: 后续可根据 supplierId 查询供应商名称并设置到 supplierName
            return vo;
        }).collect(Collectors.toList());
        voPage.setRecords(voRecords);
        return voPage;
    }

    @Override
    public RejectionOrderVO getDetail(Long id) {
        RejectionOrder order = this.getById(id);
        if (order == null) {
            throw new BizException("查询的拒收单不存在");
        }

        RejectionOrderVO vo = BeanUtil.copyProperties(order, RejectionOrderVO.class);

        // 查询拒收单商品明细
        List<RejectionOrderGoods> goodsList = rejectionOrderGoodsService.listByRejectionOrderId(id);

        if (CollUtil.isNotEmpty(goodsList)) {
            List<RejectionOrderGoodsVO> goodsVOList = goodsList.stream()
                    .map(g -> BeanUtil.copyProperties(g, RejectionOrderGoodsVO.class))
                    .collect(Collectors.toList());
            vo.setGoodsList(goodsVOList);
        }

        // TODO: 丰富VO信息，如创建人名称、供应商名称等
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean audit(Long id, RejectionAuditParam param) {
        Assert.notNull(param.getApproved(), () -> new ParamException("审核结果不能为空"));

        // 1. 获取并校验单据
        RejectionOrder order = this.getById(id);
        if (order == null) {
            throw new BizException("操作的拒收单不存在");
        }
        if (!DocumentStatusEnum.REVIEWING.getCode().equals(order.getStatus())) {
            throw new BizException("该拒收单已处理，请勿重复操作");
        }

        // 2. 更新审核相关字段
        DocumentStatusEnum newStatus = param.getApproved() ? DocumentStatusEnum.APPROVED : DocumentStatusEnum.REJECTED;
        order.setStatus(newStatus.getCode());
        order.setAuditRemark(param.getRemark());
        order.setAuditTime(LocalDateTime.now());
        // 假设从上下文中获取当前用户ID
        order.setAuditUserId(BaseContextHandler.getUserId());

        // 3. 持久化
        return this.updateById(order);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createFromSource(RejectionCreateParam param) {
        // 1. 参数校验
        if (param == null || CollUtil.isEmpty(param.getRejectedItems())) {
            throw new ParamException("创建拒收单的参数不能为空");
        }

        // 2. 创建拒收单主表对象
        RejectionOrder rejectionOrder = new RejectionOrder();

        // 2.1 生成单据号
        rejectionOrder.setRejectionCode(numberService.generateNumber(NumberType.REJECTION_ORDER));

        // 2.2 设置来源信息
        rejectionOrder.setSourceOrderId(param.getSourceId());
        rejectionOrder.setSourceOrderCode(param.getSourceCode());
        rejectionOrder.setSourceOrderType(param.getSourceType().getCode());
        rejectionOrder.setSupplierId(param.getSupplierId());
        rejectionOrder.setSupplierName(param.getSupplierName());
        rejectionOrder.setRemark(param.getRemark());
        rejectionOrder.setOrganizationId(param.getOrganizationId());

        // 2.3 设置初始状态
        rejectionOrder.setStatus(DocumentStatusEnum.REVIEWING.getCode());

        // 2.4 聚合计算总数
        int totalQuantity = param.getRejectedItems().stream()
                .map(RejectedGoodsItem::getRejectionQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .intValue();
        rejectionOrder.setTotalRejectionQuantity(totalQuantity);

        // 2.5 设置操作人信息
        rejectionOrder.setCreateUser(BaseContextHandler.getUserId());
        rejectionOrder.setUpdateUser(BaseContextHandler.getUserId());

        // 2.6 保存主表
        if (!this.save(rejectionOrder)) {
            throw new BizException("创建拒收单主表失败");
        }

        // 3. 创建拒收单商品明细对象
        List<RejectionOrderGoods> goodsList = param.getRejectedItems().stream().map(item -> {
            RejectionOrderGoods goods = new RejectionOrderGoods();
            goods.setRejectionOrderId(rejectionOrder.getId());
            goods.setRejectionOrderCode(rejectionOrder.getRejectionCode());
            goods.setSourceOrderGoodsId(item.getSourceOrderGoodsId());
            goods.setGoodsId(item.getGoodsId());
            goods.setGoodsName(item.getGoodsName());
            goods.setBatchNo(item.getBatchNo());
            goods.setRejectionQuantity(item.getRejectionQuantity());
            goods.setRejectionReason(item.getRejectionReason());
            goods.setCreateUser(BaseContextHandler.getUserId());
            goods.setUpdateUser(BaseContextHandler.getUserId());
            return goods;
        }).collect(Collectors.toList());

        // 4. 批量保存商品明细
        if (!rejectionOrderGoodsService.saveBatch(goodsList)) {
            throw new BizException("创建拒收单商品明细失败");
        }

        // 5. 返回结果
        return rejectionOrder.getId();
    }
} 