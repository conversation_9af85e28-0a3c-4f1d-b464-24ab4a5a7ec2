package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.*;
import com.dsj.inventory.bussiness.inventory.enumeration.SalesActivityStateEnum;
import com.dsj.inventory.bussiness.inventory.mapper.SalesActivityTaskMapper;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesActivityTaskParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateSalesActivityTaskParam;
import com.dsj.inventory.bussiness.inventory.service.*;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskCycleVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskMappingVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskVo;
import com.dsj.inventory.common.cache.RedisRepositoryImpl;
import com.dsj.inventory.common.utils.DozerUtils;
import com.dsj.inventory.common.constant.CacheKey;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.entity.StrPool;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 销售活动任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@Slf4j
@Service
public class SalesActivityTaskServiceImpl extends ServiceImpl<SalesActivityTaskMapper, SalesActivityTask> implements SalesActivityTaskService {

    @Autowired
    private DozerUtils dozerUtils;
    @Autowired
    private SalesActivityService salesActivityService;
    @Autowired
    private SalesActivityTaskMappingService salesActivityTaskMappingService;
    @Autowired
    private SalesActivityTaskCycleService salesActivityTaskCycleService;
    @Autowired
    private SalesActivityTaskDateService salesActivityTaskDateService;
    @Autowired
    private RedisRepositoryImpl redisRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SalesActivityTaskVo saveOrUpdateSalesActivityTask(SaveOrUpdateSalesActivityTaskParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        Integer executeState = TrueEnum.FALSE.getCode();
        param.setModuleType(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.MODULE_TYPE)));
        if(CommonUtils.isNotEmpty(param.getId())) {
            SalesActivityTask byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            List<Long> activityIdList = salesActivityTaskMappingService.lambdaQuery()
                    .eq(SalesActivityTaskMapping::getTaskId,byId.getId()).list()
                    .stream().map(m -> m.getActivityId()).collect(Collectors.toList());
            //删除任务日期
            salesActivityTaskDateService.lambdaUpdate().in(SalesActivityTaskDate::getActivityId,activityIdList)
                    .set(SalesActivityTaskDate::getIsDelete,TrueEnum.TRUE.getCode()).update(new SalesActivityTaskDate());
            //删除活动任务关联
            salesActivityTaskMappingService.lambdaUpdate().eq(SalesActivityTaskMapping::getTaskId,byId.getId())
                    .set(SalesActivityTaskMapping::getIsDelete,TrueEnum.TRUE.getCode()).update(new SalesActivityTaskMapping());
            //删除任务周期
            salesActivityTaskCycleService.lambdaUpdate().eq(SalesActivityTaskCycle::getTaskId,byId.getId())
                    .set(SalesActivityTaskCycle::getIsDelete,TrueEnum.TRUE.getCode()).update(new SalesActivityTaskCycle());
            if (CommonUtils.isNotEmpty(param.getIsDelete()) && TrueEnum.TRUE.getCode().equals(param.getIsDelete())){
                this.baseMapper.deleteById(param.getId());
                //所有进行中活动更改为有效
                salesActivityService.lambdaUpdate().in(SalesActivity::getId,activityIdList)
                        .eq(SalesActivity::getActivityState,SalesActivityStateEnum.IN_PROGRESS.getCode())
                        .set(SalesActivity::getAssertState,TrueEnum.TRUE.getCode()).update(new SalesActivity());
                //删除redis
                for(Long activityId : activityIdList){
                    redisRepository.del(CacheKey.ACTIVITY_TASK_ASSERT_STATE + StrPool.COLON + activityId);
                }
                return null;
            }
            executeState = byId.getExecuteState();
            //更改本次编辑操作中不包含的进行中活动有效状态
            List<Long> deleteIdList = activityIdList;
            if(CommonUtils.isNotEmpty(param.getActivityIdList())){
                deleteIdList = activityIdList.stream().filter(o->!param.getActivityIdList().contains(o)).collect(Collectors.toList());
            }
            if(CommonUtils.isNotEmpty(deleteIdList)){
                salesActivityService.lambdaUpdate().in(SalesActivity::getId,deleteIdList)
                        .eq(SalesActivity::getActivityState,SalesActivityStateEnum.IN_PROGRESS.getCode())
                        .set(SalesActivity::getAssertState,TrueEnum.TRUE.getCode()).update(new SalesActivity());
                //删除redis
                for(Long activityId : deleteIdList){
                    redisRepository.del(CacheKey.ACTIVITY_TASK_ASSERT_STATE + StrPool.COLON + activityId);
                }
            }
        }
        if(CommonUtils.isEmpty(param.getActivityIdList()) || CommonUtils.isEmpty(param.getCycleList())){
            throw new BizException("操作失败，活动和周期不能为空");
        }
        LocalDate today = LocalDate.now();
        int dayOfWeek = today.getDayOfWeek().getValue();
        List<SalesActivityTaskMapping> mappingList = new ArrayList<>();
        List<SalesActivityTaskDate> taskDateList = new ArrayList<>();
        for(Long activityId : param.getActivityIdList()){
            SalesActivity activity = salesActivityService.getById(activityId);
            if(!SalesActivityStateEnum.IN_PROGRESS.getCode().equals(activity.getActivityState())){
                throw new BizException("操作失败，" + activity.getName() + "，活动状态未处于进行中");
            }
            //判断活动是否存在其他任务，并且周期冲突
            List<SalesActivityTaskCycleVo> otherCycleList = salesActivityTaskCycleService.
                    queryCycleListByActivityId(activityId,param.getId(),null);
            if(CommonUtils.isNotEmpty(otherCycleList)){
                for(SalesActivityTaskCycleVo otherCycle : otherCycleList){
                    for(SalesActivityTaskCycleVo cycle : param.getCycleList()){
                        if(cycle.getWeek().equals(otherCycle.getWeek())){
                            int startBefore = DateUtils.LocalTimeFormat(cycle.getStartTime(),null)
                                    .compareTo(DateUtils.LocalTimeFormat(otherCycle.getStartTime(),null));
                            int startAfter = DateUtils.LocalTimeFormat(cycle.getStartTime(),null)
                                    .compareTo(DateUtils.LocalTimeFormat(otherCycle.getEndTime(),null));
                            if(startBefore >= 0 && startAfter < 0){
                                throw new BizException("操作失败，" + activity.getName() + "，周" + cycle.getWeek()
                                        + "时间段与任务" + otherCycle.getTaskName() + "冲突");
                            }
                            int endBefore = DateUtils.LocalTimeFormat(cycle.getEndTime(),null)
                                    .compareTo(DateUtils.LocalTimeFormat(otherCycle.getStartTime(),null));
                            int endAfter = DateUtils.LocalTimeFormat(cycle.getEndTime(),null)
                                    .compareTo(DateUtils.LocalTimeFormat(otherCycle.getEndTime(),null));
                            if(endBefore > 0 && endAfter <= 0){
                                throw new BizException("操作失败，" + activity.getName() + "，周" + cycle.getWeek()
                                        + "时间段与任务" + otherCycle.getTaskName() + "冲突");
                            }
                            if(startBefore <= 0 && endAfter >= 0){
                                throw new BizException("操作失败，" + activity.getName() + "，周" + cycle.getWeek()
                                        + "时间段与任务" + otherCycle.getTaskName() + "冲突");
                            }
                        }
                    }
                }
            }
            if(TrueEnum.TRUE.getCode().equals(executeState)){
                //处理活动任务日期
                LocalDate currentDate = LocalDate.now();
                LocalDate activityEndDate = activity.getEndDate().toLocalDate();
                List<LocalDate> dateList = new ArrayList<>();
                dateList.add(currentDate);
                while (currentDate.isBefore(activityEndDate) || currentDate.equals(activityEndDate)){
                    currentDate = currentDate.plusDays(1);
                    dateList.add(currentDate);
                }
                List<SalesActivityTaskCycleVo> allCycleList = salesActivityTaskCycleService.
                        queryCycleListByActivityId(activityId,null,TrueEnum.TRUE.getCode());
                for(LocalDate date : dateList){
                    int dateWeek = date.getDayOfWeek().getValue();
                    for(SalesActivityTaskCycleVo cycle : allCycleList){
                        if(dateWeek == cycle.getWeek()){
                            //生效日期
                            LocalDateTime startDateTime = LocalDateTime.of(date,DateUtils.LocalTimeFormat(cycle.getStartTime(),null));
                            SalesActivityTaskDate taskStartDate = SalesActivityTaskDate.builder().activityId(activityId)
                                    .taskDate(startDateTime).assertState(TrueEnum.TRUE.getCode()).build();
                            taskDateList.add(taskStartDate);
                            //失效日期
                            LocalDateTime endDateTime = LocalDateTime.of(date,DateUtils.LocalTimeFormat(cycle.getEndTime(),null));
                            SalesActivityTaskDate taskEndDate = SalesActivityTaskDate.builder().activityId(activityId)
                                    .taskDate(endDateTime).assertState(TrueEnum.FALSE.getCode()).build();
                            taskDateList.add(taskEndDate);
                        }
                    }
                }
                //判断活动生效状态
                Integer assertState = TrueEnum.FALSE.getCode();
                for(SalesActivityTaskCycleVo cycle : allCycleList){
                    if(dayOfWeek == cycle.getWeek()){
                        int nowBefore = LocalTime.now().compareTo(DateUtils.LocalTimeFormat(cycle.getStartTime(),null));
                        int nowAfter = LocalTime.now().compareTo(DateUtils.LocalTimeFormat(cycle.getEndTime(),null));
                        if(nowBefore >= 0 && nowAfter < 0){
                            //当前时间存在任务周期
                            assertState = TrueEnum.TRUE.getCode();
                            break;
                        }
                    }
                }
                //更改活动生效状态
                salesActivityService.lambdaUpdate().eq(SalesActivity::getId,activityId)
                        .set(SalesActivity::getAssertState,assertState).update(new SalesActivity());
            }
            mappingList.add(SalesActivityTaskMapping.builder().activityId(activityId).build());
        }
        StringBuilder cycle = new StringBuilder();
        for(SalesActivityTaskCycleVo cycleVo : param.getCycleList()){
            cycle.append("周" + DateUtils.transformWeek(cycleVo.getWeek()) + cycleVo.getStartTime() + "至" + cycleVo.getEndTime() + "<br>");
        }
        SalesActivityTask activityTask = dozerUtils.map(param,SalesActivityTask.class);
        activityTask.setCycle(cycle.toString());
        this.saveOrUpdate(activityTask);
        //保存活动和任务的关联关系
        mappingList.stream().map(m -> m.setTaskId(activityTask.getId())).collect(Collectors.toList());
        salesActivityTaskMappingService.saveBatch(mappingList);
        //保存任务周期
        param.getCycleList().stream().map(c -> c.setTaskId(activityTask.getId())).collect(Collectors.toList());
        List<SalesActivityTaskCycle> cycleList = dozerUtils.mapList(param.getCycleList(),SalesActivityTaskCycle.class);
        salesActivityTaskCycleService.saveBatch(cycleList);
        //处理活动有效/失效时间
        if(TrueEnum.TRUE.getCode().equals(executeState)){
            //保存活动任务生效状态变更日期
            if(CommonUtils.isNotEmpty(taskDateList)){
                salesActivityTaskDateService.saveBatch(taskDateList);
            }
            for(Long activityId : param.getActivityIdList()){
                SalesActivityTaskDate taskDate = salesActivityTaskDateService.lambdaQuery()
                        .eq(SalesActivityTaskDate::getActivityId,activityId)
                        .gt(SalesActivityTaskDate::getTaskDate,LocalDateTime.now())
                        .orderByAsc(SalesActivityTaskDate::getTaskDate)
                        .last("limit 1").one();
                if(CommonUtils.isNotEmpty(taskDate)){
                    //更新活动下一次生效状态变更时间
                    long seconds = LocalDateTime.now().until(taskDate.getTaskDate(), ChronoUnit.SECONDS);
                    redisRepository.setExpire(CacheKey.ACTIVITY_TASK_ASSERT_STATE + StrPool.COLON + activityId, taskDate.getAssertState(), seconds);
                }
            }
        }
        return this.getSalesActivityTaskById(activityTask.getId());
    }

    @Override
    public Page<SalesActivityTaskVo> querySalesActivityTaskPage(QuerySalesActivityTaskParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<SalesActivityTaskVo> list = this.baseMapper.querySalesActivityTaskPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public SalesActivityTaskVo getSalesActivityTaskById(Long id) {
        SalesActivityTaskVo vo = this.baseMapper.getSalesActivityTaskById(id);
        if(CommonUtils.isNotEmpty(vo)){
            //活动List
            List<SalesActivityTaskMappingVo> activityList = salesActivityTaskMappingService.queryActivityTaskMappingListByTaskId(vo.getId());
            if(CommonUtils.isNotEmpty(activityList)){
                vo.setActivityList(activityList);
            }
            //任务周期
            List<SalesActivityTaskCycle> cycleList = salesActivityTaskCycleService.lambdaQuery()
                    .eq(SalesActivityTaskCycle::getTaskId,vo.getId()).list();
            if(CommonUtils.isNotEmpty(cycleList)){
                vo.setCycleList(dozerUtils.mapList(cycleList,SalesActivityTaskCycleVo.class));
            }
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pauseSalesActivityTask(Long id) {
        SalesActivityTaskVo salesActivityTask = this.getSalesActivityTaskById(id);
        if(CommonUtils.isEmpty(salesActivityTask)){
            throw new BizException("操作失败，活动任务不存在");
        }
        List<Long> activityIdList = salesActivityTaskMappingService.lambdaQuery()
                .eq(SalesActivityTaskMapping::getTaskId,id).list()
                .stream().map(m -> m.getActivityId()).collect(Collectors.toList());
        if(TrueEnum.TRUE.getCode().equals(salesActivityTask.getExecuteState())){
            //任务不执行
            this.lambdaUpdate().set(SalesActivityTask::getExecuteState, TrueEnum.FALSE.getCode())
                    .eq(SalesActivityTask::getId,id).update(new SalesActivityTask());
            //删除任务日期
            salesActivityTaskDateService.lambdaUpdate().in(SalesActivityTaskDate::getActivityId,activityIdList)
                    .set(SalesActivityTaskDate::getIsDelete,TrueEnum.TRUE.getCode()).update(new SalesActivityTaskDate());
            //所有进行中活动更改为有效
            salesActivityService.lambdaUpdate().in(SalesActivity::getId,activityIdList)
                    .eq(SalesActivity::getActivityState,SalesActivityStateEnum.IN_PROGRESS.getCode())
                    .set(SalesActivity::getAssertState,TrueEnum.TRUE.getCode()).update(new SalesActivity());
            //删除redis
            for(Long activityId : activityIdList){
                redisRepository.del(CacheKey.ACTIVITY_TASK_ASSERT_STATE + StrPool.COLON + activityId);
            }
        }else{
            //任务执行
            this.lambdaUpdate().set(SalesActivityTask::getExecuteState, TrueEnum.TRUE.getCode())
                    .eq(SalesActivityTask::getId,id).update(new SalesActivityTask());
            int dayOfWeek = LocalDate.now().getDayOfWeek().getValue();
            List<SalesActivityTaskDate> taskDateList = new ArrayList<>();
            for(Long activityId : activityIdList){
                SalesActivity activity = salesActivityService.getById(activityId);
                if(!SalesActivityStateEnum.IN_PROGRESS.getCode().equals(activity.getActivityState())){
                    throw new BizException("操作失败，" + activity.getName() + "，活动状态未处于进行中");
                }
                List<SalesActivityTaskCycleVo> allCycleList = salesActivityTaskCycleService
                        .queryCycleListByActivityId(activityId,null,TrueEnum.TRUE.getCode());
                //判断活动生效状态
                Integer assertState = TrueEnum.FALSE.getCode();
                for(SalesActivityTaskCycleVo cycle : allCycleList){
                    if(dayOfWeek == cycle.getWeek()){
                        int nowBefore = LocalTime.now().compareTo(DateUtils.LocalTimeFormat(cycle.getStartTime(),null));
                        int nowAfter = LocalTime.now().compareTo(DateUtils.LocalTimeFormat(cycle.getEndTime(),null));
                        if(nowBefore >= 0 && nowAfter < 0){
                            //当前时间存在任务周期
                            assertState = TrueEnum.TRUE.getCode();
                            break;
                        }
                    }
                }
                //更改活动生效状态
                salesActivityService.lambdaUpdate().eq(SalesActivity::getId,activityId)
                        .set(SalesActivity::getAssertState,assertState).update(new SalesActivity());
                //处理活动任务日期
                LocalDate currentDate = LocalDate.now();
                LocalDate activityEndDate = activity.getEndDate().toLocalDate();
                List<LocalDate> dateList = new ArrayList<>();
                dateList.add(currentDate);
                while (currentDate.isBefore(activityEndDate) || currentDate.equals(activityEndDate)){
                    currentDate = currentDate.plusDays(1);
                    dateList.add(currentDate);
                }
                for(LocalDate date : dateList){
                    int dateWeek = date.getDayOfWeek().getValue();
                    for(SalesActivityTaskCycleVo cycle : allCycleList){
                        if(dateWeek == cycle.getWeek()){
                            //生效
                            LocalDateTime startDateTime = LocalDateTime.of(date,DateUtils.LocalTimeFormat(cycle.getStartTime(),null));
                            SalesActivityTaskDate taskStartDate = SalesActivityTaskDate.builder().activityId(activityId)
                                    .taskDate(startDateTime).assertState(TrueEnum.TRUE.getCode()).build();
                            taskDateList.add(taskStartDate);
                            //失效
                            LocalDateTime endDateTime = LocalDateTime.of(date,DateUtils.LocalTimeFormat(cycle.getEndTime(),null));
                            SalesActivityTaskDate taskEndDate = SalesActivityTaskDate.builder().activityId(activityId)
                                    .taskDate(endDateTime).assertState(TrueEnum.FALSE.getCode()).build();
                            taskDateList.add(taskEndDate);
                        }
                    }
                }
            }
            //保存活动任务生效状态变更日期
            if(CommonUtils.isNotEmpty(taskDateList)){
                salesActivityTaskDateService.saveBatch(taskDateList);
            }
            for(Long activityId : activityIdList){
                SalesActivityTaskDate taskDate = salesActivityTaskDateService.lambdaQuery()
                        .eq(SalesActivityTaskDate::getActivityId,activityId)
                        .gt(SalesActivityTaskDate::getTaskDate,LocalDateTime.now())
                        .orderByAsc(SalesActivityTaskDate::getTaskDate)
                        .last("limit 1").one();
                if(CommonUtils.isNotEmpty(taskDate)){
                    //更新活动下一次生效状态变更时间
                    long seconds = LocalDateTime.now().until(taskDate.getTaskDate(), ChronoUnit.SECONDS);
                    redisRepository.setExpire(CacheKey.ACTIVITY_TASK_ASSERT_STATE + StrPool.COLON + activityId, taskDate.getAssertState(), seconds);
                }
            }
        }
    }

}
