package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 损溢单处理状态 0暂存 1完成
 * <AUTHOR>
 */

@Getter
public enum GainsLossesOrderStateEnum {

    TEMPORARY(0, "暂存"),
    COMPLETED(1, "完成"),
    ;

    private Integer code;

    private String desc;

    GainsLossesOrderStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (GainsLossesOrderStateEnum a : GainsLossesOrderStateEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
