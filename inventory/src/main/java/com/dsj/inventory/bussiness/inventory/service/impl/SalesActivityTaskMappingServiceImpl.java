package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityTaskMapping;
import com.dsj.inventory.bussiness.inventory.mapper.SalesActivityTaskMappingMapper;
import com.dsj.inventory.bussiness.inventory.service.SalesActivityTaskMappingService;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskMappingVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 销售活动任务关联信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Service
public class SalesActivityTaskMappingServiceImpl extends ServiceImpl<SalesActivityTaskMappingMapper, SalesActivityTaskMapping> implements SalesActivityTaskMappingService {

    @Override
    public List<SalesActivityTaskMappingVo> queryActivityTaskMappingListByTaskId(Long taskId) {
        return this.baseMapper.queryActivityTaskMappingListByTaskId(taskId);
    }
}
