package com.dsj.inventory.bussiness.inventory.service;

import com.dsj.inventory.bussiness.inventory.dto.GspCheckResult;

import java.util.List;
import java.util.Map;

/**
 * GSP合规校验服务
 * 实现采购计划的GSP合规性校验功能
 */
public interface GspComplianceService {
    
    /**
     * 采购计划GSP校验
     * 对整个采购计划进行全面的GSP合规检查
     * @param planId 采购计划ID
     * @return 校验结果
     */
    GspCheckResult validatePurchasePlan(Long planId);
    
    /**
     * 批量GSP校验采购计划明细
     * @param planGoodsIds 采购计划明细ID列表
     * @return 每个明细的校验结果映射
     */
    Map<Long, GspCheckResult> batchValidatePlanGoods(List<Long> planGoodsIds);
    
    /**
     * 校验供应商资质
     * 检查供应商的营业执照、GSP证书、税务登记证等
     * @param organizationSupplierId 私域供应商ID
     * @return 校验结果
     */
    GspCheckResult validateSupplierQualification(Long organizationSupplierId);
    
    /**
     * 校验商品资质
     * 检查商品的注册证、进口药品注册证等
     * @param organizationMedicineId 私域商品ID
     * @return 校验结果
     */
    GspCheckResult validateMedicineQualification(Long organizationMedicineId);
    
    /**
     * 校验经营范围匹配
     * 检查供应商的经营范围是否包含该商品类别
     * @param organizationSupplierId 私域供应商ID
     * @param organizationMedicineId 私域商品ID
     * @return 校验结果
     */
    GspCheckResult validateBusinessScope(Long organizationSupplierId, Long organizationMedicineId);
    
    /**
     * 校验企业自身资质
     * 检查企业自身的营业执照、GSP证书等
     * @param organizationId 组织ID
     * @return 校验结果
     */
    GspCheckResult validateEnterpriseQualification(Long organizationId);
    
    /**
     * 校验单个采购计划明细
     * @param planGoodsId 采购计划明细ID
     * @return 校验结果
     */
    GspCheckResult validatePlanGoods(Long planGoodsId);
    
    /**
     * 快速校验供应商和商品组合
     * 用于新增采购计划时的实时校验
     * @param organizationSupplierId 私域供应商ID
     * @param organizationMedicineId 私域商品ID
     * @param organizationId 组织ID
     * @return 校验结果
     */
    GspCheckResult quickValidate(Long organizationSupplierId, Long organizationMedicineId, Long organizationId);
    
    /**
     * 获取即将到期的证照列表
     * @param organizationId 组织ID
     * @param days 预警天数（证照在N天内到期）
     * @return 即将到期的证照列表
     */
    List<GspCheckResult.LicenseExpiryWarning> getExpiringLicenses(Long organizationId, Integer days);
    
    /**
     * 更新采购计划的GSP校验状态
     * @param planId 采购计划ID
     * @param checkResult 校验结果
     */
    void updatePlanGspStatus(Long planId, GspCheckResult checkResult);
    
    /**
     * 检查GSP校验是否启用
     * @param organizationId 组织ID
     * @return 是否启用GSP校验
     */
    Boolean isGspCheckEnabled(Long organizationId);
    
    /**
     * 获取GSP校验配置
     * @param organizationId 组织ID
     * @return 校验模式配置
     */
    GspCheckConfig getGspCheckConfig(Long organizationId);
    
    /**
     * 批量更新采购计划明细的GSP状态
     * @param planGoodsIds 采购计划明细ID列表
     */
    void batchUpdatePlanGoodsGspStatus(List<Long> planGoodsIds);
    
    /**
     * GSP校验配置内部类
     */
    class GspCheckConfig {
        private Integer checkMode;          // 校验模式
        private Boolean enabled;            // 是否启用
        private Integer warningDays;        // 证照到期预警天数
        private Boolean autoCheck;          // 是否自动校验
        private Boolean blockInvalid;       // 是否阻止无效采购
        
        public GspCheckConfig() {}
        
        public GspCheckConfig(Integer checkMode, Boolean enabled) {
            this.checkMode = checkMode;
            this.enabled = enabled;
        }
        
        // Getter和Setter方法
        public Integer getCheckMode() { return checkMode; }
        public void setCheckMode(Integer checkMode) { this.checkMode = checkMode; }
        
        public Boolean getEnabled() { return enabled; }
        public void setEnabled(Boolean enabled) { this.enabled = enabled; }
        
        public Integer getWarningDays() { return warningDays; }
        public void setWarningDays(Integer warningDays) { this.warningDays = warningDays; }
        
        public Boolean getAutoCheck() { return autoCheck; }
        public void setAutoCheck(Boolean autoCheck) { this.autoCheck = autoCheck; }
        
        public Boolean getBlockInvalid() { return blockInvalid; }
        public void setBlockInvalid(Boolean blockInvalid) { this.blockInvalid = blockInvalid; }
    }
}