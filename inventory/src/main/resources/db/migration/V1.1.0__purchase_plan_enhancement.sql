-- =====================================================
-- 采购计划功能增强数据库迁移脚本
-- 版本: V1.1.0
-- 描述: 为采购计划模块添加自动生成、GSP校验等新功能所需的字段
-- 作者: Radium
-- 日期: 2025-06-23
-- =====================================================

-- =====================================================
-- 1. 扩展采购计划主表 (jxc_purchase_plan)
-- =====================================================

-- 检查字段是否已存在，避免重复添加
SET @sql = '';

-- 添加自动生成标识字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan' 
  AND column_name = 'auto_generated';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE jxc_purchase_plan ADD COLUMN auto_generated TINYINT DEFAULT 0 COMMENT ''是否自动生成：0-手动创建，1-自动生成'' AFTER deal_state;',
    'SELECT ''字段 auto_generated 已存在，跳过添加'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加GSP校验状态字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan' 
  AND column_name = 'gsp_check_status';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE jxc_purchase_plan ADD COLUMN gsp_check_status TINYINT DEFAULT 0 COMMENT ''GSP校验状态：0-未校验，1-校验通过，-1-校验不通过'' AFTER auto_generated;',
    'SELECT ''字段 gsp_check_status 已存在，跳过添加'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加GSP校验信息字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan' 
  AND column_name = 'check_message';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE jxc_purchase_plan ADD COLUMN check_message VARCHAR(1000) DEFAULT NULL COMMENT ''GSP校验信息，记录校验结果详情'' AFTER gsp_check_status;',
    'SELECT ''字段 check_message 已存在，跳过添加'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 2. 扩展采购计划商品明细表 (jxc_purchase_plan_goods)
-- =====================================================

-- 添加供应商ID字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'organization_supplier_id';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE jxc_purchase_plan_goods ADD COLUMN organization_supplier_id BIGINT DEFAULT NULL COMMENT ''供应商ID，关联jxc_organization_supplier表'' AFTER organization_medicine_id;',
    'SELECT ''字段 organization_supplier_id 已存在，跳过添加'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加在途数量字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'in_transit_quantity';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE jxc_purchase_plan_goods ADD COLUMN in_transit_quantity DOUBLE DEFAULT 0 COMMENT ''在途数量，已下单未入库的数量'' AFTER repertory;',
    'SELECT ''字段 in_transit_quantity 已存在，跳过添加'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加日均销量字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'daily_avg_sales';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE jxc_purchase_plan_goods ADD COLUMN daily_avg_sales DOUBLE DEFAULT 0 COMMENT ''日均销量，用于计算库存上下限'' AFTER in_transit_quantity;',
    'SELECT ''字段 daily_avg_sales 已存在，跳过添加'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加库存上限字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'stock_upper_limit';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE jxc_purchase_plan_goods ADD COLUMN stock_upper_limit DOUBLE DEFAULT 0 COMMENT ''库存上限，上限天数×日均销量'' AFTER daily_avg_sales;',
    'SELECT ''字段 stock_upper_limit 已存在，跳过添加'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加库存下限字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'stock_lower_limit';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE jxc_purchase_plan_goods ADD COLUMN stock_lower_limit DOUBLE DEFAULT 0 COMMENT ''库存下限，下限天数×日均销量'' AFTER stock_upper_limit;',
    'SELECT ''字段 stock_lower_limit 已存在，跳过添加'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加推荐采购数量字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'recommend_quantity';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE jxc_purchase_plan_goods ADD COLUMN recommend_quantity DOUBLE DEFAULT 0 COMMENT ''推荐采购数量，系统自动计算的建议采购量'' AFTER stock_lower_limit;',
    'SELECT ''字段 recommend_quantity 已存在，跳过添加'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加价格策略字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'price_strategy';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE jxc_purchase_plan_goods ADD COLUMN price_strategy TINYINT DEFAULT 3 COMMENT ''价格策略：1-按最低进价，2-按最新供应商，3-按上次进价'' AFTER recommend_quantity;',
    'SELECT ''字段 price_strategy 已存在，跳过添加'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 3. 创建索引优化查询性能
-- =====================================================

-- 为采购计划表添加索引
-- 自动生成标识索引（用于查询自动生成的计划）
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan' 
  AND index_name = 'idx_auto_generated';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_auto_generated ON jxc_purchase_plan(auto_generated, organization_id);',
    'SELECT ''索引 idx_auto_generated 已存在，跳过创建'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- GSP校验状态索引（用于查询校验状态）
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan' 
  AND index_name = 'idx_gsp_check_status';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_gsp_check_status ON jxc_purchase_plan(gsp_check_status, organization_id);',
    'SELECT ''索引 idx_gsp_check_status 已存在，跳过创建'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为采购计划商品明细表添加索引
-- 供应商ID索引（用于按供应商查询商品）
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND index_name = 'idx_organization_supplier_id';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_organization_supplier_id ON jxc_purchase_plan_goods(organization_supplier_id);',
    'SELECT ''索引 idx_organization_supplier_id 已存在，跳过创建'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 价格策略索引（用于按价格策略查询）
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND index_name = 'idx_price_strategy';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_price_strategy ON jxc_purchase_plan_goods(price_strategy, plan_id);',
    'SELECT ''索引 idx_price_strategy 已存在，跳过创建'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 库存相关字段复合索引（用于库存分析查询）
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND index_name = 'idx_stock_analysis';

SET @sql = IF(@index_exists = 0, 
    'CREATE INDEX idx_stock_analysis ON jxc_purchase_plan_goods(organization_medicine_id, repertory, stock_lower_limit);',
    'SELECT ''索引 idx_stock_analysis 已存在，跳过创建'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 4. 数据初始化（可选）
-- =====================================================

-- 将现有数据的自动生成标识设置为手动创建
UPDATE jxc_purchase_plan 
SET auto_generated = 0, 
    gsp_check_status = 0 
WHERE auto_generated IS NULL 
   OR gsp_check_status IS NULL;

-- 将现有商品明细的价格策略设置为默认值（按上次进价）
UPDATE jxc_purchase_plan_goods 
SET price_strategy = 3,
    in_transit_quantity = 0,
    daily_avg_sales = 0,
    stock_upper_limit = 0,
    stock_lower_limit = 0,
    recommend_quantity = 0
WHERE price_strategy IS NULL 
   OR in_transit_quantity IS NULL
   OR daily_avg_sales IS NULL
   OR stock_upper_limit IS NULL
   OR stock_lower_limit IS NULL
   OR recommend_quantity IS NULL;

-- =====================================================
-- 5. 验证脚本执行结果
-- =====================================================

-- 验证采购计划表字段添加情况
SELECT 
    'jxc_purchase_plan' as table_name,
    column_name,
    data_type,
    is_nullable,
    column_default,
    column_comment
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan' 
  AND column_name IN ('auto_generated', 'gsp_check_status', 'check_message')
ORDER BY ordinal_position;

-- 验证采购计划商品明细表字段添加情况
SELECT 
    'jxc_purchase_plan_goods' as table_name,
    column_name,
    data_type,
    is_nullable,
    column_default,
    column_comment
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name IN ('organization_supplier_id', 'in_transit_quantity', 'daily_avg_sales', 
                      'stock_upper_limit', 'stock_lower_limit', 'recommend_quantity', 'price_strategy')
ORDER BY ordinal_position;

-- 验证索引创建情况
SELECT 
    table_name,
    index_name,
    GROUP_CONCAT(column_name ORDER BY seq_in_index) as columns,
    non_unique
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name IN ('jxc_purchase_plan', 'jxc_purchase_plan_goods')
  AND index_name IN ('idx_auto_generated', 'idx_gsp_check_status', 'idx_organization_supplier_id', 
                     'idx_price_strategy', 'idx_stock_analysis')
GROUP BY table_name, index_name, non_unique
ORDER BY table_name, index_name;

-- 显示迁移完成信息
SELECT 
    '采购计划功能增强数据库迁移完成' as status,
    NOW() as completion_time,
    'V1.1.0' as version;
