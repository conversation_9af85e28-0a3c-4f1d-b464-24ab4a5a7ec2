package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskCycleVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 销售活动任务
 * <AUTHOR>
 * @date 2023/04/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateSalesActivityTaskParam", description="新增or编辑 销售活动任务参数")
public class SaveOrUpdateSalesActivityTaskParam {

    @ApiModelProperty(hidden = true, value = "id,更新是传入")
    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(hidden = true,value = "模块分类，1 云药快 ；2 外部；3 在线医院；4 商城；5进销存")
    private Integer moduleType;

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "活动类别，1单品活动 2会员日活动 3整单活动")
    private Integer activityCategory;

    @ApiModelProperty(value = "活动IdList")
    private List<Long> activityIdList;

    @ApiModelProperty(value = "任务周期")
    private List<SalesActivityTaskCycleVo> cycleList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
