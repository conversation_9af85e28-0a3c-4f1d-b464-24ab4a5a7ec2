package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.PurchaseOrder;
import com.dsj.inventory.bussiness.inventory.entity.PurchaseOrderGoods;
import com.dsj.inventory.bussiness.purchaseplan.entity.PurchasePlan;
import com.dsj.inventory.bussiness.inventory.enumeration.PurchaseOrderStateEnum;
import com.dsj.inventory.bussiness.inventory.mapper.PurchaseOrderMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryPurchaseOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdatePurchaseOrderGoodsParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdatePurchaseOrderParam;
import com.dsj.inventory.bussiness.inventory.service.PurchaseOrderGoodsService;
import com.dsj.inventory.bussiness.inventory.service.PurchaseOrderService;
import com.dsj.inventory.bussiness.inventory.service.PurchasePlanService;
import com.dsj.inventory.bussiness.inventory.vo.PurchaseOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchaseOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchaseOrderGoodsVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchaseOrderVo;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import cn.hutool.core.bean.BeanUtil;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineExpand;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.enumeration.GoodsTypeEnum;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineExpandService;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 进销存采购订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
@Service
public class PurchaseOrderServiceImpl extends ServiceImpl<PurchaseOrderMapper, PurchaseOrder> implements PurchaseOrderService {

    private static final Logger log = LoggerFactory.getLogger(PurchaseOrderServiceImpl.class);

    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @Autowired
    private PurchaseOrderGoodsService purchaseOrderGoodsService;
    @Autowired
    private PurchasePlanService purchasePlanService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationMedicineExpandService organizationMedicineExpandService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchaseOrderDetailVo saveOrUpdatePurchaseOrder(SaveOrUpdatePurchaseOrderParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        Organization organization = organizationService.getById(param.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        if(CommonUtils.isNotEmpty(param.getId())) {
            PurchaseOrder byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            //删除商品明细
            purchaseOrderGoodsService.markAsDeletedByOrderId(param.getId());
        }else {
            //生成采购订单单号：CG+年后两位+月日四位+药房识别码六位+三位
            String day = DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_FORMAT);
            String dayStr = day.replaceAll("-", "").substring(2,8);
            Integer count = this.getPurchaseOrderCount(organization.getId(),day);
            count = count + 1;
            param.setCode("CG" + dayStr + organization.getSerialNo() + String.format("%03d",count));
        }

        if(CommonUtils.isNotEmpty(param.getPlanId())){
            PurchasePlan purchasePlan = purchasePlanService.getById(param.getPlanId());
            if(CommonUtils.isEmpty(purchasePlan)){
                throw new BizException("操作失败，绑定采购计划不存在");
            }
            int count = this.countByPlanId(param.getPlanId(), param.getId());
            if(count > 0 ){
                throw new BizException("操作失败，采购计划已绑定，不能重复绑定");
            }
            param.setOrderCode(purchasePlan.getCode());
        }
        StringBuilder purchaseContent = new StringBuilder();
        Set<String> goodsTypeSet = new HashSet<>();
        BigDecimal totalPrice = BigDecimal.valueOf(0);
        if(CommonUtils.isNotEmpty(param.getGoodsList())){
            List<Long> goodsIdList = param.getGoodsList().stream().map(g -> g.getOrganizationMedicineId()).distinct().collect(Collectors.toList());
            if(goodsIdList.size() != param.getGoodsList().size()){
                throw new BizException("操作失败，存在重复商品");
            }
            for(SaveOrUpdatePurchaseOrderGoodsParam goodsParam : param.getGoodsList()){
                OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(goodsParam.getOrganizationMedicineId());
                if(CommonUtils.isEmpty(organizationMedicine)){
                    throw new BizException("操作失败，商品Id"+goodsParam.getOrganizationMedicineId()+"不存在");
                }
                goodsParam.setGoodsName(organizationMedicine.getName());
                goodsParam.setGeneralName(organizationMedicine.getGeneralName());
                goodsParam.setGoodsCode(organizationMedicine.getGoodsCode());
                goodsParam.setPackUnit(organizationMedicine.getPackUnit());
                goodsParam.setSpecification(organizationMedicine.getSpecification());
                goodsParam.setDrugType(organizationMedicine.getDrugType());
                goodsParam.setManufacturer(organizationMedicine.getManufacturer());
                OrganizationMedicineExpand medicineExpand = organizationMedicineExpandService.getById(organizationMedicine.getId());
                if(CommonUtils.isNotEmpty(medicineExpand)) {
                    goodsParam.setLaunchPermitHolder(medicineExpand.getLaunchPermitHolder());
                }
                goodsParam.setPrice(organizationMedicine.getOfflinePrice());
                goodsParam.setRepertory(organizationMedicine.getMedicineRepertory());
                BigDecimal totalPurchasePrice = BigDecimal.valueOf(goodsParam.getPurchasePrice())
                        .multiply(BigDecimal.valueOf(goodsParam.getPurchaseNumber()));
                totalPurchasePrice = totalPurchasePrice.setScale(0,BigDecimal.ROUND_HALF_UP);
                if(totalPurchasePrice.intValue() != goodsParam.getTotalPurchasePrice()){
                    throw new BizException("操作失败，商品"+goodsParam.getGoodsName()+"小计金额计算不一致");
                }
                goodsParam.setTotalPurchasePrice(totalPurchasePrice.intValue());
                purchaseContent.append(goodsParam.getGoodsName()+"*"+goodsParam.getPurchaseNumber()+goodsParam.getPackUnit()+",");
                
                String goodsTypeDescription = GoodsTypeEnum.getDesc(organizationMedicine.getGoodsType());
                if (goodsTypeDescription == null) {
                    throw new BizException("操作失败，商品[" + goodsParam.getGoodsName() + "]的类型编码[" + organizationMedicine.getGoodsType() + "]不合法或未在枚举中定义");
                }
                goodsTypeSet.add(goodsTypeDescription);
                totalPrice = totalPrice.add(BigDecimal.valueOf(goodsParam.getTotalPurchasePrice()));
            }
            param.setPurchaseContent(purchaseContent.substring(0,purchaseContent.length()-1));
            if (!goodsTypeSet.isEmpty()) {
                param.setGoodsTypes(String.join(",",goodsTypeSet));
            } else {
                param.setGoodsTypes(null);
            }
            if(totalPrice.intValue() != param.getTotalPrice()){
                throw new BizException("操作失败，总金额计算不一致");
            }
            param.setTotalPrice(totalPrice.intValue());
        }
        if(CommonUtils.isEmpty(param.getPurchaserId())){
            param.setPurchaserId(BaseContextHandler.getUserId());
        }
        PurchaseOrder purchaseOrder = BeanUtil.toBean(param,PurchaseOrder.class);
        this.saveOrUpdate(purchaseOrder);
        if(CommonUtils.isNotEmpty(param.getPlanId())){
            //绑定计划变更为已执行
            purchasePlanService.updateStateToExecuted(param.getPlanId());
        }
        if(CommonUtils.isNotEmpty(param.getGoodsList())){
            param.getGoodsList().stream().map(g -> g.setOrderId(purchaseOrder.getId())).collect(Collectors.toList());
            List<PurchaseOrderGoods> goodsList = param.getGoodsList().stream()
                               .map(item -> BeanUtil.toBean(item, PurchaseOrderGoods.class))
                               .collect(Collectors.toList());
            purchaseOrderGoodsService.saveBatch(goodsList);
        }
        return this.getPurchaseOrderById(purchaseOrder.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePurchaseOrder(Long id) {
        PurchaseOrder byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        if(!byId.getDealState().equals(PurchaseOrderStateEnum.TEMPORARY.getCode())){
            throw new BizException("操作失败，采购订单当前状态不允许删除");
        }
        //删除商品明细
        purchaseOrderGoodsService.markAsDeletedByOrderId(id);
        this.baseMapper.deleteById(id);
    }

    @Override
    public Page<PurchaseOrderVo> queryPurchaseOrderPage(QueryPurchaseOrderParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        Page<PurchaseOrderVo> page = new Page<>(param.getPage(),param.getSize());
        List<PurchaseOrderVo> list = this.baseMapper.queryPurchaseOrderPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public PurchaseOrderDetailVo getPurchaseOrderById(Long id) {
        PurchaseOrderDetailVo vo = this.baseMapper.getPurchaseOrderById(id);
        if(CommonUtils.isNotEmpty(vo)){
            List<PurchaseOrderGoods> goodsList = purchaseOrderGoodsService.listByOrderId(vo.getId());
            if(CommonUtils.isNotEmpty(goodsList)){
                vo.setGoodsList(goodsList.stream()
                                       .map(item -> BeanUtil.toBean(item, PurchaseOrderGoodsVo.class))
                                       .collect(Collectors.toList()));
            }
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitPurchaseOrder(Long id) {
        PurchaseOrder byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        Organization organization = organizationService.getById(byId.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        updatePurchaseOrderState(id, PurchaseOrderStateEnum.WAITING.getCode());
    }

    @Override
    public List<PurchaseOrderExportVo> queryPurchaseOrderList(QueryPurchaseOrderParam param) {
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        return this.baseMapper.queryPurchaseOrderList(param);
    }

    @Override
    public void deletePurchaseOrderByOrganizationId(Long organizationId) {
        this.baseMapper.deletePurchaseOrderByOrganizationId(organizationId);
    }

    @Override
    public Integer getPurchaseOrderCount(Long organizationId, String day) {
        return this.baseMapper.getPurchaseOrderCount(organizationId, day);
    }

    @Override
    public int countByPlanId(Long planId, Long excludeOrderId) {
        return this.baseMapper.countByPlanId(planId, excludeOrderId);
    }

    /**
     * 更新采购订单状态(私有方法)
     * @param orderId 订单ID
     * @param targetState 目标状态
     */
    private void updatePurchaseOrderState(Long orderId, Integer targetState) {
        this.baseMapper.updateState(orderId, targetState);
    }
    
    /**
     * 更新采购订单状态，带原始状态校验(私有方法)
     * @param orderId 订单ID
     * @param currentState 当前状态
     * @param targetState 目标状态
     */
    private void updatePurchaseOrderState(Long orderId, Integer currentState, Integer targetState) {
        this.baseMapper.updateStateWithCondition(orderId, currentState, targetState);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderState(Long orderId, Integer currentState, Integer targetState) {
       updatePurchaseOrderState(orderId, currentState, targetState);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePurchaseOrderStatusAfterReceipt(Long purchaseOrderId) {
        if (purchaseOrderId == null) {
            log.warn("尝试更新状态的采购单ID为null");
            return;
        }
        
        log.debug("开始更新采购单状态，采购单ID: {}", purchaseOrderId);
        
        // 获取采购单信息
        PurchaseOrder purchaseOrder = this.getById(purchaseOrderId);
        if (purchaseOrder == null) {
            log.warn("尝试更新不存在的采购单状态，ID: {}", purchaseOrderId);
            return;
        }
        
        // 获取采购单商品明细
        List<PurchaseOrderGoods> goodsList = purchaseOrderGoodsService.listByOrderIdAndNotDeleted(purchaseOrderId);
        if (CommonUtils.isEmpty(goodsList)) {
            log.info("采购单ID: {} 没有商品明细。当前状态: {}。状态不变。", purchaseOrderId, purchaseOrder.getDealState());
            return;
        }
        
        // 分析收货情况
        ReceiptAnalysisResult analysisResult = analyzeReceiptStatus(goodsList);
        
        // 根据分析结果确定目标状态
        Integer targetState = determineTargetState(purchaseOrder.getDealState(), analysisResult);
        
        // 如果状态需要变更，则更新
        if (!targetState.equals(purchaseOrder.getDealState())) {
            log.info("更新采购单ID: {} 状态从 {} 到 {}", 
                purchaseOrderId, 
                getPurchaseOrderStateDesc(purchaseOrder.getDealState()), 
                getPurchaseOrderStateDesc(targetState));
            updatePurchaseOrderState(purchaseOrderId, targetState);
        } else {
            log.info("采购单ID: {} 状态 {} 保持不变。", 
                purchaseOrderId, 
                getPurchaseOrderStateDesc(purchaseOrder.getDealState()));
        }
    }

    /**
     * 获取采购单状态的描述信息，处理可能的null值
     * @param stateCode 状态码
     * @return 状态描述
     */
    private String getPurchaseOrderStateDesc(Integer stateCode) {
        if (stateCode == null) {
            return "未知状态";
        }
        String desc = PurchaseOrderStateEnum.getDesc(stateCode);
        return desc != null ? desc : "未知状态(" + stateCode + ")";
    }

    /**
     * 分析采购单商品的收货情况
     * @param goodsList 采购单商品列表
     * @return 收货分析结果
     */
    private ReceiptAnalysisResult analyzeReceiptStatus(List<PurchaseOrderGoods> goodsList) {
        if (CommonUtils.isEmpty(goodsList)) {
            return new ReceiptAnalysisResult(false, false, 0, 0);
        }
        
        int totalValidItems = 0;
        int fullyReceivedItems = 0;
        int partiallyReceivedItems = 0;
        
        for (PurchaseOrderGoods item : goodsList) {
            // 获取当前收货数量，确保非null
            Integer currentReceivedQty = item.getReceivedQuantity() == null ? 0 : item.getReceivedQuantity();
            
            // 获取采购数量，确保非null并处理Double类型
            Double purchaseQtyDouble = item.getPurchaseNumber() == null ? 0.0 : item.getPurchaseNumber();
            int purchaseQty = purchaseQtyDouble.intValue();
            
            // 跳过无效的采购数量（小于等于0）
            if (purchaseQty <= 0) {
                log.warn("采购单商品ID: {} 的采购数量 ({}) 无效，在状态计算中跳过。", 
                    item.getId(), purchaseQty);
                
                // 如果已有收货，则计入部分收货
                if (currentReceivedQty > 0) {
                    partiallyReceivedItems++;
                }
                continue;
            }
            
            // 统计有效商品项
            totalValidItems++;
            
            // 判断收货情况
            if (currentReceivedQty >= purchaseQty) {
                // 完全收货
                fullyReceivedItems++;
            } else if (currentReceivedQty > 0) {
                // 部分收货
                partiallyReceivedItems++;
            }
        }
        
        // 计算收货状态
        boolean allItemsFullyReceived = (totalValidItems > 0) && (fullyReceivedItems == totalValidItems);
        boolean anyItemReceived = (fullyReceivedItems + partiallyReceivedItems) > 0;
        
        return new ReceiptAnalysisResult(
            allItemsFullyReceived,
            anyItemReceived,
            fullyReceivedItems,
            partiallyReceivedItems
        );
    }

    /**
     * 根据收货分析结果确定采购单目标状态
     * @param currentState 当前状态
     * @param analysisResult 收货分析结果
     * @return 目标状态代码
     */
    private Integer determineTargetState(Integer currentState, ReceiptAnalysisResult analysisResult) {
        // 如果全部商品均已完全收货
        if (analysisResult.isAllItemsFullyReceived()) {
            return PurchaseOrderStateEnum.COMPLETED.getCode();
        }
        
        // 如果有任何商品已收货（部分或全部）
        if (analysisResult.isAnyItemReceived()) {
            return PurchaseOrderStateEnum.PARTIALLY_RECEIVED.getCode();
        }
        
        // 如果没有收货，保持原状态
        if (PurchaseOrderStateEnum.TEMPORARY.getCode().equals(currentState)) {
            return PurchaseOrderStateEnum.TEMPORARY.getCode();
        } else {
            return PurchaseOrderStateEnum.WAITING.getCode();
        }
    }

    /**
     * 收货分析结果类
     */
    private static class ReceiptAnalysisResult {
        private final boolean allItemsFullyReceived;
        private final boolean anyItemReceived;
        private final int fullyReceivedItemCount;
        private final int partiallyReceivedItemCount;
        
        public ReceiptAnalysisResult(
            boolean allItemsFullyReceived, 
            boolean anyItemReceived,
            int fullyReceivedItemCount,
            int partiallyReceivedItemCount
        ) {
            this.allItemsFullyReceived = allItemsFullyReceived;
            this.anyItemReceived = anyItemReceived;
            this.fullyReceivedItemCount = fullyReceivedItemCount;
            this.partiallyReceivedItemCount = partiallyReceivedItemCount;
        }
        
        public boolean isAllItemsFullyReceived() {
            return allItemsFullyReceived;
        }
        
        public boolean isAnyItemReceived() {
            return anyItemReceived;
        }
        
        public int getFullyReceivedItemCount() {
            return fullyReceivedItemCount;
        }
        
        public int getPartiallyReceivedItemCount() {
            return partiallyReceivedItemCount;
        }
    }

}
