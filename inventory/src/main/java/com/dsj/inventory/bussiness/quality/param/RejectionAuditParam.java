package com.dsj.inventory.bussiness.quality.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(description = "拒收单审核参数")
public class RejectionAuditParam {

    @ApiModelProperty(value = "审核是否通过", required = true)
    @NotNull(message = "审核结果不能为空")
    private Boolean approved;

    @ApiModelProperty(value = "审核意见")
    private String remark;
} 