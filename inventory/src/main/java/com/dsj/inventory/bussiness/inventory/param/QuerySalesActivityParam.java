package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询销售活动商品
 * <AUTHOR>
 * @date 2023/04/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QuerySalesActivityParam",description = "查询销售活动商品参数")
public class QuerySalesActivityParam extends BasePage {

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "模块分类，1 云药快 ；2 外部；3 在线医院；4 商城；5进销存")
    private Integer moduleType;

    @ApiModelProperty(value = "活动类别，1单品活动 2会员日活动 3整单活动")
    private Integer activityCategory;

    @ApiModelProperty(value = "活动类型 1逢倍折扣 2满件减 3单品特价 4满元减元")
    private Integer activityType;

    @ApiModelProperty(value = "活动状态 0未开始 1进行中 2已暂停 3已结束")
    private Integer activityState;

    @ApiModelProperty(value = "生效状态 0未生效 1生效中")
    private Integer assertState;

    @ApiModelProperty(value = "活动名称模糊搜索")
    private String nameLike;

    @ApiModelProperty(value = "创建人名称模糊搜索")
    private String createUserNameLike;

    @ApiModelProperty(value = "活动开始时间")
    private String startDate;

    @ApiModelProperty(value = "创建时间，查询条件--起始时间")
    private String createStart;

    @ApiModelProperty(value = "创建时间，查询条件--结束时间")
    private String createEnd;

    @ApiModelProperty(value = "适用人群，0全部，其余数字为会员等级")
    private Integer applyTo;

    @ApiModelProperty(value = "商品goodsIds")
    private String goodsIds;

    @ApiModelProperty(value = "是否包含商品明细 0否1是，默认不包含")
    private Integer containGoodsList = 0;

}
