package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 进销存盘点计划
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_check")
@ApiModel(value="Check", description="进销存盘点计划")
public class Check extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织Id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "盘点单号")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "盘点计划名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "盘点状态：1盘点中 2已完成")
    @TableField("check_state")
    private Integer checkState;

    @ApiModelProperty(value = "盘点货位Id，多个用逗号分割，值为1时是全部")
    @TableField("shelf_ids")
    private String shelfIds;

    @ApiModelProperty(value = "盘点人Id，多个用逗号分割")
    @TableField("check_ids")
    private String checkIds;

    @ApiModelProperty(value = "盘点时间")
    @TableField("check_date")
    private LocalDateTime checkDate;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "报溢单Id")
    @TableField("gains_id")
    private Long gainsId;

    @ApiModelProperty(value = "报损单Id")
    @TableField("losses_id")
    private Long lossesId;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
