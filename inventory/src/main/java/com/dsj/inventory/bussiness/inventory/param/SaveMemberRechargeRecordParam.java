package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 会员充值记录
 * <AUTHOR>
 * @date 2023/04/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveMemberRechargeRecordParam", description="新增 会员充值记录参数")
public class SaveMemberRechargeRecordParam {

    @ApiModelProperty(value = "组织id")
    private Long organizationId;

    @ApiModelProperty(value = "会员Id")
    private Long memberId;

    @ApiModelProperty(value = "支付方式 1现金")
    private Integer payWay;

    @ApiModelProperty(value = "充值类型 1金额")
    private Integer rechargeType;

    @ApiModelProperty(value = "充值金额")
    private Integer rechargePrice;

    @ApiModelProperty(value = "赠送金额")
    private Integer givePrice;

}
