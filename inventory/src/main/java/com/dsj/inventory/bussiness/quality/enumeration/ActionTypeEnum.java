package com.dsj.inventory.bussiness.quality.enumeration;

import lombok.Getter;

/**
 * 审批操作类型枚举
 */
@Getter
public enum ActionTypeEnum {
    
    CREATE(0, "创建"),
    SUBMIT(1, "提交"),
    APPROVE(2, "通过"),
    REJECT(3, "驳回"),
    WITHDRAW(4, "撤回");
    
    private final Integer code;
    private final String desc;
    
    ActionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return "";
        }
        for (ActionTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return "";
    }
} 