package com.dsj.inventory.common.utils;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.pocky.transport.bussiness.auth.entity.User;
import com.pocky.transport.bussiness.auth.service.IUserService;
import com.pocky.transport.bussiness.wx.vo.AccountMsgNotifyResponseVo;
import com.dsj.inventory.common.cache.RedisRepositoryImpl;
import com.dsj.inventory.common.constant.CacheKey;
import com.dsj.inventory.common.entity.*;
import com.dsj.inventory.common.utils.CommonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @ClassName : WxMsgUtils
 * @Description : 小程序推送消息工具类
 * @Date :
 */
@Slf4j
@Data
@Component
public class WxMsgUtils {


    @Autowired
    private WxMaService wxMaService;

//    @Autowired
//    private WxMpService wxMpService;

    @Autowired
    private RedisRepositoryImpl redisRepository;

    @Autowired
    private IUserService userService;

    @Autowired
    private Environment environment;

    /** 请求URL */
    private static final String BASE_URL = "https://api.weixin.qq.com/";

    /** 公众号获取access_token */
    private static final String ACCESS_TOKEN = "cgi-bin/token";

    /** 公众号获取access_token */
    private static final String STABLE_TOKEN = "cgi-bin/stable_token";

    /** 小程序消息推送 */
    private static final String SEND = "cgi-bin/message/subscribe/send?access_token=";

    /** 公众号消息推送 */
    private static final String UNIFORM_SEND = "cgi-bin/message/template/send?access_token=";

    /** 公众号获取用户openId */
    private static final String OPEN_ID_LIST = "cgi-bin/user/get";

    /** 公众号获取用户 */
    private static final String USER_INFO = "/cgi-bin/user/info";

    /**
     * 小程序消息推送
     * @param message
     * @return
     */
    public WxMsgResponseVo send(WxMaSubscribeMessage message){
        try {
            String accessToken = wxMaService.getAccessToken();
            String url = BASE_URL + SEND + accessToken;
            String result = HttpUtil.post(url, JSONUtil.toJsonStr(message));
            log.info("=======小程序推送消息======请求链接:{}, to:{},MsgBody:{},result:{}",url,message.getToUser(),JSONUtil.toJsonStr(message),result);
            WxMsgResponseVo wxMsgResponseVo = JSONUtil.toBean(result, WxMsgResponseVo.class);
            if (CommonUtils.isEmpty(wxMsgResponseVo)){
                log.info("小程序推送消息失败调用微信返回为空");
            } else if ("40001".equals(wxMsgResponseVo.getErrcode()) || "40014".equals(wxMsgResponseVo.getErrcode())
            || "42001".equals(wxMsgResponseVo.getErrcode()) ) {
                log.info("小程序推送消息失败,accessToken过期，重新获取accessToken发起请求！");
                //access_token 过期需要刷新获取
                accessToken = wxMaService.getAccessToken(true);
                url = BASE_URL + SEND + accessToken;
                result = HttpUtil.post(url, JSONUtil.toJsonStr(message));
                log.info("=======小程序推送消息======请求链接:{}, to:{},MsgBody:{},result:{}",url,message.getToUser(),message,result);
                wxMsgResponseVo = JSONUtil.toBean(result, WxMsgResponseVo.class);
            }
            return wxMsgResponseVo;
        } catch (WxErrorException e) {
            log.error("小程序消息推送异常：{}",e);
        }
        return null;
    }

    /**
     * 公众号消息推送
     */
    public WxMsgResponseVo accountSend(WxAccountSendRequestVo message){
        try {
            String accessToken = this.getStableAccessToken(false);
            String url = BASE_URL + UNIFORM_SEND + accessToken;
            String result = HttpUtil.post(url, JSONUtil.toJsonStr(message));
            log.info("=======微信公众号推送消息======请求链接:{}, to:{},MsgBody:{},result:{}",url,message.getTouser(),JSONUtil.toJsonStr(message),result);
            WxMsgResponseVo wxMsgResponseVo = JSONUtil.toBean(result, WxMsgResponseVo.class);
            if (CommonUtils.isEmpty(wxMsgResponseVo)){
                log.info("微信公众号推送消息失败调用微信返回为空");
            }else if ("40001".equals(wxMsgResponseVo.getErrcode()) || "40014".equals(wxMsgResponseVo.getErrcode())
                    || "42001".equals(wxMsgResponseVo.getErrcode()) ) {
                log.info("微信公众号推送消息失败,accessToken过期，重新获取accessToken发起请求！");
                //access_token 过期需要刷新获取
                accessToken = this.getStableAccessToken(true);
                url = BASE_URL + UNIFORM_SEND + accessToken;
                result = HttpUtil.post(url, JSONUtil.toJsonStr(message));
                log.info("=======微信公众号推送消息======请求链接:{}, to:{},MsgBody:{},result:{}",url,message.getTouser(),JSONUtil.toJsonStr(message),result);
                wxMsgResponseVo = JSONUtil.toBean(result, WxMsgResponseVo.class);
            }
            log.info("微信公众号推送消息-调用微信返回内容{}",wxMsgResponseVo);
            return wxMsgResponseVo;
        } catch (Exception e) {
            log.error("=============微信公众号消息推送异常===========：{}",e);
        }
        return null;
    }

    /**
     * 获取微信公众号AccessToken
     * @return
     */
    public String getAccountAccessToken(){
        String accessToken = redisRepository.get(CacheKey.WX_ACCOUNT_ACCESS_TOKEN);
        if(CommonUtils.isEmpty(accessToken)){
            UrlBuilder urlBuilder = UrlBuilder.of(BASE_URL + ACCESS_TOKEN, CharsetUtil.CHARSET_UTF_8);
            urlBuilder.addQuery("grant_type","client_credential");
            urlBuilder.addQuery("appid",environment.getProperty("pocky.wx.mp.appid"));
            urlBuilder.addQuery("secret",environment.getProperty("pocky.wx.mp.appSecret"));
            log.info("===========获取微信公众号accessToken============，请求：{}",urlBuilder.toString());
            String result = HttpUtil.get(urlBuilder.toString());
            log.info("===========获取微信公众号accessToken============，返回结果：{}",result);
            WxAccessTokenResponseVo response = JSONUtil.toBean(result, WxAccessTokenResponseVo.class);
            redisRepository.setExpire(CacheKey.WX_ACCOUNT_ACCESS_TOKEN,response.getAccess_token(), response.getExpires_in()-300);
            accessToken = response.getAccess_token();
        }
        return accessToken;
    }

    /**
     *
     * @return
     */
    /**
     * 获取微信公众号StableAccessToken
     * @param refresh 是否重新获取
     * @return
     */
    public String getStableAccessToken(boolean refresh){
        String accessToken = redisRepository.get(CacheKey.WX_ACCOUNT_ACCESS_TOKEN);
        if(CommonUtils.isEmpty(accessToken) || refresh){
            UrlBuilder urlBuilder = UrlBuilder.of(BASE_URL + STABLE_TOKEN, CharsetUtil.CHARSET_UTF_8);
            Map<String,Object> map = new HashMap<>();
            map.put("grant_type","client_credential");
            map.put("appid",environment.getProperty("pocky.wx.mp.appid"));
            map.put("secret",environment.getProperty("pocky.wx.mp.appSecret"));
            map.put("force_refresh",false);
            log.info("===========获取微信公众号accessToken============，请求：{}",urlBuilder.toString());
            String result = HttpUtil.post(urlBuilder.toString(),JSONUtil.toJsonStr(map));
            log.info("===========获取微信公众号accessToken============，返回结果：{}",result);
            WxAccessTokenResponseVo response = JSONUtil.toBean(result, WxAccessTokenResponseVo.class);
            redisRepository.setExpire(CacheKey.WX_ACCOUNT_ACCESS_TOKEN,response.getAccess_token(), response.getExpires_in()-300);
            accessToken = response.getAccess_token();
        }
        return accessToken;
    }

    /**
     * 处理微信公众号用户OpenId
     * @return
     */
    public void handleAccountOpenId(){
        UrlBuilder urlBuilder = UrlBuilder.of(BASE_URL + OPEN_ID_LIST, CharsetUtil.CHARSET_UTF_8);
        urlBuilder.addQuery("access_token",this.getStableAccessToken(false));
        String lastOpenId = redisRepository.get(CacheKey.WX_ACCOUNT_LAST_OPEN_ID);
        if(CommonUtils.isNotEmpty(lastOpenId)){
            urlBuilder.addQuery("next_openid",lastOpenId);
        }
        log.info("===========获取微信公众号openIdList============，请求：{}",urlBuilder.toString());
        String result = HttpUtil.get(urlBuilder.toString());
        log.info("===========获取微信公众号openIdList============，返回结果：{}",result);
        WxAccountOpenIdResponseVo response = JSONUtil.toBean(result, WxAccountOpenIdResponseVo.class);
        if(CommonUtils.isNotEmpty(response.getData()) && CommonUtils.isNotEmpty(response.getData().getOpenid())){
            List<User> userList = userService.lambdaQuery().isNotNull(User::getAccountOpenId).list();
            List<String> userAccountOpenIdList = new ArrayList<>();
            if(CommonUtils.isNotEmpty(userList)){
                userAccountOpenIdList = userList.stream().map(u -> u.getAccountOpenId()).distinct().collect(Collectors.toList());
            }
            for(String openId : response.getData().getOpenid()){
                if(CommonUtils.isNotEmpty(userAccountOpenIdList) && userAccountOpenIdList.contains(openId)){
                    continue;
                }
                UrlBuilder userUrl = UrlBuilder.of(BASE_URL + USER_INFO, CharsetUtil.CHARSET_UTF_8);
                userUrl.addQuery("access_token",this.getStableAccessToken(false));
                userUrl.addQuery("openid",openId);
                log.info("===========获取微信公众号用户信息============，请求：{}",userUrl.toString());
                String userResult = HttpUtil.get(userUrl.toString());
                log.info("===========获取微信公众号用户信息============，返回结果：{}",userResult);
                WxAccountUserResponseVo userResponse = JSONUtil.toBean(userResult, WxAccountUserResponseVo.class);
                if(CommonUtils.isNotEmpty(userResponse.getUnionid())){
                    userService.lambdaUpdate().eq(User::getUnionId,userResponse.getUnionid())
                            .set(User::getAccountOpenId,openId)
                            .update(new User());
                }
            }
            if(10000 == response.getCount()){
                redisRepository.set(CacheKey.WX_ACCOUNT_LAST_OPEN_ID,response.getNext_openid());
                this.handleAccountOpenId();
            }
        }
        if(CommonUtils.isNotEmpty(redisRepository.get(CacheKey.WX_ACCOUNT_LAST_OPEN_ID))){
            redisRepository.del(CacheKey.WX_ACCOUNT_LAST_OPEN_ID);
        }
    }

    /**
     * 处理微信公众号关注回调
     * @param msg
     */
    public void handleAccountNotify(AccountMsgNotifyResponseVo msg){
        try {
            if(CommonUtils.isNotEmpty(msg) && CommonUtils.isNotEmpty(msg.getEvent())){
                if(msg.getEvent().equals("subscribe")){
                    log.info("==========处理微信公众号关注回调=========");
                    List<User> userList = userService.lambdaQuery().eq(User::getAccountOpenId,msg.getToUserName()).list();
                    if(CommonUtils.isEmpty(userList)){
                        UrlBuilder userUrl = UrlBuilder.of(BASE_URL + USER_INFO, CharsetUtil.CHARSET_UTF_8);
                        userUrl.addQuery("access_token",this.getStableAccessToken(false));
                        userUrl.addQuery("openid",msg.getToUserName());
                        log.info("===========获取微信公众号用户信息============，请求：{}",userUrl.toString());
                        String userResult = HttpUtil.get(userUrl.toString());
                        log.info("===========获取微信公众号用户信息============，返回结果：{}",userResult);
                        WxAccountUserResponseVo userResponse = JSONUtil.toBean(userResult, WxAccountUserResponseVo.class);
                        if(CommonUtils.isNotEmpty(userResponse.getUnionid())){
                            userService.lambdaUpdate().eq(User::getUnionId,userResponse.getUnionid())
                                    .set(User::getAccountOpenId,msg.getToUserName())
                                    .update(new User());
                        }
                    }
                }
            }
        }catch (Exception e){
            log.info("==========微信公众号回调=========，错误：{}",e.getMessage());
        }
    }

    /**
     * 验证微信签名
     */
    public boolean checkSignature(String signature, String timestamp,String nonce, String token) {
        // 1.将token、timestamp、nonce三个参数进行字典序排序
        String[] arr = new String[]{token, timestamp, nonce};
        Arrays.sort(arr);
        // 2. 将三个参数字符串拼接成一个字符串进行sha1加密
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < arr.length; i++) {
            content.append(arr[i]);
        }
        MessageDigest md = null;
        String tmpStr = null;
        try {
            md = MessageDigest.getInstance("SHA-1");
            // 将三个参数字符串拼接成一个字符串进行sha1加密
            byte[] digest = md.digest(content.toString().getBytes());
            tmpStr = byteToStr(digest);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        content = null;
        // 3.将sha1加密后的字符串可与signature对比，标识该请求来源于微信
        return tmpStr != null && tmpStr.equals(signature.toUpperCase());
    }

    private static String byteToStr(byte[] byteArray) {
        StringBuilder strDigest = new StringBuilder();
        for (int i = 0; i < byteArray.length; i++) {
            strDigest.append(byteToHexStr(byteArray[i]));
        }
        return strDigest.toString();
    }

    private static String byteToHexStr(byte mByte) {
        char[] Digit = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A',
                'B', 'C', 'D', 'E', 'F'};
        char[] tempArr = new char[2];
        tempArr[0] = Digit[(mByte >>> 4) & 0X0F];
        tempArr[1] = Digit[mByte & 0X0F];
        String s = new String(tempArr);
        return s;
    }

}
