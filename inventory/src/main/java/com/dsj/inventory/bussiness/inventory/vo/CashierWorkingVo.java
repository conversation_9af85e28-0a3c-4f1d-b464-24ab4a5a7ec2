package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 进销存收银员值班记录
 * <AUTHOR>
 * @date 2023/04/03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="CashierWorkingVo", description="进销存收银员值班记录实体")
@ToString(callSuper = true)
public class CashierWorkingVo {

    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "收银员id")
    private Long cashierId;

    @ApiModelProperty(value = "收银员名称")
    private String cashierName;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "实收金额")
    private Integer actualPrice;

    @ApiModelProperty(value = "收款合计")
    private Integer totalPrice;

    @ApiModelProperty(value = "找零金额")
    private Integer giveChangePrice;

    @ApiModelProperty(value = "抹零金额")
    private Integer deductPrice;

    @ApiModelProperty(value = "优惠金额")
    private Integer preferentialPrice;

    @ApiModelProperty(value = "退款金额")
    private Integer returnPrice;

    @ApiModelProperty(value = "充值金额")
    private Integer rechargePrice;

    @ApiModelProperty(value = "实际充值金额")
    private Integer rechargeRealPrice;

    @ApiModelProperty(value = "赠送充值金额")
    private Integer rechargeGivePrice;

    @ApiModelProperty(value = "支付信息，json字符串")
    private String payInfo;

    @ApiModelProperty(value = "储值")
    private Integer storedPay;

    @ApiModelProperty(value = "活动减额")
    private Integer activityReducePrice;

    @ApiModelProperty(value = "优惠券金额")
    private Integer couponPrice;

    @ApiModelProperty(value = "积分抵扣")
    private Integer integralPay;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
