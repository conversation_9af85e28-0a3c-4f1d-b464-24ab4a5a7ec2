package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.MedicineTemplate;
import com.dsj.inventory.bussiness.inventory.param.QueryMedicineTemplateParam;
import com.dsj.inventory.bussiness.inventory.vo.MedicineTemplateVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 药品拉取模板 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
public interface MedicineTemplateMapper extends BaseMapper<MedicineTemplate> {

    /**
     * 获取药品拉取模板分页列表
     * @param page
     * @param param
     * @return
     */
    List<MedicineTemplateVo> queryMedicineTemplatePage(@Param("page") Page page, @Param("param") QueryMedicineTemplateParam param);

    /**
     * 获取药品拉取模板详情
     * @param id
     * @return
     */
    MedicineTemplateVo getMedicineTemplateById(Long id);

}
