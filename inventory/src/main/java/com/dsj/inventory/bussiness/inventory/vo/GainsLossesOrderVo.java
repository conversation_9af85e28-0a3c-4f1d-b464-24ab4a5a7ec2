package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 损溢单
 * <AUTHOR>
 * @date 2023/04/06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="GainsLossesOrderVo", description="损溢单实体")
@ToString(callSuper = true)
public class GainsLossesOrderVo {

    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "损溢单号")
    private String code;

    @ApiModelProperty(value = "处理状态 0暂存 1已完成")
    private Integer dealState;

    @ApiModelProperty(value = "损溢类型 1损2溢")
    private Integer type;

    @ApiModelProperty(value = "报损总数")
    private String lossesAmount;

    @ApiModelProperty(value = "报溢总数")
    private String gainsAmount;

    @ApiModelProperty(value = "成本金额（成本均价总额）")
    private Integer costTotalPrice;

    @ApiModelProperty(value = "零售总金额（私域商品总金额）")
    private Integer totalPrice;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "损溢商品名")
    private String goodsNames;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
