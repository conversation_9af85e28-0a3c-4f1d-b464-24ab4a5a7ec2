package com.dsj.inventory.common.enumeration;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/12/07
 */

@Getter
public enum RedisStreamEnum {

    SALE("sale-prescription-consumer", "sale-prescription-group","sale-prescription-stream","销售单同步"),
    PRESCRIPTION_FILE("prescription-file-consumer", "prescription-file-group","prescription-file-stream","处方文件"),
    ;

    private String consumerName;

    private String groupName;

    private String streamName;

    private String desc;

    RedisStreamEnum(String redisCode, String joinCode, String streamName, String desc) {
        this.consumerName = redisCode;
        this.groupName = joinCode;
        this.streamName = streamName;
        this.desc = desc;
    }

    public static String getDesc(String redisCode) {
        for (com.dsj.inventory.common.enumeration.RedisStreamEnum a : com.dsj.inventory.common.enumeration.RedisStreamEnum.values()) {
            if (a.getConsumerName().equals(redisCode)) {
                return a.desc;
            }
        }
        return null;
    }
}
