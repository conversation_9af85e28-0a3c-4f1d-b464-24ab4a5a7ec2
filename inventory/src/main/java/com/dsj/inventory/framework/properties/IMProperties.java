package com.dsj.inventory.framework.properties;


import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 */
@Setter
@Getter
@ConfigurationProperties(prefix = "pocky.im")
@Configuration
public class IMProperties {
    /**
     * 腾讯im sdkAppId
     */
    private String sdkAppId = "12345678";

    /**
     * 腾讯im 秘钥
     */
    private String secretkey = "12345678";

    /**
     * 腾讯im 签名过期时间 默认7天
     */
    private int expiretime = 604800;


}
