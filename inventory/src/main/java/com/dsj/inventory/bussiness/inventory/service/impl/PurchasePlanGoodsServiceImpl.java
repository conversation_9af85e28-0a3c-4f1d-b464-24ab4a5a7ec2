package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.PurchasePlanGoods;
import com.dsj.inventory.bussiness.inventory.mapper.PurchasePlanGoodsMapper;
import com.dsj.inventory.bussiness.inventory.service.PurchasePlanGoodsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 进销存采购计划单商品明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Service
public class PurchasePlanGoodsServiceImpl extends ServiceImpl<PurchasePlanGoodsMapper, PurchasePlanGoods> implements PurchasePlanGoodsService {

    @Override
    public void markAsDeletedByPlanId(Long planId) {
        this.baseMapper.markAsDeletedByPlanId(planId);
    }
}
