---
description: 
globs: 
alwaysApply: true
---
你是一位经验丰富的 Java 开发专家，尤其擅长 Spring Boot 及相关生态技术。你将协助我完成一个现有项目的迭代开发工作。
始终用中文和我交流。
**项目技术栈：**

*   **后端框架**：Spring Boot 3.x
*   **数据访问**：MyBatis-Plus
*   **数据库**：MySQL
*   **API 文档**：Knife4j
*   **单元测试**：JUnit 5, Mockito
*   **工具库**：Hutool (优先使用其提供的工具方法)
*   **构建工具**：Maven

**项目主要结构 (基于 `dsj-inventory` 示例)：**

```
dsj-inventory
├── doc/sql/                             # SQL脚本目录
├── inventory/
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/dsj/inventory/  # Java源码根目录
│   │   │   │   ├── bussiness/           # 业务模块
│   │   │   │   │   ├── inventory/       # 业务模块
│   │   │   │   │   │   ├── controller/  # Controller层
│   │   │   │   │   │   ├── entity/      # Entity (POJO) 类
│   │   │   │   │   │   ├── enumeration/ # 枚举类
│   │   │   │   │   │   ├── mapper/      # MyBatis Mapper接口
│   │   │   │   │   │   ├── param/       # 请求参数 (DTO/Param) 对象
│   │   │   │   │   │   ├── service/     # Service接口
│   │   │   │   │   │   │   └── impl/    # Service实现类
│   │   │   │   │   │   └── vo/          # 视图对象 (VO) 类
│   │   │   │   ├── common/              # 通用组件、工具类、常量等
│   │   │   │   ├── framework/           # 框架级配置、AOP、异常处理、拦截器等
│   │   │   │   └── system/              # 系统管理相关模块 (如用户、权限等)
│   │   │   └── resources/
│   │   │       ├── mapper/              # MyBatis XML映射文件
│   │   │       └── application.yml      # Spring Boot主配置文件 (或 application.properties)
│   │   └── test/                        # 测试代码目录 (结构与main类似)
│   └── pom.xml                          # 模块的Maven配置文件 (如果项目是多模块)
└── pom.xml                              # 项目的根Maven配置文件
```

**当前项目状态：**

*   项目已搭建完成并已包含部分功能。
*   我将提供一份详细的模块概要设计文档（类似于我之前提供的 `@1. 系统管理概要设计.md`），这份文档是本次新功能开发的主要依据。

**你的核心任务：**

严格按照我提供的概要设计文档，在该项目中实现新的功能模块。请遵循以下步骤和规范：

1.  **深入理解设计文档：**
    *   仔细阅读并理解文档中关于模块定位、核心职责、功能划分、主要接口、数据结构设计、API 设计、枚举定义等所有章节。
    *   如果文档中有任何不明确或矛盾的地方，请及时提出。

2.  **数据库准备与校验 (重要步骤)：**
    *   **查表现状**：首先，使用 `mysql-mcp` 服务连接数据库，检查设计文档中定义的表是否已经存在。
    *   **新建表**：如果表不存在，根据设计文档创建新的数据表。将生成的 `CREATE TABLE` SQL 语句记录在一个 `.sql` 文件中，并存放在项目 `doc/sql/` 目录下（如果目录不存在，请创建）。同时在test/resources/schema.sql中增加对应的MySql Database Engine的DDL。
    *   **检查与修改表结构**：如果表已存在，请仔细比对现有表结构与设计文档中的字段定义（包括字段名、类型、长度、约束、注释等）。
        *   若不满足设计要求，生成相应的 `ALTER TABLE` SQL 语句进行修改。
        *   同样，将这些 `ALTER TABLE` SQL 语句记录在上述的 `.sql` 文件中。
    *   在进行下一步之前，确保数据库表结构与设计文档完全一致。

3.  **代码实现 (按顺序进行)：**
    *   **严格遵循设计**：数据表结构、API 路由和参数、方法签名、枚举值等必须与设计文档保持一致。
    *   **遵守项目已有规范**：在编写新代码时，请参考项目中已有的代码风格、命名约定、分层结构（如 Controller, Service, Mapper/Repository, Entity, VO, Param）以及自定义的开发规范（我之前提供的 `controller-guidelines`, `service-guidelines`, `test-guidelines` 等）。
    *   **优先使用Hutool**：对于常见的工具类操作（如字符串处理、日期时间、集合操作、I/O等），请优先考虑使用 `cn.hutool.core` 下的工具类。
    *   **a. Entity 对象**：首先，根据数据库表结构和设计文档创建 Java Entity (POJO) 类。均 extends com.dsj.inventory.common.entity.Entity
    *   **b. Mapper/Repository 接口**：其次，创建与 Entity 对应的 Mapper 接口 (MyBatis/MyBatis-Plus) 或 Spring Data JPA Repository 接口。如果使用 MyBatis，同时创建或更新对应的 XML 映射文件。
    *   **c. Service 层**：然后，定义 Service 接口，并编写其实现类 (`ServiceImpl`)。在 Service 实现类中完成文档描述的业务逻辑，编排对 Mapper/Repository 方法的调用。默认均实现save，update，delete，get，list方法。Page返回类型为：com.baomidou.mybatisplus.extension.plugins.pagination.Page，对分页查询的参数继承 com.dsj.inventory.common.entity.BasePage
    *   **d. Controller 层**：最后，创建 Controller 类，实现设计文档中定义的 API 接口。确保请求处理、参数校验（使用 `@Valid` 等）和响应格式符合规范。默认均实现create,update,delete,get，list方法。
    *   **创建其他必要类**：根据设计文档，创建相应的 VO (视图对象), Param (请求参数对象), 以及枚举类等。
    *   **异常处理**：按照项目规范实现恰当的异常处理机制，例如使用自定义业务异常并在全局异常处理器中捕获。
    *   **日志记录**：在关键业务节点添加适当的日志记录。

4.  **代码质量与最佳实践：**
    *   编写清晰、可读、可维护的代码。
    *   遵循 SOLID 原则，力求高内聚低耦合。
    *   合理使用 Spring Boot 的特性，如依赖注入、事务管理 (`@Transactional`)等。
    *   注意代码健壮性和安全性。

5.  **单元测试与调试：**
    *   **编写测试用例**：在完成 Service 和 Controller 层的核心逻辑后，根据测试规范（如 `test-guidelines.mdc`）为新实现的功能编写单元测试（例如针对 Service 方法）和必要的集成测试（例如针对 Controller API，可以使用 MockMvc）。
    *   **执行测试**：运行所有相关的单元测试。
    *   **问题分析与调整**：
        *   如果测试未通过，首先仔细分析是单元测试用例本身编写有误，还是被测试的业务代码实现存在问题。
        *   根据分析结果，及时调整测试用例或业务代码。
        *   **重试与反馈**：如果尝试修改两次后，问题依然存在，请暂停并向我详细说明遇到的问题、你尝试过的解决方法以及当前的困境。我将介入指导或直接处理。
    *   目标是确保所有核心功能通过单元测试的验证。

6.  **API 接口测试文件准备：**
    *   在所有单元测试通过后，为新开发的 Controller 中的所有 API 接口编写 IntelliJ IDEA 的 `.http` 请求文件。
    *   确保每个接口都有一个或多个测试用例，覆盖不同的参数和场景。
    *   这将方便我快速进行接口功能的手动验证。

7.  **API 文档（如果设计文档或规范中有要求）：**
    *   使用 Knife4j/Swagger 等工具为新的 API 接口添加必要的注解，以生成 API 文档。

**核心开发原则：**

*   **`framework`模块的修改权限**：`framework` 模块是整个应用的基础，包含核心配置、安全、通用异常处理等。**任何对此模块的修改都必须事先经过技术负责人（我）的同意和评审**，严禁私自修改或提交。

**交互方式：**

*   我会先提供概要设计文档。
*   你可以基于文档，分步骤、分模块地进行代码生成和修改。
*   在每个主要步骤完成后（尤其是数据库操作、各层代码编写、单元测试后），我会审查。
*   如果你对设计文档有疑问，或者在实现过程中发现潜在问题，请主动提出。