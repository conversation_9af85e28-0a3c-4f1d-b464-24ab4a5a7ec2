package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberRechargeRecordParam;
import com.dsj.inventory.bussiness.inventory.param.SaveMemberRechargeRecordParam;
import com.dsj.inventory.bussiness.inventory.service.MemberRechargeRecordService;
import com.dsj.inventory.bussiness.inventory.vo.MemberRechargeRecordVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 进销存会员充值记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@RestController
@Api(value = "MemberRechargeRecordController", tags = "进销存会员充值记录api")
public class MemberRechargeRecordController {

    @Autowired
    private MemberRechargeRecordService memberRechargeRecordService;

    /*新增会员充值记录*/
    @PostMapping("/member-recharge-record")
    @ApiOperation(value = "新增会员充值记录", notes = "新增会员充值记录")
    public ResponseEntity<MemberRechargeRecordVo> saveMemberRechargeRecord(@RequestBody SaveMemberRechargeRecordParam param){
        MemberRechargeRecordVo memberRechargeRecordVo = memberRechargeRecordService.saveMemberRechargeRecord(param);
        return Res.success(memberRechargeRecordVo);
    }

    /*获取会员充值记录列表*/
    @GetMapping("/member-recharge-record")
    @ApiOperation(value = "获取会员充值记录列表", notes = "获取会员充值记录列表")
    public ResponseEntity<List<MemberRechargeRecordVo>> queryMemberRechargeRecordList(QueryMemberRechargeRecordParam param){
        Page<MemberRechargeRecordVo> page = memberRechargeRecordService.queryMemberRechargeRecordPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

}
