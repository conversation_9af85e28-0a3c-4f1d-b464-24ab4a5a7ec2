package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.SalesGoods;
import com.dsj.inventory.bussiness.inventory.vo.SalesGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存销售单商品明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface SalesGoodsService extends IService<SalesGoods> {

    /**
     * 获取销售单商品明细列表
     * @param orderId
     * @return
     */
    List<SalesGoodsVo> getSalesGoodsListByOrderId(Long orderId);

}
