package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityTaskMapping;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskMappingVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 销售活动任务关联信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
public interface SalesActivityTaskMappingMapper extends BaseMapper<SalesActivityTaskMapping> {

    /**
     * 获取活动任务关联活动
     * @param taskId
     * @return
     */
    List<SalesActivityTaskMappingVo> queryActivityTaskMappingListByTaskId(@Param("taskId") Long taskId);

}
