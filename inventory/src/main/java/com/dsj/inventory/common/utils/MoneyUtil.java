package com.dsj.inventory.common.utils;

import cn.hutool.core.util.NumberUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p>
 * 金额计算工具类，统一处理金额计算精度、舍入规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
public class MoneyUtil {

    /**
     * 默认金额精度（小数位数）
     */
    public static final int DEFAULT_SCALE = 2;
    
    /**
     * 金额计算中使用的舍入模式
     */
    public static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;
    
    /**
     * 创建BigDecimal，避免使用new BigDecimal(double)导致精度问题
     * @param value 数值
     * @return BigDecimal对象
     */
    public static BigDecimal toBigDecimal(Number value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        return NumberUtil.toBigDecimal(value);
    }
    
    /**
     * 创建BigDecimal，避免使用new BigDecimal(String)导致精度问题
     * @param value 数值字符串
     * @return BigDecimal对象
     */
    public static BigDecimal toBigDecimal(String value) {
        return NumberUtil.toBigDecimal(value);
    }
    
    /**
     * 对金额进行精度设置和舍入
     * @param amount 金额
     * @return 处理后的金额
     */
    public static BigDecimal round(BigDecimal amount) {
        return round(amount, DEFAULT_SCALE);
    }
    
    /**
     * 对金额进行精度设置和舍入
     * @param amount 金额
     * @param scale 小数位数
     * @return 处理后的金额
     */
    public static BigDecimal round(BigDecimal amount, int scale) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        return NumberUtil.round(amount, scale, DEFAULT_ROUNDING_MODE);
    }
    
    /**
     * 金额加法运算
     * @param amount1 金额1
     * @param amount2 金额2
     * @return 计算结果
     */
    public static BigDecimal add(Number amount1, Number amount2) {
        return NumberUtil.add(toBigDecimal(amount1), toBigDecimal(amount2));
    }
    
    /**
     * 金额减法运算
     * @param amount1 金额1
     * @param amount2 金额2
     * @return 计算结果
     */
    public static BigDecimal sub(Number amount1, Number amount2) {
        return NumberUtil.sub(toBigDecimal(amount1), toBigDecimal(amount2));
    }
    
    /**
     * 金额乘法运算
     * @param amount1 金额1
     * @param amount2 金额2
     * @return 计算结果
     */
    public static BigDecimal mul(Number amount1, Number amount2) {
        return NumberUtil.mul(toBigDecimal(amount1), toBigDecimal(amount2));
    }
    
    /**
     * 金额除法运算
     * @param amount1 金额1
     * @param amount2 金额2
     * @return 计算结果
     */
    public static BigDecimal div(Number amount1, Number amount2) {
        return NumberUtil.div(toBigDecimal(amount1), toBigDecimal(amount2), DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }
    
    /**
     * 获取金额的整数部分
     * @param amount 金额
     * @return 整数值
     */
    public static Integer toInteger(BigDecimal amount) {
        if (amount == null) {
            return 0;
        }
        return amount.setScale(0, DEFAULT_ROUNDING_MODE).intValue();
    }
    
    /**
     * 安全比较两个金额值是否相等（避免浮点数精度问题）
     * @param amount1 金额1
     * @param amount2 金额2
     * @return 是否相等
     */
    public static boolean equals(BigDecimal amount1, BigDecimal amount2) {
        if (amount1 == null && amount2 == null) {
            return true;
        }
        if (amount1 == null || amount2 == null) {
            return false;
        }
        return amount1.compareTo(amount2) == 0;
    }
    
    /**
     * 安全比较两个金额值是否相等（避免浮点数精度问题）
     * @param amount1 金额1
     * @param amount2 金额2
     * @return 是否相等
     */
    public static boolean equals(Number amount1, Number amount2) {
        return equals(toBigDecimal(amount1), toBigDecimal(amount2));
    }
    
    /**
     * 判断金额是否为零或null
     * @param amount 金额
     * @return 是否为零或null
     */
    public static boolean isZeroOrNull(BigDecimal amount) {
        return amount == null || BigDecimal.ZERO.compareTo(amount) == 0;
    }
    
    /**
     * 判断金额是否大于零
     * @param amount 金额
     * @return 是否大于零
     */
    public static boolean isGreaterThanZero(BigDecimal amount) {
        return amount != null && amount.compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * 判断金额是否小于零
     * @param amount 金额
     * @return 是否小于零
     */
    public static boolean isLessThanZero(BigDecimal amount) {
        return amount != null && amount.compareTo(BigDecimal.ZERO) < 0;
    }
} 