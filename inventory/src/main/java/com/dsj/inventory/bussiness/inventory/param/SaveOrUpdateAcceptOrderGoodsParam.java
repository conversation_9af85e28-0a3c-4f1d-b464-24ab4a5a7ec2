package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 验收单商品明细
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateAcceptOrderGoodsParam", description="新增or编辑 验收单商品明细参数")
public class SaveOrUpdateAcceptOrderGoodsParam {

    @ApiModelProperty(hidden = true,value = "验收单Id")
    private Long orderId;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(hidden = true,value = "通用名")
    private String goodsName;

    @ApiModelProperty(hidden = true,value = "商品名称")
    private String generalName;

    @ApiModelProperty(hidden = true,value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(hidden = true,value = "包装单位")
    private String packUnit;

    @ApiModelProperty(hidden = true,value = "规格型号")
    private String specification;

    @ApiModelProperty(hidden = true,value = "剂型")
    private String drugType;

    @ApiModelProperty(hidden = true,value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(hidden = true,value = "上市许可持有人")
    private String launchPermitHolder;

    @ApiModelProperty(hidden = true,value = "产地")
    private String producePlace;

    @ApiModelProperty(hidden = true,value = "当前零售价（私域商品价格）")
    private Integer price;

    @ApiModelProperty(hidden = true,value = "会员价（私域商品会员价）")
    private Integer memberPrice;

    @ApiModelProperty(hidden = true,value = "当前库存")
    private Double repertory;

    @ApiModelProperty(hidden = true,value = "含税价（进货价）")
    private Integer purchasePrice;

    @ApiModelProperty(hidden = true,value = "折后单价（含税价折后）")
    private Integer discountPrice;

    @ApiModelProperty(hidden = true,value = "采购数量")
    private Double purchaseNumber;

    @ApiModelProperty(hidden = true,value = "含税金额（进货总金额）")
    private Integer totalPurchasePrice;

    @ApiModelProperty(hidden = true,value = "折后金额（含税金额折后）")
    private Integer totalDiscountPrice;

    @ApiModelProperty(hidden = true,value = "生产日期")
    private LocalDateTime produceDate;

    @ApiModelProperty(hidden = true,value = "有效期至")
    private LocalDateTime userDate;

    @ApiModelProperty(hidden = true,value = "收货数量")
    private Double receiveAmount;

    @ApiModelProperty(value = "赠送数量")
    private Double giveAmount = 0.00;

    @ApiModelProperty(value = "合格数量")
    private Double qualifiedAmount;

    @ApiModelProperty(value = "不合格数量")
    private Double unqualifiedAmount = 0.00;

    @ApiModelProperty(value = "不合格原因")
    private String unqualifiedReason;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
