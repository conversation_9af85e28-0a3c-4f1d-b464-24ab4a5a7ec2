package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.*;
import com.dsj.inventory.bussiness.inventory.enumeration.PurchaseOrderStateEnum;
import com.dsj.inventory.bussiness.inventory.enumeration.PurchasePlanStateEnum;
import com.dsj.inventory.bussiness.inventory.mapper.PurchasePlanMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryPurchasePlanParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdatePurchasePlanGoodsParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdatePurchasePlanParam;
import com.dsj.inventory.bussiness.inventory.service.*;
import com.dsj.inventory.bussiness.inventory.vo.PurchasePlanDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchasePlanExportVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchasePlanGoodsVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchasePlanVo;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineExpand;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.enumeration.GoodsTypeEnum;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineExpandService;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.bean.BeanUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 进销存采购计划单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Service
public class PurchasePlanServiceImpl extends ServiceImpl<PurchasePlanMapper, PurchasePlan> implements PurchasePlanService {

    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @Autowired
    private PurchasePlanGoodsService purchasePlanGoodsService;
    @Autowired
    private OrganizationSupplierService organizationSupplierService;
    @Autowired
    private PurchaseOrderService purchaseOrderService;
    @Autowired
    private PurchaseOrderGoodsService purchaseOrderGoodsService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationMedicineExpandService organizationMedicineExpandService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PurchasePlanDetailVo saveOrUpdatePurchasePlan(SaveOrUpdatePurchasePlanParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        Organization organization = organizationService.getById(param.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        List<SaveOrUpdatePurchasePlanParam> dataList = new ArrayList<>();
        if(CommonUtils.isNotEmpty(param.getId())) {
            PurchasePlan byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            //删除商品明细
            purchasePlanGoodsService.markAsDeletedByPlanId(param.getId());
            if(CommonUtils.isNotEmpty(param.getGoodsList())){
                List<Long> supplierIdList = param.getGoodsList().stream().map(s -> s.getOrganizationSupplierId()).distinct().collect(Collectors.toList());
                if(supplierIdList.size() > 1 || !supplierIdList.get(0).equals(byId.getOrganizationSupplierId())){
                    boolean deleteFlag = true;
                    //生成采购计划单号：JH+年后两位+月日四位+药房识别码六位+三位
                    String day = DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_FORMAT);
                    String dayStr = day.replaceAll("-", "").substring(2,8);
                    Integer count = this.baseMapper.getPurchasePlanCount(organization.getId(),day);
                    for(Long supplierId : supplierIdList){
                        OrganizationSupplier organizationSupplier = organizationSupplierService.getById(supplierId);
                        if(CommonUtils.isEmpty(organizationSupplier)){
                            throw new BizException("操作失败，供应商" + organizationSupplier.getName() + "不存在");
                        }
                        SaveOrUpdatePurchasePlanParam data = BeanUtil.toBean(param,SaveOrUpdatePurchasePlanParam.class);
                        if(!supplierId.equals(byId.getOrganizationSupplierId())){
                            data.setId(null);
                            count = count + 1;
                            data.setCode("JH" + dayStr + organization.getSerialNo() + String.format("%03d",count));
                        }else {
                            deleteFlag = false;
                        }
                        List<SaveOrUpdatePurchasePlanGoodsParam> goodsDataList = new ArrayList<>();
                        StringBuilder purchaseContent = new StringBuilder();
                        Set<String> goodsTypeSet = new HashSet<>();
                        BigDecimal totalPrice = BigDecimal.valueOf(0);
                        for(SaveOrUpdatePurchasePlanGoodsParam goodsParam : param.getGoodsList()){
                            if(supplierId.equals(goodsParam.getOrganizationSupplierId())){
                                SaveOrUpdatePurchasePlanGoodsParam goodData = BeanUtil.toBean(goodsParam,SaveOrUpdatePurchasePlanGoodsParam.class);
                                goodData.setPlanId(null);
                                OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(goodData.getOrganizationMedicineId());
                                if(CommonUtils.isEmpty(organizationMedicine)){
                                    throw new BizException("操作失败，商品Id"+goodData.getOrganizationMedicineId()+"不存在");
                                }
                                goodData.setGoodsName(organizationMedicine.getName());
                                goodData.setGeneralName(organizationMedicine.getGeneralName());
                                goodData.setGoodsCode(organizationMedicine.getGoodsCode());
                                goodData.setPackUnit(organizationMedicine.getPackUnit());
                                goodData.setSpecification(organizationMedicine.getSpecification());
                                goodData.setDrugType(organizationMedicine.getDrugType());
                                goodData.setManufacturer(organizationMedicine.getManufacturer());
                                OrganizationMedicineExpand medicineExpand = organizationMedicineExpandService.getById(organizationMedicine.getId());
                                if(CommonUtils.isNotEmpty(medicineExpand)){
                                    goodData.setLaunchPermitHolder(medicineExpand.getLaunchPermitHolder());
                                }
                                goodData.setPrice(organizationMedicine.getOfflinePrice());
                                goodData.setRepertory(organizationMedicine.getMedicineRepertory());
                                BigDecimal totalPurchasePrice = BigDecimal.valueOf(goodsParam.getPurchasePrice())
                                        .multiply(BigDecimal.valueOf(goodsParam.getPurchaseNumber()));
                                totalPurchasePrice = totalPurchasePrice.setScale(0,BigDecimal.ROUND_HALF_UP);
                                if(totalPurchasePrice.intValue() != goodsParam.getTotalPurchasePrice()){
                                    throw new BizException("操作失败，商品"+goodData.getGoodsName()+"小计金额计算不一致");
                                }
                                goodData.setTotalPurchasePrice(totalPurchasePrice.intValue());
                                goodsDataList.add(goodData);
                                purchaseContent.append(goodData.getGoodsName()+"*"+goodData.getPurchaseNumber()+goodData.getPackUnit()+",");
                                goodsTypeSet.add(GoodsTypeEnum.getDesc(organizationMedicine.getGoodsType()));
                                totalPrice = totalPrice.add(BigDecimal.valueOf(goodData.getTotalPurchasePrice()));
                            }
                        }
                        List<Long> goodsIdList = goodsDataList.stream().map(g -> g.getOrganizationMedicineId()).distinct().collect(Collectors.toList());
                        if(goodsIdList.size() != goodsDataList.size()){
                            throw new BizException("操作失败，供应商" + organizationSupplier.getName() + "存在重复商品");
                        }
                        data.setGoodsList(goodsDataList);
                        data.setPurchaseContent(purchaseContent.substring(0,purchaseContent.length()-1));
                        data.setGoodsTypes(String.join(",",goodsTypeSet));
                        data.setTotalPrice(totalPrice.intValue());
                        data.setOrganizationSupplierId(supplierId);
                        dataList.add(data);
                    }
                    if(deleteFlag){
                        this.baseMapper.deleteById(param.getId());
                    }
                }else {
                    OrganizationSupplier organizationSupplier = organizationSupplierService.getById(supplierIdList.get(0));
                    if(CommonUtils.isEmpty(organizationSupplier)){
                        throw new BizException("操作失败，供应商" + organizationSupplier.getName() + "不存在");
                    }
                    List<SaveOrUpdatePurchasePlanGoodsParam> goodsDataList = new ArrayList<>();
                    StringBuilder purchaseContent = new StringBuilder();
                    Set<String> goodsTypeSet = new HashSet<>();
                    BigDecimal totalPrice = BigDecimal.valueOf(0);
                    for(SaveOrUpdatePurchasePlanGoodsParam goodsParam : param.getGoodsList()){
                        SaveOrUpdatePurchasePlanGoodsParam goodData = BeanUtil.toBean(goodsParam,SaveOrUpdatePurchasePlanGoodsParam.class);
                        goodData.setPlanId(null);
                        OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(goodData.getOrganizationMedicineId());
                        if(CommonUtils.isEmpty(organizationMedicine)){
                            throw new BizException("操作失败，商品Id"+goodData.getOrganizationMedicineId()+"不存在");
                        }
                        goodData.setGoodsName(organizationMedicine.getName());
                        goodData.setGeneralName(organizationMedicine.getGeneralName());
                        goodData.setGoodsCode(organizationMedicine.getGoodsCode());
                        goodData.setPackUnit(organizationMedicine.getPackUnit());
                        goodData.setSpecification(organizationMedicine.getSpecification());
                        goodData.setDrugType(organizationMedicine.getDrugType());
                        goodData.setManufacturer(organizationMedicine.getManufacturer());
                        OrganizationMedicineExpand medicineExpand = organizationMedicineExpandService.getById(organizationMedicine.getId());
                        if(CommonUtils.isNotEmpty(medicineExpand)) {
                            goodData.setLaunchPermitHolder(medicineExpand.getLaunchPermitHolder());
                        }
                        goodData.setPrice(organizationMedicine.getOfflinePrice());
                        goodData.setRepertory(organizationMedicine.getMedicineRepertory());
                        BigDecimal totalPurchasePrice = BigDecimal.valueOf(goodsParam.getPurchasePrice())
                                .multiply(BigDecimal.valueOf(goodsParam.getPurchaseNumber()));
                        totalPurchasePrice = totalPurchasePrice.setScale(0,BigDecimal.ROUND_HALF_UP);
                        if(totalPurchasePrice.intValue() != goodsParam.getTotalPurchasePrice()){
                            throw new BizException("操作失败，商品"+goodData.getGoodsName()+"小计金额计算不一致");
                        }
                        goodData.setTotalPurchasePrice(totalPurchasePrice.intValue());
                        goodsDataList.add(goodData);
                        purchaseContent.append(goodData.getGoodsName()+"*"+goodData.getPurchaseNumber()+goodData.getPackUnit()+",");
                        goodsTypeSet.add(GoodsTypeEnum.getDesc(organizationMedicine.getGoodsType()));
                        totalPrice = totalPrice.add(BigDecimal.valueOf(goodData.getTotalPurchasePrice()));
                    }
                    List<Long> goodsIdList = goodsDataList.stream().map(g -> g.getOrganizationMedicineId()).distinct().collect(Collectors.toList());
                    if(goodsIdList.size() != goodsDataList.size()){
                        throw new BizException("操作失败，供应商" + organizationSupplier.getName() + "存在重复商品");
                    }
                    param.setGoodsList(goodsDataList);
                    param.setPurchaseContent(purchaseContent.substring(0,purchaseContent.length()-1));
                    param.setGoodsTypes(String.join(",",goodsTypeSet));
                    param.setTotalPrice(totalPrice.intValue());
                    param.setOrganizationSupplierId(goodsDataList.get(0).getOrganizationSupplierId());
                    dataList.add(param);
                }
            }
        }else {
            if(CommonUtils.isNotEmpty(param.getGoodsList())){
                //生成采购计划单号：JH+年后两位+月日四位+药房识别码六位+三位
                String day = DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_FORMAT);
                String dayStr = day.replaceAll("-", "").substring(2,8);
                Integer count = this.baseMapper.getPurchasePlanCount(organization.getId(),day);
                List<Long> supplierIdList = param.getGoodsList().stream().map(s -> s.getOrganizationSupplierId()).distinct().collect(Collectors.toList());
                for(Long supplierId : supplierIdList){
                    OrganizationSupplier organizationSupplier = organizationSupplierService.getById(supplierId);
                    if(CommonUtils.isEmpty(organizationSupplier)){
                        throw new BizException("操作失败，供应商" + organizationSupplier.getName() + "不存在");
                    }
                    SaveOrUpdatePurchasePlanParam data = BeanUtil.toBean(param,SaveOrUpdatePurchasePlanParam.class);
                    data.setId(null);
                    List<SaveOrUpdatePurchasePlanGoodsParam> goodsDataList = new ArrayList<>();
                    StringBuilder purchaseContent = new StringBuilder();
                    Set<String> goodsTypeSet = new HashSet<>();
                    BigDecimal totalPrice = BigDecimal.valueOf(0);
                    for(SaveOrUpdatePurchasePlanGoodsParam goodsParam : param.getGoodsList()){
                        if(supplierId.equals(goodsParam.getOrganizationSupplierId())){
                            SaveOrUpdatePurchasePlanGoodsParam goodData = BeanUtil.toBean(goodsParam,SaveOrUpdatePurchasePlanGoodsParam.class);
                            goodData.setPlanId(null);
                            OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(goodData.getOrganizationMedicineId());
                            if(CommonUtils.isEmpty(organizationMedicine)){
                                throw new BizException("操作失败，商品Id"+goodData.getOrganizationMedicineId()+"不存在");
                            }
                            goodData.setGoodsName(organizationMedicine.getName());
                            goodData.setGeneralName(organizationMedicine.getGeneralName());
                            goodData.setGoodsCode(organizationMedicine.getGoodsCode());
                            goodData.setPackUnit(organizationMedicine.getPackUnit());
                            goodData.setSpecification(organizationMedicine.getSpecification());
                            goodData.setDrugType(organizationMedicine.getDrugType());
                            goodData.setManufacturer(organizationMedicine.getManufacturer());
                            OrganizationMedicineExpand medicineExpand = organizationMedicineExpandService.getById(organizationMedicine.getId());
                            if(CommonUtils.isNotEmpty(medicineExpand)) {
                                goodData.setLaunchPermitHolder(medicineExpand.getLaunchPermitHolder());
                            }
                            goodData.setPrice(organizationMedicine.getOfflinePrice());
                            goodData.setRepertory(organizationMedicine.getMedicineRepertory());
                            BigDecimal totalPurchasePrice = BigDecimal.valueOf(goodsParam.getPurchasePrice())
                                    .multiply(BigDecimal.valueOf(goodsParam.getPurchaseNumber()));
                            totalPurchasePrice = totalPurchasePrice.setScale(0,BigDecimal.ROUND_HALF_UP);
                            if(totalPurchasePrice.intValue() != goodsParam.getTotalPurchasePrice()){
                                throw new BizException("操作失败，商品"+goodData.getGoodsName()+"小计金额计算不一致");
                            }
                            goodData.setTotalPurchasePrice(totalPurchasePrice.intValue());
                            goodsDataList.add(goodData);
                            purchaseContent.append(goodData.getGoodsName()+"*"+goodData.getPurchaseNumber()+goodData.getPackUnit()+",");
                            goodsTypeSet.add(GoodsTypeEnum.getDesc(organizationMedicine.getGoodsType()));
                            totalPrice = totalPrice.add(BigDecimal.valueOf(goodData.getTotalPurchasePrice()));
                        }
                    }
                    List<Long> goodsIdList = goodsDataList.stream().map(g -> g.getOrganizationMedicineId()).distinct().collect(Collectors.toList());
                    if(goodsIdList.size() != goodsDataList.size()){
                        throw new BizException("操作失败，供应商" + organizationSupplier.getName() + "存在重复商品");
                    }
                    data.setGoodsList(goodsDataList);
                    data.setPurchaseContent(purchaseContent.substring(0,purchaseContent.length()-1));
                    data.setGoodsTypes(String.join(",",goodsTypeSet));
                    data.setTotalPrice(totalPrice.intValue());
                    data.setOrganizationSupplierId(supplierId);
                    count = count + 1;
                    data.setCode("JH" + dayStr + organization.getSerialNo() + String.format("%03d",count));
                    dataList.add(data);
                }
            }
        }
        for(SaveOrUpdatePurchasePlanParam data : dataList){
            PurchasePlan purchasePlan = BeanUtil.toBean(data,PurchasePlan.class);
            this.saveOrUpdate(purchasePlan);
            if(CommonUtils.isNotEmpty(data.getGoodsList())){
                data.getGoodsList().stream().map(g -> g.setPlanId(purchasePlan.getId())).collect(Collectors.toList());
                List<PurchasePlanGoods> goodsList = data.getGoodsList().stream().map(item -> BeanUtil.toBean(item, PurchasePlanGoods.class)).collect(Collectors.toList());
                purchasePlanGoodsService.saveBatch(goodsList);
            }
            if(PurchasePlanStateEnum.EXECUTED.getCode().equals(purchasePlan.getDealState())){
                PurchaseOrder purchaseOrder = BeanUtil.toBean(purchasePlan,PurchaseOrder.class);
                purchaseOrder.setId(null);
                purchaseOrder.setPlanId(purchasePlan.getId());
                purchaseOrder.setOrderCode(purchasePlan.getCode());
                purchaseOrder.setPurchaserId(BaseContextHandler.getUserId());
                purchaseOrder.setDealState(PurchaseOrderStateEnum.TEMPORARY.getCode());
                purchaseOrder.setCreateTime(null);
                purchaseOrder.setCreateUser(null);
                //生成采购订单单号：CG+年后两位+月日四位+药房识别码六位+三位
                String day = DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_FORMAT);
                String dayStr = day.replaceAll("-", "").substring(2,8);
                Integer count = purchaseOrderService.getPurchaseOrderCount(organization.getId(),day);
                count = count + 1;
                purchaseOrder.setCode("CG" + dayStr + organization.getSerialNo() + String.format("%03d",count));
                purchaseOrderService.save(purchaseOrder);
                if(CommonUtils.isNotEmpty(data.getGoodsList())){
                    List<PurchaseOrderGoods> orderGoodsList = data.getGoodsList().stream().map(item -> BeanUtil.toBean(item, PurchaseOrderGoods.class)).collect(Collectors.toList());
                    orderGoodsList.stream().forEach(g -> {g.setOrderId(purchaseOrder.getId());g.setId(null);});
                    purchaseOrderGoodsService.saveBatch(orderGoodsList);
                }
            }
        }
        return BeanUtil.toBean(param,PurchasePlanDetailVo.class);
    }

    @Override
    public void deletePurchasePlan(Long id) {
        PurchasePlan byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        if(!byId.getDealState().equals(PurchasePlanStateEnum.WAITING.getCode())){
            throw new BizException("操作失败，采购计划当前状态不允许删除");
        }
        //删除商品明细
        purchasePlanGoodsService.markAsDeletedByPlanId(id);
        this.baseMapper.deleteById(id);
    }

    @Override
    public Page<PurchasePlanVo> queryPurchasePlanPage(QueryPurchasePlanParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<PurchasePlanVo> list = this.baseMapper.queryPurchasePlanPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public PurchasePlanDetailVo getPurchasePlanById(Long id) {
        PurchasePlanDetailVo vo = this.baseMapper.getPurchasePlanById(id);
        if(CommonUtils.isNotEmpty(vo)){
            List<PurchasePlanGoods> goodsList = purchasePlanGoodsService.lambdaQuery().eq(PurchasePlanGoods::getPlanId,vo.getId()).list();
            if(CommonUtils.isNotEmpty(goodsList)){
                List<PurchasePlanGoodsVo> goodsVoList = goodsList.stream().map(item -> BeanUtil.toBean(item, PurchasePlanGoodsVo.class)).collect(Collectors.toList());
                goodsVoList.stream().map(g -> g.setOrganizationSupplierId(vo.getOrganizationSupplierId())).collect(Collectors.toList());
                vo.setGoodsList(goodsVoList);
            }
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startPurchasePlan(Long id) {
        PurchasePlanDetailVo purchasePlan = this.getPurchasePlanById(id);
        if(CommonUtils.isEmpty(purchasePlan)){
            throw new BizException("操作失败，计划不存在");
        }
        if(PurchasePlanStateEnum.EXECUTED.getCode().equals(purchasePlan.getDealState())){
            throw new BizException("操作失败，计划已执行");
        }
        Organization organization = organizationService.getById(purchasePlan.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        PurchaseOrder purchaseOrder = BeanUtil.toBean(purchasePlan,PurchaseOrder.class);
        purchaseOrder.setId(null);
        purchaseOrder.setPlanId(id);
        purchaseOrder.setOrderCode(purchasePlan.getCode());
        purchaseOrder.setPurchaserId(BaseContextHandler.getUserId());
        purchaseOrder.setDealState(PurchaseOrderStateEnum.TEMPORARY.getCode());
        purchaseOrder.setCreateTime(null);
        purchaseOrder.setCreateUser(null);
        //生成采购订单单号：CG+年后两位+月日四位+药房识别码六位+三位
        String day = DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_FORMAT);
        String dayStr = day.replaceAll("-", "").substring(2,8);
        Integer count = purchaseOrderService.getPurchaseOrderCount(organization.getId(),day);
        count = count + 1;
        purchaseOrder.setCode("CG" + dayStr + organization.getSerialNo() + String.format("%03d",count));
        purchaseOrderService.save(purchaseOrder);
        if(CommonUtils.isNotEmpty(purchasePlan.getGoodsList())){
            List<PurchaseOrderGoods> goodsList = purchasePlan.getGoodsList().stream().map(item -> BeanUtil.toBean(item, PurchaseOrderGoods.class)).collect(Collectors.toList());
            goodsList.stream().forEach(g -> {g.setOrderId(purchaseOrder.getId());g.setId(null);});
            purchaseOrderGoodsService.saveBatch(goodsList);
        }
        //将采购计划变更为已执行
        this.baseMapper.updateState(id, PurchasePlanStateEnum.EXECUTED.getCode());
    }

    @Override
    public List<PurchasePlanExportVo> queryPurchasePlanList(QueryPurchasePlanParam param) {
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        return this.baseMapper.queryPurchasePlanList(param);
    }

    @Override
    public void deletePurchasePlanByOrganizationId(Long organizationId) {
        this.baseMapper.deletePurchasePlanByOrganizationId(organizationId);
    }

    @Override
    public Integer getPurchasePlanCount(Long organizationId, String day) {
        return this.baseMapper.getPurchasePlanCount(organizationId, day);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStateToExecuted(Long planId) {
        // 此处逻辑是绑定采购订单后，将计划状态从"待执行"变为"已执行"
        this.baseMapper.updateStateWithCondition(planId, PurchasePlanStateEnum.WAITING.getCode(), PurchasePlanStateEnum.EXECUTED.getCode());
    }

}
