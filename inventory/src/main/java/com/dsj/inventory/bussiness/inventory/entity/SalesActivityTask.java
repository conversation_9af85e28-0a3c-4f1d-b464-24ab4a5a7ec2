package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 销售活动任务信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_sales_activity_task")
@ApiModel(value="SalesActivityTask", description="销售活动任务信息")
public class SalesActivityTask extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "模块分类，1 云药快 ；2 外部；3 在线医院；4 商城；5进销存")
    @TableField("module_type")
    private Integer moduleType;

    @ApiModelProperty(value = "任务名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "是否执行 0未执行 1已执行")
    @TableField("execute_state")
    private Integer executeState;

    @ApiModelProperty(value = "活动类别，1单品活动 2会员日活动 3整单活动")
    @TableField("activity_category")
    private Integer activityCategory;

    @ApiModelProperty(value = "周期")
    @TableField("cycle")
    private String cycle;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}