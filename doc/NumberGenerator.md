# 编号生成器设计文档

## 1. 概述

本文档详细描述了系统中的编号生成器设计与实现。编号生成器是一个用于生成各类业务编号的组件，支持多种编号类型，并保证在分布式环境下的唯一性和连续性。

## 2. 功能需求

编号生成器需要满足以下功能需求：

- 支持多种编号类型，包括：供应商首营单据编号、供应商编号、采购订单编号、采购计划编号、退货编号等
- 具有良好的扩展性，支持后续添加更多编号类型
- 在分布式环境下保证编号的唯一性，解决多主机部署时的并发问题
- 编号格式统一，便于识别和管理

## 3. 设计方案

### 3.1 整体架构

编号生成器采用了策略模式与工厂模式的组合设计，主要包含以下几个核心组件：

1. 编号类型定义（`NumberType`）
2. 编号生成器接口（`NumberGenerator`）
3. 抽象编号生成器（`AbstractNumberGenerator`）
4. 基于Redis的实现（`RedisNumberGenerator`）
5. 基于雪花算法的实现（`SnowflakeNumberGenerator`）
6. 编号生成器工厂（`NumberGeneratorFactory`）
7. 编号服务（`NumberService`）

### 3.2 核心组件说明

#### 3.2.1 编号类型（NumberType）

编号类型枚举定义了系统中支持的所有编号类型，每种类型都包含前缀和描述信息：

```java
public enum NumberType {
    SUPPLIER_FIRST_DOCUMENT("SF", "供应商首营单据编号"),
    SUPPLIER_CODE("SC", "供应商编号"),
    PURCHASE_ORDER("PO", "采购订单编号"),
    PURCHASE_PLAN("PP", "采购计划编号"),
    RETURN_ORDER("RO", "退货编号");
    
    // ...省略getter方法
}
```

#### 3.2.2 编号生成器接口（NumberGenerator）

定义了编号生成器的基本行为：

```java
public interface NumberGenerator {
    String generate();
    NumberType getType();
}
```

#### 3.2.3 抽象编号生成器（AbstractNumberGenerator）

实现了编号生成的通用逻辑，定义了编号的基本格式：前缀 + 日期部分 + 序列号部分

```java
public abstract class AbstractNumberGenerator implements NumberGenerator {
    // ...
    @Override
    public String generate() {
        String datePart = getDatePart();
        String sequencePart = getSequencePart();
        return formatNumber(datePart, sequencePart);
    }
    
    // ...其他辅助方法
}
```

#### 3.2.4 Redis编号生成器（RedisNumberGenerator）

基于Redis的原子操作实现分布式环境下的序列号生成：

```java
public class RedisNumberGenerator extends AbstractNumberGenerator {
    // ...
    @Override
    protected String getSequencePart() {
        String date = getDatePart();
        String key = String.format("number_generator:%s:%s", type.getPrefix(), date);
        
        // 使用Redis的原子操作获取并递增序列号
        RedisAtomicLong counter = new RedisAtomicLong(key, redisTemplate.getConnectionFactory());
        
        // 设置过期时间
        counter.expire(3, TimeUnit.DAYS);
        
        // 获取递增后的值并格式化
        long sequence = counter.incrementAndGet();
        return String.format("%0" + sequenceLength + "d", sequence);
    }
    // ...
}
```

#### 3.2.5 雪花算法编号生成器（SnowflakeNumberGenerator）

基于hutool工具包中的雪花算法实现高性能的分布式唯一ID生成：

```java
public class SnowflakeNumberGenerator extends AbstractNumberGenerator {
    private final cn.hutool.core.lang.Snowflake snowflake;
    
    // ...
    
    public SnowflakeNumberGenerator(NumberType type, long workerId, long dataCenterId) {
        super(type);
        // 使用hutool的雪花算法实现，设置工作节点ID和数据中心ID
        this.snowflake = IdUtil.getSnowflake(workerId, dataCenterId);
    }
    
    @Override
    protected String getSequencePart() {
        long snowflakeId = snowflake.nextId();
        return String.format("%010d", snowflakeId % 10000000000L);
    }
}
```

#### 3.2.6 编号生成器工厂（NumberGeneratorFactory）

负责创建和管理所有编号生成器实例：

```java
@Component
public class NumberGeneratorFactory {
    // ...
    
    @PostConstruct
    public void init() {
        for (NumberType type : NumberType.values()) {
            if (useSnowflake) {
                generatorMap.put(type, createSnowflakeGenerator(type));
            } else {
                generatorMap.put(type, createRedisGenerator(type));
            }
        }
    }
    
    public NumberGenerator getGenerator(NumberType type) {
        return generatorMap.get(type);
    }
    
    // ...其他工厂方法
}
```

#### 3.2.7 编号服务（NumberService）

提供业务层面的编号生成服务：

```java
@Service
public class NumberService {
    private final NumberGeneratorFactory generatorFactory;
    
    // ...
    
    public String generateSupplierFirstDocumentNumber() {
        return generatorFactory.getGenerator(NumberType.SUPPLIER_FIRST_DOCUMENT).generate();
    }
    
    // ...其他编号生成方法
    
    public String generateNumber(NumberType type) {
        return generatorFactory.getGenerator(type).generate();
    }
}
```

## 4. 编号格式说明

所有编号都遵循统一的格式：前缀 + 日期部分 + 序列号部分

- 前缀：由`NumberType`枚举中定义的前缀决定，通常为2个英文字母
- 日期部分：采用`yyyyMMdd`格式的日期字符串，如20240601
- 序列号部分：根据编号类型不同，长度可能不同，通常为5-6位数字，由0填充

示例：
- 供应商首营单据编号：SF20240601000001
- 供应商编号：SC2024060100001
- 采购订单编号：PO20240601000001

## 5. 分布式解决方案

系统提供了两种分布式环境下保证编号唯一性的解决方案：

### 5.1 基于Redis的解决方案（默认）

利用Redis的原子操作（INCR命令）实现分布式序列号生成。每种编号类型在每天都有独立的计数器，格式为：`number_generator:{前缀}:{日期}`，如`number_generator:PO:20240601`。

优点：
- 实现简单，易于理解
- 保证编号的连续性

### 5.2 基于雪花算法的解决方案

使用雪花算法生成分布式唯一ID，适合对性能要求高、可以接受ID不连续的场景。

优点：
- 高性能，不依赖外部存储
- 适合高并发场景

## 6. 类图

```mermaid
classDiagram
    %% 定义类和接口
    class NumberType {
        <<enumeration>>
        -String prefix
        -String description
        +String getPrefix()
        +String getDescription()
    }
    
    class NumberGenerator {
        <<interface>>
        +String generate()
        +NumberType getType()
    }
    
    class AbstractNumberGenerator {
        <<abstract>>
        #NumberType type
        #getDatePart() String
        #getSequencePart() String*
        #formatNumber(datePart, sequencePart) String
        +generate() String
        +getType() NumberType
    }
    
    class RedisNumberGenerator {
        -RedisTemplate redisTemplate
        -int sequenceLength
        #getSequencePart() String
        +resetCounter(date) void
    }
    
    class SnowflakeNumberGenerator {
        -cn.hutool.core.lang.Snowflake snowflake
        #getSequencePart() String
    }
    
    class NumberGeneratorFactory {
        -RedisTemplate redisTemplate
        -Map~NumberType, NumberGenerator~ generatorMap
        -long workerId
        -long dataCenterId
        -boolean useSnowflake
        +init() void
        +getGenerator(type) NumberGenerator
        -createRedisGenerator() NumberGenerator
        -createSnowflakeGenerator() NumberGenerator
        +registerGenerator() void
    }
    
    class NumberService {
        -NumberGeneratorFactory generatorFactory
        +generateSupplierFirstDocumentNumber() String
        +generateSupplierCode() String
        +generatePurchaseOrderNumber() String
        +generatePurchasePlanNumber() String
        +generateReturnOrderNumber() String
        +generateNumber(type) String
    }
    
    %% 定义关系
    NumberGenerator <|.. AbstractNumberGenerator : 实现
    AbstractNumberGenerator <|-- RedisNumberGenerator : 继承
    AbstractNumberGenerator <|-- SnowflakeNumberGenerator : 继承
    
    NumberGeneratorFactory ..> RedisNumberGenerator : 创建
    NumberGeneratorFactory ..> SnowflakeNumberGenerator : 创建
    NumberGeneratorFactory ..> NumberGenerator : 使用
    NumberGeneratorFactory ..> NumberType : 使用
    
    NumberService --> NumberGeneratorFactory : 依赖
    NumberService --> NumberType : 使用
```

## 7. 使用指南

### 7.1 基本使用

#### 7.1.1 在业务服务中使用

最常见的使用方式是在业务服务中注入`NumberService`，然后调用相应的方法：

```java
@Service
public class SupplierService {
    
    @Autowired
    private NumberService numberService;
    
    /**
     * 创建新供应商
     */
    public void createSupplier(SupplierDTO supplierDTO) {
        // 获取供应商编号
        String supplierCode = numberService.generateSupplierCode();
        
        Supplier supplier = new Supplier();
        supplier.setCode(supplierCode);
        supplier.setName(supplierDTO.getName());
        // 设置其他属性...
        
        // 保存供应商
        supplierRepository.save(supplier);
    }
    
    /**
     * 创建供应商首营单据
     */
    public void createSupplierFirstDocument(Long supplierId) {
        // 获取供应商首营单据编号
        String documentNumber = numberService.generateSupplierFirstDocumentNumber();
        
        SupplierFirstDocument document = new SupplierFirstDocument();
        document.setDocumentNumber(documentNumber);
        document.setSupplierId(supplierId);
        // 设置其他属性...
        
        // 保存单据
        documentRepository.save(document);
    }
}
```

#### 7.1.2 使用通用方法

如果需要动态确定编号类型，可以使用通用方法：

```java
@Service
public class PurchaseService {

    @Autowired
    private NumberService numberService;
    
    /**
     * 创建采购相关单据
     * @param type 单据类型
     */
    public String createPurchaseDocument(DocumentType type) {
        // 根据单据类型选择对应的编号类型
        NumberType numberType;
        
        switch (type) {
            case ORDER:
                numberType = NumberType.PURCHASE_ORDER;
                break;
            case PLAN:
                numberType = NumberType.PURCHASE_PLAN;
                break;
            case RETURN:
                numberType = NumberType.RETURN_ORDER;
                break;
            default:
                throw new IllegalArgumentException("不支持的单据类型: " + type);
        }
        
        // 生成对应类型的编号
        String documentNumber = numberService.generateNumber(numberType);
        
        // 创建单据...
        return documentNumber;
    }
}
```

### 7.2 高级用法

#### 7.2.1 添加新的编号类型

当系统需要支持新的编号类型时，需要在`NumberType`枚举中添加定义：

```java
public enum NumberType {
    // 原有类型
    SUPPLIER_FIRST_DOCUMENT("SF", "供应商首营单据编号"),
    SUPPLIER_CODE("SC", "供应商编号"),
    PURCHASE_ORDER("PO", "采购订单编号"),
    PURCHASE_PLAN("PP", "采购计划编号"),
    RETURN_ORDER("RO", "退货编号"),
    
    // 新增类型
    INVENTORY_CHECK("IC", "库存盘点单编号"),
    DELIVERY_ORDER("DO", "出库单编号"),
    RECEIVING_ORDER("RE", "入库单编号");
    
    // ... 其他代码不变
}
```

添加后，系统会在启动时自动为新编号类型创建对应的生成器，无需额外配置。

#### 7.2.2 自定义编号生成逻辑

如果标准的Redis和雪花算法实现不满足特殊需求，可以自定义编号生成逻辑：

```java
/**
 * 基于数据库的编号生成器
 */
public class DbNumberGenerator extends AbstractNumberGenerator {
    
    private final JdbcTemplate jdbcTemplate;
    private final int sequenceLength;
    
    public DbNumberGenerator(NumberType type, JdbcTemplate jdbcTemplate, int sequenceLength) {
        super(type);
        this.jdbcTemplate = jdbcTemplate;
        this.sequenceLength = sequenceLength;
    }
    
    @Override
    protected String getSequencePart() {
        // 使用数据库序列生成序列号
        String date = getDatePart();
        String key = type.getPrefix() + "_" + date;
        
        // 使用数据库事务确保原子性
        Long sequence = jdbcTemplate.execute(new ConnectionCallback<Long>() {
            @Override
            public Long doInConnection(Connection con) throws SQLException {
                try (PreparedStatement ps = con.prepareStatement(
                        "INSERT INTO number_sequence (sequence_key, next_val, update_time) " +
                        "VALUES (?, 1, NOW()) " +
                        "ON DUPLICATE KEY UPDATE next_val = next_val + 1, update_time = NOW(); " +
                        "SELECT next_val FROM number_sequence WHERE sequence_key = ?")) {
                    ps.setString(1, key);
                    ps.setString(2, key);
                    try (ResultSet rs = ps.executeQuery()) {
                        if (rs.next()) {
                            return rs.getLong(1);
                        }
                        return 1L;
                    }
                }
            }
        });
        
        // 格式化序列号
        return String.format("%0" + sequenceLength + "d", sequence);
    }
}
```

然后注册到工厂：

```java
@Configuration
public class CustomNumberGeneratorConfig {
    
    @Autowired
    private NumberGeneratorFactory factory;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @PostConstruct
    public void init() {
        // 为特定类型注册自定义生成器
        DbNumberGenerator generator = new DbNumberGenerator(
            NumberType.INVENTORY_CHECK, jdbcTemplate, 6);
        factory.registerGenerator(NumberType.INVENTORY_CHECK, generator);
    }
}
```

#### 7.2.3 配置雪花算法

如果需要使用雪花算法而非Redis，可以通过配置修改：

```java
@Configuration
public class NumberGeneratorConfig {
    @Bean
    public NumberGeneratorFactory numberGeneratorFactory(RedisTemplate<String, String> redisTemplate) {
        // 使用雪花算法
        return new NumberGeneratorFactory(redisTemplate, true, 1L, 1L);
    }
}
```

## 8. 注意事项

1. Redis依赖：使用Redis实现时，需确保Redis服务可用
2. 雪花算法配置：使用雪花算法时，确保每个服务实例有唯一的workerId和dataCenterId
3. 时钟同步：使用雪花算法时，服务器间的时钟应保持同步
4. 序列号范围：根据序列号长度，注意可能的溢出问题
5. Redis键过期：默认Redis键保留3天，如需调整可修改`RedisNumberGenerator.getSequencePart()`方法

## 9. 性能考虑

1. Redis连接池：确保合理配置Redis连接池参数
2. 批量获取：对于需要批量生成编号的场景，考虑实现批量生成接口
3. 本地缓存：对于高频访问的编号类型，可以在本地缓存一定数量的编号

## 10. 扩展性设计

1. 新增编号类型：只需在`NumberType`枚举中添加新类型
2. 自定义生成器：通过实现`NumberGenerator`接口并注册到`NumberGeneratorFactory`来支持自定义生成逻辑
3. 编号格式：通过重写`AbstractNumberGenerator.formatNumber()`方法来自定义编号格式
