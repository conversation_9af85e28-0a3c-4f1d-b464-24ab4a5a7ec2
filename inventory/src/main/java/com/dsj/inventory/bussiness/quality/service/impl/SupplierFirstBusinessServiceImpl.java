package com.dsj.inventory.bussiness.quality.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationSupplier;
import com.dsj.inventory.bussiness.inventory.service.OrganizationSupplierService;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationSupplierVo;
import com.dsj.inventory.bussiness.quality.entity.SupplierFirstBusinessEntity;
import com.dsj.inventory.bussiness.quality.enumeration.*;
import com.dsj.inventory.bussiness.quality.mapper.SupplierFirstBusinessMapper;
import com.dsj.inventory.bussiness.quality.param.SupplierFirstBusinessApprovalParam;
import com.dsj.inventory.bussiness.quality.param.SupplierFirstBusinessQueryParam;
import com.dsj.inventory.bussiness.quality.service.SupplierFirstBusinessService;
import com.dsj.inventory.bussiness.quality.vo.SupplierFirstBusinessVO;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.SystemRoleCodeEnum;
import com.dsj.inventory.common.utils.PageUtils;
import com.dsj.inventory.framework.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商首营审批表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Service
public class SupplierFirstBusinessServiceImpl extends ServiceImpl<SupplierFirstBusinessMapper, SupplierFirstBusinessEntity> implements SupplierFirstBusinessService {

    @Autowired
    private OrganizationSupplierService organizationSupplierService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSupplierFirstBusiness(OrganizationSupplier supplier) {
        // 首先检查是否已经存在该供应商的草稿状态记录
        SupplierFirstBusinessEntity existingDraft = this.baseMapper.getSupplierFirstBusinessBySupplierIdAndStatus(supplier.getId(), null);

        // 如果已存在草稿，则直接返回其ID
        if (existingDraft != null) {
            return existingDraft.getId();
        }

        // 不存在草稿，创建新记录
        SupplierFirstBusinessEntity entity = new SupplierFirstBusinessEntity();

        // 设置供应商相关信息
        entity.setSupplierId(supplier.getId());

        // 可以根据需要从OrganizationSupplier中提取其他信息
        // 例如：entity.setSupplierName(supplier.getName());

        // 设置默认值
        entity.setApplyTime(LocalDateTime.now());
        entity.setApprovalType(ApprovalTypeEnum.FIRST_BUSINESS.getCode());
        entity.setStatus(DocumentStatusEnum.DRAFT.getCode()); // 默认状态: 草稿
        entity.setApprovalStep(SupplierFirstApprovalStepEnum.PURCHASE_MANAGER_REVIEW.getCode()); // 第一步：采购经理审核
        entity.setActionType(ActionTypeEnum.CREATE.getCode()); // 操作类型为创建
        entity.setApprovalSequence(getNextApprovalSequence(supplier.getId()));

        this.save(entity);
        return entity.getId();
    }

    @Override
    public SupplierFirstBusinessVO getSupplierFirstBusinessById(Long id) {
        SupplierFirstBusinessEntity entity = this.getById(id);
        if (entity == null) {
            return null;
        }
        return convertToVO(entity);
    }

    @Override
    public Page<SupplierFirstBusinessVO> querySupplierFirstBusinessPage(SupplierFirstBusinessQueryParam param) {
        Page<SupplierFirstBusinessEntity> page = PageUtils.toPage(param);
        LambdaQueryWrapper<SupplierFirstBusinessEntity> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.eq(param.getSupplierId() != null, SupplierFirstBusinessEntity::getSupplierId, param.getSupplierId());
        queryWrapper.eq(param.getStatus() != null, SupplierFirstBusinessEntity::getStatus, param.getStatus());

        // 添加审批步骤查询条件
        if (param.getApprovalStep() != null) {
            queryWrapper.eq(SupplierFirstBusinessEntity::getApprovalStep, param.getApprovalStep());
        }

        queryWrapper.orderByDesc(SupplierFirstBusinessEntity::getApplyTime);

        page = this.page(page, queryWrapper);
        return PageUtils.toPageVO(page, this::convertToVO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long approveSupplierFirstBusiness(SupplierFirstBusinessApprovalParam param) {
        // 获取要审批的记录
        SupplierFirstBusinessEntity entity = this.getById(param.getId());
        if (entity == null) {
            throw new BizException("供应商首营审批记录不存在，ID: " + param.getId());
        }

        // 校验供应商ID是否匹配
        if (!entity.getSupplierId().equals(param.getSupplierId())) {
            throw new BizException("供应商ID与审批记录不匹配");
        }

        // 校验状态
        if (!DocumentStatusEnum.REVIEWING.getCode().equals(entity.getStatus())) {
            throw new BizException("只有审核中状态的记录才能进行审批");
        }

        // 校验当前用户是否有权限进行当前步骤的审批
        validateApprovalPermission(entity.getApprovalStep(), entity.getCurrentApproverRole());

        // 创建新的审批记录
        SupplierFirstBusinessEntity newEntity = BeanUtil.copyProperties(entity, SupplierFirstBusinessEntity.class, "id", "createTime", "createUser", "updateTime", "updateUser");
        newEntity.setId(null); // 确保创建新记录
        newEntity.setActionType(param.getActionType()); // 设置操作类型（通过/驳回）
        newEntity.setApprovalOpinion(param.getApprovalOpinion()); // 设置审批意见
        newEntity.setReviewerId(BaseContextHandler.getUserId()); // 设置审核人
        newEntity.setReviewTime(LocalDateTime.now()); // 设置审核时间
        newEntity.setApprovalSequence(getNextApprovalSequence(param.getSupplierId())); // 获取新的审批序号

        // 根据审批结果设置状态和下一步
        if (ActionTypeEnum.APPROVE.getCode().equals(param.getActionType())) {
            // 审批通过
            // 根据当前审批步骤设置下一步
            if (SupplierFirstApprovalStepEnum.PURCHASE_MANAGER_REVIEW.getCode().equals(entity.getApprovalStep())) {
                // 第一步通过，进入第二步
                newEntity.setApprovalStep(SupplierFirstApprovalStepEnum.QUALITY_MANAGER_REVIEW.getCode());
                newEntity.setCurrentApproverRole(SystemRoleCodeEnum.QUALITY_MANAGER.getCode());
                newEntity.setStatus(DocumentStatusEnum.REVIEWING.getCode()); // 仍然是审核中
            } else if (SupplierFirstApprovalStepEnum.QUALITY_MANAGER_REVIEW.getCode().equals(entity.getApprovalStep())) {
                // 第二步通过，进入第三步
                newEntity.setApprovalStep(SupplierFirstApprovalStepEnum.QUALITY_SUPERVISOR_REVIEW.getCode());
                newEntity.setCurrentApproverRole(SystemRoleCodeEnum.QUALITY_SUPERVISOR.getCode());
                newEntity.setStatus(DocumentStatusEnum.REVIEWING.getCode()); // 仍然是审核中
            } else if (SupplierFirstApprovalStepEnum.QUALITY_SUPERVISOR_REVIEW.getCode().equals(entity.getApprovalStep())) {
                // 第三步通过，审批完成
                newEntity.setApprovalStep(SupplierFirstApprovalStepEnum.COMPLETED.getCode());
                newEntity.setCurrentApproverRole(null); // 没有下一个审批人
                newEntity.setStatus(DocumentStatusEnum.APPROVED.getCode()); // 状态改为已审核

                // 更新供应商状态为已审核通过
                organizationSupplierService.updateApprovalStatus(param.getSupplierId(), DocumentStatusEnum.APPROVED);
            }
        } else if (ActionTypeEnum.REJECT.getCode().equals(param.getActionType())) {
            // 审批驳回
            newEntity.setStatus(DocumentStatusEnum.REJECTED.getCode()); // 状态改为已驳回
            newEntity.setRejectReason(param.getRejectReason()); // 设置驳回原因

            // 更新供应商状态为已驳回
            organizationSupplierService.updateApprovalStatus(param.getSupplierId(), DocumentStatusEnum.REJECTED);
        }

        this.save(newEntity);
        return newEntity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long withdrawSupplierFirstBusiness(Long supplierId) {
        // 获取最新的审核中记录
        SupplierFirstBusinessEntity latestEntity = this.baseMapper.getSupplierFirstBusinessBySupplierIdAndStatus(supplierId, null);

        // 校验状态
        if (latestEntity == null) {
            throw new BizException("未找到供应商首营审批记录");
        }

        if (!DocumentStatusEnum.REVIEWING.getCode().equals(latestEntity.getStatus())) {
            throw new BizException("只有审核中状态的审批记录才能撤回");
        }

        // 只有创建人才能撤回
        if (!BaseContextHandler.getUserId().equals(latestEntity.getCreateUser())) {
            throw new BizException("只有创建人才能撤回审批");
        }

        // 创建新的审批记录
        SupplierFirstBusinessEntity newEntity = BeanUtil.copyProperties(latestEntity, SupplierFirstBusinessEntity.class, "id", "createTime", "createUser", "updateTime", "updateUser");
        newEntity.setId(null); // 确保创建新记录
        newEntity.setStatus(DocumentStatusEnum.DRAFT.getCode()); // 状态改为草稿
        newEntity.setActionType(ActionTypeEnum.WITHDRAW.getCode()); // 操作类型为撤回
        newEntity.setApprovalSequence(getNextApprovalSequence(supplierId)); // 获取新的审批序号
        newEntity.setCurrentApproverRole(BaseContextHandler.get(BaseContextConstants.USER_ROLE)); // 当前审批人角色
        newEntity.setReviewerId(BaseContextHandler.getUserId()); // 设置撤回人
        newEntity.setReviewTime(LocalDateTime.now()); // 设置撤回时间

        this.save(newEntity);

        // 更新供应商状态为草稿
        organizationSupplierService.updateApprovalStatus(supplierId, DocumentStatusEnum.DRAFT);

        return newEntity.getId();
    }

    @Override
    public SupplierFirstBusinessVO getLatestSupplierFirstBusiness(Long supplierId) {
        SupplierFirstBusinessEntity entity = this.baseMapper.getSupplierFirstBusinessBySupplierIdAndStatus(supplierId, null);
        if (entity == null) {
            return null;
        }
        return convertToVO(entity);
    }

    @Override
    public List<SupplierFirstBusinessVO> getSupplierFirstBusinessHistory(Long supplierId) {
        LambdaQueryWrapper<SupplierFirstBusinessEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SupplierFirstBusinessEntity::getSupplierId, supplierId);
        queryWrapper.orderByAsc(SupplierFirstBusinessEntity::getApprovalSequence);

        List<SupplierFirstBusinessEntity> entities = this.list(queryWrapper);
        return entities.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public Page<SupplierFirstBusinessVO> queryPendingApprovalPage(SupplierFirstBusinessQueryParam param) {
        // 获取当前用户角色
        String userRole = BaseContextHandler.get(BaseContextConstants.USER_ROLE);
        if (StringUtils.isEmpty(userRole)) {
            throw new BizException("未获取到当前用户角色信息");
        }

        Page<SupplierFirstBusinessEntity> page = PageUtils.toPage(param);
        LambdaQueryWrapper<SupplierFirstBusinessEntity> queryWrapper = Wrappers.lambdaQuery();

        // 只查询审核中的记录
        queryWrapper.eq(SupplierFirstBusinessEntity::getStatus, DocumentStatusEnum.REVIEWING.getCode());
        // 根据角色匹配当前审批人
        queryWrapper.eq(SupplierFirstBusinessEntity::getCurrentApproverRole, userRole);

        if (param.getSupplierId() != null) {
            queryWrapper.eq(SupplierFirstBusinessEntity::getSupplierId, param.getSupplierId());
        }

        queryWrapper.orderByDesc(SupplierFirstBusinessEntity::getApplyTime);

        page = this.page(page, queryWrapper);
        return PageUtils.toPageVO(page, this::convertToVO);
    }

    /**
     * 获取下一个审批序号
     *
     * @param supplierId 供应商ID
     * @return 下一个审批序号
     */
    private Integer getNextApprovalSequence(Long supplierId) {
        // 查询当前最大序号
        LambdaQueryWrapper<SupplierFirstBusinessEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SupplierFirstBusinessEntity::getSupplierId, supplierId);
        queryWrapper.orderByDesc(SupplierFirstBusinessEntity::getApprovalSequence);
        queryWrapper.last("LIMIT 1");

        SupplierFirstBusinessEntity latestEntity = this.getOne(queryWrapper);
        if (latestEntity == null) {
            return 1; // 第一条记录
        }

        return latestEntity.getApprovalSequence() + 1;
    }

    /**
     * 校验当前用户是否有权限进行当前步骤的审批
     *
     * @param approvalStep 审批步骤
     * @param requiredRole 所需角色
     */
    private void validateApprovalPermission(Integer approvalStep, String requiredRole) {
        // 获取当前登录用户角色
        String userRole = BaseContextHandler.get(BaseContextConstants.USER_ROLE);

        if (StringUtils.isEmpty(userRole) || !requiredRole.equals(userRole)) {
            throw new BizException(String.format("您没有进行当前步骤(%s)审批的权限",
                    SupplierFirstApprovalStepEnum.getDescByCode(approvalStep)));
        }
    }

    /**
     * 将实体转换为VO对象
     *
     * @param entity 实体对象
     * @return VO对象
     */
    private SupplierFirstBusinessVO convertToVO(SupplierFirstBusinessEntity entity) {
        if (entity == null) {
            return null;
        }

        SupplierFirstBusinessVO vo = BeanUtil.copyProperties(entity, SupplierFirstBusinessVO.class);

        // 设置枚举描述字段
        if (entity.getApprovalStep() != null) {
            vo.setApprovalStepDesc(SupplierFirstApprovalStepEnum.getDescByCode(entity.getApprovalStep()));
        }

        if (entity.getActionType() != null) {
            vo.setActionTypeDesc(ActionTypeEnum.getDescByCode(entity.getActionType()));
        }

        if (entity.getStatus() != null) {
            vo.setStatusDesc(DocumentStatusEnum.getDescByCode(entity.getStatus()));
        }

        OrganizationSupplierVo organizationSupplierById = organizationSupplierService.getOrganizationSupplierById(entity.getSupplierId());
        vo.setSupplierName(organizationSupplierById.getName());

        //TODO：根据审核人id，获取审核人姓名

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSupplierModificationApproval(OrganizationSupplier supplier) {
        // 检查供应商状态是否为审核中
        if (!DocumentStatusEnum.REVIEWING.getCode().equals(supplier.getDocumentStatus())) {
            throw new BizException("只有审核中状态的供应商才能发起修改审批");
        }

        // 检查是否已存在该供应商的、正在审核中的修改审批流程
        SupplierFirstBusinessEntity existingReviewing = this.baseMapper.findLatestBySupplierIdAndType(
                supplier.getId(), ApprovalTypeEnum.MODIFICATION.getCode());

        if (existingReviewing != null && DocumentStatusEnum.REVIEWING.getCode().equals(existingReviewing.getStatus())) {
            // 如果已经存在，直接返回，避免重复创建
            return existingReviewing.getId();
        }

        // 创建新的修改审批记录
        SupplierFirstBusinessEntity entity = new SupplierFirstBusinessEntity();

        // 设置供应商相关信息
        entity.setSupplierId(supplier.getId());

        // 设置为修改审批，直接进入审核中状态
        entity.setApplyTime(LocalDateTime.now());
        entity.setStatus(DocumentStatusEnum.REVIEWING.getCode()); // 状态：审核中
        entity.setApprovalStep(ModificationApprovalStepEnum.QUALITY_MANAGER_REVIEW.getCode()); // 第一步：质量经理审核
        entity.setActionType(ActionTypeEnum.SUBMIT.getCode()); // 操作类型视为提交
        entity.setApprovalSequence(getNextApprovalSequence(supplier.getId()));
        entity.setApprovalType(ApprovalTypeEnum.MODIFICATION.getCode()); // 审批类型：修改审批
        entity.setCurrentApproverRole(SystemRoleCodeEnum.QUALITY_MANAGER.getCode()); // 当前审批角色
        entity.setCreateUser(supplier.getUpdateUser()); // 将修改者设置为申请人

        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long approveSupplierModification(SupplierFirstBusinessApprovalParam param) {
        // 获取要审批的记录
        SupplierFirstBusinessEntity entity = this.getById(param.getId());
        if (entity == null) {
            throw new BizException("供应商修改审批记录不存在，ID: " + param.getId());
        }

        // 确认是修改审批类型
        if (!ApprovalTypeEnum.MODIFICATION.getCode().equals(entity.getApprovalType())) {
            throw new BizException("该记录不是修改审批类型");
        }

        // 校验供应商ID是否匹配
        if (!entity.getSupplierId().equals(param.getSupplierId())) {
            throw new BizException("供应商ID与审批记录不匹配");
        }

        // 校验状态
        if (!DocumentStatusEnum.REVIEWING.getCode().equals(entity.getStatus())) {
            throw new BizException("只有审核中状态的记录才能进行审批");
        }

        // 校验当前用户是否有权限进行当前步骤的审批
        validateApprovalPermission(entity.getApprovalStep(), entity.getCurrentApproverRole());

        // 创建新的审批历史记录
        SupplierFirstBusinessEntity newEntity = BeanUtil.copyProperties(entity, SupplierFirstBusinessEntity.class, "id", "createTime", "createUser", "updateTime", "updateUser");
        newEntity.setId(null); // 确保创建新记录
        newEntity.setActionType(param.getActionType());
        newEntity.setApprovalOpinion(param.getApprovalOpinion());
        newEntity.setReviewerId(BaseContextHandler.getUserId());
        newEntity.setReviewTime(LocalDateTime.now());
        newEntity.setApprovalSequence(getNextApprovalSequence(param.getSupplierId()));

        // 根据审批结果设置状态和下一步
        if (ActionTypeEnum.APPROVE.getCode().equals(param.getActionType())) {
            // 审批通过
            if (ModificationApprovalStepEnum.QUALITY_MANAGER_REVIEW.getCode().equals(entity.getApprovalStep())) {
                // 质量经理审核通过 -> 进入质量负责人审核
                newEntity.setApprovalStep(ModificationApprovalStepEnum.QUALITY_SUPERVISOR_REVIEW.getCode());
                newEntity.setCurrentApproverRole(SystemRoleCodeEnum.QUALITY_SUPERVISOR.getCode());
                newEntity.setStatus(DocumentStatusEnum.REVIEWING.getCode()); // 仍然是审核中
            } else if (ModificationApprovalStepEnum.QUALITY_SUPERVISOR_REVIEW.getCode().equals(entity.getApprovalStep())) {
                // 质量负责人审核通过 -> 审批完成
                newEntity.setApprovalStep(ModificationApprovalStepEnum.COMPLETED.getCode());
                newEntity.setCurrentApproverRole(null);
                newEntity.setStatus(DocumentStatusEnum.APPROVED.getCode()); // 状态改为已审核

                // 更新供应商状态为已审核通过 (变为"可用"状态)
                organizationSupplierService.updateApprovalStatus(param.getSupplierId(), DocumentStatusEnum.APPROVED);
            }
        } else if (ActionTypeEnum.REJECT.getCode().equals(param.getActionType())) {
            // 审批驳回
            newEntity.setStatus(DocumentStatusEnum.REJECTED.getCode());
            newEntity.setRejectReason(param.getRejectReason());

            // 更新供应商状态为已驳回
            organizationSupplierService.updateApprovalStatus(param.getSupplierId(), DocumentStatusEnum.REJECTED);
        }

        this.save(newEntity);
        return newEntity.getId();
    }
} 