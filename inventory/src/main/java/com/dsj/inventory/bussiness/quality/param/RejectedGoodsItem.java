package com.dsj.inventory.bussiness.quality.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel("被拒收商品明细参数")
public class RejectedGoodsItem {
    @ApiModelProperty(value = "来源单据商品行ID", required = true)
    @NotNull
    private Long sourceOrderGoodsId;

    @ApiModelProperty(value = "商品ID", required = true)
    @NotNull
    private Long goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "批号")
    private String batchNo;

    @ApiModelProperty(value = "拒收数量", required = true)
    @NotNull
    @DecimalMin(value = "0.01", message = "拒收数量必须大于0")
    private BigDecimal rejectionQuantity;

    @ApiModelProperty(value = "拒收原因", required = true)
    @NotBlank
    private String rejectionReason;

    @ApiModelProperty(value = "采购单价")
    private BigDecimal purchasePrice;
} 