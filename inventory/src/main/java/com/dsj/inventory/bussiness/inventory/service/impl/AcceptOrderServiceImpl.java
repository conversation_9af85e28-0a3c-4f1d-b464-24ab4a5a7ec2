package com.dsj.inventory.bussiness.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.AcceptOrder;
import com.dsj.inventory.bussiness.inventory.entity.AcceptOrderGoods;
import com.dsj.inventory.bussiness.inventory.entity.ReceiveOrder;
import com.dsj.inventory.bussiness.inventory.entity.ReceiveOrderGoods;
import com.dsj.inventory.bussiness.inventory.enumeration.AcceptOrderStateEnum;
import com.dsj.inventory.bussiness.inventory.mapper.AcceptOrderMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryAcceptOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateAcceptOrderGoodsParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateAcceptOrderParam;
import com.dsj.inventory.bussiness.inventory.service.*;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderGoodsVo;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderVo;
import com.dsj.inventory.bussiness.quality.param.RejectedGoodsItem;
import com.dsj.inventory.bussiness.quality.param.RejectionCreateParam;
import com.dsj.inventory.bussiness.quality.service.RejectionOrderService;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.generator.NumberService;
import com.dsj.inventory.common.generator.NumberType;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 进销存验收单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@Service
public class AcceptOrderServiceImpl extends ServiceImpl<AcceptOrderMapper, AcceptOrder> implements AcceptOrderService {
    private static final Logger log = LoggerFactory.getLogger(AcceptOrderServiceImpl.class);

    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @Autowired
    private AcceptOrderGoodsService acceptOrderGoodsService;
    @Autowired
    private ReceiveOrderService receiveOrderService;
    @Autowired
    private ReceiveOrderGoodsService receiveOrderGoodsService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private RejectionOrderService rejectionOrderService;
    @Autowired
    private OrganizationSupplierService organizationSupplierService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AcceptOrderDetailVo saveOrUpdateAcceptOrder(SaveOrUpdateAcceptOrderParam param) {
        if (CommonUtils.isEmpty(param)) {
            throw new ParamException("参数异常");
        }
        Organization organization = organizationService.getById(param.getOrganizationId());
        if (CommonUtils.isEmpty(organization)) {
            throw new BizException("组织不存在");
        }
        if (CommonUtils.isEmpty(organization.getInventoryDate())) {
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        if (CommonUtils.isNotEmpty(param.getId())) {
            AcceptOrder byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            //删除商品明细
            acceptOrderGoodsService.markAsDeletedByOrderId(param.getId());
        } else {
            //生成验收单号：YS+年后两位+月日四位+药房识别码六位+三位
            String day = DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_FORMAT);
            String dayStr = day.replaceAll("-", "").substring(2, 8);
            Integer count = this.getAcceptOrderCount(organization.getId(), day);
            count = count + 1;
            param.setCode("YS" + dayStr + organization.getSerialNo() + String.format("%03d", count));
        }

        if (CommonUtils.isEmpty(param.getOrderId())) {
            throw new BizException("操作失败，未选择收货单");
        }
        ReceiveOrder receiveOrder = receiveOrderService.getById(param.getOrderId());
        if (CommonUtils.isEmpty(receiveOrder)) {
            throw new BizException("操作失败，收货单不存在");
        }
        int count = this.baseMapper.countByOrderIdExcludeAcceptOrderId(param.getOrderId(), param.getId());
        if (count > 0) {
            throw new BizException("操作失败，收货单已绑定，不能重复绑定");
        }
        param.setOrderCode(receiveOrder.getCode());

        BigDecimal totalPrice = BigDecimal.valueOf(0);
        BigDecimal discountTotalPrice = BigDecimal.valueOf(0);
        if (CommonUtils.isNotEmpty(param.getGoodsList())) {
            List<SaveOrUpdateAcceptOrderGoodsParam> goodsList = param.getGoodsList();
            // 校验 1: 按 "商品ID" 进行分组去重，因为批号将由后端生成，前端不再传递。
            goodsList = goodsList.stream().collect(Collectors.groupingBy
                    (SaveOrUpdateAcceptOrderGoodsParam::getOrganizationMedicineId)).values().stream().map(d -> {
                SaveOrUpdateAcceptOrderGoodsParam goods = d.get(0);
                return goods;
            }).collect(Collectors.toList());
            if (goodsList.size() != param.getGoodsList().size()) {
                throw new BizException("操作失败，存在重复商品");
            }

            // 校验 2: 按 "商品ID" 分组，将同一商品的所有批次的 "合格数量" 和 "不合格数量" 相加，
            //         然后验证它们的总和是否与该商品的 "收货数量" 一致。
            List<SaveOrUpdateAcceptOrderGoodsParam> goodsDistinctList = BeanUtil.copyToList(param.getGoodsList(), SaveOrUpdateAcceptOrderGoodsParam.class);
            goodsDistinctList = goodsDistinctList.stream().collect(Collectors.groupingBy(g -> g.getOrganizationMedicineId())).values().stream().map(d -> {
                SaveOrUpdateAcceptOrderGoodsParam goods = d.get(0);
                goods.setOrganizationMedicineId(d.get(0).getOrganizationMedicineId());
                goods.setQualifiedAmount(d.stream().mapToDouble(SaveOrUpdateAcceptOrderGoodsParam::getQualifiedAmount).sum());
                goods.setUnqualifiedAmount(d.stream().mapToDouble(SaveOrUpdateAcceptOrderGoodsParam::getUnqualifiedAmount).sum());
                return goods;
            }).collect(Collectors.toList());
            for (SaveOrUpdateAcceptOrderGoodsParam goodsParam : goodsDistinctList) {
                BigDecimal amount = BigDecimal.valueOf(goodsParam.getQualifiedAmount()).add(BigDecimal.valueOf(goodsParam.getUnqualifiedAmount()));
                if (amount.doubleValue() != goodsParam.getReceiveAmount()) {
                    throw new BizException("操作失败，" + goodsParam.getGoodsName() + "合格数量和不合格数量与收货数量不一致");
                }
            }

            // 遍历所有商品，进行详细的金额计算与校验
            for (SaveOrUpdateAcceptOrderGoodsParam goodsParam : param.getGoodsList()) {
                OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(goodsParam.getOrganizationMedicineId());
                if (CommonUtils.isEmpty(organizationMedicine) && CommonUtils.isNotEmpty(organizationMedicine.getId())) {
                    throw new BizException("操作失败，商品Id" + goodsParam.getOrganizationMedicineId() + "不存在");
                }
                ReceiveOrderGoods receiveOrderGoods = receiveOrderGoodsService.getByOrderIdAndOrgMedicineId(param.getOrderId(), organizationMedicine.getId());
                if (CommonUtils.isEmpty(receiveOrderGoods) && CommonUtils.isNotEmpty(receiveOrderGoods.getId())) {
                    throw new BizException("操作失败，收货单不存在" + goodsParam.getOrganizationMedicineId());
                }

                // 从关联的收货单和药品信息中回填数据，确保数据一致性
                goodsParam.setGoodsName(receiveOrderGoods.getGoodsName());
                goodsParam.setGeneralName(receiveOrderGoods.getGeneralName());
                goodsParam.setGoodsCode(receiveOrderGoods.getGoodsCode());
                goodsParam.setPackUnit(receiveOrderGoods.getPackUnit());
                goodsParam.setSpecification(receiveOrderGoods.getSpecification());
                goodsParam.setDrugType(receiveOrderGoods.getDrugType());
                goodsParam.setManufacturer(receiveOrderGoods.getManufacturer());
                goodsParam.setLaunchPermitHolder(receiveOrderGoods.getLaunchPermitHolder());
                goodsParam.setProducePlace(receiveOrderGoods.getProducePlace());
                goodsParam.setPrice(organizationMedicine.getOfflinePrice());
                goodsParam.setMemberPrice(organizationMedicine.getMemberPrice());
                goodsParam.setRepertory(organizationMedicine.getMedicineRepertory());
                goodsParam.setPurchasePrice(receiveOrderGoods.getPurchasePrice());
                goodsParam.setDiscountPrice(receiveOrderGoods.getDiscountPrice());
                goodsParam.setPurchaseNumber(receiveOrderGoods.getPurchaseNumber());
                goodsParam.setReceiveAmount(receiveOrderGoods.getReceiveAmount());

                // 计算规则 1: 计算用于金额计算的基准数量。
                // 基准数量 = 合格数量 - 赠送数量
                BigDecimal amount = BigDecimal.valueOf(goodsParam.getQualifiedAmount())
                        .subtract(BigDecimal.valueOf(goodsParam.getGiveAmount()));

                // 计算规则 2: 计算单个商品的原价小计金额，并进行校验。
                // 单个商品原价小计 = 基准数量 * 采购单价 (结果四舍五入到整数)
                BigDecimal goodsTotalPurchasePrice = BigDecimal.valueOf(goodsParam.getPurchasePrice()).multiply(amount);
                goodsTotalPurchasePrice = goodsTotalPurchasePrice.setScale(0, RoundingMode.HALF_UP);
                if (!Integer.valueOf(goodsTotalPurchasePrice.intValue()).equals(goodsParam.getTotalPurchasePrice())) {
                    throw new BizException("操作失败，商品" + goodsParam.getGoodsName() + "小计金额计算不一致");
                }
                // 校验通过后，使用后端计算出的精确值覆盖前端传入的值
                goodsParam.setTotalPurchasePrice(goodsTotalPurchasePrice.intValue());

                // 计算规则 3: 计算单个商品的折后小计金额，并进行校验。
                // 单个商品折后小计 = 基准数量 * 折扣单价 (结果四舍五入到整数)
                BigDecimal goodsTotalDiscountPrice = BigDecimal.valueOf(goodsParam.getDiscountPrice()).multiply(amount);
                goodsTotalDiscountPrice = goodsTotalDiscountPrice.setScale(0, RoundingMode.HALF_UP);
                if (!Integer.valueOf(goodsTotalDiscountPrice.intValue()).equals(goodsParam.getTotalDiscountPrice())) {
                    throw new BizException("操作失败，商品" + goodsParam.getGoodsName() + "折后金额计算不一致");
                }
                // 校验通过后，使用后端计算出的精确值覆盖前端传入的值
                goodsParam.setTotalDiscountPrice(goodsTotalDiscountPrice.intValue());

                // 累加规则 1: 将当前商品的 "原价小计金额" 累加到订单的 "总金额" 中。
                totalPrice = totalPrice.add(goodsTotalPurchasePrice);
                // 累加规则 2: 将当前商品的 "折后小计金额" 累加到订单的 "折后总金额" 中。
                discountTotalPrice = discountTotalPrice.add(goodsTotalDiscountPrice);
            }
            param.setPurchaseContent(receiveOrder.getPurchaseContent());
            param.setGoodsTypes(receiveOrder.getGoodsTypes());

            // 最终校验 1: 校验所有商品累加后的 "总金额" 是否与前端传入的订单总金额一致。
            if (totalPrice.intValue() != param.getTotalPrice()) {
                throw new BizException("操作失败，总金额计算不一致");
            }
            // 最终校验通过后，使用后端计算出的精确值覆盖前端传入的值。
            param.setTotalPrice(totalPrice.intValue());

            // 最终校验 2: 校验所有商品累加后的 "折后总金额" 是否与前端传入的订单折后总金额一致。
            if (!Integer.valueOf(discountTotalPrice.intValue()).equals(param.getDiscountTotalPrice())) {
                throw new BizException("操作失败，折后金额计算不一致");
            }
            // 最终校验通过后，使用后端计算出的精确值覆盖前端传入的值。
            param.setDiscountTotalPrice(discountTotalPrice.intValue());
        }
        if (CommonUtils.isEmpty(param.getAcceptId())) {
            param.setAcceptId(BaseContextHandler.getUserId());
        }
        AcceptOrder acceptOrder = BeanUtil.toBean(param, AcceptOrder.class);
        this.saveOrUpdate(acceptOrder);
        //绑定收货单变更为已完成
        receiveOrderService.completeReceiveOrder(param.getOrderId());
        List<AcceptOrderGoods> savedGoodsList = new ArrayList<>();
        if (CommonUtils.isNotEmpty(param.getGoodsList())) {
            param.getGoodsList().stream().map(g -> g.setOrderId(acceptOrder.getId())).collect(Collectors.toList());
            List<AcceptOrderGoods> goodsList = BeanUtil.copyToList(param.getGoodsList(), AcceptOrderGoods.class);
            acceptOrderGoodsService.saveBatch(goodsList);
            savedGoodsList.addAll(goodsList);
        }

        // --- 开始处理拒收 ---
        // 1. 创建一个临时的信息组合对象列表
        List<TempRejectedInfo> tempRejectedInfos = new ArrayList<>();
        List<SaveOrUpdateAcceptOrderGoodsParam> originalParams = param.getGoodsList();

        for (int i = 0; i < savedGoodsList.size(); i++) {
            SaveOrUpdateAcceptOrderGoodsParam p = originalParams.get(i);
            // 2. 筛选出不合格(即被拒收)的商品
            if (p.getUnqualifiedAmount() != null && p.getUnqualifiedAmount() > 0) {
                // 3. 将实体和参数配对放入临时列表
                tempRejectedInfos.add(new TempRejectedInfo(savedGoodsList.get(i), p));
            }
        }

        // 4. 如果存在被拒收的商品，则创建拒收单
        if (!tempRejectedInfos.isEmpty()) {
            createRejectionOrderFromSource(acceptOrder, receiveOrder, tempRejectedInfos);
        }
        // --- 拒收处理结束 ---

        return this.getAcceptOrderById(acceptOrder.getId());
    }

    private void createRejectionOrderFromSource(AcceptOrder acceptOrder, ReceiveOrder receiveOrder, List<TempRejectedInfo> rejectedGoods) {
        RejectionCreateParam rejectionParam = new RejectionCreateParam();
        rejectionParam.setSourceId(acceptOrder.getId());
        rejectionParam.setSourceCode(acceptOrder.getCode());
        rejectionParam.setSourceType(com.dsj.inventory.bussiness.quality.enumeration.RejectionSourceTypeEnum.ACCEPTANCE);
        rejectionParam.setSupplierId(receiveOrder.getOrganizationSupplierId());
        rejectionParam.setSupplierName(organizationSupplierService.getOrganizationSupplierNameById(receiveOrder.getOrganizationSupplierId()));
        rejectionParam.setRemark(acceptOrder.getRemark());

        List<RejectedGoodsItem> rejectedItems = rejectedGoods.stream().map(info -> {
            AcceptOrderGoods entity = info.getEntity();
            SaveOrUpdateAcceptOrderGoodsParam p = info.getParam();

            RejectedGoodsItem rejectedItem = new RejectedGoodsItem();
            rejectedItem.setSourceOrderGoodsId(entity.getId());
            rejectedItem.setGoodsId(entity.getOrganizationMedicineId());
            rejectedItem.setGoodsName(entity.getGoodsName());
            if (entity.getPurchasePrice() != null) {
                rejectedItem.setPurchasePrice(new BigDecimal(entity.getPurchasePrice()));
            }
            if (entity.getUnqualifiedAmount() != null) {
                rejectedItem.setRejectionQuantity(BigDecimal.valueOf(entity.getUnqualifiedAmount()));
            }
            rejectedItem.setRejectionReason(p.getUnqualifiedReason()); // 从参数获取拒收原因
            return rejectedItem;
        }).collect(Collectors.toList());

        rejectionParam.setRejectedItems(rejectedItems);

        log.info("验收单[{}]创建关联拒收单...", acceptOrder.getCode());
        rejectionOrderService.createFromSource(rejectionParam);
    }

    @Override
    public void deleteAcceptOrder(Long id) {
        AcceptOrder byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        if (!byId.getDealState().equals(AcceptOrderStateEnum.TEMPORARY.getCode())) {
            throw new BizException("操作失败，验收单当前状态不允许删除");
        }
        //删除商品明细
        acceptOrderGoodsService.markAsDeletedByOrderId(id);
        this.removeById(id);
    }

    @Override
    public Page<AcceptOrderVo> queryAcceptOrderPage(QueryAcceptOrderParam param) {
        if (CommonUtils.isEmpty(param)) {
            throw new ParamException("参数异常");
        }
        if (!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))) {
            throw new BizException("组织无权限");
        } else if (OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))) {
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        Page page = new Page<>(param.getPage(), param.getSize());
        List<AcceptOrderVo> list = this.baseMapper.queryAcceptOrderPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public AcceptOrderDetailVo getAcceptOrderById(Long id) {
        AcceptOrderDetailVo vo = this.baseMapper.getAcceptOrderById(id);
        if (CommonUtils.isNotEmpty(vo)) {
            List<AcceptOrderGoodsVo> goodsList = acceptOrderGoodsService.getAcceptOrderGoodsList(vo.getId());
            if (CommonUtils.isNotEmpty(goodsList)) {
                vo.setGoodsList(goodsList);
            }
        }
        return vo;
    }

    @Override
    public void submitAcceptOrder(Long id) {
        AcceptOrder byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        Organization organization = organizationService.getById(byId.getOrganizationId());
        if (CommonUtils.isEmpty(organization)) {
            throw new BizException("组织不存在");
        }
        if (CommonUtils.isEmpty(organization.getInventoryDate())) {
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        this.baseMapper.updateStateToAccepted(id);
    }

    @Override
    public List<AcceptOrderExportVo> queryAcceptOrderList(QueryAcceptOrderParam param) {
        if (!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))) {
            throw new BizException("组织无权限");
        } else if (OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))) {
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        return this.baseMapper.queryAcceptOrderList(param);
    }

    @Override
    public void deleteAcceptOrderByOrganizationId(Long organizationId) {
        this.baseMapper.deleteAcceptOrderByOrganizationId(organizationId);
    }

    @Override
    public Integer getAcceptOrderCount(Long organizationId, String day) {
        return this.baseMapper.getAcceptOrderCount(organizationId, day);
    }

    @Override
    public void completeAcceptOrder(Long orderId) {
        this.baseMapper.updateAcceptedToCompleted(orderId);
    }

    /**
     * 用于临时存储已保存的验收明细实体及其对应的原始参数
     */
    @Getter
    @AllArgsConstructor
    private static class TempRejectedInfo {
        private final AcceptOrderGoods entity;
        private final SaveOrUpdateAcceptOrderGoodsParam param;
    }
}
