package com.dsj.inventory.common.enumeration;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.dsj.inventory.common.enumeration.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 验证码类型
 *
 * <AUTHOR>
 * @date 2019/08/06
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "TemplateCodeType", description = "短信模板类型")
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum TemplateCodeType implements BaseEnum {
    /**
     * 验证码 注册用户
     */
    CODE_REGISTER_USER("注册用户的验证码", "1"),
    CODE_COLLECTING_ACCOUNT("开通财务账户的验证码", "2"),
    CODE_TENANT_APPLY("商家入驻的验证码", "3"),
    CODE_TENANT_APPLY_SUCCESS("商家入驻成功", "4"),
    CODE_TENANT_APPLY_FAIL("商家入驻失败", "5"),
    CODE_LOGIN_USER("用户登录的验证码", "6")
    ;


    @ApiModelProperty(value = "描述")
    private String desc;
    @ApiModelProperty(value = "编码")
    private String code;


    public static com.dsj.inventory.common.enumeration.TemplateCodeType match(String val, com.dsj.inventory.common.enumeration.TemplateCodeType def) {
        for (com.dsj.inventory.common.enumeration.TemplateCodeType enm : com.dsj.inventory.common.enumeration.TemplateCodeType.values()) {
            if (enm.name().equalsIgnoreCase(val)) {
                return enm;
            }
        }
        return def;
    }

    public static com.dsj.inventory.common.enumeration.TemplateCodeType get(String val) {
        return match(val, null);
    }

    public boolean eq(String val) {
        return this.name().equalsIgnoreCase(val);
    }

    public boolean eq(com.dsj.inventory.common.enumeration.TemplateCodeType val) {
        if (val == null) {
            return false;
        }
        return eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码")
    public String getCode() {
        return this.code;
    }
}
