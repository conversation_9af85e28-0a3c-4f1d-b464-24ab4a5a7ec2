package com.dsj.inventory.common.generator;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 编号生成器抽象基类
 * 提供编号生成的通用逻辑实现
 */
public abstract class AbstractNumberGenerator implements NumberGenerator {

    protected static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    
    protected final NumberType type;
    
    protected AbstractNumberGenerator(NumberType type) {
        this.type = type;
    }

    @Override
    public NumberType getType() {
        return type;
    }

    /**
     * 获取日期部分
     * @return 日期字符串，格式为yyyyMMdd
     */
    protected String getDatePart() {
        return LocalDate.now().format(DATE_FORMATTER);
    }
    
    /**
     * 获取序列号部分
     * @return 序列号字符串
     */
    protected abstract String getSequencePart();
    
    /**
     * 组装编号
     * @param datePart 日期部分
     * @param sequencePart 序列号部分
     * @return 完整编号
     */
    protected String formatNumber(String datePart, String sequencePart) {
        return type.getPrefix() + datePart + sequencePart;
    }

    @Override
    public String generate() {
        String datePart = getDatePart();
        String sequencePart = getSequencePart();
        return formatNumber(datePart, sequencePart);
    }
} 