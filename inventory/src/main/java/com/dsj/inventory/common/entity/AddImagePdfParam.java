package com.dsj.inventory.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * pdf添加图片参数
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class AddImagePdfParam {
    /**
     * 图片路径
     */
    private String imagePath;

    /**
     * 搜索词
     */
    private String fieldName;

    /**
     * 横轴大小
     */
    private int x;

    /**
     * 纵轴大小
     */
    private int y;

    /**
     * 图片宽度
     */
    private int width;

    /**
     * 图片高度
     */
    private int height;

}
