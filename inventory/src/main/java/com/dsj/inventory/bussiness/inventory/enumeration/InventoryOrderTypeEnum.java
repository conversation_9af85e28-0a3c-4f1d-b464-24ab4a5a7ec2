package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 库存变动单据类型 1入库、2出库、3损益、4销售
 * <AUTHOR>
 */

@Getter
public enum InventoryOrderTypeEnum {

    INITIALIZE(0, "初始化"),
    INCOMING(1, "入库"),
    DELIVERY(2, "出库"),
    GAINS_LOSSES(3, "损益"),
    SALES(4, "销售"),
    MOVE(5, "移动"),
    ;

    private Integer code;

    private String desc;

    InventoryOrderTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (InventoryOrderTypeEnum a : InventoryOrderTypeEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
