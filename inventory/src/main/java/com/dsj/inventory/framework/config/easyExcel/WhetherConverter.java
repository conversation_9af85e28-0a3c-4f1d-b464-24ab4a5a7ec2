package com.dsj.inventory.framework.config.easyExcel;


/**
 * 是否转换器
 */

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.dsj.inventory.common.enumeration.TrueEnum;

public class WhetherConverter implements Converter<Integer> {

    @Override
    public Class<Integer> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
                                           GlobalConfiguration globalConfiguration) {
        if (cellData.getStringValue().equals(TrueEnum.TRUE.getDesc())){
            return TrueEnum.TRUE.getCode();
        }else {
            return TrueEnum.FALSE.getCode();
        }
    }

    @Override
    public CellData<Integer> convertToExcelData(Integer value, ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        if (value.equals(TrueEnum.TRUE.getCode())){
            return new CellData<>(TrueEnum.TRUE.getDesc());
        }else {
            return new CellData<>(TrueEnum.FALSE.getDesc());
        }
    }

}
