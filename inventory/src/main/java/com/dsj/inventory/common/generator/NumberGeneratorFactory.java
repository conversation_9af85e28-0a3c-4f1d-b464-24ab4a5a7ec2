package com.dsj.inventory.common.generator;

import com.dsj.inventory.framework.properties.DatabaseProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.EnumMap;
import java.util.Map;

/**
 * 编号生成器工厂
 * 负责创建和管理所有编号生成器实例
 */
@Component
public class NumberGeneratorFactory {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    // 环境配置（从配置文件中获取工作机器ID和数据中心ID）
    @Value("${pocky.server.workerId:1}")
    private long workerId;

    @Value("${pocky.server.dataCenterId:1}")
    private long dataCenterId;

    // 默认使用Redis方案
    @Value("${pocky.server.numberGeneratorUseSnowflake:false}")
    private boolean numberGeneratorUseSnowflake;

    // 编号生成器容器，按照编号类型组织
    private final Map<NumberType, NumberGenerator> generatorMap = new EnumMap<>(NumberType.class);

    /**
     * 初始化所有预定义的编号生成器
     */
    @PostConstruct
    public void init() {
        // 为每种编号类型创建生成器
        for (NumberType type : NumberType.values()) {
            if (numberGeneratorUseSnowflake) {
                generatorMap.put(type, createSnowflakeGenerator(type));
            } else {
                generatorMap.put(type, createRedisGenerator(type));
            }
        }
    }

    /**
     * 获取指定类型的编号生成器
     *
     * @param type 编号类型
     * @return 对应的编号生成器
     */
    public NumberGenerator getGenerator(NumberType type) {
        return generatorMap.get(type);
    }

    /**
     * 创建Redis编号生成器
     *
     * @param type 编号类型
     * @return Redis编号生成器
     */
    private NumberGenerator createRedisGenerator(NumberType type) {
        // 对不同类型的编号可以设置不同的序列号长度
        int sequenceLength;
        switch (type) {
            case SUPPLIER_FIRST_DOCUMENT:
                sequenceLength = 6; // 例如：SF202401010001
                break;
            case SUPPLIER_CODE:
                sequenceLength = 5; // 例如：SC2024010100001
                break;
            case PURCHASE_ORDER:
                sequenceLength = 6; // 例如：PO202401010001
                break;
            case PURCHASE_PLAN:
                sequenceLength = 6; // 例如：PP202401010001
                break;
            case RETURN_ORDER:
                sequenceLength = 6; // 例如：RO202401010001
                break;
            default:
                sequenceLength = 6;
        }
        return new RedisNumberGenerator(type, redisTemplate, sequenceLength);
    }

    /**
     * 创建雪花算法编号生成器
     *
     * @param type 编号类型
     * @return 雪花算法编号生成器
     */
    private NumberGenerator createSnowflakeGenerator(NumberType type) {
        return new SnowflakeNumberGenerator(type, workerId, dataCenterId);
    }

    /**
     * 注册自定义编号生成器
     *
     * @param type      编号类型
     * @param generator 编号生成器实例
     */
    public void registerGenerator(NumberType type, NumberGenerator generator) {
        generatorMap.put(type, generator);
    }
} 