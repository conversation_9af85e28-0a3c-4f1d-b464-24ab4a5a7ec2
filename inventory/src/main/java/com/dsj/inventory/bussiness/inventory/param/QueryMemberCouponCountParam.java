package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询会员优惠券统计参数
 *
 * <AUTHOR>
 * @date 2022/3/28 17:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QueryMemberCouponCountParam",description = "查询会员优惠券统计参数")
public class QueryMemberCouponCountParam {

    @ApiModelProperty(hidden = true,value = "系统用户Id")
    private Long userId;

    @ApiModelProperty(hidden = true,value = "会员Id")
    private Long memberId;

    @ApiModelProperty(hidden = true,value = "模块分类，1 云药快 ；2 外部；3 在线医院；4 商城；")
    private Integer moduleType;

}
