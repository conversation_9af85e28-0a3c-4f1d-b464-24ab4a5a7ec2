<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.SalesActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.SalesActivity">
    <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="activity_type" property="activityType" />
        <result column="activity_state" property="activityState" />
        <result column="name" property="name" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="rule" property="rule" />
        <result column="apply_to" property="applyTo" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        organization_id, activity_type, activity_state, name, start_date, end_date, rule, apply_to, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="querySalesActivityPage" resultType="com.dsj.inventory.bussiness.inventory.vo.SalesActivityVo">
        SELECT distinct jsa.id
        ,jsa.organization_id organizationId
        ,jsa.module_type moduleType
        ,jsa.activity_category activityCategory
        ,jsa.activity_type activityType
        ,jsa.activity_state activityState
        ,jsa.assert_state assertState
        ,jsa.name
        ,jsa.start_date startDate
        ,jsa.end_date endDate
        ,jsa.rule
        ,jsa.apply_to applyTo
        ,su.name createUserName
        ,jsa.create_time createTime
        FROM jxc_sales_activity jsa
        LEFT JOIN sys_user su ON su.id = jsa.create_user AND su.is_delete = 0
        <if test="param.goodsIds != null and param.goodsIds != ''">
            INNER JOIN jxc_sales_activity_goods jsag ON jsag.activity_id = jsa.id AND jsag.is_delete = 0
            AND jsag.organization_medicine_id in
            <foreach item="item" index="index" collection="param.goodsIds.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        WHERE
        jsa.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jsa.organization_id = #{param.organizationId}
        </if>
        <if test="param.moduleType != null ">
            AND jsa.module_type = #{param.moduleType}
        </if>
        <if test="param.activityCategory != null ">
            AND jsa.activity_category = #{param.activityCategory}
        </if>
        <if test="param.activityType != null ">
            AND jsa.activity_type = #{param.activityType}
        </if>
        <if test="param.activityState != null ">
            AND jsa.activity_state = #{param.activityState}
        </if>
        <if test="param.assertState != null ">
            AND jsa.assert_state = #{param.assertState}
        </if>
        <if test="param.applyTo != null ">
            AND jsa.apply_to <![CDATA[<=]]> #{param.applyTo}
        </if>
        <if test="param.nameLike != null and param.nameLike != '' ">
            AND jsa.name like CONCAT('%',#{param.nameLike},'%')
        </if>
        <if test="param.createUserNameLike != null and param.createUserNameLike != '' ">
            AND su.name like CONCAT('%',#{param.createUserNameLike},'%')
        </if>
        <if test="param.startDate != null and param.startDate != '' ">
            AND DATE_FORMAT(jsa.start_date,"%Y-%m-%d") = #{param.startDate}
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jsa.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        GROUP BY jsa.id
        ORDER BY jsa.create_time desc
    </select>

    <select id="getSalesActivityById" resultType="com.dsj.inventory.bussiness.inventory.vo.SalesActivityVo">
        SELECT jsa.id
             ,jsa.organization_id organizationId
             ,jsa.module_type moduleType
             ,jsa.activity_category activityCategory
             ,jsa.activity_type activityType
             ,jsa.activity_state activityState
             ,jsa.assert_state assertState
             ,jsa.name
             ,jsa.start_date startDate
             ,jsa.end_date endDate
             ,jsa.rule
             ,jsa.apply_to applyTo
             ,su.name createUserName
             ,jsa.create_time createTime
        FROM jxc_sales_activity jsa
        LEFT JOIN sys_user su ON su.id = jsa.create_user AND su.is_delete = 0
        WHERE
            jsa.is_delete = 0
        AND jsa.id = #{id}
    </select>


</mapper>