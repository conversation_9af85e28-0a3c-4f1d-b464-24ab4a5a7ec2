package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 退货单商品明细
 * <AUTHOR>
 * @date 2023/04/13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DeliveryOrderGoodsVo", description="退货单商品明细实体")
@ToString(callSuper = true)
public class DeliveryOrderGoodsVo {

    private Long id;

    @ApiModelProperty(value = "退货单Id")
    private Long orderId;

    @ApiModelProperty(value = "库存Id")
    private Long inventoryId;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "通用名")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    private String generalName;

    @ApiModelProperty(value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(value = "包装单位")
    private String packUnit;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "剂型")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(value = "上市许可持有人")
    private String launchPermitHolder;

    @ApiModelProperty(value = "产地")
    private String producePlace;

    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(value = "生产日期")
    private LocalDateTime produceDate;

    @ApiModelProperty(value = "有效期至")
    private LocalDateTime userDate;

    @ApiModelProperty(value = "批号")
    private String batchNumber;

    @ApiModelProperty(value = "货位Id")
    private Long shelfId;

    @ApiModelProperty(value = "货位Id")
    private String shelfName;

    @ApiModelProperty(value = "批次（入库单号）")
    private String batch;

    @ApiModelProperty(value = "入库时的商品明细Id")
    private Long incomingGoodsId;

    @ApiModelProperty(value = "原单数量")
    private Double incomingAmount;

    @ApiModelProperty(value = "库存数量")
    private Double inventoryAmount;

    @ApiModelProperty(value = "已退货数量")
    private Double returnedAmount;

    @ApiModelProperty(value = "退货数量")
    private Double returnAmount;

    @ApiModelProperty(value = "赠送数量")
    private Double giveAmount;

    @ApiModelProperty(value = "出库含税价（退货单价）")
    private Integer returnPrice;

    @ApiModelProperty(value = "含税金额（出库含税价*退货数量）")
    private Integer returnTotalPrice;

    @ApiModelProperty(value = "出库成本均价（当时的进货价）")
    private Integer costPrice;

    @ApiModelProperty(value = "退货成本金额（出库成本均价*退货数量）")
    private Integer returnCostTotalPrice;

    @ApiModelProperty(value = "进退货差价（退货成本金额-含税金额）")
    private Integer disparityPrice;

    @ApiModelProperty(value = "退货原因")
    private String reason;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
