package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesActivityTaskParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateSalesActivityTaskParam;
import com.dsj.inventory.bussiness.inventory.service.SalesActivityTaskService;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 销售活动任务 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@RestController
@Api(value = "SalesActivityTaskController", tags = "销售活动任务api")
public class SalesActivityTaskController {

    @Autowired
    private SalesActivityTaskService salesActivityTaskService;

    /*新增销售活动任务*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"supplier-wholesale-promotion-regulars"})
    @PostMapping("/sales-activity-task")
    @ApiOperation(value = "新增销售活动任务", notes = "新增销售活动任务")
    public ResponseEntity<SalesActivityTaskVo> saveSalesActivityTask(@RequestBody SaveOrUpdateSalesActivityTaskParam param){
        SalesActivityTaskVo SalesActivitytaskVo = salesActivityTaskService.saveOrUpdateSalesActivityTask(param);
        return Res.success(SalesActivitytaskVo);
    }

    /*编辑销售活动任务*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"supplier-wholesale-promotion-regulars"})
    @PutMapping("/sales-activity-task/{id}")
    @ApiOperation(value = "编辑销售活动任务", notes = "编辑销售活动任务")
    public ResponseEntity<SalesActivityTaskVo> updateSalesActivityTask(@PathVariable Long id, @RequestBody SaveOrUpdateSalesActivityTaskParam param){
        param.setId(id);
        SalesActivityTaskVo SalesActivitytaskVo = salesActivityTaskService.saveOrUpdateSalesActivityTask(param);
        return Res.success(SalesActivitytaskVo);
    }

    /*删除销售活动任务*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"supplier-wholesale-promotion-regulars"})
    @DeleteMapping("/sales-activity-task/{id}")
    @ApiOperation(value = "删除销售活动任务", notes = "删除销售活动任务")
    public ResponseEntity<Void> deleteSalesActivityTask(@PathVariable Long id){
        SaveOrUpdateSalesActivityTaskParam param = SaveOrUpdateSalesActivityTaskParam.builder().isDelete(TrueEnum.TRUE.getCode()).id(id).build();
        salesActivityTaskService.saveOrUpdateSalesActivityTask(param);
        return Res.success();
    }

    /*获取销售活动任务列表*/
    @GetMapping("/sales-activity-task")
    @ApiOperation(value = "获取销售活动任务列表", notes = "获取销售活动任务列表")
    public ResponseEntity<List<SalesActivityTaskVo>> querySalesActivityTaskList(QuerySalesActivityTaskParam param){
        Page<SalesActivityTaskVo> page = salesActivityTaskService.querySalesActivityTaskPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取销售活动任务详情*/
    @GetMapping("/sales-activity-task/{id}")
    @ApiOperation(value = "获取销售活动任务详情", notes = "获取销售活动任务详情")
    public ResponseEntity<SalesActivityTaskVo> querySalesActivityTask(@PathVariable Long id){
        return Res.success(salesActivityTaskService.getSalesActivityTaskById(id));
    }

    /*暂停/执行销售活动任务*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"supplier-wholesale-promotion-regulars"})
    @PostMapping("/sales-activity-task/{id}/action-pause")
    @ApiOperation(value = "暂停/执行销售活动任务", notes = "暂停/执行销售活动任务")
    public ResponseEntity<Void> pauseSalesActivityTask(@PathVariable Long id){
        salesActivityTaskService.pauseSalesActivityTask(id);
        return Res.success();
    }

}
