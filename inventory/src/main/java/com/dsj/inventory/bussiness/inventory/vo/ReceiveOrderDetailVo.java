package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 收货单详情
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ReceiveOrderDetailVo", description="收货单详情实体")
@ToString(callSuper = true)
public class ReceiveOrderDetailVo {

    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "采购订单Id")
    private Long orderId;

    @ApiModelProperty(value = "采购单号")
    private String orderCode;

    @ApiModelProperty(value = "收货单号")
    private String code;

    @ApiModelProperty(value = "开票日期")
    private LocalDateTime billDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "处理状态 0暂存 1已复核 2已完成")
    private Integer dealState;

    @ApiModelProperty(value = "采购员Id")
    private Long purchaserId;

    @ApiModelProperty(value = "采购员名称")
    private String purchaserName;

    @ApiModelProperty(value = "复核人Id")
    private Long receiverId;

    @ApiModelProperty(value = "复核人名称")
    private String receiverName;

    @ApiModelProperty(value = "私域供应商Id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "供应商名称")
    private String organizationSupplierName;

    @ApiModelProperty(value = "供应商编号")
    private String organizationSupplierCode;

    @ApiModelProperty(value = "总金额")
    private Integer totalPrice;

    @ApiModelProperty(value = "折后金额")
    private Integer discountTotalPrice;

    @ApiModelProperty(value = "零售价总计（私域商品价格*收货数量）")
    private Integer totalRetailPrice;

    @ApiModelProperty(value = "承运单位")
    private String carrier;

    @ApiModelProperty(value = "起运地址")
    private String startAddress;

    @ApiModelProperty(value = "起运时间")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "承运方式")
    private String transportWay;

    @ApiModelProperty(value = "运输工具")
    private String transportTool;

    @ApiModelProperty(value = "发票日期")
    private LocalDateTime invoiceDate;

    @ApiModelProperty(value = "发票代码")
    private String invoiceCode;

    @ApiModelProperty(value = "发票金额")
    private Integer invoicePrice;

    @ApiModelProperty(value = "发票号")
    private String invoiceNumber;

    @ApiModelProperty(value = "收货运单号")
    private String receiveOrderNumber;

    @ApiModelProperty(value = "随货同行单日期")
    private LocalDateTime followGoodsOrderDate;

    @ApiModelProperty(value = "到货日期")
    private LocalDateTime arrivalDate;

    @ApiModelProperty(value = "到货温度")
    private String arrivalTemp;

    @ApiModelProperty(value = "运输温湿度记录")
    private String transportHumidityRecord;

    @ApiModelProperty(value = "是否冷藏 0否1是")
    private Integer refrigerateState;

    @ApiModelProperty(value = "批发企业业务员编号")
    private String wholesalerStaffNumber;

    @ApiModelProperty(value = "发票照片")
    private String invoicePath;

    @ApiModelProperty(value = "随货同行单照片")
    private String followGoodsOrderPath;

    @ApiModelProperty(value = "质检报告照片")
    private String checkReportPath;

    @ApiModelProperty(value = "商品种类")
    private String goodsTypes;

    @ApiModelProperty(value = "采购内容")
    private String purchaseContent;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "商品明细")
    private List<ReceiveOrderGoodsVo> goodsList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
