<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.MemberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.Member">
    <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="user_id" property="userId" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="phone" property="phone" />
        <result column="gender" property="gender" />
        <result column="birth" property="birth" />
        <result column="card" property="card" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="county" property="county" />
        <result column="address" property="address" />
        <result column="remark" property="remark" />
        <result column="balance" property="balance" />
        <result column="usable_integral" property="usableIntegral" />
        <result column="accrue_integral" property="accrueIntegral" />
        <result column="consume_pwd" property="consumePwd" />
        <result column="safety_state" property="safetyState" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        organization_id, user_id, code, name, phone, gender, birth, card, province, city, county, address, remark, balance, usable_integral, accrue_integral, consume_pwd, safety_state, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="queryMemberPage" resultType="com.dsj.inventory.bussiness.inventory.vo.MemberVo">
        SELECT distinct jm.id
        ,jm.organization_id organizationId
        ,do.name organizationName
        ,jm.user_id userId
        ,jm.code
        ,jm.name
        ,jm.phone
        ,jm.gender
        ,jm.birth
        ,jm.card
        ,jm.province
        ,jm.city
        ,jm.county
        ,jm.address
        ,jm.remark
        ,jm.balance
        ,jm.usable_integral usableIntegral
        ,jm.accrue_integral accrueIntegral
        ,jm.consume_pwd consumePwd
        ,jm.safety_state safetyState
        ,jm.transact_user transactUser
        ,su.name transactUserName
        ,jm.create_time createTime
        FROM jxc_member jm
        LEFT JOIN sys_user su ON su.id = jm.transact_user AND su.is_delete = 0
        LEFT JOIN di_organization do ON do.id = jm.organization_id AND do.is_delete = 0
        WHERE
        jm.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jm.organization_id = #{param.organizationId}
        </if>
        <if test="param.userId != null and param.userId != '' ">
            AND jm.user_id = #{param.userId}
        </if>
        <if test="param.phone != null and param.phone != '' ">
            AND jm.phone = #{param.phone}
        </if>
        <if test="param.nameLike != null and param.nameLike != '' ">
            AND jm.name like CONCAT('%',#{param.nameLike},'%')
        </if>
        <if test="param.phoneLike != null and param.phoneLike != '' ">
            AND jm.phone like CONCAT('%',#{param.phoneLike},'%')
        </if>
        ORDER BY jm.create_time desc
    </select>

    <select id="getMemberById" resultType="com.dsj.inventory.bussiness.inventory.vo.MemberVo">
        SELECT jm.id
             ,jm.organization_id organizationId
             ,do.name organizationName
             ,jm.user_id userId
             ,jm.code
             ,jm.name
             ,jm.phone
             ,jm.gender
             ,jm.birth
             ,jm.card
             ,jm.province
             ,jm.city
             ,jm.county
             ,jm.address
             ,jm.remark
             ,jm.balance
             ,jm.usable_integral usableIntegral
             ,jm.accrue_integral accrueIntegral
             ,jm.consume_pwd consumePwd
             ,jm.safety_state safetyState
             ,jm.transact_user transactUser
             ,su.name transactUserName
             ,jm.create_time createTime
        FROM jxc_member jm
        LEFT JOIN sys_user su ON su.id = jm.transact_user AND su.is_delete = 0
        LEFT JOIN di_organization do ON do.id = jm.organization_id AND do.is_delete = 0
        WHERE
            jm.is_delete = 0
        AND jm.id = #{id}
    </select>

    <select id="queryMemberList" resultType="com.dsj.inventory.bussiness.inventory.vo.MemberExportVo">
        SELECT distinct jm.id
        ,jm.code
        ,jm.name
        ,jm.phone
        ,su.name transactUserName
        ,jm.create_time createTime
        FROM jxc_member jm
        LEFT JOIN sys_user su ON su.id = jm.transact_user AND su.is_delete = 0
        WHERE
        jm.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jm.organization_id = #{param.organizationId}
        </if>
        <if test="param.userId != null and param.userId != '' ">
            AND jm.user_id = #{param.userId}
        </if>
        <if test="param.phone != null and param.phone != '' ">
            AND jm.phone = #{param.phone}
        </if>
        <if test="param.nameLike != null and param.nameLike != '' ">
            AND jm.name like CONCAT('%',#{param.nameLike},'%')
        </if>
        <if test="param.phoneLike != null and param.phoneLike != '' ">
            AND jm.phone like CONCAT('%',#{param.phoneLike},'%')
        </if>
        ORDER BY jm.create_time desc
    </select>


</mapper>
