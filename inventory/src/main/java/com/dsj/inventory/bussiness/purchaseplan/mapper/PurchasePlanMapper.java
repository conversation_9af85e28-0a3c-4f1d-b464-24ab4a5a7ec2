package com.dsj.inventory.bussiness.purchaseplan.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.purchaseplan.param.QueryPurchasePlanParam;
import com.dsj.inventory.bussiness.purchaseplan.vo.PurchasePlanDetailVo;
import com.dsj.inventory.bussiness.purchaseplan.vo.PurchasePlanExportVo;
import com.dsj.inventory.bussiness.purchaseplan.vo.PurchasePlanVo;
import com.dsj.inventory.bussiness.purchaseplan.entity.PurchasePlan;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存采购计划单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
public interface PurchasePlanMapper extends BaseMapper<PurchasePlan> {

    /**
     * 获取采购计划分页列表
     *
     * @param page
     * @param param
     * @return
     */
    List<PurchasePlanVo> queryPurchasePlanPage(@Param("page") Page page, @Param("param") QueryPurchasePlanParam param);

    /**
     * 获取采购计划详情
     *
     * @param id
     * @return
     */
    PurchasePlanDetailVo getPurchasePlanById(Long id);

    /**
     * 导出采购计划列表
     *
     * @param param
     * @return
     */
    List<PurchasePlanExportVo> queryPurchasePlanList(@Param("param") QueryPurchasePlanParam param);

    /**
     * 删除组织的采购计划
     *
     * @param organizationId
     */
    void deletePurchasePlanByOrganizationId(Long organizationId);

    /**
     * 统计组织当天采购计划数量
     *
     * @param organizationId
     * @param day
     * @return
     */
    Integer getPurchasePlanCount(@Param("organizationId") Long organizationId, @Param("day") String day);

    /**
     * 更新采购计划状态
     *
     * @param planId 计划ID
     * @param state  目标状态
     */
    default void updateState(Long planId, Integer state) {
        this.update(null, new LambdaUpdateWrapper<PurchasePlan>()
                .eq(PurchasePlan::getId, planId)
                .set(PurchasePlan::getDealState, state));
    }

    /**
     * 更新采购计划状态（带条件）
     *
     * @param planId       计划ID
     * @param currentState 当前状态
     * @param targetState  目标状态
     */
    default void updateStateWithCondition(Long planId, Integer currentState, Integer targetState) {
        this.update(null, new LambdaUpdateWrapper<PurchasePlan>()
                .eq(PurchasePlan::getId, planId)
                .eq(PurchasePlan::getDealState, currentState)
                .set(PurchasePlan::getDealState, targetState));
    }

}
