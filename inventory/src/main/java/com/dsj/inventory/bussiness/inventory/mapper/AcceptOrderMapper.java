package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.AcceptOrder;
import com.dsj.inventory.bussiness.inventory.enumeration.AcceptOrderStateEnum;
import com.dsj.inventory.bussiness.inventory.param.QueryAcceptOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存验收单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface AcceptOrderMapper extends BaseMapper<AcceptOrder> {

    /**
     * 获取验收单分页列表
     * @param page
     * @param param
     * @return
     */
    List<AcceptOrderVo> queryAcceptOrderPage(@Param("page") Page page, @Param("param") QueryAcceptOrderParam param);

    /**
     * 获取验收单详情
     * @param id
     * @return
     */
    AcceptOrderDetailVo getAcceptOrderById(Long id);

    /**
     * 导出验收单列表
     * @param param
     * @return
     */
    List<AcceptOrderExportVo> queryAcceptOrderList(@Param("param") QueryAcceptOrderParam param);

    /**
     * 删除组织的验收单
     * @param organizationId
     */
    void deleteAcceptOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天验收单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getAcceptOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);

    /**
     * 检查收货单是否已经绑定到其他验收单
     * @param orderId 收货单ID
     * @param excludeAcceptOrderId 需要排除的验收单ID（可为null）
     * @return 绑定计数
     */
    default int countByOrderIdExcludeAcceptOrderId(Long orderId, Long excludeAcceptOrderId) {
        LambdaQueryWrapper<AcceptOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AcceptOrder::getOrderId, orderId);
        if (excludeAcceptOrderId != null) {
            wrapper.ne(AcceptOrder::getId, excludeAcceptOrderId);
        }
        return this.selectCount(wrapper);
    }
    
    /**
     * 更新验收单状态为已验收
     * @param id 验收单ID
     * @return 是否更新成功
     */
    default boolean updateStateToAccepted(Long id) {
        LambdaUpdateWrapper<AcceptOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AcceptOrder::getId, id);
        updateWrapper.set(AcceptOrder::getDealState, AcceptOrderStateEnum.ACCEPTED.getCode());
        return update(null, updateWrapper) > 0;
    }
    
    /**
     * 将已验收状态的验收单更新为已完成状态
     * @param orderId 验收单ID
     * @return 是否更新成功
     */
    default boolean updateAcceptedToCompleted(Long orderId) {
        LambdaUpdateWrapper<AcceptOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AcceptOrder::getId, orderId);
        updateWrapper.eq(AcceptOrder::getDealState, AcceptOrderStateEnum.ACCEPTED.getCode());
        updateWrapper.set(AcceptOrder::getDealState, AcceptOrderStateEnum.COMPLETED.getCode());
        return update(null, updateWrapper) > 0;
    }

}
