package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 优惠券
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SalesCouponVo", description="优惠券实体")
@ToString(callSuper = true)
public class SalesCouponVo {

    private Long id;

    @ApiModelProperty(value = "组织id")
    private Long organizationId;

    @ApiModelProperty(value = "模块分类，1 云药快 ；2 外部；3 在线医院；4 商城；5进销存")
    private Integer moduleType;

    @ApiModelProperty(value = "优惠券类型 1代金券 2折扣券")
    private Integer couponType;

    @ApiModelProperty(value = "优惠券模式 1发放 2领取")
    private Integer couponMode;

    @ApiModelProperty(value = "优惠券状态 0未过期 1已过期")
    private Integer couponState;

    @ApiModelProperty(value = "优惠券名称")
    private String name;

    @ApiModelProperty(value = "使用条件")
    private String couponCondition;

    @ApiModelProperty(value = "减免金额")
    private Integer reducePrice;

    @ApiModelProperty(value = "折扣数")
    private Double percentage;

    @ApiModelProperty(value = "生效天数（发放后多少天生效）")
    private Integer assertDay;

    @ApiModelProperty(value = "有效天数")
    private Integer useDay;

    @ApiModelProperty(value = "优惠券开始时间")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "优惠券结束时间")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "优惠券领取开始时间")
    private LocalDateTime getStartDate;

    @ApiModelProperty(value = "优惠券领取结束时间")
    private LocalDateTime getEndDate;

    @ApiModelProperty(value = "每人发放/领取数量限制")
    private Integer amountLimit;

    @ApiModelProperty(value = "领取总量")
    private Integer getAmountLimit;

    @ApiModelProperty(value = "领取对象 1所有买家 2已合作买家")
    private Integer getGroup;

    @ApiModelProperty(value = "适用人群，会员等级")
    private Integer applyTo;

    @ApiModelProperty(value = "是否适用全部商品 0否1是")
    private Integer goodsLimit;

    @ApiModelProperty(value = "发放数量")
    private Integer grantAmount;

    @ApiModelProperty(value = "使用数量")
    private Integer useAmount;

    @ApiModelProperty(value = "过期数量")
    private Integer expireAmount;

    @ApiModelProperty(value = "剩余数量")
    private Integer surplusAmount;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "商品明细")
    private List<SalesCouponGoodsVo> goodsList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
