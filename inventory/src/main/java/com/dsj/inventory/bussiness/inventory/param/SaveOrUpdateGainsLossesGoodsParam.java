package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 损溢单商品明细
 * <AUTHOR>
 * @date 2023/04/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateGainsLossesGoodsParam", description="新增or编辑 损溢单商品明细参数")
public class SaveOrUpdateGainsLossesGoodsParam {

    @ApiModelProperty(hidden = true,value = "损溢单Id")
    private Long orderId;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "库存Id")
    private Long inventoryId;

    @ApiModelProperty(hidden = true,value = "通用名")
    private String goodsName;

    @ApiModelProperty(hidden = true,value = "商品名称")
    private String generalName;

    @ApiModelProperty(hidden = true,value = "包装单位")
    private String packUnit;

    @ApiModelProperty(hidden = true,value = "规格型号")
    private String specification;

    @ApiModelProperty(hidden = true,value = "剂型")
    private String drugType;

    @ApiModelProperty(hidden = true,value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(hidden = true,value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(hidden = true,value = "库存数量")
    private Double inventoryAmount;

    @ApiModelProperty(hidden = true,value = "批号")
    private String batchNumber;

    @ApiModelProperty(hidden = true,value = "货位Id")
    private Long shelfId;

    @ApiModelProperty(hidden = true,value = "批次（入库单号）")
    private String batch;

    @ApiModelProperty(value = "实际数")
    private Double realAmount;

    @ApiModelProperty(value = "损益数")
    private Double plugAmount;

    @ApiModelProperty(hidden = true,value = "成本均价")
    private Integer costPrice;

    @ApiModelProperty(hidden = true,value = "成本金额（成本均价总额）")
    private Integer costTotalPrice;

    @ApiModelProperty(hidden = true,value = "移动加权平均成本价")
    private Integer averageCost;

    @ApiModelProperty(hidden = true,value = "移动加权平均成本价总额")
    private Integer averageTotalCost;

    @ApiModelProperty(value = "损溢原因")
    private String reason;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
