package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 进销存会员优惠券信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_member_coupon")
@ApiModel(value="MemberCoupon", description="进销存会员优惠券信息")
public class MemberCoupon extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "会员Id")
    @TableField("member_id")
    private Long memberId;

    @ApiModelProperty(value = "系统用户Id")
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty(value = "优惠券Id")
    @TableField("coupon_id")
    private Long couponId;

    @ApiModelProperty(value = "优惠券状态 0未使用 1已使用 2已过期")
    @TableField("coupon_state")
    private Integer couponState;

    @ApiModelProperty(value = "优惠券开始时间")
    @TableField("start_date")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "优惠券结束时间")
    @TableField("end_date")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "领取时用户所属组织")
    @TableField("get_organization_id")
    private Long getOrganizationId;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
