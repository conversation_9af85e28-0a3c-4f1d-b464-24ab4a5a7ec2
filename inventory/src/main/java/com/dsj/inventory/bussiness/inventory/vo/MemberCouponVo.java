package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员优惠券
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MemberCouponVo", description="会员优惠券实体")
@ToString(callSuper = true)
public class MemberCouponVo {

    private Long id;

    @ApiModelProperty(value = "模块分类，1 云药快 ；2 外部；3 在线医院；4 商城；5进销存")
    private Integer moduleType;

    @ApiModelProperty(value = "会员Id")
    private Long memberId;

    @ApiModelProperty(value = "系统用户Id")
    private Long userId;

    @ApiModelProperty(value = "优惠券Id")
    private Long couponId;

    @ApiModelProperty(value = "优惠券类型 1代金券 2折扣券")
    private Integer couponType;

    @ApiModelProperty(value = "优惠券状态 0未使用 1已使用 2已过期")
    private Integer couponState;

    @ApiModelProperty(value = "优惠券名称")
    private String name;

    @ApiModelProperty(value = "使用条件")
    private String couponCondition;

    @ApiModelProperty(value = "减免金额")
    private Integer reducePrice;

    @ApiModelProperty(value = "折扣数")
    private Double percentage;

    @ApiModelProperty(value = "优惠券开始时间")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "优惠券结束时间")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "是否适用全部商品 0否1是")
    private Integer goodsLimit;

    @ApiModelProperty(value = "商品明细")
    private List<SalesCouponGoodsVo> goodsList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
