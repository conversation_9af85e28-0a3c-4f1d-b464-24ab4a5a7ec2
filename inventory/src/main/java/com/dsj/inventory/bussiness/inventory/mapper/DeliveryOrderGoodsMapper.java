package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.DeliveryOrderGoods;
import com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存退货单商品明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface DeliveryOrderGoodsMapper extends BaseMapper<DeliveryOrderGoods> {

    /**
     * 获取退货单商品明细
     * @param orderId
     * @return
     */
    List<DeliveryOrderGoodsVo> getDeliveryOrderGoodsByOrderId(Long orderId);

}
