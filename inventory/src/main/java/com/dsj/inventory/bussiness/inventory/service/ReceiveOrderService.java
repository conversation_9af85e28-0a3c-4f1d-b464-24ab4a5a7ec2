package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.ReceiveOrder;
import com.dsj.inventory.bussiness.inventory.param.QueryReceiveOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateReceiveOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存收货单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
public interface ReceiveOrderService extends IService<ReceiveOrder> {

    /**
     * 新增、编辑 收货单
     * @param param
     * @return
     */
    ReceiveOrderDetailVo saveOrUpdateReceiveOrder(SaveOrUpdateReceiveOrderParam param);

    /**
     * 删除 收货单
     * @param id
     */
    void deleteReceiveOrder(Long id);

    /**
     * 获取收货单分页列表
     * @param param
     * @return
     */
    Page<ReceiveOrderVo> queryReceiveOrderPage(QueryReceiveOrderParam param);

    /**
     * 获取收货单列表详情
     * @param id
     * @return
     */
    ReceiveOrderDetailVo getReceiveOrderById(Long id);

    /**
     * 提交收货单
     * @param id
     */
    void submitReceiveOrder(Long id);

    /**
     * 导出收货单列表
     * @param param
     * @return
     */
    List<ReceiveOrderExportVo> queryReceiveOrderList(QueryReceiveOrderParam param);

    /**
     * 删除组织的收货单
     * @param organizationId
     */
    void deleteReceiveOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天收货单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getReceiveOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);

    /**
     * 完成收货单
     * @param orderId
     */
    void completeReceiveOrder(Long orderId);
}
