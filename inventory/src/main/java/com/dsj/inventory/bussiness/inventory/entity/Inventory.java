package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 进销存商品库存
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_inventory")
@ApiModel(value="Inventory", description="进销存商品库存")
public class Inventory extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织Id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "私域商品Id")
    @TableField("organization_medicine_id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "生产日期")
    @TableField("produce_date")
    private LocalDateTime produceDate;

    @ApiModelProperty(value = "有效期至")
    @TableField("user_date")
    private LocalDateTime userDate;

    @ApiModelProperty(value = "库存数量")
    @TableField("inventory_amount")
    private Double inventoryAmount;

    @ApiModelProperty(value = "成本价格")
    @TableField("cost_price")
    private Integer costPrice;

    @ApiModelProperty(value = "批号")
    @TableField("batch_number")
    private String batchNumber;

    @ApiModelProperty(value = "货位Id")
    @TableField("shelf_id")
    private Long shelfId;

    @ApiModelProperty(value = "批次（入库单号）")
    @TableField("batch")
    private String batch;

    @ApiModelProperty(value = "入库时的商品明细Id")
    @TableField("incoming_goods_id")
    private Long incomingGoodsId;

    @ApiModelProperty(value = "最后入库时间")
    @TableField("incoming_date")
    private LocalDateTime incomingDate;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
