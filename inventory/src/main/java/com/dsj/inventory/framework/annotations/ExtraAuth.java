package com.dsj.inventory.framework.annotations;

import java.lang.annotation.*;

/**
 * 扩展 标签，用于提供给 对外接口的 认证和业务服务
 */
@Documented
@Inherited
@Target({ElementType.METHOD,ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExtraAuth {

    //混合认证，下属认证方式有一种通过既通过,以下认证方式 优先级按编写顺序 先校验  signAuth  后校验 extraTokenAuth
    boolean blendAuth() default true;

    //是否走签名认证方式，class上不检查
    boolean signAuth() default false;

    //是否走extraToken方式，class上不检查
    boolean extraTokenAuth() default false;
}