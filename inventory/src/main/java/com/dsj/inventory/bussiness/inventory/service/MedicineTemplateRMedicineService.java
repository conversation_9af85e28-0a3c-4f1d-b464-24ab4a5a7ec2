package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.MedicineTemplateRMedicine;
import com.dsj.inventory.bussiness.inventory.vo.MedicineTemplateMedicineVo;

import java.util.List;

/**
 * <p>
 * 药品拉取模板药品信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
public interface MedicineTemplateRMedicineService extends IService<MedicineTemplateRMedicine> {

    /**
     * 获取商品明细
     * @param templateId
     * @return
     */
    List<MedicineTemplateMedicineVo> getMedicineTemplateMedicineList(Long templateId);

}
