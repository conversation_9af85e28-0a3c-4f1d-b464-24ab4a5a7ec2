package com.dsj.inventory.bussiness.quality.param;

import com.dsj.inventory.bussiness.quality.enumeration.RejectionSourceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("拒收单创建参数")
public class RejectionCreateParam {
    @ApiModelProperty(value = "来源单据ID", required = true)
    @NotNull
    private Long sourceId;

    @ApiModelProperty(value = "来源单据号", required = true)
    @NotBlank
    private String sourceCode;

    @ApiModelProperty(value = "来源单据类型", required = true, example = "RECEIVE")
    @NotNull
    private RejectionSourceTypeEnum sourceType;

    @ApiModelProperty(value = "供应商ID")
    private Long supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "制单备注")
    private String remark;

    @ApiModelProperty(value = "组织ID", required = true)
    @NotNull
    private Long organizationId;

    @ApiModelProperty(value = "被拒收的商品明细列表", required = true)
    @NotEmpty
    private List<RejectedGoodsItem> rejectedItems;
} 