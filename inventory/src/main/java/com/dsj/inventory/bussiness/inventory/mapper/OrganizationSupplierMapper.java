package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationSupplier;
import com.dsj.inventory.bussiness.inventory.param.QueryOrganizationSupplierParam;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationSupplierExportVo;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationSupplierVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 供应商组织私域 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
public interface OrganizationSupplierMapper extends BaseMapper<OrganizationSupplier> {

    /**
     * 获取供应商组织私域分页列表
     * @param page
     * @param param
     * @return
     */
    List<OrganizationSupplierVo> queryOrganizationSupplierPage(@Param("page") Page page, @Param("param") QueryOrganizationSupplierParam param);

    /**
     * 获取供应商组织私域详情
     * @param id
     * @return
     */
    OrganizationSupplierVo getOrganizationSupplierById(Long id);

    /**
     * 导出供应商组织私域列表
     * @param param
     * @return
     */
    List<OrganizationSupplierExportVo> queryOrganizationSupplierExportList(@Param("param") QueryOrganizationSupplierParam param);

}
