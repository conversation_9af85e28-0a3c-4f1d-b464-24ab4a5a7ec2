
# ERP 产品设计报告

作者: 行稳致远
创建日期: 2025-01-05
最后更新: 2025-01-11
版本编号: V1.3

## 文档控制

### 修改记录

| 版本  | 日期         | 作者     | 备注         |
| :---- | :----------- | :------- | :----------- |
| V1.0  | 2025-01-05   | 行稳致远 |              |
| V1.1  | 2025-01-11   | 行稳致远 | 调整排版     |
| V1.2  | 2025-01-15   | 行稳致远 | 流程图调整   |

### 审核记录

| 版本  | 日期         | 作者     | 备注         |
| :---- | :----------- | :------- | :----------- |
| V1.0  | 2025-01-11   | 大地无声 |              |
| V1.1  | 2025-01-15   | 大地无声 |              |
| V1.2  | 2025-01-15   | 大地无声 |              |

## 目录

-   第一章、产品定位
-   第二章、产品标准
-   第三章、业务流程
    -   3.1 图示说明
    -   3.2 总体流程图
    -   3.3 采购流程
        -   3.3.1 首营供应商
        -   3.3.2 首营品种
        -   3.3.3 采购计划
        -   3.3.4 采购订单
        -   3.3.5 收货管理
        -   3.3.6 验收管理
        -   3.3.7 拒收管理
        -   3.3.8 入库管理
        -   3.3.9 采购退货
        -   3.3.10 采购调价
    -   3.4 零售管理
        -   3.4.1 会员办卡流程
        -   3.4.2 会员信息修改
        -   3.4.3 门店零售
        -   3.4.4 零售退货
    -   3.5 质量管理
        -   3.5.1 供应商证照管理
        -   3.5.2 商品证照管理
        -   3.5.3 养护管理
        -   3.5.4 不合格品管理
-   第四章、产品评估
    -   4.1 现有功能
    -   4.2 补充功能
-   第五章、产品模块
-   第六章、产品亮点

## 第一章、产品定位

本产品为一款应用到医药单体店的 ERP 软件，旨在满足医药单体店在药品采购、库存管理、销售管理、会员管理、促销管理以及质量管理等方面的需求，提升药店的运营效率和竞争力。

## 第二章、产品标准

1.  **产品化：** 标准化产品，能够兼容大部分医药单体店的市场需求，减少定制化开发成本。
2.  **流程畅通：** 系统界面友好，“可见即可用”，确保在运行过程中不出错，数据查询准确，计算结果正确。
3.  **使用方便：** 系统操作简单易懂，提供快捷键、跳转等功能，确保用户使用连续性。
4.  **功能全面：** 系统涵盖 GSP 模块、医保接口、会员模块、促销模块等，满足药店全方位管理需求。

## 第三章、业务流程

### 3.1 图示说明

| 符号描述 (名称) | 含义                 | 使用说明                                                                                                 |
| :---------------- | :------------------- | :------------------------------------------------------------------------------------------------------- |
| 圆角矩形 (开始/结束) | 表示流程起点及终点   | 1. 在流程开始、结束时作为起始符和终止符。                                                              |
| 矩形 (活动框)   | 表示流程活动步骤     | 1. 其中内容一般用动宾结构短语表示；<br>2. 应按照活动开展的顺序从上至下、从左至右排列。                       |
| 平行四边形 (流程接口) | 预先定义的其他流程   | 1. 通常取代“开始”或“结束”，表示承接上游流程或转入下游流程；<br>2. 流程接口应填写流程名称。               |
| 菱形 (判断)       | 流程判断点           | 1. 判断下一步活动的行动方向。如：付款申请审核，“是”则进行付款，‘否’则走其他步骤。                       |
| 文档 (单文档)     | 输入或输出的文件     | 1. 用来表示业务活动附带（输入）或产生（输出）的文件、表单、报表等文档信息；<br>2. 多种文档输入或输出应用双文档/多文档符号表示。 |
| 文档堆叠 (双/多文档) | 输入或输出的文件     | (同上)                                                                                                   |
| 箭头 (动态连接线) | 指向路径方向         | 1. 用来连接流程图内的各种形状；<br>2. 箭头的方向应保证流程图从上到下、从左到右的整体流向。                 |

### 3.2 总体流程图

● 本流程适用单体药店

```mermaid
graph TD
    subgraph 采购
        direction LR
        A1[首营供应商] --> A3(采购计划)
        A2[首营品种] --> A3
        A3 --> A4(采购订单)
        A4 --调价--> A5(采购调价) --> FIN2(应付管理)
        A4 --退货申请--> A6(采购退货申请)
    end

    subgraph 质量
        direction LR
        Q1[证照管理]
        Q2[药品质量]
        Q3[效期管理]
        Q4(采购验收)
    end

    subgraph 仓储
        direction LR
        S1(采购收货) --> S2(采购拒收)
        S1 --> S3(采购入库)
        S1 --> S4(采购退货) --> A6
        S5[盘点管理]
        S6[货位调整]
    end

    subgraph 门店
        direction LR
        M1[办理会员卡] --> M2(零售收银)
        M2 --> M3(零售退货)
        M4[积分兑换]
        M5[审方管理]
        M6[门店对账] --> FIN1(应收管理)
    end

    subgraph 营运
        direction LR
        O1[调整售价]
        O2[促销管理]
    end

    subgraph 财务
        direction LR
        FIN1[应收管理]
        FIN2[应付管理]
    end

    A4 --> S1
    Q4 --> S1
    Q1 --> A1
    Q1 --> A2
    Q2 --> Q4
    Q3 --> Q4
    S3 --> S5
    S3 --> S6
    S3 --> M2
    M2 --> M3
    M2 --> M4
    M2 --> M5
    M2 --> FIN2
    O1 --> M2
    O2 --> M2

    linkStyle default interpolate basis
```

*(注: 原始流程图布局复杂，Mermaid 简化了连接以保持清晰度)*

### 3.3 采购流程

#### 3.3.1 首营供应商

● 本流程适用于以下场景:
√ 新开发的供应商，对供应商提供的资质材料，走首营供应商管理流程，做首营审批管理。

1)  **流程图**

```mermaid
graph TD
    subgraph 供应商
        A[供应商谈判] --> B(提供资质材料);
    end
    subgraph 采购部
        B -- 资质材料 --> C(采购员核对资质);
        C -- 否 --> A;
        C -- 是 --> D(采购员录入首营供应商);
        D -- 资质材料 --> E(采购经理核对);
        E -- 否 --> D;
    end
    subgraph 质量部
       E -- 是, 资质材料 --> F(质量经理核对);
       F -- 否 --> E;
    end
     subgraph 质量负责人
       F -- 是, 资质材料 --> G(质量负责人核对);
       G -- 否 --> F;
       G -- 是 --> H[结束];
    end
```

2)  **流程及说明**

| 序号 | 部门/岗位   | 步骤             | 流程                                                       | 说明                                                                                                                                                                                                                                                                                                                                                           | 系统动作       |
| :--- | :---------- | :--------------- | :--------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------- |
| 1    | 采购员      | 供应商谈判       | 与供应商谈判，确认是否有合作意向。如有合作意向，供应商需按规定提供资质材料。 | 供应商需提交的资质材料有：1、企业法人营业执照（副本，需有年检，变更记录） 2、药品生产许可证/经营许可证（副本，变更记录） 3、GMP/GSP 证书 4、税务登记证（国地税合一） 5、组织机构代码证书 6、质量体系调查表 7、质量保证协议书（盖公章、最好盖上法人章） 8、物料购销合同 9、委托书及身份证复印件 10、其他要求。 | 建档准备工作   |
| 2    | 采购员      | 审核供应商资质材料 | 采购员核对供应商资质材料。                             | 需核对的资质材料同上。                                                                                                                                                                                                                                                                                                                                         | 建档准备工作   |
| 3    | 采购员      | 录入【首营供应商】 | 供应商资质材料齐全完备，采购员则在系统录入【首营供应商】并做一审。 |                                                                                                                                                                                                                                                                                                                                                                | 建档           |
| 4    | 采购经理    | 审核供应商资质材料 | 采购部经理再次确认供应商首营资料，如确认没问题，则二审，否则退回给采购员重新核对。 | 同 2                                                                                                                                                                                                                                                                                                                                           | 一审           |
| 5    | 质量经理    | 审核供应商资质材料 | 质量经理核对供应商首营资料，如确认没问题，则三审，否则退回给采购经理重新核对。 | 同 2                                                                                                                                                                                                                                                                                                                                           | 二审           |
| 6    | 质量负责人  | 审核供应商资质材料 | 质量负责人核对供应商首营资料，如确认没问题，则终审，否则退回给质量经理重新核对。 | 同 2                                                                                                                                                                                                                                                                                                                                           | 三审           |

3)  **打印格式 / 报表说明**

| 序号 | 打印格式/报表名称 | 用途             | 信息维度                               |
| :--- | :---------------- | :--------------- | :------------------------------------- |
| 1    | 首营供应商审批表  | 打印签字存档     | 打印方案                               |
| 2    | 供应商查询        | 用于查询供应商   | 主要字段: 供应商编码、供应商名称、…… |

#### 3.3.2 首营品种

● 本流程适用于以下场景:
√ 初次购买的品种在系统中走首营品种审批流程。

1)  **流程图**

```mermaid
 graph TD
    subgraph 供应商
        A[供应商谈判] --> B(提供品种资质);
    end
    subgraph 采购部
        B -- 资质 --> C(采购员核对资质);
        C -- 否 --> A;
        C -- 是 --> D(采购员录入首营品种);
        D -- 资质 --> E(采购经理核对);
        E -- 否 --> D;
    end
    subgraph 质量部
       E -- 是, 资质 --> F(质量经理核对);
       F -- 否 --> E;
    end
     subgraph 质量负责人
       F -- 是, 资质 --> G(质量负责人核对);
       G -- 否 --> F;
       G -- 是 --> H[结束];
    end
```

2)  **流程及说明**

| 序号 | 部门/岗位   | 步骤             | 流程                                                       | 说明                                                                                                                                                                 | 系统动作       |
| :--- | :---------- | :--------------- | :--------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------- |
| 1    | 采购员      | 审核品种资质材料 | 与供应商谈判，确认有合作意向。供应商需按规定提供品种资质材料。 | 供应商需提交的品种资质材料有：1、药品生产许可证（副本，变更记录） 2、进口注册证 3、生产批文 4、物价批文 5、购销合同 6、委托书及身份证复印件 7、其他要求。 | 建档准备工作   |
| 2    | 采购员      | 审核品种资质材料 | 采购员核对品种资质材料。                             | 需核对的资质材料有：1、药品生产许可证（副本，变更记录）                                                                                                          | 建档准备工作   |
| 3    | 采购员      | 录入【首营品种】 | 供应商品种资质材料齐全完备，采购员则在系统录入【首营品种】并做一审。 | 2、进口注册证 3、生产批文 4、物价批文 5、购销合同 6、委托书及身份证复印件 7、其他要求。                                                                      | 建档           |
| 4    | 采购经理    | 审核品种资质材料 | 采购部经理再次确认品种首营资料，如确认没问题，则二审，否则退回给采购员重新核对。 | 同 2                                                                                                                                                                 | 一审           |
| 5    | 质量经理    | 审核品种资质材料 | 质量经理核对品种首营资料，如确认没问题，则三审，否则退回给采购经理重新核对。 | 同 2                                                                                                                                                                 | 二审           |
| 6    | 质量负责人  | 审核品种资质材料 | 质量负责人核对品种首营资料，如确认没问题，则终审，否则退回给质量经理重新核对。 | 同 2                                                                                                                                                                 | 三审           |

3)  **打印格式 / 报表说明**

| 序号 | 打印格式/报表名称 | 用途               | 信息维度                                                           |
| :--- | :---------------- | :----------------- | :----------------------------------------------------------------- |
| 1    | 首营品种审批表    | 打印签字存档       | 打印方案                                                           |
| 2    | 商品信息查询      | 用于查询商品信息 | 主要字段: 商品编码、商品名称、商品规格、产地、单位、批准文号、条形码、…… |

#### 3.3.3 采购计划

● 本流程适用于以下场景:
√ 按需求制定采购计划，确定需要购买的品种、数量、交期。

1)  **流程图**

```mermaid
graph TD
    A[开始] --> B{生成方式};
    B -- 自动 --> C(自动生成采购计划);
    B -- 手动 --> D(其他方式);
    subgraph 采购部
        C --> E(制作采购计划);
        D --> E;
        E --> F{是否需要<br/>采购比价};
        F -- 是 --> G(采购询价);
        F -- 否 --> H(采购员审核);
        G -- 询价结果 --> H;
        H --> I(采购经理审批);
        I --> J[采购订单流程];
    end
    subgraph 供应商
       G --> G_Supplier(供应商报价);
       G_Supplier --> G;
    end
```

2)  **流程说明及管控点**

| 序号 | 部门/岗位       | 步骤             | 流程                                   | 说明                                                                                               | 系统动作        |
| :--- | :-------------- | :--------------- | :------------------------------------- | :------------------------------------------------------------------------------------------------- | :-------------- |
| 1    | 采购员/采购经理 | 自动生成采购计划 | 采购来源：根据库存上下限自动生成采购计划 | 1、设置合理的库存上下限天数，再根据库存上下限自动生成                                            | 生成            |
| 2    | 采购员/采购经理 | 其他方式         | 采购来源：其他方式，形成相应的采购计划 | 1、采购人员主动进行采购品种计划，常规商品，EXCEL 导入等                                             | 主动录入、EXCEL导入 |
| 3    | 采购部          | 制作采购计划     | 制作采购计划                           | 1. 确定采购品种，数量。                                                                          | 采购计划        |
| 4    | 采购员          | 是否需要采购比价 | 确定是否需要进行比价。                   | 1. 比价出低价格供应商，还得考虑付款方式，返利政策等。                                            |                 |
| 5    | 供应商          | 采购询价流程     | 对需要比价的品种进行询价               | 1. 供应商对需要询价的品种进行                                                                    |                 |
| 6    | 采购员          | 采购员审核       | 确定采购的品种，数量，供应商，价格，付款方式等。 | 1、核对品种与数量，相应的采购进价及供应商<br>2、确定付款方式（现金、预付款等）与账期（30天结、押批等） | 采购计划        |
| 7    | 采购员/采购经理 | 采购经理审批     | 采购经理针对采购员采购计划进行审批     | 1、确认品种、数量、进价、供应商<br>2、确认预算                                                    | 采购计划        |

3)  **打印格式 / 报表说明**

| 序号 | 打印格式/报表名称 | 用途         | 信息维度                                   |
| :--- | :---------------- | :----------- | :----------------------------------------- |
| 1    | 采购计划          | 用于存档查询 | 主要字段: 供应商编码、供应商名称、品种、数量及价格……… |

#### 3.3.4 采购订单

● 本流程适用于以下场景:
√ 向采购供应商下发采购订货单。

1)  **流程图**

```mermaid
graph TD
    A(采购计划流程) --> B[开始];
    subgraph 采购部
        B --> C(录入采购订货单);
        C --> D{是否<br/>特殊药品};
        D -- 否 --> E(审核采购订货单);
        E --> F(导出订货单);
    end
    subgraph 质管部
        D -- 是 --> G(质量审核<br/>采购订货单);
        G -- 是 --> E;
        G -- 否 --> C;
    end
    subgraph 财务部
       F --> H{是否<br/>预付款};
       H -- 是 --> I(预付款流程);
    end
    subgraph 供应商
        H -- 否 --> K(供应商<br/>送货流程);
        I --> K;
    end
     K -- 订货信息 --> F;
```

2)  **流程及说明**

| 序号 | 部门/岗位       | 步骤               | 流程                                 | 说明                                                                                                                                                                            | 系统动作   |
| :--- | :-------------- | :----------------- | :----------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :--------- |
| 1    | 采购员/采购经理 | 录入采购订单       | 确定采购订货单，可以手工录入或者提取采购计划 | 1、采购计划单据是否已执行；品种、价格、供应商确定是否无误<br>2、订货单针对付款方式、进价进行修正<br>3、核对供应商的品种返利<br>4、订货价是否允许高于最新进价         | 制单       |
| 2    | 采购员/采购经理 | 是否特殊药品       | 审核特殊品种采购订单                 | 若特殊药品质量管理部审核                                                                                                                                                      | 制单       |
| 3    | 采购员/采购经理 | 审核采购订货单     | 确定采购订货单                       | 1、采购计划单据是否已执行；品种、价格、供应商确定是否无误<br>2、订货单针对付款方式、进价进行修正<br>3、核对供应商的品种返利<br>4、订货价是否允许高于最新进价         | 审核       |
| 4    | 质管部          | 质量审核采购订货单 | 审核采购订货单                       | 如果存在特殊药品就需要质量管理部进行审核                                                                                                                                          | 审核       |
| 5    | 采购经理        | 审批采购订货单     | 审批采购订货单                       | 核对采购员订货单（主要供应商、品种，价格，返利；是否预付款；以及订货价高于最新进价的审批）                                                                           | 审核       |
| 6    | 采购员          | 导出订货单         | 告之供应商确定订货的信息               | 订货单信息包括品种、数量、价格发给供应商业务员                                                                                                                                  | 导出       |
| 7    | 财务部          | 是否预付款         | 订货单下单后，走预付款流程             | 1、预付款单是否必须由采购订货生成<br>2、预付款由采购部填写后，再转给财务部                                                                                                  | 流程衔接   |
| 8    | 供应商          | 供应商送货流程     | 根据最终的采购订单，通知供应商送货     | 沟通到货时长，运输方式等。                                                                                                                                                    |            |

#### 3.3.5 收货管理

● 本流程适用于以下场景:
√ 供应商按订单送货，到货后进行收货。

1)  **流程图**

```mermaid
graph TD
    A(采购订单流程) --> B[开始];
    subgraph 供应商
        B --> C(运输商品);
        C -- 随货同行单/药品质量档案 --> D;
    end
    subgraph 仓库
        D(核实到货数量<br/>及检查外包装) --> E(录入收货单);
        E --> F{判断供应商<br/>证照效期};
        F -- 过期 --> G(拒收流程);
        F -- 正常 --> H{判断是否超出<br/>供应商经营范围};
        H -- 超出 --> G;
        H -- 正常 --> I{到货商品是否<br/>与订购单相符};
        I -- 否 --> I_Decision{是否收货?};
        I -- 是 --> J{是否满足<br/>收货标准};
        J -- 否 --> J_Decision{是否收货?};
        J -- 是 --> K(验收流程);
        I_Decision -- 否 --> G;
        J_Decision -- 否 --> G;
    end
    subgraph 采购部
       I_Decision -- 是 --> L(补入订单);
       L --> J;
       J_Decision -- 是 --> K;
    end
```

2)  **流程及说明**

| 序号 | 部门/岗位 | 步骤                   | 流程                                                     | 说明                                                                                                                                                                      | 系统动作 |
| :--- | :-------- | :--------------------- | :------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :------- |
| 1    | 供应商    | 运输商品               | 供应商根据前期采购订单请的品种、数量运输商品。               | 需提供《随货通行单》、《药品质量档案》。                                                                                                                            |          |
| 2    | 收货员    | 核实到货数量以及检查外包装 | 根据供应商的随货同行单，与实物核对数量，并检查外包装是否完好。 | 1、需对照随货同行单仔细核对商品数量及批号, 填入实收数量, 如果发现批号不相符等问题需填入拒收数量生成拒收单。<br>2、需仔细核对商品外包装发现有无破损, 如果有填入拒收数量生成拒收单。 |          |
| 3    | 收货员    | 录入收货单             | 根据供应商的随货同行单，调取相应的采购订单，录入收货单信息。 | 需录入实收数量、批号、生产日期、有效期等基本信息                                                                                                                          | 制单     |
| 4    | 收货员    | 判断供应商证照效期     | 根据系统提示判断供应商证照效期                             | 如果证照过期则转【采购拒收流程】                                                                                                                                          | 收货单   |
| 5    | 收货员    | 判断是否超出供应商经营范围 | 根据系统提示判断是否超出供应商经营范围                     | 如果相应的商品超出供应商经营范围则转【采购拒收流程】                                                                                                                      | 收货单   |
| 6    | 收货员    | 到货商品是否           | 系统自动判断到货商品是否                                   | 如果到货品种或数量与订单不相同需联系采购确认是否收货（大多为实物数量超出订购单数量）                                                                                        | 收货单   |
| 7    | 收货员    | 是否满足收货标准       | 当数量<=订购单数量时，检查商品是否满足收货标准。             | 1. 如果符合验收标准,则转【采购验收流程】<br>2. 如果不符合验收标准,则转【采购拒收流程】                                                                                    | 收货单   |
| 8    | 采购部    | 是否收货               | 当数量>订购单数量时，采购部判断是否收货。                  | 需参考药店政策，商品畅销情况。判断可收货则补录订单，否则拒收超出部分。                                                                                                    | 收货单   |
| 9    | 采购部    | 补录订单               | 当数量>订购单数量时，采购部同意收货，根据无订单品种、数量、价格制作采购订单。 | 需核对品种、数量、以及价格。录入后再循环进入【录入收货单】步骤。                                                                                                            | 采购订单 |

#### 3.3.6 验收管理

● 本流程适用于以下场景:
√ 仓库收货后，由质量部验收员进行质量检验及验收。

1)  **流程图**

```mermaid
graph TD
    A(收货流程) --> B[开始];
    subgraph 验收员一
        B --> C(录入验收单);
        C -- 收货单 --> D(核对批号是否相符);
        D -- 否 --> E(拒收流程);
        D -- 是 --> F{是否冷链药品};
        F -- 是 --> G{是否有<br/>温度记录};
        G -- 否 --> E;
        G -- 是 --> H{是否特殊药品};
        H -- 否 --> J;
        F -- 否 --> H;
        H -- 是 --> I{是否符合<br/>特殊药品管理标准};
        I -- 否 --> E;
        I -- 是 --> J{是否符合<br/>运输或验收要求};
        J -- 否 --> E;
        J -- 是 --> K{是否需要<br/>双人验收};
    end
     subgraph 验收员二
        K -- 是 --> L{是否符合<br/>验收标准};
        L -- 是 --> M(入库流程);
        L -- 否 --> E;
     end
     K -- 否 --> M;
```

2)  **流程及说明**

| 序号 | 部门/岗位 | 步骤                   | 流程                                                     | 说明                                                                               | 系统动作 |
| :--- | :-------- | :--------------------- | :------------------------------------------------------- | :--------------------------------------------------------------------------------- | :------- |
| 1    | 验收员    | 录入验收单             | 根据收货单录入验收单，并录入抽检数量                     | 验收数量不允许大于收货数量                                                           | 制单     |
| 2    | 验收员    | 核对批号是否相符       | 对照收货单仔细核对商品批号，填入实收数量，仔细核对商品质量状况发现有无破损，从该批入库商品抽检一定数量的商品进行检查 | 1、如果发现批号不相符等问题需录入拒收数量，产生拒收单<br>2、如果有填入拒收数量生成拒收单 |          |
| 3    | 验收员    | 是否冷链药品及特殊药品 | 核查商品是否冷链药品及特殊药品                         | 1、商品是否满足验收标准，如果不满足则需拒收                                            |          |
| 4    | 验收员    | 是否符合特殊药品管理标准 | 核查是否符合特殊药品管理标准                             | 1、商品是否符合特殊药品管理标准，如果不满足则拒收                                        |          |
| 5    | 验收员    | 是否符合运输或验收要求 | 核查是否符合运输或验收要求                             | 1、商品是否符合运输或验收要求，如果不满足则拒收                                        |          |
| 6    | 验收员    | 是否需要双人验收       | 是否需要双人验收                                         | 1、商品是否需要双人验收，如果需要则需做双人验收单                                        |          |
| 7    | 验收员二  | 是否符合验收标准       | 是否符合验收标准                                         | 1、判断双人验收商品是否符合验收标准                                                    |          |

3)  **打印格式 / 报表说明**

| 序号 | 打印格式/报表名称 | 用途             | 信息维度                                                                       |
| :--- | :---------------- | :--------------- | :----------------------------------------------------------------------------- |
| 1    | 验收单            | 用于查询验收单详情 | 主要字段: 供应商编码、供应商名称、商品编码、商品名称、生产日期、有效期、批号、数量、质量状况、拒收数量等 |

#### 3.3.7 拒收管理

● 本流程适用于以下场景:
√ 商品在采购入库过程中，收货、验收、入库时，只要发现不符合验收标准，则拒收。

1)  **流程图**

```mermaid
graph TD
    A(采购收货流程) --> B(生成拒收单);
    A1(采购验收流程) --> B;
    A2(采购入库流程) --> B;
    subgraph 质管部
         B --> C(审核拒收单);
         C --> D(拒收退货单);
    end
    subgraph 仓储部
         D --> E(退实物);
    end
    subgraph 供应商
        D --> F(接收商品);
        E --> F;
    end
    F --> G[结束];
```

2)  **流程及说明**

| 序号 | 部门/岗位       | 步骤         | 流程                               | 说明                                                     | 系统动作   |
| :--- | :-------------- | :----------- | :--------------------------------- | :------------------------------------------------------- | :--------- |
| 1    | 仓储部/质管部 | 生成拒收单   | 收货，验收，入库过程中产生拒收     | 拒收单由采购收货、验收、入库三个流程自动生成                 | 制单       |
| 2    | 质量部          | 审核拒收单   | 核对拒收数量                       | 需仔细核对拒收数量                                         | 审核       |
| 3    | 供应商          | 拒收退货单   | 由拒收单产生拒收退货单             | 拒收商品退供应商或按照药监要求进行处理                     | 流程衔接   |
| 4    | 仓储部          | 退实物       | 退实物                           | 仓库部根据拒收退货单将实物退回供应商                       |            |
| 5    | 供应商          | 接收商品     | 接收商品                         | 供应商接收被退回商品                                       |            |

3)  **打印格式 / 报表说明**

| 序号 | 打印格式/报表名称 | 用途             | 信息维度                                                                       |
| :--- | :---------------- | :--------------- | :----------------------------------------------------------------------------- |
| 1    | 拒收单            | 用于打印拒收信息 | 主要字段: 供应商编码、供应商名称、商品编码、商品名称、生产日期、有效期、批号、数量、质量状况、拒收数量 |

#### 3.3.8 入库管理

● 本流程适用于以下场景:
√ 商品经收货、验收环节，验收合格，则办理入库的业务流程。

1)  **流程图**

```mermaid
 graph TD
     A(验收流程) --> B(录入入库单);
     subgraph 仓储部
         B -- 验收单 --> C(上架商品);
         C --> D{实际货位是否<br/>与上架单一致};
         D -- 否 --> E(货位调整流程);
         D -- 是 --> F[结束];
         E --> F;
     end
```

2)  **流程及说明**

| 序号 | 部门/岗位 | 步骤       | 流程                   | 说明                                                                                                                                                              | 系统动作 |
| :--- | :-------- | :--------- | :--------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------- |
| 1    | 仓储部    | 入库流程   | 核对商品数量及批号     | 1、需对照验收单仔细核对商品数量及批号，填入实收数量，如果发现批号不相符等问题需录入拒收，生成拒收单                                                              |          |
| 2    | 仓储部    | 录入入库单 | 根据验收单数据，录入入库单 | 2、需仔细核对商品质量状况发现有无破损，如果有填入拒收数量生成拒收单 <br> 3、核对实际进价是否不高于订单价 <br> 1、制作并录入入库单,入库单可以从验收单拉取相应的数据 <br> 2、录入商品的实收数量、实际进价、货位(或由系统自动分配) | 制单     |

3)  **打印格式 / 报表说明**

| 序号 | 打印格式/报表名称 | 用途                           | 信息维度                                                                                                   |
| :--- | :---------------- | :----------------------------- | :--------------------------------------------------------------------------------------------------------- |
| 1    | 入库单            | 用于打印入库信息               | 主要字段: 供应商编码、供应商名称、商品编码、商品名称、生产日期、有效期、批号、数量、质量状况、拒收数量         |
| 2    | 入库单报表        | 由于查询入库及退货详细信息 | 主要字段: 供应商编码、供应商名称、商品编码、商品名称、生产日期、有效期、批号、数量                         |

#### 3.3.9 采购退货

● 本流程适用于以下场景:
√ 当在仓商品需要退货给供应商时，需提出退货申请的业务流程。

1)  **流程图**

```mermaid
graph TD
    A[开始] --> B(采购通知退货);
     subgraph 仓储部
        B --> C(制作采购退货单);
        C --> D(下架商品);
     end
    subgraph 质量部
        C --> E{是否满足<br/>退货标准};
        E -- 否 --> F[结束];
    end
    subgraph 供应商
        E -- 是 --> G(接收商品);
    end
     D --> G;
     G --> F;
```

2)  **流程说明及管控点**

| 序号 | 部门/岗位 | 步骤           | 流程                                   | 说明                                                                                                   | 系统动作 |
| :--- | :-------- | :------------- | :------------------------------------- | :----------------------------------------------------------------------------------------------------- | :------- |
| 1    | 供应商    | 发起退货       | 供应商主动沟通发起退货                 | 供应商主动发起退货，如质量问题，商品配送错误等等                                                               |          |
| 2    | 采购部    | 发起退货       | 采购部主动发起退货                     | 采购部根据实际情况主动发起退货，如：滞销、近效期、高进价等等原因                                                 |          |
| 3    | 仓储部    | 发起退货       | 仓储部主动发起退货                     | 仓储部主动发起退货如：近效期、包装破损等等                                                               |          |
| 4    | 采购部    | 退货沟通       | 采购部与供应商沟通退货问题             | 如果是供应商主动发起退货则与仓库核对退货商品是否在库，如果是由自己或仓储部发起退货则与供应商沟通是否允许退货 | 无       |
| 5    | 采购部    | 是否允许退货   | 是否允许退货                         | 无                                                                                                     |          |
| 6    | 仓储部    | 制作单         | 制作采购退货单                         | 根据协商结果录采购退货单，如果商品不在总库而在门店则通知门店做退仓申请                                         | 制单     |
| 7    | 质量部    | 审核           | 审核采购退货单                         | 是否符合采购退货标准，如是否是原供应商、原价格、原批号退货等                                                 | 审核     |
| 8    | 供应商    | 接收商品       | 接收商品                             | 供应商根据采购退货单接收商品                                                                             |          |

#### 3.3.10 采购调价

● 本流程适用于以下场景:
√ 进货单价录入错误，或供应商价格调整，则需进行调价处理。

1)  **流程图**

```mermaid
graph TD
     A[开始] --> B(分析调价原因);
     subgraph 采购部
         B --> C(录入调进价单);
         C --> D(采购员审核);
         D -- 否 --> C;
         D -- 是 --> E(采购经理审核);
         E -- 否 --> D;
     end
     subgraph 财务部
         E -- 是, 纸质调进价单 --> F(财务审批);
         F -- 否 --> E;
         F -- 是 --> G(供应商结算流程);
     end
     G --> H[结束];
```

2)  **流程及说明**

| 序号 | 部门/岗位 | 步骤           | 流程                   | 说明                                                                                                                                                         | 系统动作   |
| :--- | :-------- | :------------- | :--------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------- |
| 1    | 采购员/采购经理 | 分析调价原因   | 调进价单原因，注明调价原因 | 1、调价原因:手工录入入库单或订货单进价录错,入库单价格录错;后期采购政策调整(如剩余近效期库存金额与供应商协商后，供应商同意折价处理) <br> 2、需要在单据备注调价原因,便于后期检查及核对 |            |
| 3    | 采购员/采购经理 | 录入调进价单   | 制作调进价单           | 1、输入对应的供应商 <br> 2、确定某品种，批次 <br> 3、确定调价类型“按购进量”“订货单”“库存余额”                                                            | 制单       |
| 4    | 采购员/采购经理 | 采购员审核     | 采购员审核             | 采购员根据录入的单据数据进行核对，并审核                                                                                                                       | 审核       |
| 5    | 采购员/采购经理 | 采购经理审批   | 采购经理审批           | 审批采购员调进价单，并核对打印的票据                                                                                                                           | 打印       |
| 6    | 财务部    | 财务审批       | 检验及登记作账         | 1、根据采购员打印票据与软件核对,审核并且登记财务账款 <br> 2、纸质调进价单票据,需要备档                                                                            | 审核/记账 |

### 3.4 零售管理

#### 3.4.1 会员办卡流程

● 本流程适用于以下场景:
√ 顾客到门店办理(线上、线下)会员卡业务流程。

1)  **流程图**

```mermaid
graph TD
    subgraph 顾客
        A[开始] --> B(扫码或告知会员信息);
    end
    subgraph 店员
        B --> C(录入会员信息);
    end
    subgraph 店长
        C --> D(审核会员信息);
        D -- 否 --> C;
        D -- 是 --> E(注册会员成功);
    end
    E --> F[结束];
```

2)  **流程及说明**

| 序号 | 部门/岗位 | 步骤         | 流程                                   | 说明                                                                                                                                                       | 系统动作 |
| :--- | :-------- | :----------- | :------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------- | :------- |
| 1    | 顾客      | 填写会员信息 | 顾客加入连锁会员、先填写申请表、后期店员录入系统 | 顾客填扫码或告知注册会员的信息: 1、办卡渠道；2、会员卡类型；3、姓名；4、身份证；5、性别；6、生日；7、手机 8、电话；9、地址；10、会员关心病种                   |          |
| 2    | 店员      | 录入会员相关信息 | 收集新办会员的信息资料、录入系统     | 新办会员需要录入的信息: 1、办卡渠道；2、会员卡类型；3、姓名；4、身份证；5、性别；6、生日；7、手机 8、电话；9、地址；10、会员关心病种                 | 制单     |
| 3    | 店长      | 审核店员录入的会员信息 | 店长审核店员录入的会员资料信息完整度和准确度 | 店长审核店员录入的会员信息: 1、办卡渠道；2、会员卡类型；3、姓名；4、身份证；5、性别；6、生日；7、手机 8、电话；9、地址；10、会员关心病种                   | 审核     |

3)  **打印格式 / 报表说明**

| 序号 | 打印格式/报表名称 | 用途               | 信息维度             |
| :--- | :---------------- | :----------------- | :------------------- |
| 1    | 会员查询          | 用于查询门店会员信息 | 门店、新办会员卡数 |

#### 3.4.2 会员信息修改

● 本流程适用于以下场景:
√ 门店会员到门店修改会员信息的业务流程。

1)  **流程图**

```mermaid
graph TD
     subgraph 顾客
         A[开始] --> B(凭身份证或电话<br/>修改会员信息);
     end
     subgraph 店员
         B --> C(更改会员信息);
     end
    subgraph 店长
         C --> D(审核会员信息);
         D -- 否 --> C;
         D -- 是 --> E(会员信息<br/>修改成功);
     end
     E --> F[结束];
```

2)  **流程及说明**

| 序号 | 部门/岗位 | 步骤                     | 流程                                       | 说明                                                                                                                                                         | 系统动作 |
| :--- | :-------- | :----------------------- | :----------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------- | :------- |
| 1    | 顾客      | 凭身份证或手机号修改会员信息 | 顾客因为信息调整 修改申会员信息            | 顾客写修改的信息: 1、办卡渠道；2、会员卡类型；3、姓名；4、身份证；5、性别；6、生日；7、手机 8、电话；9、地址；10、会员关心病种                         |          |
| 2    | 店员      | 修改的会员相关信息       | 修改会员的信息资料、录入系统               | 店员修改会员的信息: 1、办卡渠道；2、会员卡类型；3、姓名；4、身份证；5、性别；6、生日；7、手机 8、电话；9、地址；10、会员关心病种                         | 制单     |
| 3    | 店长      | 审核店员修改的会员信息是否正确 | 店长审核店员修改的会员资料信息是否正确 | 店长审核店员修改的会员信息: 1、办卡渠道；2、会员卡类型；3、姓名；4、身份证；5、性别；6、生日；7、手机 8、电话；9、地址；10、会员关心病种                   | 审核     |

3)  **打印格式 / 报表说明**

| 序号 | 打印格式/报表名称 | 用途               | 信息维度       |
| :--- | :---------------- | :----------------- | :------------- |
| 1    | 会员修改记录      | 追溯会员信息修改 |                |

#### 3.4.3 门店零售

● 本流程适用于以下场景:
√ 顾客进店购买药品、商品的业务流程。

1)  **流程图**

```mermaid
graph TD
    subgraph 顾客
        A[开始] --> B(购药);
    end
    subgraph 营业员
        B --> C(销售);
    end
    subgraph 收银员
        C --> D(销售管理);
        D --> E{是否<br/>有促销活动};
        E -- 是 --> F{是否满足<br/>促销};
        E -- 否 --> G;
        F -- 是 --> H(选择促销方案);
        F -- 否 --> G;
        H --> G{是否需要<br/>打折};
        G -- 是 --> I{是否有<br/>打折权限};
        I -- 否 --> J(打折授权);
        I -- 是 --> K;
        G -- 否 --> K;
        K{是否处方药<br/>特殊药} -- 是 --> L(登记处方信息<br/>顾客信息);
        K -- 否 --> M(收款结账);
        L --> M;
        M --> N(交付商品);
     end
    subgraph 店长
        I -- 否 --> J;
    end
    N --> O[结束];
```

2)  **流程及说明**

| 序号 | 部门/岗位 | 步骤                 | 流程                                           | 说明                                                               | 系统动作 |
| :--- | :-------- | :------------------- | :--------------------------------------------- | :----------------------------------------------------------------- | :------- |
| 1    | 顾客      | 购药                 | 顾客进店购物                                   |                                                                    |          |
| 2    | 营业员    | 销售                 | 向顾客推销本店商品                             | 根据药店指定的政策，进行商品的推销以及检查效期。                       |          |
| 3    | 收银员    | 零售录入             | 根据实物在系统录入商品信息                     | 核对效期、批号。                                                     | 制单     |
| 4    | 收银员    | 是否有促销活动       | 商品是否有促销活动                           | 是否由系统提示商品是否有促销活动。                                   | 制单     |
| 5    | 收银员    | 是否满足促销         | 本次是否满足促销                             | 只有满足促销条件的才会弹出促销方案。                                 | 制单     |
| 6    | 收银员    | 是否有打折权限       | 收银员是否有打折权限                         | 收银员只能在授权范围给顾客打折。                                   | 制单     |
| 7    | 收银员    | 选择促销方案         | 根据药店促销政策，                             | 如果有多种促销方案，准确选择与之匹配的促销方案。                       | 制单     |
| 8    | 收银员    | 是否处方药、特殊药   | 根据系统判断商品是否处方药、特殊药，收银员告知顾客 | 商品资料必须维护好特殊药品、处方药属性，系统才能准确提示。                 | 制单     |
| 9    | 收银员    | 登记处方信息、顾客信息 | 销售特殊药品、处方药，必须登记顾客信息、处方信息 | 特殊药品、处方药必须登记顾客信息、处方信息。                           | 制单     |
| 10   | 店长      | 打折授权             | 店长授权打折商品                             | 收银员无打折的需店长打折授权                                         | 制单     |
| 11   | 收银员    | 收款结账             | 收银收款结账                                   | 准确录入收款金额                                                   | 制单     |
| 12   | 收银员    | 交付商品             | 收银员交付商品。                               | 无                                                                 |          |

3)  **打印格式 / 报表说明**

| 序号 | 打印格式/报表名称           | 用途                   | 信息维度                                                                     |
| :--- | :-------------------------- | :--------------------- | :--------------------------------------------------------------------------- |
| 1    | 小票打印                    | 给予顾客的销售凭证     | 主要字段: 销售单号、商品名称、原价、实价、付款方式、优惠金额……                 |
| 2    | 交班打印                    | 药店对账               | 主要字段: 收款方式、收银员、日期、门店名称……                                   |
| 3    | 零售汇总报表                | 查询门店的销售汇总     | 主要字段: 付款方式、销售单号、商品名称……                                     |
| 4    | 零售明细                    | 查询门店销售明细       | 主要字段: 销售单号、销售数量、销售实价……                                     |
| 5    | 特殊/处方药品销售报表       | 查询门店特殊药品销售   | 主要字段: 销售单号、销售数量、类型、顾客……                                   |
| 6    | 会员销售报表                | 查询零售相关数据       | 主要字段: 商品编码、销售单号、收银员、会计月……                               |

#### 3.4.4 零售退货

● 本流程适用于以下场景:
√ 顾客购买商品后，在规定的期限内，将商品退回门店的业务流程。

1)  **流程图**

```mermaid
 graph TD
    subgraph 顾客
        A[开始] --> B(购物小票<br/>退货);
    end
    subgraph 收银员
        B -- 购物小票 --> C(核对小票、商品);
        C --> D{是否有<br/>退货权限};
        D -- 否 --> E(退货授权);
        D -- 是 --> F(录入零售退货);
        E --> F;
        F --> G{是否有赠品};
        G -- 是 --> H(收回赠品);
        G -- 否 --> I(收回退货商品);
        H --> I;
        I --> J(退款);
    end
     subgraph 店长
         D -- 否 --> E;
     end
     J -- 退货小票 --> K[结束];
```

2)  **流程及说明**

| 序号 | 部门/岗位 | 步骤           | 流程                                               | 说明                                                           |
| :--- | :-------- | :------------- | :------------------------------------------------- | :------------------------------------------------------------- |
| 1    | 顾客      | 退货           | 顾客持购物小票退货                                 | 无                                                             |
| 2    | 收银员    | 核对小票、商品 | 与顾客交流，明确顾客退货原因                         | 非质量问题或特殊情况不允许退货, 且顾客需提供购物小票。             |
| 3    | 收银员    | 是否有退货权限 | 收银员是否有退货权限                               | 收银员如果无退货权限，需经店长授权或由店长退货。                   |
| 4    | 收银员    | 录入零售退货单 | 通过小票调出原单销售进行退货                       | 是否必须凭原单退货                                               |
| 5    | 收银员    | 是否有赠品     | 查询销售历史记录核对有无赠品                       | 如果有赠品，需要收回赠品。                                       |
| 6    | 收银员    | 收回赠品       | 收回赠品                                           | 无                                                             |
| 7    | 店长      | 退货授权       | 对无权退货的收银员授权，由店长或店长授权退货         | 只有店长或经店长授权才允许退货。                                 |
| 8    | 收银员    | 收回退货商品   | 收回退货商品                                       | 无                                                             |
| 9    | 收银员    | 退款           | 退款给顾客，录入退款金额并打印退货小票             | 准确录入退款金额。                                             |

3)  **打印格式 / 报表说明** (同 3.4.3 零售)

### 3.5 质量管理

#### 3.5.1 供应商证照管理

● 本流程适用于以下场景:
√ 对于日常发生采购交易的供应商，要时刻关注供应商证照的有效期限，对于临近有效期的证照，要重新索要证照，并更改系统中的供应商证照有效期信息。

1)  **流程图**

```mermaid
graph TD
    A[开始] --> B(供应商证照);
    subgraph 供应商
       B --> C(提供证照);
       H --> C;
    end
    subgraph 质量部
       B --> D(首营供应商);
       C --> E(录入证照信息);
       E --> F{发现过期/近效期};
       F -- 不可以 --> G(作废基础资料);
       F -- 可以 --> H(修改基础资料);
    end
    subgraph 采购部
       F -- 可以 --> I(索要相关证照);
       H --> I;
    end
    I --> H;
    G --> J[结束];
    H --> J;
```

2)  **流程及说明**

| 序号 | 部门/岗位 | 步骤           | 流程说明                             | 管控点                             | 系统动作     |
| :--- | :-------- | :------------- | :----------------------------------- | :--------------------------------- | :----------- |
| 1    | 供应商    | 提供相关证照   | 首营流程提供相关的资质证照         | 证照是否过期，近效期               |              |
| 2    | 质量部    | 录入证照信息   | 根据供应商提供的证照录入证照信息     | 效期 30 天提醒，过期以后停止业务往来 | 制单         |
| 3    | 质量部    | 发现过期/近效期 | 在录入过程或者日常工作中发现证照过期的/近有效期 |                                    | 报表/首页预警 |
| 4    | 采购部    | 索要相关证照   | 采购员联系供应商索要相关资质         | 通知具体过期/近效期的证照名称并索要相关资质 |              |
| 5    | 供应商    | 提供证照       | 供应商根据采购要求提供所需要的证照 | 是否提供                           |              |
| 6    | 质量部    | 修改基础资料   | 如果供应商提供资料则根据提供的资料修改基础资料 | 供应商证照是否过期               | 修改         |
| 7    | 质量部    | 作废基础资料   | 如果供应商不能提供资料则作废基础资料停止往来 |                                    | 修改         |

#### 3.5.2 商品证照管理

● 本流程适用于以下场景:
√ 在首营品种或供应商送货时，供应商所提供的商品资质材料统一扫描电子档，并上传系统归档管理的业务流程。

1)  **流程图**

```mermaid
 graph TD
     A[开始] --> B(商品证照);
     subgraph "仓储部/采购部" as CP
         B --> C(首营品种);
         C --> D(提供相关资料);
         B --> E(收货流程);
         E --> D;
     end
     subgraph 质量部
         D -- 商品证照 --> F(核对相关证照);
         F -- 不合格 --> CP;
         F -- 没问题 --> G(检查相关证照);
         G -- 不相符 --> F;
     end
     subgraph 质量负责人
         G -- 相符 --> H(检查相关证照);
         H -- 不相符 --> G;
         H -- 相符 --> I(录入系统);
     end
     I --> J[结束];
```

2)  **流程及说明**

| 序号 | 部门/岗位       | 步骤           | 流程说明                                               | 管控点                       | 系统动作   |
| :--- | :-------------- | :------------- | :----------------------------------------------------- | :--------------------------- | :--------- |
| 1    | 仓储/采购部   | 提供相关资料   | 收集首营流程/收货流程供应商提供的商品证照              | 无                           |            |
| 2    | 仓储/采购部   | 核对相关证照   | 对收集到的证照进行核对                                 | 核对证照效期，证照名称等       |            |
| 3    | 质量部          | 检查相关证照   | 质管员对提供证照和实货进行核对，如不相符返回给仓储部及采购部 | 证照不相符则退回上一级，阻止流程 |            |
| 4    | 质量部          | 录入系统       | 对检查没问题的证照进行录入系统                       | 证照效期                     | 制单/修改 |
| 5    | 质量负责人    | 检查相关证照   | 质量负责人检查质管员上传的证照有效性，如过证照不相符，退回给质量部，相符则流程结束 | 证照不相符则退回上一级，阻止流程 |            |

#### 3.5.3 养护管理

● 本流程适用于以下场景:
√ 对于在库的药品，根据药品特性，制定合理的养护计划，并按计划进行定期养护的业务流程。

1)  **流程图**

```mermaid
 graph TD
    A[开始] --> B(维护商品的<br/>养护类型);
    B --> C(设置养护<br/>计划设置);
    subgraph 质量部
        C --> D(生成养护<br/>计划明细列表);
        D --> E(生成养护记录);
        E --> F(现场养护药品);
        F --> G(填写养护记录);
        G --> H(打印养护记录单);
        H -- 养护记录单 --> I{是否合格};
        I -- 不合格 --> J(不合格品流程);
        I -- 合格 --> K{是否可疑};
        K -- 是 --> L(可疑锁定);
        L --> M(质量复查单);
        K -- 否 --> N[结束];
        J --> N;
        M --> N;
     end
```

2)  **流程说明及管控点**

| 序号 | 部门/岗位 | 步骤               | 流程说明                                                           | 管控点                                                                                                                       | 系统动作       |
| :--- | :-------- | :----------------- | :----------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------- | :------------- |
| 1    | 质量部    | 维护商品的养护类型 | 养护员在商品资料中维护商品的养护类型，确定一般养护/重点养护          | 正常情况重点养护一个月养护一周期，一般养护每3个月养护一周期，首营品种，进口品种，近效期品种，冷藏、冷冻以及特殊品种均属于重点养护品种 |                |
| 2    | 质量部    | 设置养护计划设置   | 根据养护类型，药店的具体情况，设置养护计划设置，之后系统按照设置自动生成养护计划明细 | 同 1                                                                                                                         | 养护计划设置 |
| 3    | 质量部    | 生成养护计划明细   | 系统根据设置的养护计划设置，自动生成养护计划明细                     | 抽检数量设置，按照规定填写抽检数量                                                                                             | 养护计划明细 |
| 4    | 质量部    | 生成养护记录       | 养护员根据养护计划明细设置抽检数量自动生成养护记录                     |                                                                                                                              | 养护记录     |
| 5    | 质量部    | 打印养护记录单     | 根据养护记录打印养护记录到                                           |                                                                                                                              | 养护记录     |
| 6    | 质量部    | 现场养护药品       | 根据养护记录到到仓库现场养护药品，并记录养护情况                     | 外包装，质量状况，效期                                                                                                         | 养护记录单   |
| 7    | 质量部    | 填写养护记录单     | 根据现场养护情况填写养护记录单                                       | 外包装，质量状况，效期                                                                                                         | 养护记录单   |
| 8    | 质量部    | 维护养护记录       | 根据养护记录单在系统维护养护记录，养护内容养护方法等信息               | 质量可疑，不合格，可疑锁定的流程                                                                                             | 养护记录     |

3)  **打印格式 / 报表说明**

| 序号 | 打印格式/报表名称 | 用途         | 信息维度                                   |
| :--- | :---------------- | :----------- | :----------------------------------------- |
| 1    | 养护记录          | 备份养护信息 | 主要字段: 养护时间, 养护人, 养护方法, 养护结论 |

#### 3.5.4 不合格品管理

● 本流程适用于以下场景:
√ 对于在库的可疑药品，根据质量复查结论，进行不合格品销毁流程。

1)  **流程图**

```mermaid
graph TD
     A(验收流程) --> B(合格品信息);
     A1(养护流程) --> B;
     subgraph 质量管理员
         B --> C(制作不合格<br/>品确认单);
         C --> D(制作不合格<br/>品上报单);
         D --> E(制作不合格<br/>品销毁单);
     end
     subgraph 质量负责人
         D --> F(审核上报单);
         F -- 不通过 --> D;
         F -- 通过 --> G(审核不合格<br/>品销毁单);
         G -- 通过 --> H{是否特殊<br/>管理药品};
         G -- 不通过 --> E;
     end
     subgraph 药监部门
         H -- 是 --> I(现场监督<br/>销毁);
     end
     H -- 否 --> J(销毁);
     I --> J;
     J -- 不合格品销毁记录 --> K[结束];
```

2)  **流程说明及管控点**

| 序号 | 部门/岗位   | 步骤                   | 流程说明                                                             | 管控点                                                                                                                                 | 系统动作 |
| :--- | :---------- | :--------------------- | :------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------- | :------- |
| 1    | 质量管理员  | 制作不合格品确认单     | 根据可疑药品登记，质量复查单审核以后不合格品/库房发现不合格商品时制作不合格品确认 | 检查商品质量状况是否情况属实                                                                                                               | 制单     |
| 2    | 质量管理员  | 制作不合格品上报单     | 根据不合格品确认单制作不合格品上报单把不合格品上报给相关人员               | 1检查商品的外包装<br>2检查商品的有效期<br>3检查商品资质                                                                                  | 制单     |
| 3    | 质量负责人  | 审核上报单           | 审核质量部长审核过的不合格品确认单制作不合格品上报上报给相关领导及部门       | 1检查商品的外包装<br>2检查商品的有效期<br>3检查商品资质<br>如果是特殊药品需要上报药监局，其他药品上报公司领导即可，领导签字后扫描上传到业务系统 | 审核     |
| 4    | 质量管理员  | 制作不合格品销毁单     | 根据领导审核的不合格品上报单制作不合格品销毁单                           | 同 3                                                                                                                                   | 制单     |
| 5    | 质量负责人  | 审核不合格品销毁单     | 审核管理员制作的不合格品销毁单                                         | 同 3                                                                                                                                   | 审核     |
| 6    | 质量负责人  | 是否特殊管理药品       | 判断药品是否特殊管理药品是则需要药监局监督销毁                         | 是否特殊药品                                                                                                                           |          |
| 7    | 药监局      | 现场监督销毁         | 如果是特殊药品需要在药监局监督下进行销毁                             | 销毁时检查药品相关质量问题                                                                                                               |          |

3)  **打印格式 / 报表说明**

| 序号 | 打印格式/报表名称 | 用途         | 信息维度                                 |
| :--- | :---------------- | :----------- | :--------------------------------------- |
| 1    | 不合格品确认单    | 确定不合格品 | 主要字段: 商品信息, 不合格原因, 数量     |
| 2    | 不合格品上报单    | 记录上报     | 主要字段: 商品信息, 不合格原因, 数量     |
| 3    | 不合格品销毁单    | 记录销毁     | 主要字段: 商品信息, 不合格原因, 数量     |

## 第四章、产品评估

目前是一款具备基础功能的软件，无法满足药店的正常业务。

### 4.1 现有功能

*   系统支持单据执行时间、执行人等信息的记录，确保流程可追溯。
*   审批流程完善，支持多级审批。
*   部分单据包含必要字段，如采购订单的付款方式、末次进价、最低进价和末次供应商等。
*   成本核算方式支持加权移动平均或批次成本核算法。
*   解决了部分流程逻辑问题，如销售管理中的反复提取挂单功能。

### 4.2 补充功能

*   **GSP 模块：** 包括首营品种、陈列养护、质量复查、不合格销毁等单据，以及验收记录、销售记录、陈列记录、销毁记录、不合格品记录等。
*   **系统设置模块：** 包括系统参数（开关）、人员权限设置、付款方式设置等。
*   **报表：** 提供库存汇总、库存明细、零售收款报表、零售流水账、零售明细等多种报表。
*   **报表导出功能：** 支持将报表数据导出为 Excel 等格式，方便用户进行进一步分析。
*   **打印单据：** 入库单、损益管理、养护记录、盘点单等。

## 第五章、产品模块

1.  **基础资料：** 包括商品资料、供应商资料、门店资料和商品分类设置等。
2.  **入库管理：** 涵盖采购计划单、采购订单、收货单、验收单、入库单和采购退货单等。
3.  **库存管理：** 支持盘点、损益单、库存汇总、库存明细、货架位调整、进销存汇总和进销存明细等功能。
4.  **价格管理：** 包括调进价单、调售价单和打印价格签等。
5.  **零售管理：** 支持零售收银、零售收款报表、零售流水账和零售明细等。
6.  **会员管理：** 包括会员资料、会员销售明细、会员修改记录、会员换合卡设置、会员积分和折率设置以及积分抵现设置等。
7.  **促销管理：** 涵盖促销调价、促销买赠、近效期促销调价和优惠券设置等。
8.  **质量管理：** 包括首营品种、首营供应商、商品修改记录、供应商修改记录、质量复查单、可疑药品登记和台账、不合格品确认、上报和销毁以及陈列计划设置等。
9.  **数据分析：** 提供零售分析报表、库存分析报表、供应商分析报表、入库满足率报表、销售同比环比报表、库存周转报表、营业员提成报表、关联销售分析报表、调价分析报表、会员复购报表、进销存报表和促销前后对比报表等。
10. **系统设置：** 包括系统参数、部门设置、人员权限和付款方式设置等。

## 第六章、产品亮点

1.  **系统首页 BI：** 提供运营看板（老板驾驶舱），方便用户实时监控药店运营情况。
2.  **灵活的“产品化”系统：** 通过系统参数设置，实现不同客户的定制化需求。
3.  **功能全面：** 涵盖 GSP、会员、促销和医保接口模块，满足药店全方位管理需求。
4.  **使用方便、操作简洁：** 系统操作简单易懂，用户上手快，提高工作效率。
5.  **数据分析：** 提供多种数据分析报表，帮助用户优化运营策略，提升药店盈利能力。