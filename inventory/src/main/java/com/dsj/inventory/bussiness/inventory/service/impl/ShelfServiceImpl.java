package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.Inventory;
import com.dsj.inventory.bussiness.inventory.entity.InventoryChangeLog;
import com.dsj.inventory.bussiness.inventory.entity.Shelf;
import com.dsj.inventory.bussiness.inventory.enumeration.InventoryChangeTypeEnum;
import com.dsj.inventory.bussiness.inventory.enumeration.InventoryOrderTypeEnum;
import com.dsj.inventory.bussiness.inventory.mapper.ShelfMapper;
import com.dsj.inventory.bussiness.inventory.param.MoveGoodsParam;
import com.dsj.inventory.bussiness.inventory.param.QueryShelfParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateShelfParam;
import com.dsj.inventory.bussiness.inventory.service.InventoryChangeLogService;
import com.dsj.inventory.bussiness.inventory.service.InventoryService;
import com.dsj.inventory.bussiness.inventory.service.ShelfService;
import com.dsj.inventory.bussiness.inventory.vo.ShelfVo;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DozerUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.auth.entity.Log;
import com.pocky.transport.bussiness.auth.entity.OrgRModule;
import com.pocky.transport.bussiness.auth.entity.UserMenu;
import com.pocky.transport.bussiness.auth.enumeration.LogModuleEnum;
import com.pocky.transport.bussiness.auth.enumeration.LogTypeEnum;
import com.pocky.transport.bussiness.auth.service.ILogService;
import com.pocky.transport.bussiness.auth.service.IUserMenuService;
import com.pocky.transport.bussiness.auth.service.OrgRModuleService;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.diagnose.vo.OrganizationVo;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 进销存货架信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Slf4j
@Service
public class ShelfServiceImpl extends ServiceImpl<ShelfMapper, Shelf> implements ShelfService {

    @Autowired
    private DozerUtils dozerUtils;
    @Autowired
    private ILogService logService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private InventoryService inventoryService;
    @Autowired
    private InventoryChangeLogService inventoryChangeLogService;
    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @Autowired
    private OrgRModuleService orgRModuleService;
    @Autowired
    private IUserMenuService userMenuService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShelfVo saveOrUpdateShelf(SaveOrUpdateShelfParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        Organization organization = organizationService.getById(param.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        String type = LogTypeEnum.INSERT.getCode();
        if(CommonUtils.isNotEmpty(param.getId())) {
            Shelf byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            if(TrueEnum.TRUE.getCode().equals(byId.getType())){
                throw new BizException("操作失败，特殊货架不允许编辑");
            }
            type = LogTypeEnum.UPDATE.getCode();
        }
        if(!checkName(param.getId(),param.getOrganizationId(),param.getName(),param.getParentId())){
            throw new BizException("操作失败，名称重复");
        }
        if(CommonUtils.isEmpty(param.getParentId())){
            param.setLevel(1);
        }else {
            Shelf parent = this.getById(param.getParentId());
            param.setLevel(parent.getLevel()+1);
        }

        Shelf shelf = dozerUtils.map(param, Shelf.class);
        this.saveOrUpdate(shelf);
        //处理根父级级到本级id串
        String levelIds = "";
        if(CommonUtils.isNotEmpty(param.getParentId())){
            Shelf parent = this.getById(param.getParentId());
            if(CommonUtils.isEmpty(parent)){
                throw new BizException("操作失败，父级货架不存在");
            }
            levelIds = parent.getLevelIds() + "," +shelf.getId();
        }else {
            levelIds = shelf.getId().toString();
        }
        this.lambdaUpdate().set(Shelf::getLevelIds,levelIds).eq(Shelf::getId,shelf.getId()).update(new Shelf());
        //增加系统操作日志
        logService.save(Log.builder().moduleName(LogModuleEnum.DRUGSTORE.getCode()).type(type)
                .logInfo(organization.getName() + LogTypeEnum.getDesc(type) + "货架" + param.getName()).build());
        return dozerUtils.map(this.getById(shelf.getId()),ShelfVo.class);
    }

    @Override
    public void deleteShelf(Long id) {
        Shelf byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        //特殊货架不允许删除
        if(TrueEnum.FALSE.getCode().equals(byId.getDeleteFlag())){
            throw new BizException("操作失败，默认生成货架不允许删除");
        }
        //判断货架是否绑定商品，绑定商品不允许删除
        List<Inventory> inventoryList = inventoryService.lambdaQuery().eq(Inventory::getShelfId,byId.getId())
                .eq(Inventory::getOrganizationId,byId.getOrganizationId()).list();
        if(CommonUtils.isNotEmpty(inventoryList)){
            throw new BizException("操作失败，货架已绑定商品");
        }
        List<Shelf> children = this.lambdaQuery().eq(Shelf::getParentId,id).list();
        if(CommonUtils.isNotEmpty(children)){
            throw new BizException("操作失败，货架存在子集");
        }
        this.baseMapper.deleteById(id);
        Organization organization = organizationService.getById(byId.getOrganizationId());
        //增加系统操作日志
        logService.save(Log.builder().moduleName(LogModuleEnum.DRUGSTORE.getCode()).type(LogTypeEnum.DELETE.getCode())
                .logInfo(organization.getName() + LogTypeEnum.DELETE.getDesc() + "货架" + byId.getName()).build());
    }

    /**
     * 校验名称
     * @param id
     * @param organizationId
     * @param name
     * @param parentId
     * @return
     */
    @Override
    public Boolean checkName(Long id, Long organizationId, String name, Long parentId){
        LambdaQueryWrapper<Shelf> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Shelf::getOrganizationId, organizationId);
        wrapper.eq(Shelf::getName, name);
        if(CommonUtils.isNotEmpty(parentId)){
            wrapper.eq(Shelf::getParentId, parentId);
        }else {
            wrapper.isNull(Shelf::getParentId);
        }
        if (CommonUtils.isNotEmpty(id)){
            wrapper.ne(Shelf::getId,id);
        }
        return this.count(wrapper) < 1;
    }

    @Override
    public Page<ShelfVo> queryShelfPage(QueryShelfParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        if(TrueEnum.TRUE.getCode().equals(param.getIsAll())){
            param.setPage(1);
            param.setSize(Integer.MAX_VALUE);
            param.setNameLike(null);
            param.setParentId(null);
            param.setLevel(null);
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        OrgRModule orgRModule = orgRModuleService.lambdaQuery().eq(OrgRModule::getOrganizationId,param.getOrganizationId()).one();
        //获取组织权限是否展示特殊货架
        if(CommonUtils.isNotEmpty(orgRModule) && TrueEnum.FALSE.getCode().equals(orgRModule.getSpecialShelf())){
            param.setType(TrueEnum.FALSE.getCode());
        }else {
            //获取人员权限是否展示特殊货架
            UserMenu userMenu = userMenuService.lambdaQuery().eq(UserMenu::getOrganizationId,param.getOrganizationId())
                    .eq(UserMenu::getUserId,BaseContextHandler.getUserId())
                    .eq(UserMenu::getMenuCode,"baseInfo-shelfs-special").one();
            if(CommonUtils.isEmpty(userMenu)){
                param.setType(TrueEnum.FALSE.getCode());
            }
        }
        List<ShelfVo> list = this.baseMapper.queryShelfPage(page, param);
        List<ShelfVo> result = new ArrayList<>();
        if(TrueEnum.TRUE.getCode().equals(param.getIsAll()) && CommonUtils.isNotEmpty(list)){
            this.treeList(list,result);
        }else {
            result = list;
        }
        return page.setRecords(result);
    }

    /**
     * 整理树结构
     * @param list 数据
     * @param result 结果
     */
    private void treeList(List<ShelfVo> list, List<ShelfVo> result){
        result.addAll(list.stream().filter(o -> o.getLevel().equals(1)).collect(Collectors.toList()));
        for(ShelfVo l1 : result){
            List<ShelfVo> level2 = list.stream().filter(o -> o.getLevel().equals(2)
                    && o.getParentId().equals(l1.getId())).collect(Collectors.toList());
            if(CommonUtils.isNotEmpty(level2) && level2.size() > 0){
                l1.setChildren(level2);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchMoveShelf(Long id, List<MoveGoodsParam> params) {
        Shelf byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，目标货架不存在");
        }
        Organization organization = organizationService.getById(byId.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        for(MoveGoodsParam param : params){
            //现有库存
            Inventory oldInventory = inventoryService.getById(param.getInventoryId());
            if(CommonUtils.isEmpty(oldInventory)){
                throw new BizException("操作失败，" + param.getInventoryId() + "库存Id不存在");
            }
            OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(oldInventory.getOrganizationMedicineId());
            if(oldInventory.getInventoryAmount() < param.getAmount()){
                throw new BizException("操作失败，" + organizationMedicine.getName() + "库存不足");
            }
            //目标库存
            Inventory targetInventory = inventoryService.lambdaQuery().eq(Inventory::getBatch,oldInventory.getBatch())
                    .eq(Inventory::getBatchNumber,oldInventory.getBatchNumber()).eq(Inventory::getShelfId,id).one();
            if(CommonUtils.isEmpty(targetInventory)){
                //新增库存
                targetInventory = Inventory.builder().organizationId(oldInventory.getOrganizationId())
                        .organizationMedicineId(oldInventory.getOrganizationMedicineId()).produceDate(oldInventory.getProduceDate())
                        .userDate(oldInventory.getUserDate()).inventoryAmount(param.getAmount())
                        .costPrice(oldInventory.getCostPrice()).batchNumber(oldInventory.getBatchNumber())
                        .shelfId(id).batch(oldInventory.getBatch()).incomingGoodsId(oldInventory.getIncomingGoodsId())
                        .incomingDate(oldInventory.getIncomingDate()).build();

            }else {
                //编辑库存数量
                BigDecimal inventoryAmount = BigDecimal.valueOf(targetInventory.getInventoryAmount())
                        .add(BigDecimal.valueOf(param.getAmount()));
                targetInventory.setInventoryAmount(inventoryAmount.doubleValue());
            }
            inventoryService.saveOrUpdate(targetInventory);
            BigDecimal inventoryAmount = BigDecimal.valueOf(oldInventory.getInventoryAmount())
                    .subtract(BigDecimal.valueOf(param.getAmount()));
            inventoryService.lambdaUpdate().eq(Inventory::getId,oldInventory.getId())
                    .set(Inventory::getInventoryAmount,inventoryAmount).update(new Inventory());
            //库存变动记录
            //原有库存减少记录
            InventoryChangeLog decreaseChangeLog = InventoryChangeLog.builder().organizationId(oldInventory.getOrganizationId())
                    .organizationMedicineId(oldInventory.getOrganizationMedicineId()).inventoryId(oldInventory.getId())
                    .orderType(InventoryOrderTypeEnum.MOVE.getCode()).changeType(InventoryChangeTypeEnum.DECREASE.getCode())
                    .changeAmount(param.getAmount()).batchNumber(oldInventory.getBatchNumber()).shelfId(oldInventory.getShelfId())
                    .batch(oldInventory.getBatch()).costPrice(oldInventory.getCostPrice())
                    .executeId(BaseContextHandler.getUserId()).build();
            inventoryChangeLogService.save(decreaseChangeLog);
            //目标库存增加记录
            InventoryChangeLog increaseChangeLog = InventoryChangeLog.builder().organizationId(targetInventory.getOrganizationId())
                    .organizationMedicineId(targetInventory.getOrganizationMedicineId()).inventoryId(targetInventory.getId())
                    .orderType(InventoryOrderTypeEnum.MOVE.getCode()).changeType(InventoryChangeTypeEnum.INCREASE.getCode())
                    .changeAmount(param.getAmount()).batchNumber(targetInventory.getBatchNumber()).shelfId(id)
                    .batch(targetInventory.getBatch()).costPrice(targetInventory.getCostPrice())
                    .executeId(BaseContextHandler.getUserId()).build();
            inventoryChangeLogService.save(increaseChangeLog);
        }
    }

    @Override
    public void initializeShelf() {
        //获取没有初始化货架的药房
        List<OrganizationVo> organizationList = this.baseMapper.queryInitializationOrganization();
        if(CommonUtils.isNotEmpty(organizationList)){
            log.info("=================初始化货位开始，共计{}个组织=================",organizationList.size());
            int i = 0;
            for(OrganizationVo organization : organizationList){
                i++;
                log.info("===============初始化{}货位{}===================",organization.getName(),i);
                Shelf shelf1 = Shelf.builder().organizationId(organization.getId()).name("默认货位")
                        .type(TrueEnum.FALSE.getCode()).level(1).deleteFlag(TrueEnum.FALSE.getCode()).build();
                this.save(shelf1);
                this.lambdaUpdate().eq(Shelf::getId,shelf1.getId())
                        .set(Shelf::getLevelIds,shelf1.getId().toString()).update(new Shelf());
                Shelf shelf2 = Shelf.builder().organizationId(organization.getId()).name("特殊货架")
                        .type(TrueEnum.TRUE.getCode()).level(1).deleteFlag(TrueEnum.FALSE.getCode()).build();
                this.save(shelf2);
                this.lambdaUpdate().eq(Shelf::getId,shelf2.getId())
                        .set(Shelf::getLevelIds,shelf2.getId().toString()).update(new Shelf());
            }
            log.info("=================初始化货位完毕，共计{}个组织=================",i);
        }
    }


}
