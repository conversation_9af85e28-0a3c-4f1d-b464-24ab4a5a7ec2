package com.dsj.inventory.bussiness.quality.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 拒收单商品明细表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("jxc_rejection_order_goods")
public class RejectionOrderGoods extends Entity {

    /**
     * 拒收单ID
     */
    private Long rejectionOrderId;

    /**
     * 拒收单号
     */
    private String rejectionOrderCode;

    /**
     * 来源单据商品明细ID
     */
    private Long sourceOrderGoodsId;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 批号
     */
    private String batchNo;

    /**
     * 有效期至
     */
    private LocalDate expireDate;

    /**
     * 拒收数量
     */
    private BigDecimal rejectionQuantity;

    /**
     * 拒收原因
     */
    private String rejectionReason;
} 