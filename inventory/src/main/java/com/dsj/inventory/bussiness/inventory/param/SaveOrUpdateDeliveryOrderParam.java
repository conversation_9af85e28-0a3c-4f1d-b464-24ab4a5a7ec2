package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 退货单
 * <AUTHOR>
 * @date 2023/04/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateDeliveryOrderParam", description="新增or编辑 退货单参数")
public class SaveOrUpdateDeliveryOrderParam {

    @ApiModelProperty(hidden = true, value = "id,更新是传入")
    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "入库单Id")
    private Long orderId;

    @ApiModelProperty(hidden = true,value = "入库单号")
    private String orderCode;

    @ApiModelProperty(hidden = true,value = "退货单号")
    private String code;

    @ApiModelProperty(value = "开票日期")
    private LocalDateTime billDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "处理状态 0暂存 1退货中 2已退货 3已取消")
    private Integer dealState;

    @ApiModelProperty(value = "私域供应商Id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "私域供应商销售人员Id")
    private Long organizationSupplierMemberId;

    @ApiModelProperty(value = "运输方式")
    private String transportWay;

    @ApiModelProperty(value = "运输单号")
    private String transportOrderCode;

    @ApiModelProperty(value = "退货总额")
    private Integer returnTotalPrice;

    @ApiModelProperty(hidden = true,value = "退货成本金额")
    private Integer returnCostTotalPrice;

    @ApiModelProperty(hidden = true,value = "进退货差价（退货成本金额-退货总额）")
    private Integer disparityPrice;

    @ApiModelProperty(hidden = true,value = "退货原因")
    private String reason;

    @ApiModelProperty(value = "商品明细")
    private List<SaveOrUpdateDeliveryOrderGoodsParam> goodsList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
