package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购订单
 * <AUTHOR>
 * @date 2023/04/06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdatePurchaseOrderParam", description="新增or编辑 采购订单参数")
public class SaveOrUpdatePurchaseOrderParam {

    @ApiModelProperty(hidden = true, value = "id,更新是传入")
    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "计划Id")
    private Long planId;

    @ApiModelProperty(hidden = true,value = "计划单号")
    private String orderCode;

    @ApiModelProperty(hidden = true,value = "订单单号")
    private String code;

    @ApiModelProperty(value = "开票日期")
    private LocalDateTime billDate;

    @ApiModelProperty(value = "预计收货日期")
    private LocalDateTime expectReceiveDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "处理状态 0暂存 1待收货 2已完成")
    private Integer dealState;

    @ApiModelProperty(value = "私域供应商Id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "采购员Id")
    private Long purchaserId;

    @ApiModelProperty(value = "总金额")
    private Integer totalPrice;

    @ApiModelProperty(hidden = true,value = "商品种类")
    private String goodsTypes;

    @ApiModelProperty(hidden = true,value = "采购内容")
    private String purchaseContent;

    @ApiModelProperty(value = "商品明细")
    private List<SaveOrUpdatePurchaseOrderGoodsParam> goodsList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
