package com.dsj.inventory.bussiness.inventory.service;

import com.dsj.inventory.bussiness.inventory.dto.AutoGenerateGoodsInfo;
import com.dsj.inventory.bussiness.inventory.vo.PurchasePlanDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchasePlanGoodsVo;

import java.util.List;

/**
 * 自动生成采购计划服务
 * 实现基于库存规则的采购计划自动生成功能
 */
public interface AutoGeneratePurchasePlanService {
    
    /**
     * 自动生成采购计划
     * 根据库存上下限规则自动生成一条或多条采购计划单（按供应商分组）
     * @param organizationId 组织ID
     * @return 生成的采购计划详情
     */
    PurchasePlanDetailVo autoGeneratePurchasePlan(Long organizationId);
    
    /**
     * 获取需要采购的商品列表（库存低于下限的商品）
     * @param organizationId 组织ID
     * @return 低库存商品信息列表
     */
    List<AutoGenerateGoodsInfo> getLowStockGoods(Long organizationId);
    
    /**
     * 批量计算采购计划明细
     * @param organizationMedicineIds 私域商品ID列表
     * @param organizationId 组织ID
     * @return 采购计划商品明细列表
     */
    List<PurchasePlanGoodsVo> calculatePurchasePlanGoods(List<Long> organizationMedicineIds, Long organizationId);
    
    /**
     * 预览自动生成的采购计划
     * 不实际保存，仅返回预览结果
     * @param organizationId 组织ID
     * @return 预览的采购计划详情
     */
    PurchasePlanDetailVo previewAutoGeneratedPlan(Long organizationId);
    
    /**
     * 按供应商分组生成多个采购计划
     * @param goodsInfoList 商品信息列表
     * @param organizationId 组织ID
     * @return 按供应商分组的采购计划列表
     */
    List<PurchasePlanDetailVo> generatePlansBySupplier(List<AutoGenerateGoodsInfo> goodsInfoList, Long organizationId);
    
    /**
     * 验证自动生成的前置条件
     * @param organizationId 组织ID
     * @return 验证结果信息
     */
    AutoGenerateValidationResult validateAutoGenerate(Long organizationId);
    
    /**
     * 获取自动生成的统计信息
     * @param organizationId 组织ID
     * @return 统计信息
     */
    AutoGenerateStatistics getAutoGenerateStatistics(Long organizationId);
    
    /**
     * 自动生成验证结果内部类
     */
    class AutoGenerateValidationResult {
        private Boolean valid;              // 是否通过验证
        private String message;             // 验证信息
        private List<String> errors;        // 错误列表
        private Integer lowStockCount;      // 低库存商品数量
        private Integer validGoodsCount;    // 有效商品数量
        
        public AutoGenerateValidationResult() {}
        
        public AutoGenerateValidationResult(Boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        // Getter和Setter方法
        public Boolean getValid() { return valid; }
        public void setValid(Boolean valid) { this.valid = valid; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public List<String> getErrors() { return errors; }
        public void setErrors(List<String> errors) { this.errors = errors; }
        
        public Integer getLowStockCount() { return lowStockCount; }
        public void setLowStockCount(Integer lowStockCount) { this.lowStockCount = lowStockCount; }
        
        public Integer getValidGoodsCount() { return validGoodsCount; }
        public void setValidGoodsCount(Integer validGoodsCount) { this.validGoodsCount = validGoodsCount; }
    }
    
    /**
     * 自动生成统计信息内部类
     */
    class AutoGenerateStatistics {
        private Integer totalGoodsCount;        // 总商品数量
        private Integer lowStockGoodsCount;     // 低库存商品数量
        private Integer noSupplierGoodsCount;   // 无供应商商品数量
        private Integer validGoodsCount;        // 可生成采购计划商品数量
        private Integer supplierCount;          // 涉及供应商数量
        private Double totalRecommendQuantity;  // 推荐采购总数量
        private Long totalRecommendAmount;      // 推荐采购总金额（分）
        
        public AutoGenerateStatistics() {}
        
        // Getter和Setter方法
        public Integer getTotalGoodsCount() { return totalGoodsCount; }
        public void setTotalGoodsCount(Integer totalGoodsCount) { this.totalGoodsCount = totalGoodsCount; }
        
        public Integer getLowStockGoodsCount() { return lowStockGoodsCount; }
        public void setLowStockGoodsCount(Integer lowStockGoodsCount) { this.lowStockGoodsCount = lowStockGoodsCount; }
        
        public Integer getNoSupplierGoodsCount() { return noSupplierGoodsCount; }
        public void setNoSupplierGoodsCount(Integer noSupplierGoodsCount) { this.noSupplierGoodsCount = noSupplierGoodsCount; }
        
        public Integer getValidGoodsCount() { return validGoodsCount; }
        public void setValidGoodsCount(Integer validGoodsCount) { this.validGoodsCount = validGoodsCount; }
        
        public Integer getSupplierCount() { return supplierCount; }
        public void setSupplierCount(Integer supplierCount) { this.supplierCount = supplierCount; }
        
        public Double getTotalRecommendQuantity() { return totalRecommendQuantity; }
        public void setTotalRecommendQuantity(Double totalRecommendQuantity) { this.totalRecommendQuantity = totalRecommendQuantity; }
        
        public Long getTotalRecommendAmount() { return totalRecommendAmount; }
        public void setTotalRecommendAmount(Long totalRecommendAmount) { this.totalRecommendAmount = totalRecommendAmount; }
    }
}