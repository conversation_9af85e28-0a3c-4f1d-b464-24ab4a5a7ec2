package com.dsj.inventory.bussiness.quality.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "MedicineFirstBusinessVO", description = "药品首营审批视图对象")
public class MedicineFirstBusinessVO {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "关联组织药品私域表ID")
    private Long orgMedicineMappingId;
    
    @ApiModelProperty(value = "药品名称")
    private String medicineName;
    
    @ApiModelProperty(value = "药品规格")
    private String medicineSpec;
    
    @ApiModelProperty(value = "药品生产厂家")
    private String manufacturer;
    
    @ApiModelProperty(value = "供应商ID")
    private Long supplierId;
    
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    
    @ApiModelProperty(value = "审批步骤")
    private Integer approvalStep;
    
    @ApiModelProperty(value = "审批步骤描述")
    private String approvalStepDesc;
    
    @ApiModelProperty(value = "操作类型")
    private Integer actionType;
    
    @ApiModelProperty(value = "操作类型描述")
    private String actionTypeDesc;
    
    @ApiModelProperty(value = "审批序号")
    private Integer approvalSequence;
    
    @ApiModelProperty(value = "当前审批角色代码")
    private String currentApproverRole;
    
    @ApiModelProperty(value = "审批角色描述")
    private String currentApproverRoleDesc;

    @ApiModelProperty(value = "申请时间")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "审核人ID (关联 sys_user 表)")
    private Long reviewerId;
    
    @ApiModelProperty(value = "审核人名称")
    private String reviewerName;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime reviewTime;

    @ApiModelProperty(value = "审批状态 (0-草稿, 1-审核中, 2-已通过, 3-已驳回, 4-已撤回)")
    private Integer status;
    
    @ApiModelProperty(value = "审批状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "审批意见")
    private String approvalOpinion;
    
    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty(value = "创建人ID")
    private Long createUser;
    
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;
} 