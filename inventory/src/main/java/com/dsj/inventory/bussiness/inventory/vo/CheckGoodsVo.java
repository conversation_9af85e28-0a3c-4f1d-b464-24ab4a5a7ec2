package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 盘点计划商品明细
 * <AUTHOR>
 * @date 2023/04/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="CheckGoodsVo", description="盘点计划商品明细实体")
@ToString(callSuper = true)
public class CheckGoodsVo {

    private Long id;

    @ApiModelProperty(value = "盘点计划Id")
    private Long checkId;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "库存Id")
    private Long inventoryId;

    @ApiModelProperty(value = "通用名")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    private String generalName;

    @ApiModelProperty(value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(value = "包装单位")
    private String packUnit;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "剂型")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(value = "条形码")
    private String barCode;

    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(value = "生产日期")
    private LocalDateTime produceDate;

    @ApiModelProperty(value = "有效期至")
    private LocalDateTime userDate;

    @ApiModelProperty(value = "批号")
    private String batchNumber;

    @ApiModelProperty(value = "货位Id")
    private Long shelfId;

    @ApiModelProperty(value = "货位名称")
    private String shelfName;

    @ApiModelProperty(value = "批次（入库单号）")
    private String batch;

    @ApiModelProperty(value = "当前零售价（私域商品价格）")
    private Integer price;

    @ApiModelProperty(value = "库存数量")
    private Double inventoryAmount;

    @ApiModelProperty(value = "盘点数量")
    private Double checkAmount;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
