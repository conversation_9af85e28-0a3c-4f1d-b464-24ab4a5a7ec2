package com.dsj.inventory.bussiness.purchaseplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 进销存采购计划单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_purchase_plan")
@ApiModel(value="PurchasePlan", description="进销存采购计划单")
public class PurchasePlan extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织Id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "计划单号")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "开票日期")
    @TableField("bill_date")
    private LocalDateTime billDate;

    @ApiModelProperty(value = "预计收货日期")
    @TableField("expect_receive_date")
    private LocalDateTime expectReceiveDate;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "处理状态 0待执行 1已执行")
    @TableField("deal_state")
    private Integer dealState;

    @ApiModelProperty(value = "是否自动生成：0-手动创建，1-自动生成")
    @TableField("auto_generated")
    private Integer autoGenerated;

    @ApiModelProperty(value = "GSP校验状态：0-未校验，1-校验通过，-1-校验不通过")
    @TableField("gsp_check_status")
    private Integer gspCheckStatus;

    @ApiModelProperty(value = "GSP校验信息，记录校验结果详情")
    @TableField("check_message")
    private String checkMessage;

    @ApiModelProperty(value = "私域供应商Id")
    @TableField("organization_supplier_id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "总金额")
    @TableField("total_price")
    private Integer totalPrice;

    @ApiModelProperty(value = "商品种类")
    @TableField("goods_types")
    private String goodsTypes;

    @ApiModelProperty(value = "采购内容")
    @TableField("purchase_content")
    private String purchaseContent;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
