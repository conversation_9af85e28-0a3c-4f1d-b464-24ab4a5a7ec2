package com.dsj.inventory.bussiness.inventory.enumeration;

/**
 * 付款方式配置键枚举
 * 定义 jxc_payment_config 表中 config_key 字段的预设值
 */
public enum PaymentConfigKeyEnum {

    APP_ID("app_id", "应用ID"),
    MCH_ID("mch_id", "商户ID"),
    API_SECRET("api_secret", "API密钥"),
    NOTIFY_URL("notify_url", "异步通知地址"),
    CERT_PATH("cert_path", "证书路径"),
    PRIVATE_KEY_PATH("private_key_path", "私钥路径"),
    PUBLIC_KEY_PATH("public_key_path", "公钥路径"),
    SERVER_URL("server_url", "支付网关地址");

    private final String key;
    private final String description;

    PaymentConfigKeyEnum(String key, String description) {
        this.key = key;
        this.description = description;
    }

    public String getKey() {
        return key;
    }

    public String getDescription() {
        return description;
    }
    
    /**
     * 根据key获取枚举值
     */
    public static PaymentConfigKeyEnum getByKey(String key) {
        if (key == null) {
            return null;
        }
        
        for (PaymentConfigKeyEnum item : values()) {
            if (item.getKey().equals(key)) {
                return item;
            }
        }
        
        return null;
    }
} 