package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.WarningLog;
import com.dsj.inventory.bussiness.inventory.param.QueryWarningLogParam;
import com.dsj.inventory.bussiness.inventory.vo.WarningLogVo;
import com.dsj.inventory.common.enumeration.TrueEnum;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存预警记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface WarningLogMapper extends BaseMapper<WarningLog> {

    /**
     * 获取预警记录分页列表
     * @param page
     * @param param
     * @return
     */
    List<WarningLogVo> queryWarningLogPage(@Param("page") Page page, @Param("param") QueryWarningLogParam param);

    /**
     * 删除已失效的滞销预警
     */
    void deleteUnsalableWarningLog(@Param("organizationId") Long organizationId, @Param("unsalableDay") Integer unsalableDay);

    /**
     * 删除已失效的近效期预警
     */
    void deleteBecomeDueWarningLog(@Param("organizationId") Long organizationId, @Param("becomeDueDay") Integer becomeDueDay);
    
    /**
     * 根据类型和药品查询预警记录
     * @param type 预警类型
     * @param organizationId 组织ID
     * @param organizationMedicineId 药品ID
     * @return 预警记录
     */
    default WarningLog findWarningLogByTypeAndMedicine(Integer type, Long organizationId, Long organizationMedicineId) {
        LambdaQueryWrapper<WarningLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarningLog::getType, type)
                .eq(WarningLog::getOrganizationId, organizationId)
                .eq(WarningLog::getOrganizationMedicineId, organizationMedicineId)
                .eq(WarningLog::getIsDelete, TrueEnum.FALSE.getCode());
        return this.selectOne(queryWrapper);
    }
    
    /**
     * 将预警记录标记为已删除
     * @param id 预警记录ID
     * @return 更新结果
     */
    default int markWarningLogAsDeleted(Long id) {
        LambdaUpdateWrapper<WarningLog> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(WarningLog::getId, id)
                .set(WarningLog::getIsDelete, TrueEnum.TRUE.getCode());
        return this.update(new WarningLog(), updateWrapper);
    }

}
