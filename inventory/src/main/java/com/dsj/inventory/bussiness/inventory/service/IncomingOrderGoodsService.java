package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.IncomingOrderGoods;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存入库单商品明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface IncomingOrderGoodsService extends IService<IncomingOrderGoods> {

    /**
     * 获取入库商品明细列表
     * @param orderId
     * @return
     */
    List<IncomingOrderGoodsVo> getIncomingOrderGoodsList(Long orderId);

    /**
     * 统计私域商品已入库数据
     * @param organizationMedicineId
     * @return
     */
    List<IncomingOrderGoodsVo> countIncomingOrderGoods(Long organizationMedicineId);

    /**
     * 根据订单ID将商品明细标记为已删除
     * @param orderId 订单ID
     */
    void markAsDeletedByOrderId(Long orderId);

}
