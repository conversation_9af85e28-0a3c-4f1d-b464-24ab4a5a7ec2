package com.dsj.inventory.framework.config;

import com.dsj.inventory.common.utils.TencentIMUtils;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Description : Im 配置
 * @Date : 2022-07-25 16:23
 */
@Configuration
@Data
public class TencentImConfig {

    @Value("${pocky.im.sdkAppId}")
    private Long SDKAPPID = 0L;
    /**
     * 默认时间：7 x 24 x 60 x 60 = 604800 = 7 天
     */
    @Value("${pocky.im.expiretime}")
    private int EXPIRETIME = 604800;

    @Value("${pocky.im.secretkey}")
    private String SECRETKEY = "";

    @Bean
    public TencentIMUtils tencentIMUtils(){
        TencentIMUtils tencentIMUtils = new TencentIMUtils();
        tencentIMUtils.setSDKAPPID(SDKAPPID);
        tencentIMUtils.setEXPIRETIME(EXPIRETIME);
        tencentIMUtils.setSECRETKEY(SECRETKEY);
        return tencentIMUtils;
    }

}
