package com.dsj.inventory.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * IM 请求内容的具体信息
 * <AUTHOR>
 * @date 2022/10/31 12:39
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class IMMsgBody {

    public static final String TIMTextElem = "TIMTextElem";
    public static final String TIMLocationElem = "TIMLocationElem";
    public static final String TIMFaceElem = "TIMFaceElem";
    public static final String TIMCustomElem = "TIMCustomElem";
    public static final String TIMSoundElem = "TIMSoundElem";
    public static final String TIMImageElem = "TIMImageElem";
    public static final String TIMFileElem = "TIMFileElem";
    public static final String TIMVideoFileElem = "TIMVideoFileElem";

    /**
     * 必填
     * TIM 消息对象类型，目前支持的消息对象包括：
     * TIMTextElem（文本消息）
     * TIMLocationElem（位置消息）
     * TIMFaceElem（表情消息）
     * TIMCustomElem（自定义消息）
     * TIMSoundElem（语音消息）
     * TIMImageElem（图像消息）
     * TIMFileElem（文件消息）
     * TIMVideoFileElem（视频消息）
     */
    private String MsgType;

    /**
     * 必填
     *对于每种 MsgType 用不同的 MsgContent 格式，具体可参考
     */
    private MsgContent MsgContent;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class MsgContent{
        /**
         * 正常存放问诊单号 放json 格式
         */
        private String Data;
        /**
         * 描述，消息在主题，离线消息
         */
        private String Desc;
        /**
         * 扩展
         */
        private String Ext;

    }

}
