package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.GainsLossesOrder;
import com.dsj.inventory.bussiness.inventory.param.QueryGainsLossesOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateGainsLossesOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存损溢单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface GainsLossesOrderService extends IService<GainsLossesOrder> {

    /**
     * 新增、编辑 损溢单
     * @param param
     * @return
     */
    GainsLossesOrderDetailVo saveOrUpdateGainsLossesOrder(SaveOrUpdateGainsLossesOrderParam param);

    /**
     * 删除 损溢单
     * @param id
     */
    void deleteGainsLossesOrder(Long id);

    /**
     * 获取损溢单分页列表
     * @param param
     * @return
     */
    Page<GainsLossesOrderVo> queryGainsLossesOrderPage(QueryGainsLossesOrderParam param);

    /**
     * 获取损溢单列表详情
     * @param id
     * @return
     */
    GainsLossesOrderDetailVo getGainsLossesOrderById(Long id);

    /**
     * 提交损溢单
     * @param id
     */
    void submitGainsLossesOrder(Long id);

    /**
     * 导出损溢单列表
     * @param param
     * @return
     */
    List<GainsLossesOrderExportVo> queryGainsLossesOrderList(QueryGainsLossesOrderParam param);

    /**
     * 删除组织的损溢单
     * @param organizationId
     */
    void deleteGainsLossesOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天损溢单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getGainsLossesOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);

}
