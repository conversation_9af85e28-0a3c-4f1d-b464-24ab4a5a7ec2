package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesActivityParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateSalesActivityParam;
import com.dsj.inventory.bussiness.inventory.service.SalesActivityService;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 进销存销售活动 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@RestController
@Api(value = "SalesActivityController", tags = "进销存销售活动api")
public class SalesActivityController {

    @Autowired
    private SalesActivityService salesActivityService;

    /*新增销售活动*/
    @OrgAuthMode(strict = true,strictUserMenus = true
            ,menuCodeArray = {"hospital-mall-promotion-activity","drugstore-mall-promotion-activity",
            "drugstore-erp-promotion-product","drugstore-erp-promotion-member-day","drugstore-erp-promotion-order",
            "drugstore-erp-promotion-coupon","supplier-wholesale-promotion-activity"})
    @PostMapping("/sales-activity")
    @ApiOperation(value = "新增销售活动", notes = "新增销售活动")
    public ResponseEntity<SalesActivityVo> saveSalesActivity(@RequestBody SaveOrUpdateSalesActivityParam param){
        SalesActivityVo salesActivityVo = salesActivityService.saveOrUpdateSalesActivity(param);
        return Res.success(salesActivityVo);
    }

    /*编辑销售活动*/
    @OrgAuthMode(strict = false,strictUserMenus = true
            ,menuCodeArray = {"hospital-mall-promotion-activity","drugstore-mall-promotion-activity",
            "drugstore-erp-promotion-product","drugstore-erp-promotion-member-day","drugstore-erp-promotion-order",
            "drugstore-erp-promotion-coupon","supplier-wholesale-promotion-activity"})
    @PutMapping("/sales-activity/{id}")
    @ApiOperation(value = "编辑销售活动", notes = "编辑销售活动")
    public ResponseEntity<SalesActivityVo> updateSalesActivity(@PathVariable Long id, @RequestBody SaveOrUpdateSalesActivityParam param){
        param.setId(id);
        SalesActivityVo salesActivityVo = salesActivityService.saveOrUpdateSalesActivity(param);
        return Res.success(salesActivityVo);
    }

    /*删除销售活动*/
    @OrgAuthMode(strict = false,strictUserMenus = true
            ,menuCodeArray = {"hospital-mall-promotion-activity","drugstore-mall-promotion-activity",
            "drugstore-erp-promotion-product","drugstore-erp-promotion-member-day","drugstore-erp-promotion-order",
            "drugstore-erp-promotion-coupon","supplier-wholesale-promotion-activity"})
    @DeleteMapping("/sales-activity/{id}")
    @ApiOperation(value = "删除销售活动", notes = "删除销售活动")
    public ResponseEntity<Void> deleteSalesActivity(@PathVariable Long id){
        SaveOrUpdateSalesActivityParam param = SaveOrUpdateSalesActivityParam.builder().isDelete(TrueEnum.TRUE.getCode()).id(id).build();
        salesActivityService.saveOrUpdateSalesActivity(param);
        return Res.success();
    }

    /*获取销售活动列表*/
    @GetMapping("/sales-activity")
    @ApiOperation(value = "获取销售活动列表", notes = "获取销售活动列表")
    public ResponseEntity<List<SalesActivityVo>> querySalesActivityList(QuerySalesActivityParam param){
        Page<SalesActivityVo> page = salesActivityService.querySalesActivityPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取销售活动详情*/
    @GetMapping("/sales-activity/{id}")
    @ApiOperation(value = "获取销售活动详情", notes = "获取销售活动详情")
    public ResponseEntity<SalesActivityVo> querySalesActivity(@PathVariable Long id){
        return Res.success(salesActivityService.getSalesActivityById(id));
    }

    /*暂停/启用销售活动*/
    @OrgAuthMode(strict = false,strictUserMenus = true
            ,menuCodeArray = {"hospital-mall-promotion-activity","drugstore-mall-promotion-activity",
            "drugstore-erp-promotion-product","drugstore-erp-promotion-member-day","drugstore-erp-promotion-order",
            "drugstore-erp-promotion-coupon","supplier-wholesale-promotion-activity"})
    @PostMapping("/sales-activity/{id}/action-pause")
    @ApiOperation(value = "暂停/启用销售活动", notes = "暂停/启用销售活动")
    public ResponseEntity<Void> pauseSalesActivity(@PathVariable Long id){
        salesActivityService.pauseSalesActivity(id);
        return Res.success();
    }

    /*终止销售活动*/
    @OrgAuthMode(strict = false,strictUserMenus = true
            ,menuCodeArray = {"hospital-mall-promotion-activity","drugstore-mall-promotion-activity",
            "drugstore-erp-promotion-product","drugstore-erp-promotion-member-day","drugstore-erp-promotion-order",
            "drugstore-erp-promotion-coupon","supplier-wholesale-promotion-activity"})
    @PostMapping("/sales-activity/{id}/action-stop")
    @ApiOperation(value = "终止销售活动", notes = "终止销售活动")
    public ResponseEntity<Void> stopSalesActivity(@PathVariable Long id){
        salesActivityService.stopSalesActivity(id);
        return Res.success();
    }

    /*销售活动定时器模拟*/
    @PostMapping("/sales-activity/action-timer")
    @ApiOperation(value = "销售活动定时器模拟", notes = "销售活动定时器模拟")
    public ResponseEntity<Void> salesActivityTimer(){
        salesActivityService.batchHandleSalesActivityState();
        return Res.success();
    }

}
