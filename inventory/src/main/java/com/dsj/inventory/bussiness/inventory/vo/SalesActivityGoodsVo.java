package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 销售活动商品明细
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SalesActivityGoodsVo", description="销售活动商品明细实体")
@ToString(callSuper = true)
public class SalesActivityGoodsVo {

    private Long id;

    @ApiModelProperty(value = "活动Id")
    private Long activityId;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "公域商品Id")
    private Long medicineId;

    @ApiModelProperty(value = "销售数量")
    private Double salesAmount;

    @ApiModelProperty(value = "通用名")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    private String generalName;

    @ApiModelProperty(value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(value = "包装单位")
    private String packUnit;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "剂型")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(value = "是否处方 0：否  1：是")
    private Integer isPrescription;

    @ApiModelProperty(value = "药品单价")
    private Integer medicinePrice;

    @ApiModelProperty(value = "线上单价（商城）")
    private Integer onlinePrice;

    @ApiModelProperty(value = "线下单价（进销存）'")
    private Integer offlinePrice;

    @ApiModelProperty(value = "批发价格")
    private Integer wholesalePrice;

    @ApiModelProperty(value = "药品库存")
    private Double medicineRepertory;

    @ApiModelProperty(value = "线上医院库存展示百分比")
    private Integer hospitalOnlinePercentage;

    @ApiModelProperty(value = "商城库存展示百分比")
    private Integer shoppingPercentage;

    @ApiModelProperty(value = "药品图片，默认取第一张")
    private String medicineFilePath;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}


