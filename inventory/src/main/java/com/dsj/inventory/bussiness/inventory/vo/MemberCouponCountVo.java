package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/1/12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="MemberCouponCountVo", description="会员优惠券统计响应实体")
public class MemberCouponCountVo {

    @ApiModelProperty(value = "总数")
    private Integer totalAmount;

    @ApiModelProperty(value = "未使用")
    private Integer notUseAmount;

    @ApiModelProperty(value = "已使用")
    private Integer useAmount;

    @ApiModelProperty(value = "已过期")
    private Integer expireAmount;

}
