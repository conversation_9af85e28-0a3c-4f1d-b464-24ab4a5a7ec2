-- =====================================================
-- 采购计划功能增强数据库回滚脚本
-- 版本: V1.1.0 Rollback
-- 描述: 回滚采购计划模块增强功能的数据库变更
-- 作者: AI Assistant
-- 日期: 2025-06-23
-- 警告: 执行此脚本将删除新增字段和索引，请谨慎操作！
-- =====================================================

-- =====================================================
-- 1. 删除采购计划表新增索引
-- =====================================================

-- 删除自动生成标识索引
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan' 
  AND index_name = 'idx_auto_generated';

SET @sql = IF(@index_exists > 0, 
    'DROP INDEX idx_auto_generated ON jxc_purchase_plan;',
    'SELECT ''索引 idx_auto_generated 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除GSP校验状态索引
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan' 
  AND index_name = 'idx_gsp_check_status';

SET @sql = IF(@index_exists > 0, 
    'DROP INDEX idx_gsp_check_status ON jxc_purchase_plan;',
    'SELECT ''索引 idx_gsp_check_status 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 2. 删除采购计划商品明细表新增索引
-- =====================================================

-- 删除供应商ID索引
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND index_name = 'idx_organization_supplier_id';

SET @sql = IF(@index_exists > 0, 
    'DROP INDEX idx_organization_supplier_id ON jxc_purchase_plan_goods;',
    'SELECT ''索引 idx_organization_supplier_id 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除价格策略索引
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND index_name = 'idx_price_strategy';

SET @sql = IF(@index_exists > 0, 
    'DROP INDEX idx_price_strategy ON jxc_purchase_plan_goods;',
    'SELECT ''索引 idx_price_strategy 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除库存分析索引
SET @index_exists = 0;
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND index_name = 'idx_stock_analysis';

SET @sql = IF(@index_exists > 0, 
    'DROP INDEX idx_stock_analysis ON jxc_purchase_plan_goods;',
    'SELECT ''索引 idx_stock_analysis 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 3. 删除采购计划表新增字段
-- =====================================================

-- 删除GSP校验信息字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan' 
  AND column_name = 'check_message';

SET @sql = IF(@col_exists > 0, 
    'ALTER TABLE jxc_purchase_plan DROP COLUMN check_message;',
    'SELECT ''字段 check_message 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除GSP校验状态字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan' 
  AND column_name = 'gsp_check_status';

SET @sql = IF(@col_exists > 0, 
    'ALTER TABLE jxc_purchase_plan DROP COLUMN gsp_check_status;',
    'SELECT ''字段 gsp_check_status 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除自动生成标识字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan' 
  AND column_name = 'auto_generated';

SET @sql = IF(@col_exists > 0, 
    'ALTER TABLE jxc_purchase_plan DROP COLUMN auto_generated;',
    'SELECT ''字段 auto_generated 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 4. 删除采购计划商品明细表新增字段
-- =====================================================

-- 删除价格策略字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'price_strategy';

SET @sql = IF(@col_exists > 0, 
    'ALTER TABLE jxc_purchase_plan_goods DROP COLUMN price_strategy;',
    'SELECT ''字段 price_strategy 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除推荐采购数量字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'recommend_quantity';

SET @sql = IF(@col_exists > 0, 
    'ALTER TABLE jxc_purchase_plan_goods DROP COLUMN recommend_quantity;',
    'SELECT ''字段 recommend_quantity 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除库存下限字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'stock_lower_limit';

SET @sql = IF(@col_exists > 0, 
    'ALTER TABLE jxc_purchase_plan_goods DROP COLUMN stock_lower_limit;',
    'SELECT ''字段 stock_lower_limit 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除库存上限字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'stock_upper_limit';

SET @sql = IF(@col_exists > 0, 
    'ALTER TABLE jxc_purchase_plan_goods DROP COLUMN stock_upper_limit;',
    'SELECT ''字段 stock_upper_limit 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除日均销量字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'daily_avg_sales';

SET @sql = IF(@col_exists > 0, 
    'ALTER TABLE jxc_purchase_plan_goods DROP COLUMN daily_avg_sales;',
    'SELECT ''字段 daily_avg_sales 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除在途数量字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'in_transit_quantity';

SET @sql = IF(@col_exists > 0, 
    'ALTER TABLE jxc_purchase_plan_goods DROP COLUMN in_transit_quantity;',
    'SELECT ''字段 in_transit_quantity 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除供应商ID字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name = 'organization_supplier_id';

SET @sql = IF(@col_exists > 0, 
    'ALTER TABLE jxc_purchase_plan_goods DROP COLUMN organization_supplier_id;',
    'SELECT ''字段 organization_supplier_id 不存在，跳过删除'' as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 5. 验证回滚结果
-- =====================================================

-- 验证采购计划表字段删除情况
SELECT 
    'jxc_purchase_plan' as table_name,
    CASE 
        WHEN COUNT(*) = 0 THEN '所有新增字段已成功删除'
        ELSE CONCAT('仍存在 ', COUNT(*), ' 个字段未删除')
    END as rollback_status
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan' 
  AND column_name IN ('auto_generated', 'gsp_check_status', 'check_message');

-- 验证采购计划商品明细表字段删除情况
SELECT 
    'jxc_purchase_plan_goods' as table_name,
    CASE 
        WHEN COUNT(*) = 0 THEN '所有新增字段已成功删除'
        ELSE CONCAT('仍存在 ', COUNT(*), ' 个字段未删除')
    END as rollback_status
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name IN ('organization_supplier_id', 'in_transit_quantity', 'daily_avg_sales', 
                      'stock_upper_limit', 'stock_lower_limit', 'recommend_quantity', 'price_strategy');

-- 验证索引删除情况
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '所有新增索引已成功删除'
        ELSE CONCAT('仍存在 ', COUNT(*), ' 个索引未删除')
    END as index_rollback_status
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
  AND table_name IN ('jxc_purchase_plan', 'jxc_purchase_plan_goods')
  AND index_name IN ('idx_auto_generated', 'idx_gsp_check_status', 'idx_organization_supplier_id', 
                     'idx_price_strategy', 'idx_stock_analysis');

-- 显示回滚完成信息
SELECT 
    '采购计划功能增强数据库回滚完成' as status,
    NOW() as completion_time,
    'V1.1.0 Rollback' as version;
