---
description: 
globs: *.md
alwaysApply: false
---
1. 节点与说明格式
  - 所有节点、判断、说明、注释内容均用英文半角引号""包裹，确保风格统一。
  - 节点名称、判断条件、连线说明、注释等，全部采用简明、准确的中文业务表达。
  - 所有节点、判断、说明、注释内容中不包含空格字符。
  - `%%` 注释必须单独一行使用，不能写在尾号
2. 结构分区
  - 使用subgraph对不同角色或部门进行分区（如“采购员”、“采购经理”、“质量经理”、“质量负责人”等），清晰展现各自职责和流程归属。
  - 每个subgraph内部可用direction TB（自上而下）或direction LR（自左而右）调整布局。
3. 颜色高亮
  - 为不同角色/部门的节点分配不同的背景色，通过style 节点名 fill:颜色,stroke:#333,stroke-width:2px实现，便于区分流程责任归属。
  - 重要的特殊节点（如“流程作废”）用更醒目的颜色高亮。
4. 在编制数据库ER图时
  - 通过 `string tableName "<b style='color:#FF5733'>**入库单明细表**</b>"` 语句在每张表的第一行声明这张表的中文名称。
  - 使用中文说明表关系。
6. 注释与可维护性
  - 流程图内可用Mermaid注释（%%）进行说明，注释内容同样用英文半角引号""包裹，便于后续维护和团队协作。
  - `%%` 注释必须单独一行使用，不能写在尾号