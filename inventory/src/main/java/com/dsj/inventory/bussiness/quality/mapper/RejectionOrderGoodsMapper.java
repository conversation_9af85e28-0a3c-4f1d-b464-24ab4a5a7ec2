package com.dsj.inventory.bussiness.quality.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.quality.entity.RejectionOrderGoods;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 拒收单商品明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Mapper
public interface RejectionOrderGoodsMapper extends BaseMapper<RejectionOrderGoods> {

    default List<RejectionOrderGoods> selectListByRejectionOrderId(Long rejectionOrderId) {
        if (rejectionOrderId == null) {
            return Collections.emptyList();
        }
        return this.selectList(new LambdaQueryWrapper<RejectionOrderGoods>().eq(RejectionOrderGoods::getRejectionOrderId, rejectionOrderId));
    }

} 