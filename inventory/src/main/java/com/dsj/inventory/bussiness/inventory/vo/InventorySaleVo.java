package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 商品销售库存
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InventorySaleVo", description="商品销售库存实体")
@ToString(callSuper = true)
public class InventorySaleVo {


    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "通用名")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    private String generalName;

    @ApiModelProperty(value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(value = "包装单位")
    private String packUnit;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "剂型")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(value = "产地")
    private String producePlace;

    @ApiModelProperty(value = "生产日期")
    private LocalDateTime produceDate;

    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(value = "条形码")
    private String barCode;

    @ApiModelProperty(value = "有效期至")
    private LocalDateTime userDate;

    @ApiModelProperty(value = "库存数量")
    private Double inventoryAmount;

    @ApiModelProperty(value = "售价")
    private Integer offlinePrice;

    @ApiModelProperty(value = "会员价")
    private Integer memberPrice;

    @ApiModelProperty(value = "批号")
    private String batchNumber;

    @ApiModelProperty(value = "货位Id")
    private Long shelfId;

    @ApiModelProperty(value = "货位名称")
    private String shelfName;

    @ApiModelProperty(value = "用法用量")
    private String usageDosage;

    @ApiModelProperty(value = "特价商品 0否1是")
    private Integer specialOffer;

    @ApiModelProperty(value = "商品图片")
    private String medicineFilePath;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
