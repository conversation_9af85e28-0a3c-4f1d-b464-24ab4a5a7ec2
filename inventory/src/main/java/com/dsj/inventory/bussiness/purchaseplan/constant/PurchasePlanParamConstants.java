package com.dsj.inventory.bussiness.purchaseplan.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 采购计划参数常量类
 * 基于OrganizationDict实现的参数配置管理
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
public class PurchasePlanParamConstants {
    
    // ==================== 字典类型 ====================
    
    /**
     * 采购计划配置字典类型
     */
    public static final String DICT_TYPE_PURCHASE_PLAN = "PURCHASE_PLAN_CONFIG";
    
    // ==================== 进货单价策略参数 ====================
    
    /**
     * 进货单价生成策略参数编码
     */
    public static final String PRICE_STRATEGY = "PRICE_STRATEGY";
    
    /**
     * 进货单价生成策略参数描述
     */
    public static final String PRICE_STRATEGY_DESC = "进货单价生成策略：1-最低进价，2-最新供应商，3-上次进价";
    
    /**
     * 进货单价生成策略默认值（3-上次进价）
     */
    public static final String PRICE_STRATEGY_DEFAULT = "3";
    
    // ==================== 最低进价取样天数参数 ====================
    
    /**
     * 最低进价取样天数参数编码
     */
    public static final String LOWEST_PRICE_DAYS = "LOWEST_PRICE_DAYS";
    
    /**
     * 最低进价取样天数参数描述
     */
    public static final String LOWEST_PRICE_DAYS_DESC = "最低进价取样天数";
    
    /**
     * 最低进价取样天数默认值（180天）
     */
    public static final String LOWEST_PRICE_DAYS_DEFAULT = "180";
    
    // ==================== 在途数量统计天数参数 ====================
    
    /**
     * 在途数量统计天数参数编码
     */
    public static final String IN_TRANSIT_DAYS = "IN_TRANSIT_DAYS";
    
    /**
     * 在途数量统计天数参数描述
     */
    public static final String IN_TRANSIT_DAYS_DESC = "在途数量统计天数";
    
    /**
     * 在途数量统计天数默认值（30天）
     */
    public static final String IN_TRANSIT_DAYS_DEFAULT = "30";
    
    // ==================== 日均销量统计天数参数 ====================
    
    /**
     * 日均销量统计天数参数编码
     */
    public static final String SALES_STAT_DAYS = "SALES_STAT_DAYS";
    
    /**
     * 日均销量统计天数参数描述
     */
    public static final String SALES_STAT_DAYS_DESC = "日均销量统计天数";
    
    /**
     * 日均销量统计天数默认值（30天）
     */
    public static final String SALES_STAT_DAYS_DEFAULT = "30";
    
    // ==================== 库存上限天数参数 ====================
    
    /**
     * 库存上限天数参数编码
     */
    public static final String UPPER_LIMIT_DAYS = "UPPER_LIMIT_DAYS";
    
    /**
     * 库存上限天数参数描述
     */
    public static final String UPPER_LIMIT_DAYS_DESC = "库存上限天数";
    
    /**
     * 库存上限天数默认值（30天）
     */
    public static final String UPPER_LIMIT_DAYS_DEFAULT = "30";
    
    // ==================== 库存下限天数参数 ====================
    
    /**
     * 库存下限天数参数编码
     */
    public static final String LOWER_LIMIT_DAYS = "LOWER_LIMIT_DAYS";
    
    /**
     * 库存下限天数参数描述
     */
    public static final String LOWER_LIMIT_DAYS_DESC = "库存下限天数";
    
    /**
     * 库存下限天数默认值（7天）
     */
    public static final String LOWER_LIMIT_DAYS_DEFAULT = "7";
    
    // ==================== GSP校验模式参数 ====================
    
    /**
     * GSP校验模式参数编码
     */
    public static final String GSP_CHECK_MODE = "GSP_CHECK_MODE";
    
    /**
     * GSP校验模式参数描述
     */
    public static final String GSP_CHECK_MODE_DESC = "GSP校验模式：1-拦截，0-不拦截不提示，-1-提示不拦截";
    
    /**
     * GSP校验模式默认值（1-拦截）
     */
    public static final String GSP_CHECK_MODE_DEFAULT = "1";
    
    // ==================== 价格策略枚举值 ====================
    
    /**
     * 价格策略：按最低进价生成
     */
    public static final Integer PRICE_STRATEGY_LOWEST = 1;
    
    /**
     * 价格策略：按最新供应商生成
     */
    public static final Integer PRICE_STRATEGY_LATEST_SUPPLIER = 2;
    
    /**
     * 价格策略：按上次进价生成
     */
    public static final Integer PRICE_STRATEGY_LAST_PRICE = 3;
    
    // ==================== GSP校验模式枚举值 ====================
    
    /**
     * GSP校验模式：拦截
     */
    public static final Integer GSP_CHECK_MODE_BLOCK = 1;
    
    /**
     * GSP校验模式：不拦截不提示
     */
    public static final Integer GSP_CHECK_MODE_IGNORE = 0;
    
    /**
     * GSP校验模式：提示不拦截
     */
    public static final Integer GSP_CHECK_MODE_WARN = -1;
    
    // ==================== 工具方法 ====================
    
    /**
     * 获取参数默认值映射
     * 
     * @return 参数编码与默认值的映射关系
     */
    public static Map<String, String> getDefaultParameterMap() {
        Map<String, String> params = new HashMap<>();
        params.put(PRICE_STRATEGY, PRICE_STRATEGY_DEFAULT);
        params.put(LOWEST_PRICE_DAYS, LOWEST_PRICE_DAYS_DEFAULT);
        params.put(IN_TRANSIT_DAYS, IN_TRANSIT_DAYS_DEFAULT);
        params.put(SALES_STAT_DAYS, SALES_STAT_DAYS_DEFAULT);
        params.put(UPPER_LIMIT_DAYS, UPPER_LIMIT_DAYS_DEFAULT);
        params.put(LOWER_LIMIT_DAYS, LOWER_LIMIT_DAYS_DEFAULT);
        params.put(GSP_CHECK_MODE, GSP_CHECK_MODE_DEFAULT);
        return params;
    }
    
    /**
     * 获取参数描述映射
     * 
     * @return 参数编码与描述的映射关系
     */
    public static Map<String, String> getParameterDescriptionMap() {
        Map<String, String> descriptions = new HashMap<>();
        descriptions.put(PRICE_STRATEGY, PRICE_STRATEGY_DESC);
        descriptions.put(LOWEST_PRICE_DAYS, LOWEST_PRICE_DAYS_DESC);
        descriptions.put(IN_TRANSIT_DAYS, IN_TRANSIT_DAYS_DESC);
        descriptions.put(SALES_STAT_DAYS, SALES_STAT_DAYS_DESC);
        descriptions.put(UPPER_LIMIT_DAYS, UPPER_LIMIT_DAYS_DESC);
        descriptions.put(LOWER_LIMIT_DAYS, LOWER_LIMIT_DAYS_DESC);
        descriptions.put(GSP_CHECK_MODE, GSP_CHECK_MODE_DESC);
        return descriptions;
    }
    
    /**
     * 获取价格策略描述
     * 
     * @param strategy 价格策略值
     * @return 策略描述
     */
    public static String getPriceStrategyDesc(Integer strategy) {
        if (PRICE_STRATEGY_LOWEST.equals(strategy)) {
            return "按最低进价生成";
        } else if (PRICE_STRATEGY_LATEST_SUPPLIER.equals(strategy)) {
            return "按最新供应商生成";
        } else if (PRICE_STRATEGY_LAST_PRICE.equals(strategy)) {
            return "按上次进价生成";
        }
        return "未知策略";
    }
    
    /**
     * 获取GSP校验模式描述
     * 
     * @param mode GSP校验模式值
     * @return 模式描述
     */
    public static String getGspCheckModeDesc(Integer mode) {
        if (GSP_CHECK_MODE_BLOCK.equals(mode)) {
            return "拦截";
        } else if (GSP_CHECK_MODE_IGNORE.equals(mode)) {
            return "不拦截不提示";
        } else if (GSP_CHECK_MODE_WARN.equals(mode)) {
            return "提示不拦截";
        }
        return "未知模式";
    }
    
    /**
     * 私有构造函数，防止实例化
     */
    private PurchasePlanParamConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
