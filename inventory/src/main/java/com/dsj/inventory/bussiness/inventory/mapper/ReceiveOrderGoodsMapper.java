package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.ReceiveOrderGoods;
import com.dsj.inventory.common.enumeration.TrueEnum;

import java.util.List;

/**
 * <p>
 * 进销存收货单商品明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
public interface ReceiveOrderGoodsMapper extends BaseMapper<ReceiveOrderGoods> {

    /**
     * 根据订单ID查询商品明细列表
     * @param orderId 订单ID
     * @return 商品明细列表
     */
    default List<ReceiveOrderGoods> selectListByOrderId(Long orderId) {
        LambdaQueryWrapper<ReceiveOrderGoods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReceiveOrderGoods::getOrderId, orderId);
        return this.selectList(queryWrapper);
    }
    
    /**
     * 将商品明细标记为已删除
     * @param orderId 订单ID
     * @return 更新记录数
     */
    default int markAsDeletedByOrderId(Long orderId) {
        LambdaUpdateWrapper<ReceiveOrderGoods> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ReceiveOrderGoods::getOrderId, orderId);
        updateWrapper.set(ReceiveOrderGoods::getIsDelete, TrueEnum.TRUE.getCode());
        return this.update(new ReceiveOrderGoods(), updateWrapper);
    }
    
    /**
     * 根据采购订单ID和组织药品ID查询商品明细
     * @param orderId 订单ID
     * @param organizationMedicineId 组织药品ID
     * @return 商品明细
     */
    default ReceiveOrderGoods selectByOrderIdAndOrgMedicineId(Long orderId, Long organizationMedicineId) {
        LambdaQueryWrapper<ReceiveOrderGoods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ReceiveOrderGoods::getOrderId, orderId);
        queryWrapper.eq(ReceiveOrderGoods::getOrganizationMedicineId, organizationMedicineId);
        return this.selectOne(queryWrapper);
    }
}
