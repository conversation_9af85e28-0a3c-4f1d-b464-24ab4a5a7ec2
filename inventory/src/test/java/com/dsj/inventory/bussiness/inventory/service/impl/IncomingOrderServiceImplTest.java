package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.*;
import com.dsj.inventory.bussiness.inventory.enumeration.AcceptOrderStateEnum;
import com.dsj.inventory.bussiness.inventory.enumeration.IncomingOrderStateEnum;
import com.dsj.inventory.bussiness.inventory.mapper.IncomingOrderMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryIncomingOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateIncomingOrderGoodsParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateIncomingOrderParam;
import com.dsj.inventory.bussiness.inventory.service.*;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderGoodsVo;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderVo;
import com.dsj.inventory.bussiness.purchaseplan.service.PurchasePlanGoodsService;
import com.dsj.inventory.bussiness.purchaseplan.service.PurchasePlanService;
import com.dsj.inventory.bussiness.quality.service.RejectionOrderService;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.generator.NumberService;
import com.dsj.inventory.common.utils.TencentIMUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.test.core.BaseDbUnitTest;
import com.dsj.inventory.framework.test.core.TestContextHelper;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.diagnose.service.UserROrganizationService;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineExpand;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineExpandService;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import com.dsj.inventory.bussiness.inventory.service.OrganizationMedicineMappingExtService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * IncomingOrderServiceImpl 测试类
 * 使用真实的 IncomingOrderMapper 进行数据库交互测试
 */
@Import({IncomingOrderServiceImpl.class})
public class IncomingOrderServiceImplTest extends BaseDbUnitTest {

    @Autowired
    private IncomingOrderServiceImpl incomingOrderService;
    
    @Autowired
    private IncomingOrderMapper incomingOrderMapper;

    @MockBean
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @MockBean
    private IncomingOrderGoodsService incomingOrderGoodsService;
    @MockBean
    private AcceptOrderService acceptOrderService;
    @MockBean
    private AcceptOrderGoodsService acceptOrderGoodsService;
    @MockBean
    private InventoryService inventoryService;
    @MockBean
    private InventoryChangeLogService inventoryChangeLogService;
    @MockBean
    private PurchasePlanService purchasePlanService;
    @MockBean
    private PurchasePlanGoodsService purchasePlanGoodsService;
    @MockBean
    private PurchaseOrderService purchaseOrderService;
    @MockBean
    private PurchaseOrderGoodsService purchaseOrderGoodsService;
    @MockBean
    private ReceiveOrderService receiveOrderService;
    @MockBean
    private ReceiveOrderGoodsService receiveOrderGoodsService;
    @MockBean
    private WarningLogService warningLogService;
    @MockBean
    private OrganizationService organizationService;
    @MockBean
    private TencentIMUtils tencentIMUtils;
    @MockBean
    private UserROrganizationService userROrganizationService;
    @MockBean
    private OrganizationMedicineExpandService organizationMedicineExpandService;
    @MockBean
    private OrganizationMedicineMappingExtService organizationMedicineMappingExtService;
    @MockBean
    private NumberService numberService;
    @MockBean
    private RejectionOrderService rejectionOrderService;
    @MockBean
    private OrganizationSupplierServiceImpl organizationSupplierService;

    // 测试数据
    private Long testOrganizationId = 1L;
    private Long testAcceptOrderId = 1L;
    private SaveOrUpdateIncomingOrderParam testIncomingOrderParam;
    private AcceptOrder testAcceptOrder;
    private Organization testOrganization;
    private SaveOrUpdateIncomingOrderGoodsParam testGoodsParam;
    private List<IncomingOrderGoodsVo> testIncomingOrderGoodsList;
    private OrganizationMedicineMapping testOrgMedicine;

    @BeforeEach
    void setUp() {
        // 设置默认的用户上下文（药店用户）
        TestContextHelper.setDefaultUserContext();
        
        // 初始化测试数据 - 入库单参数
        testIncomingOrderParam = new SaveOrUpdateIncomingOrderParam();
        testIncomingOrderParam.setOrganizationId(testOrganizationId);
        testIncomingOrderParam.setOrderId(testAcceptOrderId);
        testIncomingOrderParam.setTotalPrice(800);  // 更新为与totalpurchaseprice一致
        testIncomingOrderParam.setDiscountTotalPrice(720);  // 更新为与totaldiscountprice一致
        testIncomingOrderParam.setRemark("测试备注");
        testIncomingOrderParam.setDealState(IncomingOrderStateEnum.TEMPORARY.getCode());
        testIncomingOrderParam.setArrivalDate(LocalDate.now().atStartOfDay());
        
        // 初始化测试组织
        testOrganization = new Organization();
        testOrganization.setId(testOrganizationId);
        testOrganization.setSerialNo("123456");
        testOrganization.setInventoryDate(LocalDate.now().plusDays(30)); // 未过期
        when(organizationService.getById(testOrganizationId)).thenReturn(testOrganization);
        
        // 初始化测试验收单
        testAcceptOrder = new AcceptOrder();
        testAcceptOrder.setId(testAcceptOrderId);
        testAcceptOrder.setCode("YS20230101001");
        testAcceptOrder.setPurchaseContent("测试采购内容");
        testAcceptOrder.setGoodsTypes("测试商品类型");
        testAcceptOrder.setDealState(AcceptOrderStateEnum.COMPLETED.getCode());
        when(acceptOrderService.getById(testAcceptOrderId)).thenReturn(testAcceptOrder);
        
        // 初始化测试商品参数
        testGoodsParam = new SaveOrUpdateIncomingOrderGoodsParam();
        testGoodsParam.setOrganizationMedicineId(1L);
        testGoodsParam.setQualifiedAmount(9.0);
        testGoodsParam.setUnqualifiedAmount(1.0);
        testGoodsParam.setReceiveAmount(10.0);
        testGoodsParam.setIncomingAmount(9.0); // 入库数量等于合格数量
        testGoodsParam.setGiveAmount(1.0);  // 赠送数量1.0
        testGoodsParam.setPurchasePrice(100);  // 采购单价100
        testGoodsParam.setDiscountPrice(90);   // 折扣单价90
        testGoodsParam.setTotalPurchasePrice(800);  // 采购单价*实际数量(9.0-1.0)*100=800
        testGoodsParam.setTotalDiscountPrice(720);  // 折扣单价*实际数量(9.0-1.0)*90=720
        testGoodsParam.setPrice(120);  // 零售单价
        testGoodsParam.setProduceDate(LocalDate.now().minusMonths(1).atStartOfDay());
        testGoodsParam.setUserDate(LocalDate.now().plusYears(2).atStartOfDay());
        testGoodsParam.setCostPrice(90);
        testGoodsParam.setShelfId(1L);
        
        // 模拟验收单商品
        AcceptOrderGoods testAcceptOrderGoods = new AcceptOrderGoods();
        testAcceptOrderGoods.setId(1L);
        testAcceptOrderGoods.setGoodsName("测试商品");
        testAcceptOrderGoods.setGeneralName("测试通用名");
        testAcceptOrderGoods.setGoodsCode("TEST001");
        testAcceptOrderGoods.setPackUnit("盒");
        testAcceptOrderGoods.setSpecification("100mg*30片");
        testAcceptOrderGoods.setDrugType("处方药");
        testAcceptOrderGoods.setManufacturer("测试厂家");
        testAcceptOrderGoods.setPurchaseNumber(20.0);
        testAcceptOrderGoods.setReceiveAmount(10.0);
        testAcceptOrderGoods.setQualifiedAmount(9.0);
        testAcceptOrderGoods.setUnqualifiedAmount(1.0);
        testAcceptOrderGoods.setPurchasePrice(100);
        testAcceptOrderGoods.setDiscountPrice(90);
        testAcceptOrderGoods.setProducePlace("中国");
        testAcceptOrderGoods.setProduceDate(LocalDate.now().minusMonths(1).atStartOfDay());
        testAcceptOrderGoods.setUserDate(LocalDate.now().plusYears(2).atStartOfDay());
        when(acceptOrderGoodsService.getByOrderIdAndOrgMedicineId(anyLong(), anyLong()))
            .thenReturn(testAcceptOrderGoods);
        
        // 模拟商品映射
        testOrgMedicine = new OrganizationMedicineMapping();
        testOrgMedicine.setId(1L);
        testOrgMedicine.setName("测试商品");
        testOrgMedicine.setGeneralName("测试通用名");
        testOrgMedicine.setGoodsCode("TEST001");
        testOrgMedicine.setPackUnit("盒");
        testOrgMedicine.setSpecification("100mg*30片");
        testOrgMedicine.setDrugType("处方药");
        testOrgMedicine.setManufacturer("测试厂家");
        testOrgMedicine.setOfflinePrice(120);
        testOrgMedicine.setMemberPrice(110);
        testOrgMedicine.setMedicineRepertory(100.0);
        testOrgMedicine.setGoodsType("1");
        testOrgMedicine.setProducePlace("中国");
        testOrgMedicine.setOrganizationId(testOrganizationId);
        testOrgMedicine.setRepertoryLowerLimit(10);
        testOrgMedicine.setRepertoryUpperLimit(200);
        testOrgMedicine.setInventoryShelve(TrueEnum.TRUE.getCode());
        when(organizationMedicineMappingService.getById(anyLong())).thenReturn(testOrgMedicine);
        
        // 模拟扩展服务
        when(organizationMedicineMappingExtService.updateMedicineInventoryInfo(anyLong(), anyDouble(), anyInt(), any(LocalDateTime.class), anyBoolean(), any(LocalDateTime.class)))
            .thenReturn(true);
        
        // 模拟预警日志服务
        when(warningLogService.findWarningLogByTypeAndMedicine(anyInt(), anyLong(), anyLong()))
            .thenReturn(null);
        when(warningLogService.markWarningLogAsDeleted(anyLong()))
            .thenReturn(true);
            
        // 模拟商品扩展信息
        OrganizationMedicineExpand testMedicineExpand = new OrganizationMedicineExpand();
        testMedicineExpand.setId(1L);
        testMedicineExpand.setLaunchPermitHolder("测试持证商");
        when(organizationMedicineExpandService.getById(anyLong())).thenReturn(testMedicineExpand);
        
        // 模拟入库单商品统计
        List<IncomingOrderGoodsVo> incomingGoodsCountList = new ArrayList<>();
        IncomingOrderGoodsVo incomingGoodsCount = new IncomingOrderGoodsVo();
        incomingGoodsCount.setOrganizationMedicineId(1L);
        incomingGoodsCount.setIncomingAmount(9.0);
        incomingGoodsCount.setReturnedAmount(0.0);
        incomingGoodsCount.setCostPrice(90);
        incomingGoodsCountList.add(incomingGoodsCount);
        when(incomingOrderGoodsService.countIncomingOrderGoods(anyLong())).thenReturn(incomingGoodsCountList);
        
        // 准备入库单商品列表
        testIncomingOrderGoodsList = new ArrayList<>();
        IncomingOrderGoodsVo goodsVo = new IncomingOrderGoodsVo();
        goodsVo.setId(1L);
        goodsVo.setOrderId(1L);
        goodsVo.setGoodsName("测试商品");
        goodsVo.setBatchNumber("B202301");
        goodsVo.setIncomingAmount(9.0);
        goodsVo.setQualifiedAmount(9.0);
        goodsVo.setUnqualifiedAmount(1.0);
        goodsVo.setOrganizationMedicineId(1L);
        goodsVo.setGiveAmount(1.0);
        goodsVo.setPurchasePrice(100);
        goodsVo.setDiscountPrice(90);
        goodsVo.setProduceDate(LocalDate.now().minusMonths(1).atStartOfDay());
        goodsVo.setUserDate(LocalDate.now().plusYears(2).atStartOfDay());
        goodsVo.setCostPrice(90);
        goodsVo.setShelfId(1L);
        goodsVo.setPurchaseNumber(20.0);  // 设置采购数量
        goodsVo.setReceiveAmount(10.0);   // 设置收货数量
        goodsVo.setPrice(120);            // 设置价格
        goodsVo.setTotalPurchasePrice(800); // 设置总采购价格: (9.0-1.0)*100=800
        goodsVo.setTotalDiscountPrice(720); // 设置总折扣价格: (9.0-1.0)*90=720
        goodsVo.setGeneralName("测试通用名");
        goodsVo.setGoodsCode("TEST001");
        goodsVo.setPackUnit("盒");
        goodsVo.setSpecification("100mg*30片");
        goodsVo.setDrugType("处方药");
        goodsVo.setManufacturer("测试厂家");
        goodsVo.setMemberPrice(110);
        goodsVo.setLaunchPermitHolder("测试持证商");
        goodsVo.setProducePlace("中国");
        testIncomingOrderGoodsList.add(goodsVo);
        
        when(incomingOrderGoodsService.getIncomingOrderGoodsList(anyLong())).thenReturn(testIncomingOrderGoodsList);
        
        // 清理可能的测试数据（使用真实的IncomingOrderMapper）
        try {
            incomingOrderMapper.deleteById(1L);
        } catch (Exception e) {
            // 忽略可能的错误
        }
    }
    
    @Test
    @DisplayName("saveOrUpdateIncomingOrder - 新增入库单 - 成功")
    void saveOrUpdateIncomingOrder_New_Success() {
        // Arrange
        List<SaveOrUpdateIncomingOrderGoodsParam> goodsList = Collections.singletonList(testGoodsParam);
        testIncomingOrderParam.setGoodsList(goodsList);
        
        // 确保测试数据的完整性
        testGoodsParam.setPurchaseNumber(20.0); //采购数量
        testGoodsParam.setReceiveAmount(10.0); // 收货数量
        testGoodsParam.setIncomingAmount(9.0); // 入库数量等于合格数量
        testGoodsParam.setGiveAmount(1.0);  // 赠送数量1.0
        testGoodsParam.setPurchasePrice(100);  // 采购单价100
        testGoodsParam.setDiscountPrice(90);   // 折扣单价90
        testGoodsParam.setTotalPurchasePrice(800);  // 采购单价*实际数量(9.0-1.0)*100=800
        testGoodsParam.setTotalDiscountPrice(720);  // 折扣单价*实际数量(9.0-1.0)*90=720
        testGoodsParam.setPrice(120);  // 零售单价
        
        // Act
        IncomingOrderDetailVo result = incomingOrderService.saveOrUpdateIncomingOrder(testIncomingOrderParam);
        
        // Assert
        assertNotNull(result, "返回结果不能为空");
        assertNotNull(result.getId(), "新创建的入库单ID不能为空");

        // 验证返回的code是否符合生成规则
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
        String expectedCodePrefix = "RK" + dateStr + testOrganization.getSerialNo();
        assertTrue(result.getCode().startsWith(expectedCodePrefix), "入库单编号格式不正确");

        // 从数据库中重新查询以进行验证
        IncomingOrder savedOrder = incomingOrderMapper.selectById(result.getId());
        assertNotNull(savedOrder, "入库单应已保存到数据库");
        assertEquals(result.getCode(), savedOrder.getCode(), "数据库中保存的编号应与返回的一致");
        assertEquals(IncomingOrderStateEnum.TEMPORARY.getCode(), savedOrder.getDealState(), "新建的入库单状态应为暂存");

        // Verify
        verify(incomingOrderGoodsService).saveBatch(any());
        if (testIncomingOrderParam.getOrderId() != null) {
            verify(acceptOrderService).completeAcceptOrder(testAcceptOrderId);
        }
    }

    @Test
    @DisplayName("saveOrUpdateIncomingOrder - 组织无效期已过期 - 抛出业务异常")
    void saveOrUpdateIncomingOrder_OrganizationExpired_ThrowsBizException() {
        // Arrange
        testOrganization.setInventoryDate(LocalDate.now().minusDays(1)); // 设置为已过期
        when(organizationService.getById(testOrganizationId)).thenReturn(testOrganization);
        
        // Act & Assert
        BizException exception = assertThrows(BizException.class, () -> {
            incomingOrderService.saveOrUpdateIncomingOrder(testIncomingOrderParam);
        });
        
        // 验证异常消息包含"进销存有效期已过期"
        assertTrue(exception.getMessage().contains("进销存有效期已过期"), "异常消息应包含过期提示");
    }
    
    @Test
    @DisplayName("saveOrUpdateIncomingOrder - 小计金额计算不一致 - 抛出业务异常")
    void saveOrUpdateIncomingOrder_InconsistentAmount_ThrowsBizException() {
        // Arrange
        testGoodsParam.setTotalPurchasePrice(500); // 设置不一致的小计金额
        
        // 确保测试数据的完整性
        testGoodsParam.setPurchaseNumber(20.0);
        testGoodsParam.setReceiveAmount(10.0);
        testGoodsParam.setIncomingAmount(9.0);
        testGoodsParam.setGiveAmount(1.0);
        testGoodsParam.setTotalDiscountPrice(720);
        
        List<SaveOrUpdateIncomingOrderGoodsParam> goodsList = Collections.singletonList(testGoodsParam);
        testIncomingOrderParam.setGoodsList(goodsList);
        
        // Act & Assert
        BizException exception = assertThrows(BizException.class, () -> {
            incomingOrderService.saveOrUpdateIncomingOrder(testIncomingOrderParam);
        });
        
        // 验证异常消息包含相关提示
        assertTrue(exception.getMessage().contains("小计金额计算不一致"), "异常消息应包含金额不一致的相关提示");
    }
    
    @Test
    @DisplayName("saveOrUpdateIncomingOrder - 已完成状态 - 执行入库操作")
    void saveOrUpdateIncomingOrder_CompletedState_ExecuteInventory() {
        // Arrange
        testIncomingOrderParam.setDealState(IncomingOrderStateEnum.COMPLETED.getCode());
        
        // 确保测试数据的完整性
        testGoodsParam.setPurchaseNumber(20.0);
        testGoodsParam.setReceiveAmount(10.0);
        testGoodsParam.setIncomingAmount(9.0);
        testGoodsParam.setGiveAmount(1.0);
        testGoodsParam.setTotalDiscountPrice(720);
        
        List<SaveOrUpdateIncomingOrderGoodsParam> goodsList = Collections.singletonList(testGoodsParam);
        testIncomingOrderParam.setGoodsList(goodsList);
        
        // Mock inventory service and change log service
        when(inventoryService.save(any(Inventory.class))).thenReturn(true);
        when(inventoryChangeLogService.save(any(InventoryChangeLog.class))).thenReturn(true);
        when(organizationMedicineMappingService.update(any(), any())).thenReturn(true);
        
        // Mock purchase plan service for direct entry
        when(purchasePlanService.getPurchasePlanCount(anyLong(), anyString())).thenReturn(0);
        when(purchasePlanService.save(any())).thenReturn(true);
        when(purchasePlanGoodsService.saveBatch(anyList())).thenReturn(true);
        
        // Mock purchase order service
        when(purchaseOrderService.getPurchaseOrderCount(anyLong(), anyString())).thenReturn(0);
        when(purchaseOrderService.save(any())).thenReturn(true);
        when(purchaseOrderGoodsService.saveBatch(anyList())).thenReturn(true);
        
        // Mock receive order service
        when(receiveOrderService.getReceiveOrderCount(anyLong(), anyString())).thenReturn(0);
        when(receiveOrderService.save(any())).thenReturn(true);
        when(receiveOrderGoodsService.saveBatch(anyList())).thenReturn(true);
        
        // Mock accept order service for direct entry
        when(acceptOrderService.getAcceptOrderCount(anyLong(), anyString())).thenReturn(0);
        when(acceptOrderService.saveOrUpdate(any())).thenReturn(true);
        when(acceptOrderGoodsService.saveBatch(anyList())).thenReturn(true);
        
        // Act
        IncomingOrderDetailVo result = incomingOrderService.saveOrUpdateIncomingOrder(testIncomingOrderParam);
        
        // Assert
        assertNotNull(result, "返回结果不能为空");
        
        // Verify inventory operations were called
        verify(inventoryService).save(any(Inventory.class));
        verify(inventoryChangeLogService).save(any(InventoryChangeLog.class));
        verify(organizationMedicineMappingExtService).updateMedicineInventoryInfo(anyLong(), anyDouble(), anyInt(), any(LocalDateTime.class), anyBoolean(), any(LocalDateTime.class));
        
        // Verify process creation for direct entry
        if (testIncomingOrderParam.getOrderId() == null) {
            verify(purchasePlanService).save(any());
            verify(purchasePlanGoodsService).saveBatch(anyList());
            verify(purchaseOrderService).save(any());
            verify(purchaseOrderGoodsService).saveBatch(anyList());
            verify(receiveOrderService).save(any());
            verify(receiveOrderGoodsService).saveBatch(anyList());
            verify(acceptOrderService).saveOrUpdate(any());
            verify(acceptOrderGoodsService).saveBatch(anyList());
            verify(incomingOrderMapper).updateOrderIdAndCode(any(), any(), any());
        }
    }
    
    @Test
    @DisplayName("deleteIncomingOrder - 删除暂存状态入库单 - 成功")
    void deleteIncomingOrder_TemporaryState_Success() {
        // Arrange
        Long incomingOrderId = 1L;
        IncomingOrder mockIncomingOrder = new IncomingOrder();
        mockIncomingOrder.setId(incomingOrderId);
        mockIncomingOrder.setDealState(IncomingOrderStateEnum.TEMPORARY.getCode());
        
        // 使用真实的IncomingOrderMapper创建数据
        incomingOrderMapper.insert(mockIncomingOrder);
        
        // Act
        incomingOrderService.deleteIncomingOrder(incomingOrderId);
        
        // Verify
        verify(incomingOrderGoodsService).markAsDeletedByOrderId(incomingOrderId);
        
        // 验证数据库中已删除
        IncomingOrder deletedOrder = incomingOrderMapper.selectById(incomingOrderId);
        assertNull(deletedOrder, "入库单应已被删除");
    }

    @Test
    @DisplayName("deleteIncomingOrder - 非暂存状态不允许删除 - 抛出业务异常")
    void deleteIncomingOrder_NonTemporaryState_ThrowsBizException() {
        // Arrange
        Long incomingOrderId = 1L;
        IncomingOrder mockIncomingOrder = new IncomingOrder();
        mockIncomingOrder.setId(incomingOrderId);
        mockIncomingOrder.setDealState(IncomingOrderStateEnum.COMPLETED.getCode());
        
        // 使用真实的IncomingOrderMapper创建数据
        incomingOrderMapper.insert(mockIncomingOrder);
        
        // Act & Assert
        BizException exception = assertThrows(BizException.class, () -> {
            incomingOrderService.deleteIncomingOrder(incomingOrderId);
        });
        
        // 验证异常消息包含相关提示
        assertTrue(exception.getMessage().contains("当前状态不允许删除"), "异常消息应包含状态不允许删除的提示");
    }
    
    @Test
    @DisplayName("queryIncomingOrderPage - 药店用户查询 - 成功返回分页数据")
    void queryIncomingOrderPage_DrugstoreUser_Success() {
        // Arrange
        QueryIncomingOrderParam queryParam = new QueryIncomingOrderParam();
        queryParam.setPage(1);
        queryParam.setSize(10);
        queryParam.setOrganizationId(testOrganizationId);

        // 插入测试数据
        IncomingOrder testOrder = new IncomingOrder();
        testOrder.setId(1L);
        testOrder.setCode("RK-QUERY-TEST-001");
        testOrder.setOrganizationId(testOrganizationId);
        testOrder.setDealState(IncomingOrderStateEnum.TEMPORARY.getCode());
        testOrder.setCreateTime(LocalDateTime.now());
        incomingOrderMapper.insert(testOrder);
        
        // Act
        Page<IncomingOrderVo> result = incomingOrderService.queryIncomingOrderPage(queryParam);
        
        // Assert
        assertNotNull(result, "返回结果不能为空");
        assertEquals(1, result.getTotal(), "应查询到1条记录");
        assertEquals(1, result.getRecords().size(), "应返回正确的记录数量");
        assertEquals("RK-QUERY-TEST-001", result.getRecords().get(0).getCode(), "应返回正确的入库单编号");
    }
    
    @Test
    @DisplayName("getIncomingOrderById - 获取入库单详情 - 成功")
    void getIncomingOrderById_Success() {
        // Arrange
        Long incomingOrderId = 1L;
        // 插入测试数据
        IncomingOrder testOrder = new IncomingOrder();
        testOrder.setId(incomingOrderId);
        testOrder.setCode("RK-GET-BY-ID-TEST");
        testOrder.setOrganizationId(testOrganizationId);
        testOrder.setOrderId(testAcceptOrderId);
        testOrder.setDealState(IncomingOrderStateEnum.TEMPORARY.getCode());
        testOrder.setCreateTime(LocalDateTime.now());
        incomingOrderMapper.insert(testOrder);

        // 商品列表由 mock 的 incomingOrderGoodsService 提供，这部分不变
        when(incomingOrderGoodsService.getIncomingOrderGoodsList(incomingOrderId)).thenReturn(testIncomingOrderGoodsList);
        
        // Act
        IncomingOrderDetailVo result = incomingOrderService.getIncomingOrderById(incomingOrderId);
        
        // Assert
        assertNotNull(result, "返回结果不能为空");
        assertEquals(incomingOrderId, result.getId(), "应返回正确的入库单ID");
        assertEquals("RK-GET-BY-ID-TEST", result.getCode(), "应返回正确的入库单编号");
        assertNotNull(result.getGoodsList(), "商品列表不应为空");
        assertEquals(1, result.getGoodsList().size(), "应返回正确的商品数量");
        assertEquals("测试商品", result.getGoodsList().get(0).getGoodsName(), "应返回正确的商品名称");
    }
    
    @Test
    @DisplayName("submitIncomingOrder - 提交暂存状态入库单 - 成功")
    void submitIncomingOrder_TemporaryState_Success() {
        // Arrange
        Long incomingOrderId = 1L;
        IncomingOrder mockIncomingOrder = new IncomingOrder();
        mockIncomingOrder.setId(incomingOrderId);
        mockIncomingOrder.setOrganizationId(testOrganizationId);
        mockIncomingOrder.setDealState(IncomingOrderStateEnum.TEMPORARY.getCode());
        mockIncomingOrder.setCode("RK-TEST-001");
        mockIncomingOrder.setTotalPrice(800);  // 与商品的 totalPurchasePrice 匹配
        mockIncomingOrder.setDiscountTotalPrice(720);  // 与商品的 totalDiscountPrice 匹配
        
        // 使用真实的IncomingOrderMapper创建数据
        incomingOrderMapper.insert(mockIncomingOrder);
        
        // Mock getIncomingOrderById
        IncomingOrderDetailVo incomingOrderDetailVo = new IncomingOrderDetailVo();
        incomingOrderDetailVo.setId(incomingOrderId);
        incomingOrderDetailVo.setOrganizationId(testOrganizationId);
        incomingOrderDetailVo.setDealState(IncomingOrderStateEnum.TEMPORARY.getCode());
        incomingOrderDetailVo.setGoodsList(testIncomingOrderGoodsList);
        incomingOrderDetailVo.setCode("RK-TEST-001");
        incomingOrderDetailVo.setPurchaserId(1L);
        incomingOrderDetailVo.setAcceptId(1L);
        incomingOrderDetailVo.setArrivalDate(LocalDate.now().atStartOfDay());
        incomingOrderDetailVo.setTotalPrice(800);  // 与商品的 totalPurchasePrice 匹配
        incomingOrderDetailVo.setDiscountTotalPrice(720);  // 与商品的 totalDiscountPrice 匹配
        
        when(incomingOrderGoodsService.getIncomingOrderGoodsList(incomingOrderId)).thenReturn(testIncomingOrderGoodsList);

        // Mock inventory service and change log service
        when(inventoryService.save(any(Inventory.class))).thenReturn(true);
        when(inventoryChangeLogService.save(any(InventoryChangeLog.class))).thenReturn(true);
        
        // Mock purchase plan service for direct entry
        when(purchasePlanService.getPurchasePlanCount(anyLong(), anyString())).thenReturn(0);
        when(purchasePlanService.save(any())).thenReturn(true);
        when(purchasePlanGoodsService.saveBatch(anyList())).thenReturn(true);
        
        // Mock purchase order service
        when(purchaseOrderService.getPurchaseOrderCount(anyLong(), anyString())).thenReturn(0);
        when(purchaseOrderService.save(any())).thenReturn(true);
        when(purchaseOrderGoodsService.saveBatch(anyList())).thenReturn(true);
        
        // Mock receive order service
        when(receiveOrderService.getReceiveOrderCount(anyLong(), anyString())).thenReturn(0);
        when(receiveOrderService.save(any())).thenReturn(true);
        when(receiveOrderGoodsService.saveBatch(anyList())).thenReturn(true);
        
        // Mock accept order service for direct entry
        when(acceptOrderService.getAcceptOrderCount(anyLong(), anyString())).thenReturn(0);
        when(acceptOrderService.saveOrUpdate(any())).thenReturn(true);
        when(acceptOrderGoodsService.saveBatch(anyList())).thenReturn(true);
        
        // Act
        incomingOrderService.submitIncomingOrder(incomingOrderId);
        
        // Verify
        IncomingOrder updatedOrder = incomingOrderMapper.selectById(incomingOrderId);
        assertEquals(IncomingOrderStateEnum.COMPLETED.getCode(), updatedOrder.getDealState(), "入库单状态应已更新为已完成");
        
        // Verify inventory operations were called
        verify(inventoryService).save(any(Inventory.class));
        verify(inventoryChangeLogService).save(any(InventoryChangeLog.class));
        verify(organizationMedicineMappingExtService).updateMedicineInventoryInfo(anyLong(), anyDouble(), anyInt(), any(LocalDateTime.class), anyBoolean(), any(LocalDateTime.class));
        
        // Verify process creation for direct entry
        verify(purchasePlanService).save(any());
        verify(purchasePlanGoodsService).saveBatch(anyList());
        verify(purchaseOrderService).save(any());
        verify(purchaseOrderGoodsService).saveBatch(anyList());
        verify(receiveOrderService).save(any());
        verify(receiveOrderGoodsService).saveBatch(anyList());
        verify(acceptOrderService).saveOrUpdate(any());
        verify(acceptOrderGoodsService).saveBatch(anyList());
    }
    
    @Test
    @DisplayName("queryIncomingOrderList - 导出入库单列表 - 成功")
    void queryIncomingOrderList_Success() {
        // Arrange
        QueryIncomingOrderParam queryParam = new QueryIncomingOrderParam();
        queryParam.setOrganizationId(testOrganizationId);

        // 插入测试数据
        IncomingOrder testOrder = new IncomingOrder();
        testOrder.setId(1L);
        testOrder.setCode("RK-EXPORT-TEST-001");
        testOrder.setOrganizationId(testOrganizationId);
        testOrder.setDealState(IncomingOrderStateEnum.TEMPORARY.getCode());
        testOrder.setCreateTime(LocalDateTime.now());
        incomingOrderMapper.insert(testOrder);
        
        // Act
        List<IncomingOrderExportVo> result = incomingOrderService.queryIncomingOrderList(queryParam);
        
        // Assert
        assertNotNull(result, "返回结果不能为空");
        assertEquals(1, result.size(), "应返回正确的记录数量");
        assertEquals("RK-EXPORT-TEST-001", result.get(0).getCode(), "应返回正确的入库单编号");
    }
} 