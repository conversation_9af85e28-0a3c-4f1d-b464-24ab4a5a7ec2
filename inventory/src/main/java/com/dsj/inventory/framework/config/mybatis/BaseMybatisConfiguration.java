package com.dsj.inventory.framework.config.mybatis;


import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.dsj.inventory.framework.config.mybatis.MyMetaObjectHandler;
import com.dsj.inventory.framework.properties.DatabaseProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Mybatis 常用重用拦截器，pocky.database.multiTenantType=任意模式 都需要实例出来
 * <p>
 * 拦截器执行一定是： WriteInterceptor > DataScopeInterceptor > PaginationInterceptor
 *
 * <AUTHOR>
 * @date 2018/10/24
 */
@Configuration
@Slf4j
@EnableConfigurationProperties({MybatisPlusProperties.class})
public class BaseMybatisConfiguration {
  @Autowired
  protected final DatabaseProperties databaseProperties;

  public BaseMybatisConfiguration(DatabaseProperties databaseProperties) {
    this.databaseProperties = databaseProperties;
  }

  /**
   * Mybatis Plus 注入器
   */
  @Bean("myMetaObjectHandler")
  @ConditionalOnMissingBean
  public MetaObjectHandler getMyMetaObjectHandler() {
    return new MyMetaObjectHandler(databaseProperties.getWorkerId(), databaseProperties.getDataCenterId());
  }

  /**
   * 分页插件
   * @return
   */
  @Bean
  public PaginationInterceptor paginationInterceptor() {
    PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
    paginationInterceptor.setLimit(-1);
    return paginationInterceptor;
  }

}
