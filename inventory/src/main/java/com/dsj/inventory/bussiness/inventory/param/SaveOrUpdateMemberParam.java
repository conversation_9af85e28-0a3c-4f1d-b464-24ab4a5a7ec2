package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 会员
 * <AUTHOR>
 * @date 2023/04/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateMemberParam", description="新增 会员参数")
public class SaveOrUpdateMemberParam {

    @ApiModelProperty(hidden = true, value = "id,更新是传入")
    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(hidden = true,value = "系统账号Id")
    private Long userId;

    @ApiModelProperty(hidden = true,value = "会员卡号")
    private String code;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "性别1 男  2 女  0 未知")
    private Integer gender;

    @ApiModelProperty(value = "生日(yyyy-MM-dd)")
    private String birth;

    @ApiModelProperty(value = "身份证号码")
    private String card;

    @ApiModelProperty(value = "省")
    private Long province;

    @ApiModelProperty(value = "市")
    private Long city;

    @ApiModelProperty(value = "县")
    private Long county;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "充值金额")
    private Integer rechargePrice = 0;

    @ApiModelProperty(value = "赠送金额")
    private Integer givePrice = 0;

    @ApiModelProperty(value = "会员积分")
    private Integer usableIntegral = 0;

    @ApiModelProperty(value = "支付方式 1现金")
    private Integer payWay;

    @ApiModelProperty(value = "充值类型 1金额")
    private Integer rechargeType;

    @ApiModelProperty(value = "消费密码")
    private String consumePwd;

    @ApiModelProperty(value = "是否开启安全验证 0否1是")
    private Integer safetyState;

    @ApiModelProperty(value = "办理人Id")
    private Long transactUser;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
