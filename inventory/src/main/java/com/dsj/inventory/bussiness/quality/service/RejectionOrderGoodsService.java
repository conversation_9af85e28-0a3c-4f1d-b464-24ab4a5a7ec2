package com.dsj.inventory.bussiness.quality.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.quality.entity.RejectionOrderGoods;

import java.util.List;

/**
 * <p>
 * 拒收单商品明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface RejectionOrderGoodsService extends IService<RejectionOrderGoods> {

    /**
     * 根据拒收单ID查询商品列表
     * @param rejectionOrderId 拒收单ID
     * @return 商品列表
     */
    List<RejectionOrderGoods> listByRejectionOrderId(Long rejectionOrderId);
} 