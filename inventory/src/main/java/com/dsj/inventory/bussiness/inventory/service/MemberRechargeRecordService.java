package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.MemberRechargeRecord;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberRechargeRecordParam;
import com.dsj.inventory.bussiness.inventory.param.SaveMemberRechargeRecordParam;
import com.dsj.inventory.bussiness.inventory.vo.MemberRechargeRecordVo;

/**
 * <p>
 * 进销存会员充值记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface MemberRechargeRecordService extends IService<MemberRechargeRecord> {

    /**
     * 新增会员充值记录
     * @param param
     * @return
     */
    MemberRechargeRecordVo saveMemberRechargeRecord(SaveMemberRechargeRecordParam param);

    /**
     * 获取会员充值记录分页列表
     * @param param
     * @return
     */
    Page<MemberRechargeRecordVo> queryMemberRechargeRecordPage(QueryMemberRechargeRecordParam param);

}
