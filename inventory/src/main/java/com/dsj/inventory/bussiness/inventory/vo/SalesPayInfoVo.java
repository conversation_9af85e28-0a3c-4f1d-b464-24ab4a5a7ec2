package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 销售支付信息
 * <AUTHOR>
 * @date 2023/05/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SalesPayInfoVo", description="销售支付信息")
@ToString(callSuper = true)
public class SalesPayInfoVo {

    @ApiModelProperty(value = "支付方式")
    private String payWay;

    @ApiModelProperty(value = "支付金额")
    private Integer payPrice;

}
