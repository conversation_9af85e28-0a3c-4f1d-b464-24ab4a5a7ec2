package com.dsj.inventory.bussiness.inventory.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 销售活动任务周期
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SalesActivityTaskCycleVo", description="销售活动任务周期")
@ToString(callSuper = true)
public class SalesActivityTaskCycleVo {

    @ApiModelProperty(hidden = true)
    private Long id;

    @ApiModelProperty(hidden = true,value = "任务Id")
    private Long taskId;

    @ApiModelProperty(hidden = true,value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "星期")
    private Integer week;

    @DateTimeFormat(pattern = "HH:mm")
    @JsonFormat(pattern = "HH:mm")
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @DateTimeFormat(pattern = "HH:mm")
    @JsonFormat(pattern = "HH:mm")
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}

