package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryIncomingOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateIncomingOrderParam;
import com.dsj.inventory.bussiness.inventory.service.IncomingOrderService;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.EasyExcelUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 进销存入库单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@RestController
@Api(value = "IncomingOrderController", tags = "进销存入库单api")
public class IncomingOrderController {

    @Autowired
    private IncomingOrderService incomingOrderService;

    /*新增入库单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-5"})
    @PostMapping("/incoming-order")
    @ApiOperation(value = "新增入库单", notes = "新增入库单")
    public ResponseEntity<IncomingOrderDetailVo> saveIncomingOrder(@RequestBody SaveOrUpdateIncomingOrderParam param){
        IncomingOrderDetailVo incomingVo = incomingOrderService.saveOrUpdateIncomingOrder(param);
        return Res.success(incomingVo);
    }

    /*编辑入库单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-5"})
    @PutMapping("/incoming-order/{id}")
    @ApiOperation(value = "编辑入库单", notes = "编辑入库单")
    public ResponseEntity<IncomingOrderDetailVo> updateIncomingOrder(@PathVariable Long id, @RequestBody SaveOrUpdateIncomingOrderParam param){
        param.setId(id);
        IncomingOrderDetailVo incomingVo = incomingOrderService.saveOrUpdateIncomingOrder(param);
        return Res.success(incomingVo);
    }

    /*删除入库单*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-5"})
    @DeleteMapping("/incoming-order/{id}")
    @ApiOperation(value = "删除入库单", notes = "删除入库单")
    public ResponseEntity<Void> deleteIncomingOrder(@PathVariable Long id){
        incomingOrderService.deleteIncomingOrder(id);
        return Res.success();
    }

    /*获取入库单列表*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-5"})
    @GetMapping("/incoming-order")
    @ApiOperation(value = "获取入库单列表", notes = "获取入库单列表")
    public ResponseEntity<List<IncomingOrderVo>> queryIncomingOrderList(QueryIncomingOrderParam param){
        Page<IncomingOrderVo> page = incomingOrderService.queryIncomingOrderPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取入库单详情*/
    @GetMapping("/incoming-order/{id}")
    @ApiOperation(value = "获取入库单详情", notes = "获取入库单详情")
    public ResponseEntity<IncomingOrderDetailVo> queryIncomingOrder(@PathVariable Long id){
        return Res.success(incomingOrderService.getIncomingOrderById(id));
    }

    /*提交入库单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-5"})
    @PostMapping("/incoming-order/{id}/action-submit")
    @ApiOperation(value = "提交入库单", notes = "提交入库单")
    public ResponseEntity<Void> submitIncomingOrder(@PathVariable Long id){
        incomingOrderService.submitIncomingOrder(id);
        return Res.success();
    }

    /*入库单导出*/
    @GetMapping("/incoming-order/action-export")
    @ApiOperation(value = "导出入库单", notes = "导出入库单")
    public ResponseEntity<Void> exportIncomingOrder(HttpServletResponse response, QueryIncomingOrderParam param) throws IOException {
        param.setNeedExport(true);
        List<IncomingOrderExportVo> list = incomingOrderService.queryIncomingOrderList(param);
        EasyExcelUtils.exportOrdinaryExcel(response,list,"入库单导出列表","入库单导出列表", IncomingOrderExportVo.class);
        return Res.success();
    }

}
