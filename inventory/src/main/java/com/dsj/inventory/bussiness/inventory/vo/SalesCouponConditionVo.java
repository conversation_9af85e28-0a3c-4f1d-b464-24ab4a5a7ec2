package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 优惠券条件
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SalesCouponConditionVo", description="优惠券条件")
@ToString(callSuper = true)
public class SalesCouponConditionVo {

    @ApiModelProperty(value = "满元")
    private Integer salesPrice;

}
