package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.AcceptOrderGoods;
import com.dsj.inventory.bussiness.inventory.mapper.AcceptOrderGoodsMapper;
import com.dsj.inventory.bussiness.inventory.service.AcceptOrderGoodsService;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderGoodsVo;
import com.dsj.inventory.common.enumeration.TrueEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 进销存验收单商品明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@Service
public class AcceptOrderGoodsServiceImpl extends ServiceImpl<AcceptOrderGoodsMapper, AcceptOrderGoods> implements AcceptOrderGoodsService {

    @Override
    public List<AcceptOrderGoodsVo> getAcceptOrderGoodsList(Long orderId) {
        return this.baseMapper.getAcceptOrderGoodsList(orderId);
    }

    @Override
    public void markAsDeletedByOrderId(Long orderId) {
        this.baseMapper.markAsDeletedByOrderId(orderId);
    }

    @Override
    public AcceptOrderGoods getByOrderIdAndOrgMedicineId(Long orderId, Long organizationMedicineId) {
        return this.baseMapper.selectByOrderIdAndOrgMedicineId(orderId, organizationMedicineId);
    }
}
