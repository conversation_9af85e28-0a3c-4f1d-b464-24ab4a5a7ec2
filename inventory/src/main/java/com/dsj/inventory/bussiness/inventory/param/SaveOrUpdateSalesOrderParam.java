package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 销售单
 * <AUTHOR>
 * @date 2023/04/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateSalesOrderParam", description="新增or编辑 销售单参数")
public class SaveOrUpdateSalesOrderParam {

    @ApiModelProperty(hidden = true, value = "id,更新是传入")
    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(hidden = true,value = "销售单号")
    private String code;

    @ApiModelProperty(value = "销售员Id")
    private Long saleId;

    @ApiModelProperty(value = "订单状态：1 挂单 2已完成 3已退单")
    private Integer orderState;

    @ApiModelProperty(value = "退单时间")
    private LocalDateTime returnDate;

    @ApiModelProperty(value = "退单原因")
    private String returnReason;

    @ApiModelProperty(value = "挂单时间")
    private LocalDateTime hangDate;

    @ApiModelProperty(value = "挂单备注")
    private String hangRemark;

    @ApiModelProperty(hidden = true,value = "收银员id")
    private Long cashierId;

    @ApiModelProperty(value = "应收")
    private Integer receivablePrice;

    @ApiModelProperty(value = "优惠")
    private Integer preferentialPrice;

    @ApiModelProperty(value = "抹零")
    private Integer deductPrice;

    @ApiModelProperty(value = "合计")
    private Integer totalPrice;

    @ApiModelProperty(value = "实收")
    private Integer actualPrice;

    @ApiModelProperty(value = "找零")
    private Integer giveChangePrice;

    @ApiModelProperty(hidden = true,value = "商品数量")
    private Integer goodsAmount;

    @ApiModelProperty(value = "本次积分")
    private Integer integral = 0;

    @ApiModelProperty(value = "扣除积分")
    private Integer deductIntegral = 0;

    @ApiModelProperty(value = "关联处方类型 1常规处方  2外部处方（拍照处方）")
    private Integer prescriptionType;

    @ApiModelProperty(value = "处方Id，逗号分隔")
    private String prescriptionIds;

    @ApiModelProperty(hidden = true,value = "最后结算时间")
    private LocalDateTime settlementDate;

    @ApiModelProperty(value = "会员Id")
    private Long memberId;

    @ApiModelProperty(value = "活动Id")
    private Long activityId;

    @ApiModelProperty(value = "活动减额")
    private Integer activityReducePrice;

    @ApiModelProperty(value = "优惠券Id")
    private Long couponId;

    @ApiModelProperty(value = "优惠券金额")
    private Integer couponPrice;

    @ApiModelProperty(hidden = true,value = "支付方式")
    private String payWay;

    @ApiModelProperty(value = "支付信息，json字符串")
    private String payInfo;

    @ApiModelProperty(value = "储值")
    private Integer storedPay = 0;

    @ApiModelProperty(value = "积分抵扣")
    private Integer integralPay = 0;

    @ApiModelProperty(hidden = true,value = "商品信息")
    private String goodsInfo;

    @ApiModelProperty(value = "商品明细")
    private List<SaveOrUpdateSalesGoodsParam> goodsList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
