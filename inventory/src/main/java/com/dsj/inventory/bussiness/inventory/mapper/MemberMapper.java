
package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.Member;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberParam;
import com.dsj.inventory.bussiness.inventory.vo.MemberExportVo;
import com.dsj.inventory.bussiness.inventory.vo.MemberVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存会员 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface MemberMapper extends BaseMapper<Member> {

    /**
     * 获取会员分页列表
     * @param page
     * @param param
     * @return
     */
    List<MemberVo> queryMemberPage(@Param("page") Page page, @Param("param") QueryMemberParam param);

    /**
     * 获取会员详情
     * @param id
     * @return
     */
    MemberVo getMemberById(Long id);

    /**
     * 导出会员列表
     * @param param
     * @return
     */
    List<MemberExportVo> queryMemberList(@Param("param") QueryMemberParam param);

}
