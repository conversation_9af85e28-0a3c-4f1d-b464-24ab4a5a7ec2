package com.dsj.inventory.bussiness.inventory.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.dsj.inventory.framework.config.easyExcel.LocalDateTimeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.time.LocalDateTime;

/**
 * 销售单导出
 * <AUTHOR>
 * @date 2023/04/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SalesOrderExportVo", description="销售单导出实体")
@ToString(callSuper = true)
@ColumnWidth(25)
@HeadRowHeight(20)
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER)//内容样式
public class SalesOrderExportVo {

    @ApiModelProperty(value = "销售单号")
    @ExcelProperty(value = "销售单号")
    private String code;

    @ApiModelProperty(value = "订单状态：1 挂单 2已完成 3已退单")
    @ExcelProperty(value = "销售状态")
    private String orderState;

    @ApiModelProperty(value = "收银员名称")
    @ExcelProperty(value = "收银员")
    private String cashierName;

    @ApiModelProperty(value = "支付方式")
    @ExcelProperty(value = "支付方式")
    private String payWay;

    @ApiModelProperty(value = "应收")
    @ExcelProperty(value = "应收金额")
    private Integer receivablePrice;

    @ApiModelProperty(value = "实收")
    @ExcelProperty(value = "实收金额")
    private String actualPrice;

    @ApiModelProperty(value = "商品信息")
    @ExcelProperty(value = "所购商品及数量")
    private String goodsInfo;

    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "创建时间",converter = LocalDateTimeConverter.class)
    private LocalDateTime createTime;

}
