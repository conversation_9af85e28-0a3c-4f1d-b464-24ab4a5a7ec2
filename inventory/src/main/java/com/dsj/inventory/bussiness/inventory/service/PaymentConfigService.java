package com.dsj.inventory.bussiness.inventory.service;

import com.dsj.inventory.bussiness.inventory.entity.PaymentConfig;
import com.dsj.inventory.bussiness.inventory.param.PaymentConfigParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 付款方式配置 Service 接口
 */
public interface PaymentConfigService {

    /**
     * 根据付款方式ID删除所有配置项
     *
     * @param paymentMethodId 付款方式ID
     */
    void deleteByPaymentMethodId(Long paymentMethodId);

    /**
     * 批量保存付款方式的配置项（先删后插模式）
     *
     * @param paymentMethodId 付款方式ID
     * @param organizationId  组织ID
     * @param configParams    配置参数列表
     */
    void replacePaymentConfigs(Long paymentMethodId, Long organizationId, List<PaymentConfigParam> configParams);
    
    /**
     * 批量直接插入配置项
     * @param configs 配置实体列表
     */
    void saveAllConfigs(List<PaymentConfig> configs);


    /**
     * 根据付款方式ID查询其所有配置项
     *
     * @param paymentMethodId 付款方式ID
     * @return 配置项列表
     */
    List<PaymentConfig> listByPaymentMethodId(Long paymentMethodId);

    /**
     * 根据付款方式ID查询其所有启用的配置项
     *
     * @param paymentMethodId 付款方式ID
     * @return 启用的配置项列表
     */
    List<PaymentConfig> listEnabledByPaymentMethodId(Long paymentMethodId);
    
    /**
     * 根据一批付款方式ID查询它们的所有配置项，并按付款方式ID分组
     *
     * @param paymentMethodIds 付款方式ID集合
     * @return Key为付款方式ID，Value为对应的配置项列表的Map
     */
    Map<Long, List<PaymentConfig>> mapAllConfigsByPaymentMethodIds(Set<Long> paymentMethodIds);

    /**
     * 根据一批付款方式ID查询它们所有启用的配置项，并按付款方式ID分组
     *
     * @param paymentMethodIds 付款方式ID集合
     * @return Key为付款方式ID，Value为对应的启用配置项列表的Map
     */
    Map<Long, List<PaymentConfig>> mapEnabledConfigsByPaymentMethodIds(Set<Long> paymentMethodIds);
} 