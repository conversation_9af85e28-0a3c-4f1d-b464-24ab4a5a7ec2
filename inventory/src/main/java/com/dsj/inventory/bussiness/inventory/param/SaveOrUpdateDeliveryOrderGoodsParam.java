package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 退货单商品明细
 * <AUTHOR>
 * @date 2023/04/13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateDeliveryOrderGoodsParam", description="新增or编辑 退货单商品明细参数")
public class SaveOrUpdateDeliveryOrderGoodsParam {

    @ApiModelProperty(hidden = true,value = "退货单Id")
    private Long orderId;

    @ApiModelProperty(value = "库存Id")
    private Long inventoryId;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(hidden = true,value = "通用名")
    private String goodsName;

    @ApiModelProperty(hidden = true,value = "商品名称")
    private String generalName;

    @ApiModelProperty(hidden = true,value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(hidden = true,value = "包装单位")
    private String packUnit;

    @ApiModelProperty(hidden = true,value = "规格型号")
    private String specification;

    @ApiModelProperty(hidden = true,value = "剂型")
    private String drugType;

    @ApiModelProperty(hidden = true,value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(hidden = true,value = "上市许可持有人")
    private String launchPermitHolder;

    @ApiModelProperty(hidden = true,value = "产地")
    private String producePlace;

    @ApiModelProperty(hidden = true,value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(hidden = true,value = "生产日期")
    private LocalDateTime produceDate;

    @ApiModelProperty(hidden = true,value = "有效期至")
    private LocalDateTime userDate;

    @ApiModelProperty(hidden = true,value = "批号")
    private String batchNumber;

    @ApiModelProperty(hidden = true,value = "货位Id")
    private Long shelfId;

    @ApiModelProperty(hidden = true,value = "批次（入库单号）")
    private String batch;

    @ApiModelProperty(hidden = true,value = "入库时的商品明细Id")
    private Long incomingGoodsId;

    @ApiModelProperty(hidden = true,value = "原单数量")
    private Double incomingAmount;

    @ApiModelProperty(hidden = true,value = "库存数量")
    private Double inventoryAmount;

    @ApiModelProperty(hidden = true,value = "已退货数量")
    private Double returnedAmount;

    @ApiModelProperty(value = "退货数量")
    private Double returnAmount;

    @ApiModelProperty(value = "赠送数量")
    private Double giveAmount;

    @ApiModelProperty(value = "出库含税价（退货单价）")
    private Integer returnPrice;

    @ApiModelProperty(value = "含税金额（出库含税价*退货数量）")
    private Integer returnTotalPrice;

    @ApiModelProperty(hidden = true,value = "出库成本均价（当时的进货价）")
    private Integer costPrice;

    @ApiModelProperty(hidden = true,value = "退货成本金额（出库成本均价*退货数量）")
    private Integer returnCostTotalPrice;

    @ApiModelProperty(hidden = true,value = "进退货差价（退货成本金额-含税金额）")
    private Integer disparityPrice;

    @ApiModelProperty(value = "退货原因")
    private String reason;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
