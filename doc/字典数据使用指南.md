# 字典数据使用指南

## 概述

本系统提供了两套字典数据管理工具，用于满足不同层级的配置需求：
- **系统字典 (SysDict)**：全局生效的标准化字典数据
- **组织字典 (OrganizationDict)**：组织级别的个性化字典数据

## 1. 系统字典 (SysDict)

### 1.1 功能特点
- **全局生效**：对系统内所有组织都生效
- **统一管理**：由系统管理员统一配置和维护
- **标准化**：提供系统级别的基础配置项
- **层级支持**：支持父子级别的字典结构

### 1.2 数据结构
```java
// 表名：sys_dict
public class SysDict {
    private Long id;           // 主键
    private String dictName;   // 字典名称
    private String dictCode;   // 字典编码（唯一）
    private String dictValue;  // 字典值
    private Long parentId;     // 父级ID（0表示顶级）
    private String dictType;   // 字典类型
    private String dictDesc;   // 字典描述
    private Integer isDelete;  // 删除标志
}
```

### 1.3 服务接口使用

#### 1.3.1 注入服务
```java
@Autowired
private SysDictService sysDictService;
```

#### 1.3.2 常用方法

**根据字典类型查询**
```java
// 查询指定类型的所有字典项
List<SysDict> dictList = sysDictService.getByDictType("ORDER_STATUS");
```

**根据字典编码查询**
```java
// 查询特定编码的字典项
SysDict dict = sysDictService.getByDictCode("ORDER_PENDING");
```

**查询子字典**
```java
// 查询某个字典下的所有直接子字典
List<SysDict> children = sysDictService.getByParentId(parentId);
```

**分页查询**
```java
Page<SysDict> page = new Page<>(1, 10);
IPage<SysDict> result = sysDictService.getDictPage(page, "订单", "ORDER_STATUS");
```

**新增字典**
```java
SysDict newDict = new SysDict();
newDict.setDictName("待处理");
newDict.setDictCode("ORDER_PENDING");
newDict.setDictValue("1");
newDict.setDictType("ORDER_STATUS");
newDict.setDictDesc("订单待处理状态");
boolean success = sysDictService.saveDict(newDict);
```

### 1.4 使用场景
- 订单状态、支付状态等业务状态配置
- 系统级别的枚举值定义
- 通用的分类配置
- 标准化的业务参数

## 2. 组织字典 (OrganizationDict)

### 2.1 功能特点
- **组织隔离**：只对特定组织生效，组织间数据隔离
- **个性化配置**：由组织管理员根据组织需求配置
- **灵活扩展**：支持组织特殊业务需求
- **权限分离**：组织管理员只能管理本组织的字典

### 2.2 数据结构
```java
// 表名：jxc_organization_dict
public class OrganizationDict {
    private Long id;             // 主键
    private Long organizationId; // 组织ID（关键字段）
    private String dictName;     // 字典名称
    private String dictCode;     // 字典编码
    private String dictValue;    // 字典值
    private String dictType;     // 字典类型
    private String dictDesc;     // 字典描述
    private Long parentId;       // 父级ID
    // 继承Entity的创建时间、更新时间等字段
}
```

### 2.3 服务接口使用

#### 2.3.1 注入服务
```java
@Autowired
private OrganizationDictService organizationDictService;
```

#### 2.3.2 常用方法

**根据组织和类型查询**
```java
// 查询指定组织的特定类型字典列表
Long organizationId = 1001L;
List<OrganizationDictVO> dictList = organizationDictService
    .listByOrgAndType(organizationId, "WAREHOUSE_TYPE");
```

**精确查询字典项**
```java
// 根据组织ID、字典类型和编码查询具体字典项
OrganizationDictVO dict = organizationDictService
    .getByOrgTypeAndCode(organizationId, "WAREHOUSE_TYPE", "COLD_STORAGE");
```

**分页查询**
```java
QueryOrganizationDictParam param = new QueryOrganizationDictParam();
param.setOrganizationId(organizationId);
param.setDictType("WAREHOUSE_TYPE");
param.setPage(1);
param.setSize(10);
Page<OrganizationDictVO> result = organizationDictService
    .queryOrganizationDictPage(param);
```

**新增或更新字典**
```java
SaveOrUpdateOrganizationDictParam param = new SaveOrUpdateOrganizationDictParam();
param.setOrganizationId(organizationId);
param.setDictName("冷库");
param.setDictCode("COLD_STORAGE");
param.setDictValue("1");
param.setDictType("WAREHOUSE_TYPE");
param.setDictDesc("冷藏仓库类型");
Long dictId = organizationDictService.saveOrUpdateOrganizationDict(param);
```

**删除字典**
```java
Boolean success = organizationDictService.deleteOrganizationDict(dictId);
```

### 2.4 使用场景
- 组织特有的仓库类型配置
- 组织自定义的商品分类
- 组织特殊的业务流程状态
- 组织个性化的参数设置

## 3. 最佳实践

### 3.1 选择原则
- **系统字典**：用于标准化、通用性的配置
- **组织字典**：用于个性化、组织特有的配置

### 3.2 编码规范
```java
// 字典类型命名：使用大写字母和下划线
"ORDER_STATUS"、"PAYMENT_TYPE"、"WAREHOUSE_TYPE"

// 字典编码命名：使用大写字母和下划线，具有语义性
"ORDER_PENDING"、"PAYMENT_ALIPAY"、"WAREHOUSE_NORMAL"
```

### 3.3 数据一致性
- 同一组织内，相同类型和编码的字典项唯一
- 系统字典的编码全局唯一
- 删除字典前需检查是否有子字典项

### 3.4 权限控制
- 系统字典：仅系统管理员可操作
- 组织字典：组织管理员只能操作本组织数据

## 4. 注意事项

### 4.1 性能考虑
- 字典数据通常变化不频繁，建议使用缓存
- 批量查询时优先使用类型查询，避免逐个查询

### 4.2 数据安全
- 组织字典严格按组织ID隔离，避免数据泄露
- 删除操作需要检查关联关系，防止数据不一致

### 4.3 扩展性
- 新增字典类型时，建议先在系统字典中定义标准
- 组织字典可以在系统字典基础上进行个性化扩展

## 5. 常见问题

### Q1: 如何判断使用哪种字典？
**A**: 如果是所有组织都需要的标准配置，使用系统字典；如果是组织特有的配置，使用组织字典。

### Q2: 组织字典可以覆盖系统字典吗？
**A**: 不能直接覆盖，但可以在业务逻辑中优先使用组织字典，系统字典作为默认值。

### Q3: 如何实现字典的缓存？
**A**: 建议在Service层添加缓存注解，或使用Redis等缓存中间件。

### Q4: 字典数据如何初始化？
**A**: 系统字典通过SQL脚本初始化；组织字典可以在组织创建时初始化默认配置。

---
