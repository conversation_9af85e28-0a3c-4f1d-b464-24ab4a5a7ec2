package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryAcceptOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateAcceptOrderParam;
import com.dsj.inventory.bussiness.inventory.service.AcceptOrderService;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.EasyExcelUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 进销存验收单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@RestController
@Api(value = "AcceptOrderController", tags = "进销存验收单api")
public class AcceptOrderController {

    @Autowired
    private AcceptOrderService acceptOrderService;

    /*新增验收单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-4"})
    @PostMapping("/accept-order")
    @ApiOperation(value = "新增验收单", notes = "新增验收单")
    public ResponseEntity<AcceptOrderDetailVo> saveAcceptOrder(@RequestBody SaveOrUpdateAcceptOrderParam param){
        AcceptOrderDetailVo acceptVo = acceptOrderService.saveOrUpdateAcceptOrder(param);
        return Res.success(acceptVo);
    }

    /*编辑验收单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-4"})
    @PutMapping("/accept-order/{id}")
    @ApiOperation(value = "编辑验收单", notes = "编辑验收单")
    public ResponseEntity<AcceptOrderDetailVo> updateAcceptOrder(@PathVariable Long id, @RequestBody SaveOrUpdateAcceptOrderParam param){
        param.setId(id);
        AcceptOrderDetailVo acceptVo = acceptOrderService.saveOrUpdateAcceptOrder(param);
        return Res.success(acceptVo);
    }

    /*删除验收单*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-4"})
    @DeleteMapping("/accept-order/{id}")
    @ApiOperation(value = "删除验收单", notes = "删除验收单")
    public ResponseEntity<Void> deleteAcceptOrder(@PathVariable Long id){
        acceptOrderService.deleteAcceptOrder(id);
        return Res.success();
    }

    /*获取验收单列表*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-4"})
    @GetMapping("/accept-order")
    @ApiOperation(value = "获取验收单列表", notes = "获取验收单列表")
    public ResponseEntity<List<AcceptOrderVo>> queryAcceptOrderList(QueryAcceptOrderParam param){
        Page<AcceptOrderVo> page = acceptOrderService.queryAcceptOrderPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取验收单详情*/
    @GetMapping("/accept-order/{id}")
    @ApiOperation(value = "获取验收单详情", notes = "获取验收单详情")
    public ResponseEntity<AcceptOrderDetailVo> queryAcceptOrder(@PathVariable Long id){
        return Res.success(acceptOrderService.getAcceptOrderById(id));
    }

    /*提交验收单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-4"})
    @PostMapping("/accept-order/{id}/action-submit")
    @ApiOperation(value = "提交验收单", notes = "提交验收单")
    public ResponseEntity<Void> submitAcceptOrder(@PathVariable Long id){
        acceptOrderService.submitAcceptOrder(id);
        return Res.success();
    }

    /*验收单导出*/
    @GetMapping("/accept-order/action-export")
    @ApiOperation(value = "导出验收单", notes = "导出验收单")
    public ResponseEntity<Void> exportAcceptOrder(HttpServletResponse response, QueryAcceptOrderParam param) throws IOException {
        param.setNeedExport(true);
        List<AcceptOrderExportVo> list = acceptOrderService.queryAcceptOrderList(param);
        EasyExcelUtils.exportOrdinaryExcel(response,list,"验收单导出列表","验收单导出列表", AcceptOrderExportVo.class);
        return Res.success();
    }

}
