package com.dsj.inventory.bussiness.quality.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "MedicineFirstBusinessCreateParam", description = "药品首营审批创建参数")
public class MedicineFirstBusinessCreateParam {

    @ApiModelProperty(value = "关联组织药品私域表ID", required = true)
    @NotNull(message = "药品ID不能为空")
    private Long orgMedicineMappingId;
    
    @ApiModelProperty(value = "关联供应商ID", required = true)
    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;

    @ApiModelProperty(value = "申请时间 (不填则默认为当前时间)")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "审批状态 (不填则默认为0-待审批)")
    private Integer status;

    @ApiModelProperty(value = "审批意见")
    private String approvalOpinion;

    @ApiModelProperty(value = "备注")
    private String remark;
} 