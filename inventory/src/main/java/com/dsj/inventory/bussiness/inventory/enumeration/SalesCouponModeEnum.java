package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 优惠券模式 1发放 2领取
 * <AUTHOR>
 */

@Getter
public enum SalesCouponModeEnum {

    GRANT(1, "发放"),
    GET(2, "领取"),
    ;

    private Integer code;

    private String desc;

    SalesCouponModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (SalesCouponModeEnum a : SalesCouponModeEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
