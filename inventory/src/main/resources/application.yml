server:
  port: 9016
  #servlet:
   # context-path: /medicine
  tomcat:
    #最小线程数
    min-spare-threads: 100
    #最大线程数
    max-threads: 800

spring:
  profiles:
    active: home
    include:
      - datasource
  application:
    name: dsj-inventory
  servlet:
    multipart:
      max-file-size: 512MB      # Max file size，默认1M
      max-request-size: 512MB   # Max request size，默认10M
      location: /data/tmp
  redis:
    host: ${pocky.redis.ip}
    password: ${pocky.redis.password}
    port: ${pocky.redis.port}
    database: ${pocky.redis.database}
  cache:
    type: GENERIC
  main:
    allow-bean-definition-overriding: true


management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
      enabled: true

authentication:
  expire: 2592000              # token有效期为30天
  adminExpire: 2592000           # 管理员token有效期为30天
  refreshExpire: 2592000        # 刷新token有效期为30小时

### xxl-job email
xxl.job:
  accessToken:
  i18n:
  # 以下是client 执行器端配置
  admin:
    addresses: http://127.0.0.1:9091/xxl-job-admin
  executor:
    appname: ${spring.application.name}
    ip:
    port: 0
    registryLazy: 10000  # 延迟10秒注册，防止首次启动报错
    logpath: ${logging.file.path}/${spring.application.name}/xxl/
    logretentiondays: -1

logging:
  file:
    path: ~/logs/inventory
    name: ${logging.file.path}/${spring.application.name}/root.log

threadpool:
  # 核心线程池数
  corePoolSize: 8
  #最大线程
  maxPoolSize: 16
  #队列容量
  queueCapacity: 5
  #空闲线程存活时间
  keepAliveSeconds: 300

feign:
  client:
    config:
      syqb-dict-service:
        connectTimeout: 5000
        readTimeout: 5000
        loggerLevel: full
  httpclient:
    enabled: true
