package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询销售单
 * <AUTHOR>
 * @date 2023/04/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QueryIncomingOrderParam",description = "查询销售单参数")
public class QuerySalesOrderParam extends BasePage {

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "收银员id")
    private Long cashierId;

    @ApiModelProperty(value = "会员Id")
    private Long memberId;

    @ApiModelProperty(value = "订单状态：1 挂单 2已完成 3已退单")
    private Integer orderState;

    @ApiModelProperty(value = "订单类型：1线下 2线上")
    private Integer orderType;

    @ApiModelProperty(value = "小票号模糊搜索")
    private String codeLike;

    @ApiModelProperty(value = "商品名称模糊搜索")
    private String goodsNameLike;

    @ApiModelProperty(value = "创建时间，查询条件--起始时间")
    private String createStart;

    @ApiModelProperty(value = "创建时间，查询条件--结束时间")
    private String createEnd;

    @ApiModelProperty(value = "是否包含商品明细 0否1是，默认不包含")
    private Integer containGoodsList = 0;

}
