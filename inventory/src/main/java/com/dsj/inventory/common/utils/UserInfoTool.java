package com.dsj.inventory.common.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 处理人员信息工具
 * 根据传入参数自动补全其他信息
 * 如根据根据身份证补全 年龄、性别
 *
 * <AUTHOR>
 * @date 2023年10月11日 10:44:06
 */
@Slf4j
public class UserInfoTool {

    public static <T> T fillUserInfo(T t) {
        JSONObject userInfo = JSONUtil.parseObj(t);
        if(userInfo.containsKey("card") & CommonUtils.isNotEmpty(userInfo.get("card"))){
            //根据身份证处理年龄和性别,对象原已有年龄和性别不处理。空的就处理
            if(!userInfo.containsKey("age")){
                try {
                    userInfo.set("age",Integer.valueOf(DateUtils.getAgeByBirthday(userInfo.get("card").toString().substring(6,14))));
                }catch (Exception e){
                    log.error("计算年龄出错，身份证为："+ userInfo.get("card").toString());
                }
            }
            if(!userInfo.containsKey("gender")){
                try {
                    userInfo.set("gender",CommonUtils.getGenderByCard(userInfo.get("card").toString()));
                }catch (Exception e){
                    log.error("计算性别出错，身份证为："+ userInfo.get("card").toString());
                }
            }
        }
        return (T) JSONUtil.toBean(userInfo,t.getClass());
    }
}

