package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.MedicineTemplateRMedicine;
import com.dsj.inventory.bussiness.inventory.vo.MedicineTemplateMedicineVo;

import java.util.List;

/**
 * <p>
 * 药品拉取模板药品信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
public interface MedicineTemplateRMedicineMapper extends BaseMapper<MedicineTemplateRMedicine> {

    /**
     * 获取商品明细
     * @param templateId
     * @return
     */
    List<MedicineTemplateMedicineVo> getMedicineTemplateMedicineList(Long templateId);

}
