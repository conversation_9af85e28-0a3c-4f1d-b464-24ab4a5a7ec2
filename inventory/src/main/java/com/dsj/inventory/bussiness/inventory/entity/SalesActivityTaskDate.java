package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 销售活动任务日期
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_sales_activity_task_date")
@ApiModel(value="SalesActivityTaskDate", description="销售活动任务日期")
public class SalesActivityTaskDate extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动Id")
    @TableField("activity_id")
    private Long activityId;

    @ApiModelProperty(value = "任务日期")
    @TableField("task_date")
    private LocalDateTime taskDate;

    @ApiModelProperty(value = "生效状态 0未生效 1生效中")
    @TableField("assert_state")
    private Integer assertState;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}