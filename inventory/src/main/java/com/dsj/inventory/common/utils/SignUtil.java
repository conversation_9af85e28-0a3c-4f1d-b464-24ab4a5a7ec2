package com.dsj.inventory.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;
import java.util.*;

@Slf4j
public class SignUtil {
    /**
     * 生成签名sign
     * 加密前：secretId=wx123456789&timestamp=1583332804914&secretKey=7214fefff0cf47d7950cb2fc3b5d670a
     * 加密后：E2B30D3A5DA59959FA98236944A7D9CA
     */
    public static String createSign(SortedMap<String, String> params, String key,String nonestr){
        StringBuilder sb = new StringBuilder();
        Set<Map.Entry<String, String>> es =  params.entrySet();
        Iterator<Map.Entry<String,String>> it =  es.iterator();
        //生成
        while (it.hasNext()){
            Map.Entry<String,String> entry = it.next();
            String k = entry.getKey();
            String v = entry.getValue();
            if(null != v && !"".equals(v) && !"sign".equals(k) && !"secretKey".equals(k)){
                sb.append(k+"="+v+"&");
            }
        }
        sb.append("secretKey=").append(key);
        String sign = MD5("" + nonestr.charAt(0)+nonestr.charAt(3) + sb.toString() + nonestr.charAt(5) + nonestr.charAt(2));
        return sign;
    }

    /**
     * 校验签名
     */
    public static Boolean isCorrectSign(SortedMap<String, String> params, String key,String nonestr){
        String sign = createSign(params,key,nonestr);
        String requestSign = params.get("sign");
        log.info("通过用户发送数据获取新签名：{}", sign);
        return requestSign.equals(sign);
    }

    /**
     * md5常用工具类
     */
    public static String MD5(String data){
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte [] array = md5.digest(data.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte item : array) {
                sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
            }
            return sb.toString().toLowerCase();
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 生成uuid
     */
    public static String generateUUID(){
        String uuid = UUID.randomUUID().toString().replaceAll("-","").substring(0,32);
        return uuid;
    }

    public static void main(String[] args) {

        //第二步：用户端发起请求，生成签名后发送请求
        String secretKey = "gt4o2ywc3i6wegc76fhu";
        String secretId = "6746599545093953";
        String timestamp = "1702886983675";
        String nonestr = "40289041";
        //生成签名
        SortedMap<String, String> sortedMap = new TreeMap<>();
        sortedMap.put("secretId", secretId);
        sortedMap.put("timestamp", timestamp);
        System.out.println("签名："+ com.dsj.inventory.common.utils.SignUtil.createSign(sortedMap, secretKey,nonestr));

        //第三步：校验签名
        SortedMap<String, String> sortedMap12 = new TreeMap<>();
        sortedMap12.put("secretId", secretId);
        sortedMap12.put("timestamp", timestamp);
        sortedMap12.put("sign", "");
        //3.校验签名
        Boolean flag = com.dsj.inventory.common.utils.SignUtil.isCorrectSign(sortedMap12, secretId,nonestr);
        if(flag){
            System.out.println("签名验证通过");
        }else {
            System.out.println("签名验证未通过");
        }
    }

}
