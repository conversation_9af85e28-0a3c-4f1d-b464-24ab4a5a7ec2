# dsj-inventory 库存管理系统

## 项目简介

`dsj-inventory` 是一个基于 Spring Boot 和 Spring Cloud Alibaba 构建的现代化库存管理系统。它旨在提供稳定、高效、安全的库存管理解决方案。

## 技术栈

*   **核心框架**: Spring Boot `2.3.9.RELEASE`
*   **微服务**: Spring Cloud `Hoxton.SR6`, Spring Cloud Alibaba `2.2.1.RELEASE`
*   **持久层**: MyBatis
*   **数据库**: MySQL
*   **API文档**: Knife4j
*   **工具库**: Lombok
*   **JDK版本**: Java `1.8`

## 项目结构

项目遵循标准的MVC分层架构：

```
dsj-inventory
├── inventory                      # 主模块
│   ├── src
│   │   ├── main
│   │   │   ├── java
│   │   │   │   └── com/dsj/inventory
│   │   │   │       ├── InventoryApplication.java  # Spring Boot 启动类
│   │   │   │       ├── bussiness                 # 业务逻辑层
│   │   │   │       │   └── inventory             # 库存核心业务
│   │   │   │       │       ├── controller        # 控制器 (API接口)
│   │   │   │       │       ├── service           # 服务接口
│   │   │   │       │       │   └── impl          # 服务实现
│   │   │   │       │       ├── mapper            # 数据访问层 (MyBatis Mapper)
│   │   │   │       │       ├── entity            # 数据库实体
│   │   │   │       │       ├── vo                # 视图对象 (返回给前端)
│   │   │   │       │       └── param             # 参数对象 (接收请求)
│   │   │   │       ├── common                    # 公共模块 (工具类, 常量等)
│   │   │   │       └── framework                 # 框架层 (配置, AOP, 拦截器等)
│   │   │   └── resources
│   │   │       ├── application.yml       # 应用主配置文件
│   │   │       ├── application-dev.yml   # 开发环境配置
│   │   │       ├── application-prod.yml  # 生产环境配置
│   │   │       └── mapper                # MyBatis XML映射文件
│   └── pom.xml                       # Maven 配置文件 (inventory模块)
├── pom.xml                           # Maven 配置文件 (根项目)
└── README.md                         # 项目说明文档
```

### 关键目录说明

*   **`inventory/src/main/java/com/dsj/inventory/bussiness`**: 核心业务逻辑实现。
*   **`inventory/src/main/java/com/dsj/inventory/common`**: 通用工具类、常量、枚举等。
*   **`inventory/src/main/java/com/dsj/inventory/framework`**: 框架级别的配置、切面、拦截器等。
*   **`inventory/src/main/resources`**: 配置文件和MyBatis的XML映射文件。

## 快速开始

### 环境要求

*   JDK 1.8+
*   Maven 3.6+
*   MySQL 5.7+
*   Redis (如果使用了缓存或分布式锁)

### 安装与运行

1.  **克隆项目**:
    ```bash
    git clone <repository-url>
    cd dsj-inventory
    ```
2.  **配置数据库**:
    *   在MySQL中创建数据库，例如 `dsj_inventory`。
    *   修改 `inventory/src/main/resources/application-dev.yml` 中的数据库连接信息 (URL, username, password)。
3.  **编译打包**:
    ```bash
    mvn clean package -DskipTests
    ```
4.  **运行**:
    ```bash
    java -jar inventory/target/inventory-1.0.0.jar
    ```
    或者在IDE中直接运行 `InventoryApplication.java`。

### API文档

项目启动后，可以通过以下地址访问API文档（由Knife4j生成）：

`http://localhost:端口号/doc.html` (请将"端口号"替换为实际的应用端口)

## 开发规范

请参考 `.cursor/rules/java-dev-guidelines.mdc` 文件获取详细的开发规范和最佳实践。

## 参与贡献

1.  Fork 本仓库
2.  创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3.  提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4.  推送到分支 (`git push origin feature/AmazingFeature`)
5.  创建新的 Pull Request

欢迎对本项目做出贡献！
