package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.Shelf;
import com.dsj.inventory.bussiness.inventory.param.MoveGoodsParam;
import com.dsj.inventory.bussiness.inventory.param.QueryShelfParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateShelfParam;
import com.dsj.inventory.bussiness.inventory.vo.ShelfVo;

import java.util.List;

/**
 * <p>
 * 进销存货架信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
public interface ShelfService extends IService<Shelf> {

    /**
     * 新增、编辑 货架
     * @param param
     * @return
     */
    ShelfVo saveOrUpdateShelf(SaveOrUpdateShelfParam param);

    /**
     * 删除 货架
     * @param id
     */
    void deleteShelf(Long id);

    Boolean checkName(Long id, Long organizationId, String name, Long parentId);

    /**
     * 获取货架分页列表
     * @param param
     * @return
     */
    Page<ShelfVo> queryShelfPage(QueryShelfParam param);

    /**
     * 货架批量移动商品
     * @param id
     * @param params
     */
    void batchMoveShelf(Long id, List<MoveGoodsParam> params);

    /**
     * 初始化货架
     */
    void initializeShelf();


}
