package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 进销存预警记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_warning_log")
@ApiModel(value="WarningLog", description="进销存预警记录")
public class WarningLog extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织Id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "私域商品Id")
    @TableField("organization_medicine_id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "预警类型 1缺货 2积货 3滞销 4近效期")
    @TableField("type")
    private Integer type;

    @ApiModelProperty(value = "库存Id")
    @TableField("inventory_id")
    private Long inventoryId;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
