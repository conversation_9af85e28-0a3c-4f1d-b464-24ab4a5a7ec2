package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 销售活动任务
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SalesActivityTaskVo", description="销售活动任务实体")
@ToString(callSuper = true)
public class SalesActivityTaskVo {

    private Long id;

    @ApiModelProperty(value = "组织id")
    private Long organizationId;

    @ApiModelProperty(value = "模块分类，1 云药快 ；2 外部；3 在线医院；4 商城；5进销存")
    private Integer moduleType;

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "是否执行 0未执行 1已执行")
    private Integer executeState;

    @ApiModelProperty(value = "活动类别，1单品活动 2会员日活动 3整单活动")
    private Integer activityCategory;

    @ApiModelProperty(value = "活动名称")
    private String activityNames;

    @ApiModelProperty(value = "周期")
    private String cycle;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "活动List")
    private List<SalesActivityTaskMappingVo> activityList;

    @ApiModelProperty(value = "任务周期")
    private List<SalesActivityTaskCycleVo> cycleList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}

