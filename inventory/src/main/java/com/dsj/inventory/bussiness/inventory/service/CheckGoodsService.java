package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.CheckGoods;
import com.dsj.inventory.bussiness.inventory.vo.CheckGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存盘点计划商品明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface CheckGoodsService extends IService<CheckGoods> {

    /**
     * 获取盘点计划商品明细列表
     * @param checkId
     * @return
     */
    List<CheckGoodsVo> getCheckGoodsListByCheckId(Long checkId);

}
