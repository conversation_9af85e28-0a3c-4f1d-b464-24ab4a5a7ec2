package com.dsj.inventory.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 密码校验工具
 *
 * <AUTHOR>
 * @date 2022/8/8 9:47
 */
public class PwdCheckUtil {

    /**
     * 密码必须包含大写、小写、数字和特殊字符，且长度是6~18位
     */
    private static final String PWD_REGEX = "^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[`~!@#$%^&*()-=_+;':\",./<>?])(?=\\S+$).{6,18}$";

    /**
     * 密码复杂度校验，判断有效性
     * @param password 密码信息
     * @return 校验密码是否合规有效
     */
    public static boolean isValidPassword(String password) {
        if (StringUtils.isBlank(password)) {
            return false;
        }
        return password.matches(PWD_REGEX);
    }
}

