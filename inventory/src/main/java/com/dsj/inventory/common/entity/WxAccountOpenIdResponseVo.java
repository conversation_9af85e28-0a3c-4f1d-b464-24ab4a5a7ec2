package com.dsj.inventory.common.entity;

import com.dsj.inventory.common.entity.WxAccountOpenIdListVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 微信公众号OpenId请求响应内容
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class WxAccountOpenIdResponseVo {

    /**
     * 关注该公众账号的总用户数
     */
    private Integer total;

    /**
     * 拉取的OPENID个数，最大值为10000
     */
    private Integer count;

    /**
     * 拉取列表的最后一个用户的OPENID
     */
    private String next_openid;

    /**
     * 列表数据，OPENID的列表
     */
    private WxAccountOpenIdListVo data;

}
