
package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 进销存收银员值班记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_cashier_working")
@ApiModel(value="CashierWorking", description="进销存收银员值班记录")
public class CashierWorking extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织Id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "收银员id")
    @TableField("cashier_id")
    private Long cashierId;

    @ApiModelProperty(value = "开始时间")
    @TableField("start_date")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "结束时间")
    @TableField("end_date")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "实收金额")
    @TableField("actual_price")
    private Integer actualPrice;

    @ApiModelProperty(value = "收款合计")
    @TableField("total_price")
    private Integer totalPrice;

    @ApiModelProperty(value = "找零金额")
    @TableField("give_change_price")
    private Integer giveChangePrice;

    @ApiModelProperty(value = "抹零金额")
    @TableField("deduct_price")
    private Integer deductPrice;

    @ApiModelProperty(value = "优惠金额")
    @TableField("preferential_price")
    private Integer preferentialPrice;

    @ApiModelProperty(value = "退款金额")
    @TableField("return_price")
    private Integer returnPrice;

    @ApiModelProperty(value = "充值金额")
    @TableField("recharge_price")
    private Integer rechargePrice;

    @ApiModelProperty(value = "实际充值金额")
    @TableField("recharge_real_price")
    private Integer rechargeRealPrice;

    @ApiModelProperty(value = "赠送充值金额")
    @TableField("recharge_give_price")
    private Integer rechargeGivePrice;

    @ApiModelProperty(value = "支付信息，json字符串")
    @TableField("pay_info")
    private String payInfo;

    @ApiModelProperty(value = "储值")
    @TableField("stored_pay")
    private Integer storedPay;

    @ApiModelProperty(value = "活动减额")
    @TableField("activity_reduce_price")
    private Integer activityReducePrice;

    @ApiModelProperty(value = "优惠券金额")
    @TableField("coupon_price")
    private Integer couponPrice;

    @ApiModelProperty(value = "积分抵扣")
    @TableField("integral_pay")
    private Integer integralPay;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
