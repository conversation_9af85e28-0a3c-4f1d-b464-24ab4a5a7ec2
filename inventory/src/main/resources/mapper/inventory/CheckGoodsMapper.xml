<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.CheckGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.CheckGoods">
    <result column="id" property="id" />
        <result column="organization_medicine_id" property="organizationMedicineId" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        check_id, organization_medicine_id, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="getCheckGoodsListByCheckId" resultType="com.dsj.inventory.bussiness.inventory.vo.CheckGoodsVo">
        SELECT distinct jcg.id
                      ,jcg.check_id checkId
                      ,jcg.organization_medicine_id organizationMedicineId
                      ,jcg.inventory_id inventoryId
                      ,jcg.goods_name goodsName
                      ,jcg.general_name generalName
                      ,jcg.goods_code goodsCode
                      ,jcg.pack_unit packUnit
                      ,jcg.specification
                      ,jcg.drug_type drugType
                      ,jcg.manufacturer
                      ,jcg.bar_code barCode
                      ,jcg.approval_number approvalNumber
                      ,jcg.produce_date produceDate
                      ,jcg.user_date userDate
                      ,jcg.batch_number batchNumber
                      ,jcg.batch
                      ,jcg.shelf_id shelfId
                      ,js.name shelfName
                      ,jcg.price
                      ,(CASE WHEN jc.check_state = 2 THEN jcg.inventory_amount ELSE ji.inventory_amount END) inventoryAmount
                      ,jcg.check_amount checkAmount
        FROM jxc_check_goods jcg
        LEFT JOIN jxc_check jc ON jc.id = jcg.check_id AND jc.is_delete = 0
        LEFT JOIN jxc_inventory ji ON ji.id = jcg.inventory_id AND ji.is_delete = 0
        LEFT JOIN jxc_shelf js ON js.id = jcg.shelf_id AND js.is_delete = 0
        WHERE
            jcg.is_delete = 0
        AND jcg.check_id = #{checkId}
    </select>

</mapper>
