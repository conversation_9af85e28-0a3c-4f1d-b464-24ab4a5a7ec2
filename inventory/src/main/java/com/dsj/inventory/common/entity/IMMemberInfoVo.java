package com.dsj.inventory.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * IM 群主成员信息
 *  自定义字段没有列列举，需要时再加
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class IMMemberInfoVo {

    /*** 群成员 ID*/
    private String Member_Account;

    /*** 群内身份  包括 Owner 群主、Admin 群管理员以及 Member 群成员 */
    private String Role;

    /*** 入群时间 */
    private Integer JoinTime;

    /*** 该成员当前已读消息 Seq */
    private Integer MsgSeq;

    /*** 消息接收选项 */
    private String MsgFlag;

    /*** 最后发送消息的时间 */
    private Integer LastSendMsgTime;

    /*** 群名片 */
    private String NameCard;

    /*** 禁言状态 */
    private Integer MuteUntil;
}
