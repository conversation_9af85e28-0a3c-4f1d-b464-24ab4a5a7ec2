package com.dsj.inventory.bussiness.quality.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SupplierFirstBusinessQueryParam", description = "供应商首营审批查询参数")
public class SupplierFirstBusinessQueryParam extends BasePage {

    @ApiModelProperty(value = "供应商ID")
    private Long supplierId;

    @ApiModelProperty(value = "审批状态")
    private Integer status;

    @ApiModelProperty(value = "审批步骤 (1-采购经理审核, 2-质量经理审核, 3-质量负责人审核)")
    private Integer approvalStep;

    // Add other query fields as needed, e.g., date ranges for applyTime
} 