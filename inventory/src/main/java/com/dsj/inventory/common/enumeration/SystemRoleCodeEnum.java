package com.dsj.inventory.common.enumeration;

import lombok.Getter;

/**
 * 系统角色代码枚举
 */
@Getter
public enum SystemRoleCodeEnum {

    ADMIN("ADMIN", "超级管理员"),
    USER("USER", "普通用户"),
    // Add more roles as needed, for example:
    PURCHASE_MANAGER("PURCHASE_MANAGER", "采购经理"),
    QUALITY_MANAGER("QUALITY_MANAGER", "质量经理"),
    WAREHOUSE_MANAGER("WAREHOUSE_MANAGER", "仓库管理员"),
    PURCHASER("PURCHASER", "采购员"),
    QUALITY_SUPERVISOR("QUALITY_SUPERVISOR", "质量负责人");

    private final String code;
    private final String description;

    SystemRoleCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // Optional: Method to get enum from code
    public static SystemRoleCodeEnum fromCode(String code) {
        for (SystemRoleCodeEnum roleEnum : values()) {
            if (roleEnum.getCode().equalsIgnoreCase(code)) {
                return roleEnum;
            }
        }
        return null; // Or throw an exception for unknown code
    }
} 