package com.dsj.inventory.bussiness.quality.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.quality.entity.RejectionOrder;
import com.dsj.inventory.bussiness.quality.param.RejectionOrderQueryParam;
import org.apache.ibatis.annotations.Mapper;
import cn.hutool.core.util.StrUtil;

/**
 * 拒收单 Mapper
 */
@Mapper
public interface RejectionOrderMapper extends BaseMapper<RejectionOrder> {

    default Page<RejectionOrder> selectPage(RejectionOrderQueryParam param) {
        Page<RejectionOrder> page = new Page<>(param.getPage(), param.getSize());

        LambdaQueryWrapper<RejectionOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(param.getSourceOrderCode()), RejectionOrder::getSourceOrderCode, param.getSourceOrderCode())
                .eq(param.getSupplierId() != null, RejectionOrder::getSupplierId, param.getSupplierId())
                .eq(param.getStatus() != null, RejectionOrder::getStatus, param.getStatus());

        // 处理日期范围
        if (param.getStartDate() != null) {
            wrapper.ge(RejectionOrder::getCreateTime, param.getStartDate().atStartOfDay());
        }
        if (param.getEndDate() != null) {
            wrapper.le(RejectionOrder::getCreateTime, param.getEndDate().atTime(23, 59, 59));
        }
        wrapper.orderByDesc(RejectionOrder::getCreateTime);
        return this.selectPage(page, wrapper);
    }

} 