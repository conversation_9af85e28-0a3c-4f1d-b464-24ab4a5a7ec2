
package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.MemberRechargeRecord;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberRechargeRecordParam;
import com.dsj.inventory.bussiness.inventory.vo.MemberRechargeRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存会员充值记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface MemberRechargeRecordMapper extends BaseMapper<MemberRechargeRecord> {

    /**
     * 获取会员充值记录分页列表
     * @param page
     * @param param
     * @return
     */
    List<MemberRechargeRecordVo> queryMemberRechargeRecordPage(@Param("page") Page page, @Param("param") QueryMemberRechargeRecordParam param);

}
