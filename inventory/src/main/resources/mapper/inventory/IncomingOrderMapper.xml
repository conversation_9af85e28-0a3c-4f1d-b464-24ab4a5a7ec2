<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.IncomingOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.IncomingOrder">
    <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="order_id" property="orderId" />
        <result column="order_code" property="orderCode" />
        <result column="code" property="code" />
        <result column="bill_date" property="billDate" />
        <result column="remark" property="remark" />
        <result column="deal_state" property="dealState" />
        <result column="purchaser_id" property="purchaserId" />
        <result column="incoming_id" property="incomingId" />
        <result column="organization_supplier_id" property="organizationSupplierId" />
        <result column="total_price" property="totalPrice" />
        <result column="discount_total_price" property="discountTotalPrice" />
        <result column="carrier" property="carrier" />
        <result column="start_address" property="startAddress" />
        <result column="start_date" property="startDate" />
        <result column="transport_way" property="transportWay" />
        <result column="transport_tool" property="transportTool" />
        <result column="invoice_date" property="invoiceDate" />
        <result column="invoice_code" property="invoiceCode" />
        <result column="invoice_price" property="invoicePrice" />
        <result column="receive_order_number" property="receiveOrderNumber" />
        <result column="follow_goods_order_date" property="followGoodsOrderDate" />
        <result column="arrival_date" property="arrivalDate" />
        <result column="arrival_temp" property="arrivalTemp" />
        <result column="transport_humidity_record" property="transportHumidityRecord" />
        <result column="refrigerate_state" property="refrigerateState" />
        <result column="wholesaler_staff_number" property="wholesalerStaffNumber" />
        <result column="invoice_path" property="invoicePath" />
        <result column="follow_goods_order_path" property="followGoodsOrderPath" />
        <result column="check_report_path" property="checkReportPath" />
        <result column="goods_types" property="goodsTypes" />
        <result column="purchase_content" property="purchaseContent" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        organization_id, order_id, order_code, code, bill_date, remark, deal_state, purchaser_id, incoming_id, organization_supplier_id, total_price, discount_total_price, carrier, start_address, start_date, transport_way, transport_tool, invoice_date, invoice_code, invoice_price, receive_order_number, follow_goods_order_date, arrival_date, arrival_temp, transport_humidity_record, refrigerate_state, wholesaler_staff_number, invoice_path, follow_goods_order_path, check_report_path, goods_types, purchase_content, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="queryIncomingOrderPage" resultType="com.dsj.inventory.bussiness.inventory.vo.IncomingOrderVo">
        SELECT distinct jio.id
        ,jio.organization_id organizationId
        ,jio.order_id orderId
        ,jio.order_code orderCode
        ,jio.code
        ,jio.remark
        ,jio.deal_state dealState
        ,jio.organization_supplier_id organizationSupplierId
        ,jos.name organizationSupplierName
        ,jos.code organizationSupplierCode
        ,jio.total_price totalPrice
        ,jio.goods_types goodsTypes
        ,jio.purchase_content purchaseContent
        ,su.name incomingName
        ,jio.create_time createTime
        FROM jxc_incoming_order jio
        LEFT JOIN sys_user su ON su.id = jio.incoming_id AND su.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jio.organization_supplier_id AND jos.is_delete = 0
        WHERE
        jio.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jio.organization_id = #{param.organizationId}
        </if>
        <if test="param.dealState != null ">
            AND jio.deal_state = #{param.dealState}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jio.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jio.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        ORDER BY jio.create_time desc
    </select>

    <select id="getIncomingOrderById" resultType="com.dsj.inventory.bussiness.inventory.vo.IncomingOrderDetailVo">
        SELECT distinct jio.id
                      ,jio.organization_id organizationId
                      ,jio.order_id orderId
                      ,jio.order_code orderCode
                      ,jio.code
                      ,jio.bill_date billDate
                      ,jio.remark
                      ,jio.deal_state dealState
                      ,jio.organization_supplier_id organizationSupplierId
                      ,jos.name organizationSupplierName
                      ,jos.code organizationSupplierCode
                      ,jio.total_price totalPrice
                      ,jio.goods_types goodsTypes
                      ,jio.purchase_content purchaseContent
                      ,psu.name purchaserName
                      ,jio.purchaser_id purchaserId
                      ,asu.name acceptName
                      ,jio.accept_id acceptId
                      ,su.name incomingName
                      ,jio.incoming_id incomingId
                      ,jio.discount_total_price discountTotalPrice
                      ,jio.carrier
                      ,jio.start_address startAddress
                      ,jio.start_date startDate
                      ,jio.transport_way transportWay
                      ,jio.transport_tool transportTool
                      ,jio.invoice_date invoiceDate
                      ,jio.invoice_code invoiceCode
                      ,jio.invoice_price invoicePrice
                      ,jio.invoice_number invoiceNumber
                      ,jio.goods_types goodsTypes
                      ,jio.receive_order_number receiveOrderNumber
                      ,jio.follow_goods_order_date followGoodsOrderDate
                      ,jio.arrival_date arrivalDate
                      ,jio.arrival_temp arrivalTemp
                      ,jio.transport_humidity_record transportHumidityRecord
                      ,jio.refrigerate_state refrigerateState
                      ,jio.wholesaler_staff_number wholesalerStaffNumber
                      ,jio.invoice_path invoicePath
                      ,jio.follow_goods_order_path followGoodsOrderPath
                      ,jio.check_report_path checkReportPath
                      ,jio.purchase_content purchaseContent
                      ,jio.create_time createTime
        FROM jxc_incoming_order jio
        LEFT JOIN sys_user psu ON psu.id = jio.purchaser_id AND psu.is_delete = 0
        LEFT JOIN sys_user asu ON asu.id = jio.accept_id AND asu.is_delete = 0
        LEFT JOIN sys_user su ON su.id = jio.incoming_id AND su.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jio.organization_supplier_id AND jos.is_delete = 0
        WHERE
            jio.is_delete = 0
        AND jio.id = #{id}
    </select>

    <select id="queryIncomingOrderList" resultType="com.dsj.inventory.bussiness.inventory.vo.IncomingOrderExportVo">
        SELECT distinct jio.id
        ,jio.code
        ,jio.remark
        ,(CASE
        WHEN jio.deal_state = 0 THEN '暂存'
        WHEN jio.deal_state = 1 THEN '已完成'
        END) dealState
        ,ROUND(jio.total_price/100,2) totalPrice
        ,jio.goods_types goodsTypes
        ,jio.purchase_content purchaseContent
        ,jos.name organizationSupplierName
        ,jos.code organizationSupplierCode
        ,su.name incomingName
        ,jio.create_time createTime
        FROM jxc_incoming_order jio
        LEFT JOIN sys_user su ON su.id = jio.incoming_id AND su.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jio.organization_supplier_id AND jos.is_delete = 0
        WHERE
        jio.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jio.organization_id = #{param.organizationId}
        </if>
        <if test="param.dealState != null ">
            AND jio.deal_state = #{param.dealState}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jio.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jio.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        ORDER BY jio.create_time desc
    </select>

    <update id="deleteIncomingOrderByOrganizationId">
        UPDATE jxc_incoming_order jio, jxc_incoming_order_goods jiog
        SET jio.is_delete = 1, jiog.is_delete = 1
        WHERE jio.id = jiog.order_id
          AND jio.is_delete = 0
          AND jiog.is_delete = 0
          AND jio.organization_id = #{organizationId}
    </update>

    <select id="getIncomingOrderCount" resultType="java.lang.Integer">
        SELECT COUNT(1) number
        FROM jxc_incoming_order jio
        WHERE
        DATE_FORMAT(jio.create_time,'%Y-%m-%d') = #{day}
        <if test="organizationId != null and organizationId != '' ">
            AND jio.organization_id = #{organizationId}
        </if>
    </select>

</mapper>
