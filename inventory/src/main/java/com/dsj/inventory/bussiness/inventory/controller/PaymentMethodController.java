package com.dsj.inventory.bussiness.inventory.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryPaymentMethodParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdatePaymentMethodParam;
import com.dsj.inventory.bussiness.inventory.service.PaymentMethodService;
import com.dsj.inventory.bussiness.inventory.vo.PaymentMethodVO;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 组织付款方式 Controller
 */
@Api(tags = "组织付款方式管理")
@RestController
@RequestMapping("/api/payment-method")
@RequiredArgsConstructor
public class PaymentMethodController {

    private final PaymentMethodService paymentMethodService;

    @ApiOperation(value = "保存或更新组织付款方式", notes = "创建或修改组织付款方式及其配置项")
    @PostMapping("/save")
    public ResponseEntity<Long> saveOrUpdatePaymentMethod(@Valid @RequestBody SaveOrUpdatePaymentMethodParam param) {
        Long id = paymentMethodService.saveOrUpdatePaymentMethod(param);
        return Res.success(id);
    }

    @ApiOperation(value = "删除组织付款方式", notes = "根据ID删除组织付款方式及其配置项")
    @DeleteMapping("/{id}")
    public ResponseEntity<Boolean> deletePaymentMethod(
            @ApiParam(value = "组织付款方式ID", required = true) @PathVariable Long id) {
        Boolean result = paymentMethodService.deletePaymentMethod(id);
        return Res.success(result);
    }

    @ApiOperation(value = "获取组织付款方式", notes = "根据ID获取组织付款方式详情及其配置项")
    @GetMapping("/{id}")
    public ResponseEntity<PaymentMethodVO> getPaymentMethod(
            @ApiParam(value = "组织付款方式ID", required = true) @PathVariable Long id) {
        PaymentMethodVO vo = paymentMethodService.getPaymentMethod(id);
        return Res.success(vo);
    }

    @ApiOperation(value = "分页查询组织付款方式", notes = "根据条件分页查询组织付款方式列表")
    @GetMapping("/page")
    public ResponseEntity<List<PaymentMethodVO>> queryPaymentMethodPage(QueryPaymentMethodParam param) {
        Page<PaymentMethodVO> page = paymentMethodService.queryPaymentMethodPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    @ApiOperation(value = "获取组织启用的付款方式", notes = "获取指定组织已启用的所有付款方式")
    @GetMapping("/enabled/{organizationId}")
    public ResponseEntity<List<PaymentMethodVO>> listEnabledByOrganization(
            @ApiParam(value = "组织ID", required = true) @PathVariable Long organizationId,
            @ApiParam(value = "是否包含配置项") @RequestParam(required = false, defaultValue = "false") Boolean withConfigs) {
        List<PaymentMethodVO> list = paymentMethodService.listEnabledByOrganization(organizationId, withConfigs);
        return Res.success(list);
    }
} 