package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.IncomingOrder;
import com.dsj.inventory.bussiness.inventory.enumeration.IncomingOrderStateEnum;
import com.dsj.inventory.bussiness.inventory.param.QueryIncomingOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存入库单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface IncomingOrderMapper extends BaseMapper<IncomingOrder> {

    /**
     * 获取入库单分页列表
     * @param page
     * @param param
     * @return
     */
    List<IncomingOrderVo> queryIncomingOrderPage(@Param("page") Page page, @Param("param") QueryIncomingOrderParam param);

    /**
     * 获取入库单详情
     * @param id
     * @return
     */
    IncomingOrderDetailVo getIncomingOrderById(Long id);

    /**
     * 导出入库单列表
     * @param param
     * @return
     */
    List<IncomingOrderExportVo> queryIncomingOrderList(@Param("param") QueryIncomingOrderParam param);

    /**
     * 删除组织的入库单
     * @param organizationId
     */
    void deleteIncomingOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天入库单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getIncomingOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);
    
    /**
     * 检查验收单是否已经绑定到其他入库单
     * @param orderId 验收单ID
     * @param excludeIncomingOrderId 需要排除的入库单ID（可为null）
     * @return 绑定计数
     */
    default int countByOrderIdExcludeIncomingOrderId(Long orderId, Long excludeIncomingOrderId) {
        LambdaQueryWrapper<IncomingOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IncomingOrder::getOrderId, orderId);
        if (excludeIncomingOrderId != null) {
            wrapper.ne(IncomingOrder::getId, excludeIncomingOrderId);
        }
        return this.selectCount(wrapper);
    }
    
    /**
     * 更新入库单状态为已完成
     * @param id 入库单ID
     * @return 是否更新成功
     */
    default boolean updateStateToCompleted(Long id) {
        LambdaUpdateWrapper<IncomingOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(IncomingOrder::getId, id);
        updateWrapper.set(IncomingOrder::getDealState, IncomingOrderStateEnum.COMPLETED.getCode());
        return update(null, updateWrapper) > 0;
    }
    
    /**
     * 更新入库单绑定的验收单信息
     * @param id 入库单ID
     * @param acceptOrderId 验收单ID
     * @param acceptOrderCode 验收单编码
     * @return 是否更新成功
     */
    default boolean updateOrderIdAndCode(Long id, Long acceptOrderId, String acceptOrderCode) {
        LambdaUpdateWrapper<IncomingOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(IncomingOrder::getId, id);
        updateWrapper.set(IncomingOrder::getOrderId, acceptOrderId);
        updateWrapper.set(IncomingOrder::getOrderCode, acceptOrderCode);
        return update(null, updateWrapper) > 0;
    }
}
