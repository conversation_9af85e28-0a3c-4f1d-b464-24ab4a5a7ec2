package com.dsj.inventory.bussiness.quality.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel(description = "拒收单视图对象")
public class RejectionOrderVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "拒收单号")
    private String rejectionCode;

    @ApiModelProperty(value = "组织ID")
    private Long organizationId;

    @ApiModelProperty(value = "来源单据ID")
    private Long sourceOrderId;

    @ApiModelProperty(value = "来源单据号")
    private String sourceOrderCode;

    @ApiModelProperty(value = "来源单据类型 (1:收货单, 2:验收单, 3:入库单)")
    private Integer sourceOrderType;

    @ApiModelProperty(value = "供应商ID")
    private Long supplierId;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "状态 (1:待审核, 2:审核通过, 3:审核驳回)")
    private Integer status;

    @ApiModelProperty(value = "拒收商品总数")
    private Integer totalRejectionQuantity;

    @ApiModelProperty(value = "审核人ID")
    private Long auditUserId;

    @ApiModelProperty(value = "审核人姓名")
    private String auditUserName;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "审核意见")
    private String auditRemark;

    @ApiModelProperty(value = "制单备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "商品明细列表")
    private List<RejectionOrderGoodsVO> goodsList;
} 