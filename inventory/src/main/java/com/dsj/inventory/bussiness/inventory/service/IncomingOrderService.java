package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.IncomingOrder;
import com.dsj.inventory.bussiness.inventory.param.QueryIncomingOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateIncomingOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存入库单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface IncomingOrderService extends IService<IncomingOrder> {

    /**
     * 新增、编辑 入库单
     * @param param
     * @return
     */
    IncomingOrderDetailVo saveOrUpdateIncomingOrder(SaveOrUpdateIncomingOrderParam param);

    /**
     * 删除 入库单
     * @param id
     */
    void deleteIncomingOrder(Long id);

    /**
     * 获取入库单分页列表
     * @param param
     * @return
     */
    Page<IncomingOrderVo> queryIncomingOrderPage(QueryIncomingOrderParam param);

    /**
     * 获取入库单列表详情
     * @param id
     * @return
     */
    IncomingOrderDetailVo getIncomingOrderById(Long id);

    /**
     * 提交入库单
     * @param id
     */
    void submitIncomingOrder(Long id);

    /**
     * 导出入库单列表
     * @param param
     * @return
     */
    List<IncomingOrderExportVo> queryIncomingOrderList(QueryIncomingOrderParam param);

    /**
     * 删除组织的入库单
     * @param organizationId
     */
    void deleteIncomingOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天入库单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getIncomingOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);
}
