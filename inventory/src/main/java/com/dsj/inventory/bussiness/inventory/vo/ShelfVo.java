package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 货架
 * <AUTHOR>
 * @date 2023/04/03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ShelfVo", description="货架实体")
@ToString(callSuper = true)
public class ShelfVo {

    private Long id;

    @ApiModelProperty(value = "组织id")
    private Long organizationId;

    @ApiModelProperty(value = "货架名称")
    private String name;

    @ApiModelProperty(value = "父级id")
    private Long parentId;

    @ApiModelProperty(value = "根父级级到本级id串")
    private String levelIds;

    @ApiModelProperty(value = "货架类型 0普通货架1特殊货架")
    private Integer type;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "是否允许删除，0否1是")
    private Integer deleteFlag;

    @ApiModelProperty(value = "商品数量")
    private Integer goodsNumber;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "子集货架")
    private List<ShelfVo> children;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
