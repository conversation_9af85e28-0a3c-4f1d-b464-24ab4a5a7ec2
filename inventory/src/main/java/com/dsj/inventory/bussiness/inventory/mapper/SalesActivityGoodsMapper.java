package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityGoods;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存销售活动商品明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface SalesActivityGoodsMapper extends BaseMapper<SalesActivityGoods> {

    /**
     * 获取销售活动商品
     * @param activityId
     * @return
     */
    List<SalesActivityGoodsVo> querySalesActivityGoodsList(Long activityId);

}
