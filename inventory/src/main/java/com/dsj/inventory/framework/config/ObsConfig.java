package com.dsj.inventory.framework.config;

import com.obs.services.ObsClient;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @ClassName : ObsConfig
 * @Description : 华为云OBS 配置
 * @Date : 2022-07-25 16:23
 */
@Configuration
@Data
public class ObsConfig {

    @Value("${huawei.obs.ak}")
    private String ak;

    @Value("${huawei.obs.sk}")
    private String sk;

    @Value("${huawei.obs.upload.endPoint}")
    private String endPoint;

    @Bean
    public ObsClient getObsClient(){
        ObsClient obsClient = new ObsClient(ak, sk, endPoint);
        return obsClient;
    }

}
