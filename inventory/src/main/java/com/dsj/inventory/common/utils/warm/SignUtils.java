package com.dsj.inventory.common.utils.warm;

import com.dsj.inventory.common.utils.warm.MD5Util;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;

/**
 * 暖心医生sign生成工具
 *
 * <AUTHOR>
 * @date 2023/9/7 16:41
 */
public class SignUtils {
    public static String getSign(Map<String, Object> params, String accessSecret) {
        Set<String> keysSet = params.keySet();
        Object[] keys = keysSet.toArray();
        Arrays.sort(keys);
        StringBuilder temp = new StringBuilder();
        boolean first = true;
        for (Object key : keys) {
            if (first) {
                first = false;
            } else {
                temp.append("&");}
            temp.append(key).append("=");
            Object value = params.get(key);
            String valueString = "";
            if (null != value) {
                valueString = String.valueOf(value);
            }
            temp.append(valueString);
        }
        temp.append("&").append("accessSecret").append("=").append(accessSecret);
        return  MD5Util.getMD5(temp.toString()).toUpperCase();
    }

}
