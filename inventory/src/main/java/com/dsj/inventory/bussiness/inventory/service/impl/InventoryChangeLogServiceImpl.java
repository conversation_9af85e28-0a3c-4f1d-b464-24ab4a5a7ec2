package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.InventoryChangeLog;
import com.dsj.inventory.bussiness.inventory.mapper.InventoryChangeLogMapper;
import com.dsj.inventory.bussiness.inventory.service.InventoryChangeLogService;
import com.dsj.inventory.bussiness.inventory.vo.CheckGoodsVo;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 进销存库存变动记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Service
public class InventoryChangeLogServiceImpl extends ServiceImpl<InventoryChangeLogMapper, InventoryChangeLog> implements InventoryChangeLogService {

    @Override
    public List<CheckGoodsVo> getDayCheckGoods(Long organizationId, LocalDateTime startTime, LocalDateTime endTime) {
        return this.baseMapper.getDayCheckGoods(organizationId, startTime, endTime);
    }
}
