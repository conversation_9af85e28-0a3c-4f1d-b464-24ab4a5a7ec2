package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 进销存库存变动记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_inventory_change_log")
@ApiModel(value="InventoryChangeLog", description="进销存库存变动记录")
public class InventoryChangeLog extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织Id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "私域商品Id")
    @TableField("organization_medicine_id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "库存Id")
    @TableField("inventory_id")
    private Long inventoryId;

    @ApiModelProperty(value = "变动单据Id")
    @TableField("order_id")
    private Long orderId;

    @ApiModelProperty(value = "单据类型 1入库、2出库、3损益、4销售、5移动")
    @TableField("order_type")
    private Integer orderType;

    @ApiModelProperty(value = "库存变动类型 1增加 2减少")
    @TableField("change_type")
    private Integer changeType;

    @ApiModelProperty(value = "库存变动数量")
    @TableField("change_amount")
    private Double changeAmount;

    @ApiModelProperty(value = "批号")
    @TableField("batch_number")
    private String batchNumber;

    @ApiModelProperty(value = "货位Id")
    @TableField("shelf_id")
    private Long shelfId;

    @ApiModelProperty(value = "批次（入库单号）")
    @TableField("batch")
    private String batch;

    @ApiModelProperty(value = "成本价格")
    @TableField("cost_price")
    private Integer costPrice;

    @ApiModelProperty(value = "执行人Id")
    @TableField("execute_id")
    private Long executeId;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
