package com.dsj.inventory.framework.config;

import com.dsj.inventory.framework.config.BaseConfig;
import com.dsj.inventory.framework.interceptor.ExtraHandlerInterceptor;
import com.dsj.inventory.framework.interceptor.TokenHandlerInterceptor;
import com.dsj.inventory.framework.properties.IgnoreTokenProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @createTime 2021年4月22日20:56:18
 */
@Configuration
@EnableConfigurationProperties({IgnoreTokenProperties.class})
public class AuthorityWebConfiguration extends BaseConfig implements WebMvcConfigurer {

  @Autowired
  private IgnoreTokenProperties ignoreTokenProperties;

  @Bean
  public HandlerInterceptor getTokenHandlerInterceptor() {
    return new TokenHandlerInterceptor(ignoreTokenProperties);
  }

  @Bean
  public HandlerInterceptor getExtraHandlerInterceptor() {
    return new ExtraHandlerInterceptor();
  }

  /**
   * 注册 拦截器
   */
  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    String[] commonPathPatterns = getExcludeCommonPathPatterns();
    registry.addInterceptor(getTokenHandlerInterceptor())
        .addPathPatterns("/**")
        .order(5)
        .excludePathPatterns(commonPathPatterns);
    registry.addInterceptor(getExtraHandlerInterceptor())
            .addPathPatterns("/**")
            .order(5)
            .excludePathPatterns(commonPathPatterns);
    WebMvcConfigurer.super.addInterceptors(registry);
  }

  /**
   * auth-client 中的拦截器需要排除拦截的地址
   */
  protected String[] getExcludeCommonPathPatterns() {
    String[] urls = {
        "/*.css",
        "/*.js",
        "/*.html",
        "/error",
        "/login",
        "/v2/api-docs",
        "/v2/api-docs-ext",
        "/swagger-resources/**",
        "/webjars/**",

        "/",
        "/csrf",

        "/META-INF/resources/**",
        "/resources/**",
        "/static/**",
        "/public/**",
        "classpath:/META-INF/resources/**",
        "classpath:/resources/**",
        "classpath:/static/**",
        "classpath:/public/**",

        "/cache/**",
        "/swagger-ui.html**",
        "/**/doc.html**",
        "/auth/wx/miniApp",
        "/order/payOrderCallback",
//        "/product/queryProductDetail/**",
        "/cms/**",

        "/pay/notify/order",
        "/pay/notify/refund",
        "/productCategory/**",
        "/auth/login",
        "/auth/register",
        "/auth/register/verify",
        "/auth/login/verify",
        "/product/findProductListByCategoryOrProductIdList"
    };
    return urls;
  }

}

