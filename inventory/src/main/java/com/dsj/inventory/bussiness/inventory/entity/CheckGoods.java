
package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 进销存盘点计划商品明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_check_goods")
@ApiModel(value="CheckGoods", description="进销存盘点计划商品明细")
public class CheckGoods extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘点计划Id")
    @TableField("check_id")
    private Long checkId;

    @ApiModelProperty(value = "私域商品Id")
    @TableField("organization_medicine_id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "库存Id")
    @TableField("inventory_id")
    private Long inventoryId;

    @ApiModelProperty(value = "通用名")
    @TableField("goods_name")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    @TableField("general_name")
    private String generalName;

    @ApiModelProperty(value = "商品编号")
    @TableField("goods_code")
    private String goodsCode;

    @ApiModelProperty(value = "包装单位")
    @TableField("pack_unit")
    private String packUnit;

    @ApiModelProperty(value = "规格型号")
    @TableField("specification")
    private String specification;

    @ApiModelProperty(value = "剂型")
    @TableField("drug_type")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    @TableField("manufacturer")
    private String manufacturer;

    @ApiModelProperty(value = "条形码")
    @TableField("bar_code")
    private String barCode;

    @ApiModelProperty(value = "批准文号")
    @TableField("approval_number")
    private String approvalNumber;

    @ApiModelProperty(value = "生产日期")
    @TableField("produce_date")
    private LocalDateTime produceDate;

    @ApiModelProperty(value = "有效期至")
    @TableField("user_date")
    private LocalDateTime userDate;

    @ApiModelProperty(value = "批号")
    @TableField("batch_number")
    private String batchNumber;

    @ApiModelProperty(value = "货位Id")
    @TableField("shelf_id")
    private Long shelfId;

    @ApiModelProperty(value = "批次（入库单号）")
    @TableField("batch")
    private String batch;

    @ApiModelProperty(value = "当前零售价（私域商品价格）")
    @TableField("price")
    private Integer price;

    @ApiModelProperty(value = "库存数量")
    @TableField("inventory_amount")
    private Double inventoryAmount;

    @ApiModelProperty(value = "盘点数量")
    @TableField("check_amount")
    private Double checkAmount;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
