package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 进销存会员信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_member")
@ApiModel(value="Member", description="进销存会员信息")
public class Member extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "系统账号Id")
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty(value = "会员卡号")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "姓名")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "手机号码")
    @TableField("phone")
    private String phone;

    @ApiModelProperty(value = "性别1 男  2 女  0 未知")
    @TableField("gender")
    private Integer gender;

    @ApiModelProperty(value = "生日(yyyy-MM-dd)")
    @TableField("birth")
    private String birth;

    @ApiModelProperty(value = "身份证号码")
    @TableField("card")
    private String card;

    @ApiModelProperty(value = "省")
    @TableField("province")
    private Long province;

    @ApiModelProperty(value = "市")
    @TableField("city")
    private Long city;

    @ApiModelProperty(value = "县")
    @TableField("county")
    private Long county;

    @ApiModelProperty(value = "详细地址")
    @TableField("address")
    private String address;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "余额")
    @TableField("balance")
    private Integer balance;

    @ApiModelProperty(value = "可用积分")
    @TableField("usable_integral")
    private Integer usableIntegral;

    @ApiModelProperty(value = "累积积分")
    @TableField("accrue_integral")
    private Integer accrueIntegral;

    @ApiModelProperty(value = "消费密码")
    @TableField("consume_pwd")
    private String consumePwd;

    @ApiModelProperty(value = "是否开启安全验证 0否1是")
    @TableField("safety_state")
    private Integer safetyState;

    @ApiModelProperty(value = "办理人Id")
    @TableField("transact_user")
    private Long transactUser;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
