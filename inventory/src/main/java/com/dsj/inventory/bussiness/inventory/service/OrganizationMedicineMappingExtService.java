package com.dsj.inventory.bussiness.inventory.service;

import java.time.LocalDateTime;

/**
 * <p>
 * 组织私域药品映射 扩展服务类
 * </p>
 *
 * <AUTHOR>
 */
public interface OrganizationMedicineMappingExtService {

    /**
     * 更新药品库存信息
     * 
     * @param id 药品ID
     * @param repertory 库存数量
     * @param averageCost 平均成本
     * @param lastIncomingTime 最后入库时间
     * @param updateInventoryShelve 是否需要更新上架状态
     * @param lastSaleTime 最后销售时间，为null则不更新
     * @return 是否更新成功
     */
    boolean updateMedicineInventoryInfo(Long id, double repertory, int averageCost,
                                      LocalDateTime lastIncomingTime, boolean updateInventoryShelve,
                                      LocalDateTime lastSaleTime);
} 