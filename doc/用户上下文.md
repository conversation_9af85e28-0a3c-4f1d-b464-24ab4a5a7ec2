
## BaseContextHandler 使用规范
始终用中文和我交流。
`com.dsj.inventory.common.context.BaseContextHandler` 用于在应用中管理和获取当前请求的上下文信息，特别是当前登录用户及其组织的相关数据。它通过 `ThreadLocal` 机制实现线程隔离的上下文存储。

**核心功能：**

*   存储和获取用户ID、用户名、用户账户。
*   存储和获取用户所属的组织ID和组织类型。
*   存储和获取其他与当前请求上下文相关的自定义键值对。

**信息设置（通常在系统层面，如拦截器中完成）：**

这些 `set` 方法通常在请求进入系统的早期阶段被调用，例如在一个全局拦截器中解析JWT Token后，将用户信息存入 `BaseContextHandler`。

*   `BaseContextHandler.setUserId(Long userId)`: 设置当前用户ID。
*   `BaseContextHandler.setName(String name)`: 设置当前用户名。
*   `BaseContextHandler.setAccount(String account)`: 设置当前用户账户。
*   `BaseContextHandler.set(String key, Object value)`: 用于设置其他上下文信息，例如：
    *   `BaseContextHandler.set(BaseContextConstants.USER_ORG_ID, Integer orgId)`: 设置用户组织ID。
    *   `BaseContextHandler.set(BaseContextConstants.USER_ORG_TYPE, String orgTypeCode)`: 设置用户组织类型代码。

**信息获取（在业务代码中按需调用）：**

在Service层、Controller层或其他业务组件中，可以通过以下静态方法获取上下文信息：

*   `BaseContextHandler.getUserId()`: 获取当前用户ID (Long)。
*   `BaseContextHandler.getName()`: 获取当前用户名 (String)。
*   `BaseContextHandler.getAccount()`: 获取当前用户账户 (String)。
*   `BaseContextHandler.get(String key, Class<T> type)`: 获取指定键的值，并转换为指定类型。例如：
    *   `Integer orgId = BaseContextHandler.get(BaseContextConstants.USER_ORG_ID, Integer.class);`
    *   `String orgType = BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE, String.class);`
*   `BaseContextHandler.get(String key, Class<T> type, Object def)`: 获取指定键的值，如果不存在则返回默认值。
*   `BaseContextHandler.get(String key)`: 获取指定键的字符串形式的值，如果不存在则返回空字符串。

**常量定义：**

相关的键名常量定义在 `com.dsj.inventory.common.context.BaseContextConstants` 类中，例如：

*   `BaseContextConstants.JWT_KEY_USER_ID`
*   `BaseContextConstants.JWT_KEY_NAME`
*   `BaseContextConstants.JWT_KEY_ACCOUNT`
*   `BaseContextConstants.USER_ORG_ID`
*   `BaseContextConstants.USER_ORG_TYPE`

**使用示例（在Service层）：**

```java
// 获取当前用户ID
Long currentUserId = BaseContextHandler.getUserId();

// 获取当前用户的组织ID
Integer organizationId = BaseContextHandler.get(BaseContextConstants.USER_ORG_ID, Integer.class);
if (organizationId == null) {
    // 处理组织ID未设置的情况，可能是匿名访问或系统错误
    throw new BusinessException("无法获取当前用户的组织信息");
}

// ... 其他业务逻辑
```

**注意事项：**

*   `BaseContextHandler` 是基于 `ThreadLocal` 的，因此其存储的数据仅在当前线程（即当前HTTP请求处理线程）中有效。
*   在异步操作或新线程中，如果需要访问这些上下文信息，需要手动传递或重新设置。
*   务必确保在请求处理的开始阶段正确设置了所有必要的上下文信息。
*   在测试环境中，可以使用 `TestContextHelper` 来模拟和设置 `BaseContextHandler` 中的值，以便进行单元测试或集成测试。例如，`TestContextHelper.setDefaultUserContext()` 会设置一套默认的用户和组织信息。

*   请求结束后，相关信息会通过 `BaseContextHandler.remove()` 进行清理，这通常也是在拦截器的 `afterCompletion` 或类似机制中完成的。