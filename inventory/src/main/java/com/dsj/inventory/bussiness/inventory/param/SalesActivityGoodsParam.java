package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 销售单商品明细(销售活动整合)
 * <AUTHOR>
 * @date 2023/04/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SalesActivityGoodsParam", description="销售单商品明细(销售活动整合)")
public class SalesActivityGoodsParam {

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "通用名")
    private String goodsName;

    @ApiModelProperty(value = "包装单位")
    private String packUnit;

    @ApiModelProperty(value = "销售数量")
    private Double saleAmount;

    @ApiModelProperty(value = "赠品数量")
    private Double giveAmount;

    @ApiModelProperty(value = "单价")
    private Integer price;

    @ApiModelProperty(value = "折后单价")
    private Integer discountPrice;

    @ApiModelProperty(value = "零售价（私域商品价格）")
    private Integer salePrice;

    @ApiModelProperty(value = "总计（销售数量*折后单价）")
    private Integer totalPrice;

    @ApiModelProperty(value = "单品活动Id")
    private Long singleActivityId;

    @ApiModelProperty(value = "会员活动Id")
    private Long memberActivityId;

    @ApiModelProperty(value = "特价商品 0否1是")
    private Integer specialOffer;

}
