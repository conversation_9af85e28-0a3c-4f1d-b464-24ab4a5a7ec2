package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.CashierWorking;
import com.dsj.inventory.bussiness.inventory.vo.CashierWorkingVo;

/**
 * <p>
 * 进销存收银员值班记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface CashierWorkingService extends IService<CashierWorking> {

    /**
     * 收银员交班
     * @param organizationId
     * @return
     */
    CashierWorkingVo turnCashierWorking(Long organizationId);

    /**
     * 获取存收银员值班记录详情
     * @param id
     * @return
     */
    CashierWorkingVo getCashierWorkingById(Long id);

}
