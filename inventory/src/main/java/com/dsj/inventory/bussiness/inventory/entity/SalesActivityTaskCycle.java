package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 销售活动任务周期信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_sales_activity_task_cycle")
@ApiModel(value="SalesActivityTaskCycle", description="销售活动任务周期信息")
public class SalesActivityTaskCycle extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务Id")
    @TableField("task_id")
    private Long taskId;

    @ApiModelProperty(value = "星期")
    @TableField("week")
    private Integer week;

    @ApiModelProperty(value = "开始时间")
    @TableField("start_time")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    @TableField("end_time")
    private String endTime;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}