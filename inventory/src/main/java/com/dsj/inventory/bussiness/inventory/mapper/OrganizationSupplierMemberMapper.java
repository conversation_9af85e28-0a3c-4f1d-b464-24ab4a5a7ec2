package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationSupplierMember;
import com.dsj.inventory.bussiness.inventory.param.QueryOrganizationSupplierMemberParam;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationSupplierMemberVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 供应商组织私域销售人员 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-24
 */
public interface OrganizationSupplierMemberMapper extends BaseMapper<OrganizationSupplierMember> {

    /**
     * 获取供应商组织私域销售人员分页列表
     * @param page
     * @param param
     * @return
     */
    List<OrganizationSupplierMemberVo> queryOrganizationSupplierMemberPage(@Param("page") Page page, @Param("param") QueryOrganizationSupplierMemberParam param);


}
