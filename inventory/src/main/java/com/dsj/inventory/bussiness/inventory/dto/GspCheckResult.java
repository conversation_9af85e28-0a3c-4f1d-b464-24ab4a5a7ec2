package com.dsj.inventory.bussiness.inventory.dto;

import lombok.Data;

import java.util.List;

/**
 * GSP校验结果DTO
 * 用于返回GSP合规校验的详细结果
 */
@Data
public class GspCheckResult {
    
    private Boolean passed;              // 校验是否通过
    private String message;              // 校验总体信息
    private List<String> errorDetails;   // 错误详情列表
    private List<String> warningDetails; // 警告详情列表
    private Integer checkMode;           // 校验模式：1-拦截，0-不拦截不提示，-1-提示不拦截
    
    // 具体校验项结果
    private Boolean supplierLicenseValid;    // 供应商证照有效性
    private Boolean medicineLicenseValid;    // 商品证照有效性
    private Boolean businessScopeValid;      // 经营范围匹配性
    private Boolean enterpriseLicenseValid;  // 企业证照有效性
    
    // 证照到期提醒
    private List<LicenseExpiryWarning> expiryWarnings;
    
    public GspCheckResult() {}
    
    public GspCheckResult(Boolean passed, String message) {
        this.passed = passed;
        this.message = message;
    }
    
    public GspCheckResult(Boolean passed, String message, Integer checkMode) {
        this.passed = passed;
        this.message = message;
        this.checkMode = checkMode;
    }
    
    /**
     * 添加错误详情
     */
    public void addError(String error) {
        if (errorDetails == null) {
            errorDetails = new java.util.ArrayList<>();
        }
        errorDetails.add(error);
        this.passed = false;
    }
    
    /**
     * 添加警告详情
     */
    public void addWarning(String warning) {
        if (warningDetails == null) {
            warningDetails = new java.util.ArrayList<>();
        }
        warningDetails.add(warning);
    }
    
    /**
     * 添加证照到期警告
     */
    public void addExpiryWarning(LicenseExpiryWarning warning) {
        if (expiryWarnings == null) {
            expiryWarnings = new java.util.ArrayList<>();
        }
        expiryWarnings.add(warning);
    }
    
    /**
     * 是否有错误
     */
    public Boolean hasErrors() {
        return errorDetails != null && !errorDetails.isEmpty();
    }
    
    /**
     * 是否有警告
     */
    public Boolean hasWarnings() {
        return warningDetails != null && !warningDetails.isEmpty();
    }
    
    /**
     * 是否需要拦截
     */
    public Boolean shouldIntercept() {
        return !passed && (checkMode == null || checkMode == 1);
    }
    
    /**
     * 是否需要提醒
     */
    public Boolean shouldWarn() {
        return !passed && (checkMode == null || checkMode == -1 || checkMode == 1);
    }
    
    /**
     * 证照到期警告内部类
     */
    @Data
    public static class LicenseExpiryWarning {
        private String licenseType;     // 证照类型
        private String licenseName;     // 证照名称
        private String holderName;      // 持有者名称
        private java.time.LocalDate expiryDate;  // 到期日期
        private Integer daysToExpiry;   // 距离到期天数
        private String severity;        // 严重程度：EXPIRED, CRITICAL, WARNING, INFO
        
        public LicenseExpiryWarning() {}
        
        public LicenseExpiryWarning(String licenseType, String licenseName, String holderName, 
                                   java.time.LocalDate expiryDate, Integer daysToExpiry, String severity) {
            this.licenseType = licenseType;
            this.licenseName = licenseName;
            this.holderName = holderName;
            this.expiryDate = expiryDate;
            this.daysToExpiry = daysToExpiry;
            this.severity = severity;
        }
    }
}