package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 损溢单
 * <AUTHOR>
 * @date 2023/04/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateGainsLossesOrderParam", description="新增or编辑 损溢单参数")
public class SaveOrUpdateGainsLossesOrderParam {

    @ApiModelProperty(hidden = true, value = "id,更新是传入")
    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(hidden = true,value = "损溢单号")
    private String code;

    @ApiModelProperty(value = "处理状态 0暂存 1已完成")
    private Integer dealState;

    @ApiModelProperty(value = "损溢类型 1损2溢")
    private Integer type;

    @ApiModelProperty(hidden = true,value = "损溢总数")
    private Double totalAmount;

    @ApiModelProperty(hidden = true,value = "成本金额（成本均价总额）")
    private Integer costTotalPrice;

    @ApiModelProperty(hidden = true,value = "移动加权平均成本价总额")
    private Integer averageTotalCost;

    @ApiModelProperty(hidden = true,value = "零售总金额（私域商品总金额）")
    private Integer totalPrice;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(hidden = true,value = "损溢商品名")
    private String goodsNames;

    @ApiModelProperty(value = "商品明细")
    private List<SaveOrUpdateGainsLossesGoodsParam> goodsList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
