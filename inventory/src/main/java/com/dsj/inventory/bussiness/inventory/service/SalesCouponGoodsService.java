package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.SalesCouponGoods;
import com.dsj.inventory.bussiness.inventory.vo.SalesCouponGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存优惠券商品明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface SalesCouponGoodsService extends IService<SalesCouponGoods> {

    /**
     * 获取优惠券商品
     * @param couponId
     * @return
     */
    List<SalesCouponGoodsVo> querySalesCouponGoodsList(Long couponId);

}
