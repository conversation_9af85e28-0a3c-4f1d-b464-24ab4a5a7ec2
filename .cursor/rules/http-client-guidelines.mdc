---
description: 
globs: *.http
alwaysApply: false
---
# HTTP Client (*.http) 文件规范
始终用中文和我交流。
## 概述

本项目使用 IntelliJ IDEA 内建的 HTTP Client 功能（通过 `*.http` 文件）来测试和记录 API 接口。这些文件提供了一种便捷的方式，直接在 IDE 中发送 HTTP 请求并查看响应，无需依赖外部工具如 Postman。

## 职责

- **API 测试**: 用于快速测试后端 API 接口的功能和正确性。
- **请求示例**: 作为 API 使用方法的具体示例，方便团队成员理解和调用。
- **简单文档**: 通过注释和结构化的请求，提供轻量级的 API 文档。

## 文件组织

- **位置**: 本项目中的 HTTP 请求文件（`*.http`）和环境配置文件存放在与控制器对应的 `controller/http/` 目录下，例如 `inventory/src/main/java/com/dsj/inventory/bussiness/inventory/controller/http/`。
- **命名**: 文件名应与对应的控制器名称保持一致，例如 `MemberController.java` 对应 `MemberHttp.http`。
- **分组**: 在单个 `*.http` 文件中，使用 `###` 分隔符来组织相关的请求。

## 请求编写规范

- **请求分隔符**: 使用 `###` 来分隔文件中的不同 HTTP 请求。每个 `###` 后面应紧跟一个清晰描述该请求目的的注释。
- **请求行**: 必须包含 HTTP 方法 (GET, POST, PUT, DELETE 等) 和完整的 URL。
- **URL**: 使用环境变量，例如 `http://{{host}}:{{port}}/path` 或直接使用 `{{baseUrl}}path`。变量在 `http-client.private.env.json` 中定义。
- **请求头 (Headers)**: 按需添加必要的请求头，如 `Content-Type`, `token` 等。每行一个 Header。
- **请求体 (Body)**: 
    - 对于 `POST`, `PUT` 等需要请求体的请求，在请求头后空一行编写请求体。
    - 明确指定 `Content-Type` 请求头 (如 `application/json`)。
    - JSON 请求体应使用标准格式。
- **注释**: 使用 `#` 或 `//` 添加行内注释，解释特定参数或逻辑。

## 环境变量

- **私有环境**: 本项目使用 `http-client.private.env.json` 文件来定义环境变量。此文件存放在与 HTTP 文件相同的目录中，通常包含认证 token 等敏感信息，**不应**提交到版本控制系统，应添加到 `.gitignore`。

示例环境文件 (`http-client.private.env.json`):
```json
{
  "localhost": {
    "baseUrl": "http://localhost:9016/",
    "token": "Bearer eyJ0eXAiOiJKc29uV2ViVG9rZW4iLCJhbGciOiJIUzI1NiJ9..."
  },
  "dev": {
    "baseUrl": "http://dev-server:9016/",
    "token": "Bearer ..."
  },
  "prod": {
    "baseUrl": "http://prod-server/api/",
    "token": "Bearer ..."
  }
}
```

## 响应处理 (可选)

- 可以使用 `>` 将响应保存到文件。
- 可以使用 `<% ... %>` 块编写 JavaScript 脚本来处理响应，例如进行断言或提取变量。

```http
> {% client.global.set("auth_token", response.body.token); %}
```

## 版本控制

- `*.http` 文件 **应该** 提交到版本控制系统 (Git)。
- `http-client.private.env.json` 文件 **不应该** 提交，必须添加到 `.gitignore`。如果需要共享环境配置模板，可以提供一个不包含敏感信息的示例文件。

## 示例

```http
# MemberHttp.http

### 获取会员列表
GET {{baseUrl}}member
Content-Type: application/json
token: {{token}}

### 获取会员详情
GET {{baseUrl}}member/1
Content-Type: application/json
token: {{token}}

### 新增会员
POST {{baseUrl}}member
Content-Type: application/json
token: {{token}}

{
  "memberName": "测试会员",
  "memberPhone": "13800138000",
  "memberType": 1
}

###
```

