---
description: 
globs: **/service/**.java
alwaysApply: false
---
# Service层开发规范
始终用中文和我交流。
## 接口与实现
- **定义接口**: 每个业务模块的核心服务必须定义一个接口，命名为 `XxxService`。
- **实现类**: 服务接口必须有一个对应的实现类，命名为 `XxxServiceImpl`。
- **继承 (根据项目实践)**: 
    - 服务接口通常继承 `com.baomidou.mybatisplus.extension.service.IService<Entity>` (如果使用了MyBatis-Plus增强)。
    - 实现类通常继承 `com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<Mapper, Entity>` 并实现 `XxxService` 接口 (如果使用了MyBatis-Plus增强)。

## 职责
- Service层是业务逻辑的核心实现层。
- **负责**: 
    - 实现具体的业务规则和流程。
    - 编排对一个或多个 `Mapper` 方法的调用以完成数据库操作。
    - 进行必要的数据校验、转换和组合。
    - 调用其他 `Service` 方法以复用业务逻辑。
- **严禁**: Service层不应包含任何与HTTP协议相关的处理（如HttpServletRequest/Response），也不应直接处理前端的原始请求格式。

## 注解与依赖注入
- **实现类注解**: `XxxServiceImpl` 类必须使用 `@Service` 注解标记为Spring Bean。
- **依赖注入**: 
    - 使用 `@Autowired` 注入所需的 `Mapper` 接口。当使用MyBatis-Plus并继承 `ServiceImpl<Mapper, Entity>` 时，对应的 `Mapper` 通常会通过 `baseMapper` 自动注入，可以直接使用，无需显式 `@Autowired`。
    - 使用 `@Autowired` 注入其他需要的 `Service` 接口。
    - 推荐使用构造器注入，但如果项目统一使用字段注入，则遵循项目规范。

## 方法设计
- **输入**: 方法参数通常是 `Param` 对象、业务标识符（如ID）或基本数据类型。
- **输出**: 方法返回值通常是 `VO` 对象、基本数据类型或 `void`。
- **方法粒度**: 方法应封装一个明确、独立的业务操作。
- **命名**: 方法名应清晰地表达其业务目的 (如 `createOrder`, `getUserProfile`, `updateStockQuantity`)。

- **分页查询方法**:
    - **输入参数**: 分页查询方法的参数应为一个专门的 `QueryXxxParam` 对象。该对象应继承自 `com.dsj.inventory.common.entity.BasePage` 以包含标准的分页参数（如 `page` 代表当前页码，`size` 代表每页数量），并且可以包含其他业务查询条件。
    - **返回类型**: 分页查询方法的返回类型必须是 `com.baomidou.mybatisplus.extension.plugins.pagination.Page<XxxVO>`，其中 `XxxVO` 是列表项对应的视图对象。
    - **实现逻辑**: Service 实现中，应从继承自 `BasePage` 的 `QueryXxxParam` 中获取分页信息，创建 `Page` 实例，并将其传递给 Mapper 层进行查询。直接返回 Mapper 处理后的 `Page<XxxVO>` 对象。

## 数据处理
- **数据库交互**: 
    - 所有数据库操作必须通过注入的 `Mapper` 接口完成。
    - **严禁在Service层直接构建和使用 `com.baomidou.mybatisplus.core.conditions.Wrapper` (如 `QueryWrapper`, `UpdateWrapper`)。** 所有的动态查询、复杂条件和特定字段的更新都应封装在 `Mapper` 层的方法中。Service层应通过调用这些定义好的Mapper方法来执行操作，而不是在业务逻辑中拼接SQL条件。
- **数据转换**: 负责将 `Param` 对象转换为 `Entity` 对象（用于持久化），以及将 `Entity` 对象（或多个Entity组合）转换为 `VO` 对象（用于返回给Controller）。可以使用MapStruct、BeanUtils或手动进行转换。
- **数据校验**: 在执行核心业务逻辑前，进行必要的业务层面的数据校验（如检查库存是否充足、用户状态是否正常等）。对于 Controller 层已经校验过的格式问题，Service 层可以假设其有效。

## 事务管理
- **注解**: 对于涉及一个或多个写操作（INSERT, UPDATE, DELETE）的公共方法，应在其实现类的方法上添加 `@Transactional` 注解。
- **默认传播**: 通常使用默认的事务传播行为 (`REQUIRED`)。
- **只读事务**: 对于只有读操作的方法，可以添加 `@Transactional(readOnly = true)` 以优化性能。
- **异常回滚**: `@Transactional` 默认只在遇到 `RuntimeException` 或 `Error` 时回滚。如果需要对受检异常（Checked Exception）也进行回滚，需配置 `rollbackFor` 属性，如 `@Transactional(rollbackFor = Exception.class)`。

## 异常处理
- Service层是业务逻辑的核心，应清晰地通过异常来反映业务执行过程中的问题。
- **主要业务异常**: 
    - 对于所有可预见的业务逻辑错误（如"用户不存在"、"库存不足"、"状态不合法无法操作"等），应抛出 `com.dsj.inventory.framework.exception.BizException`。
    - 构造 `BizException` 时，应提供清晰、具体的错误消息。例如:
      ```java
      if (user == null) {
          throw new BizException("指定ID的用户不存在");
      }
      if (product.getStock() < quantity) {
          throw new BizException("商品库存不足，无法完成购买");
      }
      ```
    - 如果项目中定义了统一的错误码体系 (如 `com.dsj.inventory.framework.exception.code.ExceptionCode`) 并且前端或调用方需要根据错误码进行特定处理，可以使用错误码来构造 `BizException`：
      ```java
      import com.dsj.inventory.framework.exception.code.ExceptionCode;
      // ...
      throw new BizException(ExceptionCode.DATA_NOT_FOUND.getCode(), "自定义的详细错误信息或直接使用: " + ExceptionCode.DATA_NOT_FOUND.getMsg());
      // 或者，如果 BizException 提供了相应的工厂方法:
      // throw BizException.wrap(ExceptionCode.DATA_NOT_FOUND);
      ```
- **参数校验异常**:
    - 虽然Controller层会通过 `@Valid` 等进行基础参数校验，但Service层可能需要进行更深层次的业务参数校验。
    - 当Service方法的参数不满足业务约束时，应抛出 `com.dsj.inventory.framework.exception.ParamException`。例如：
      ```java
      if (param.getStartDate().isAfter(param.getEndDate())) {
          throw new ParamException("开始日期不能晚于结束日期");
      }
      ```
- **事务与异常**:
    - 所有涉及一个或多个写操作（INSERT, UPDATE, DELETE）的Service层公共方法，必须在其实现类的方法上添加 `@Transactional(rollbackFor = Exception.class)` 注解。这确保了任何从该方法抛出的异常（包括 `BizException`, `ParamException` 以及其他运行时或受检异常）都会触发事务回滚，保持数据一致性。
- **异常传递**:
    - Service层通常不捕获由Mapper层或更底层组件抛出的非预期异常（如数据库连接异常、SQL语法错误等）。这些异常应由全局异常处理器 (`GlobalExceptionHandler`) 统一捕获和处理。
    - 除非Service层能对某个特定异常进行有意义的恢复处理，或者需要将其包装成一个更具体的 `BizException` 并附加额外上下文信息，否则不应随意 `try-catch` 隐藏原始异常。
- **错误信息**: 异常中携带的错误消息应清晰明了，便于问题排查。如果该消息可能最终展示给用户，则应注意措辞友好。

## 示例
```java
// === Service 接口 ===
import com.baomidou.mybatisplus.extension.service.IService; // 假设使用MP

public interface ExampleService extends IService<ExampleEntity> { // 假设使用MP

    /**
     * 创建示例
     * @param param 请求参数
     * @return 新创建示例的ID
     * @throws BusinessException 如果参数无效或创建失败
     */
    Long createExample(ExampleParam param) throws BusinessException;

    /**
     * 获取示例信息
     * @param id 示例ID
     * @return 示例VO
     */
    ExampleVO getExampleInfo(Long id);
}

// === Mapper 接口 (示例) ===
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

public interface ExampleMapper extends BaseMapper<ExampleEntity> {

    /**
     * 根据名称检查示例是否存在。
     * Wrapper 的使用被封装在 Mapper 接口的默认方法中，Service层不直接接触Wrapper。
     * @param name 示例名称
     * @return 存在则返回 true, 否则返回 false
     */
    default boolean existsByName(String name) {
        return this.exists(new QueryWrapper<ExampleEntity>().eq("name", name));
    }
}


// === Service 实现类 ===
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl; // 假设使用MP
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Service;
// ... 其他 import

@Service
public class ExampleServiceImpl extends ServiceImpl<ExampleMapper, ExampleEntity> implements ExampleService { // 假设使用MP

    // Mapper 通过 ServiceImpl<M, E> 自动注入，名为 baseMapper

    @Override
    @Transactional(rollbackFor = Exception.class) // 声明事务，对所有Exception回滚
    public Long createExample(ExampleParam param) throws BusinessException {
        // 1. 业务校验
        if (param.getName() == null || param.getName().trim().isEmpty()) {
            throw new BusinessException("示例名称不能为空");
        }
        // 调用封装好的Mapper方法，避免在Service层使用Wrapper
        if (baseMapper.existsByName(param.getName())) {
             throw new BusinessException("示例名称已存在");
        }
        
        // 2. 数据转换 (Param -> Entity)
        ExampleEntity entity = new ExampleEntity();
        BeanUtils.copyProperties(param, entity); // 或使用 MapStruct
        entity.setCreateTime(LocalDateTime.now());
        
        // 3. 调用Mapper进行持久化 (使用ServiceImpl提供的方法)
        boolean success = this.save(entity);
        if (!success) {
            throw new BusinessException("创建示例失败");
        }
        
        // 4. 返回结果
        return entity.getId(); // 假设ID是自增的，并且已回填
    }

    @Override
    public ExampleVO getExampleInfo(Long id) {
        // 1. 调用Mapper查询数据 (使用ServiceImpl提供的方法)
        ExampleEntity entity = this.getById(id);
        if (entity == null) {
            return null; // 或者抛出异常 new ResourceNotFoundException("示例未找到: " + id);
        }
        
        // 2. 数据转换 (Entity -> VO)
        ExampleVO vo = new ExampleVO();
        BeanUtils.copyProperties(entity, vo); // 或使用 MapStruct
        
        // 3. 返回VO
        return vo;
    }
}

// 假设 ExampleEntity, ExampleParam, ExampleVO, BusinessException 已定义
```
