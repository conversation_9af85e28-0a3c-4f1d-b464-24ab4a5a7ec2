package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 药品拉取模板
 * <AUTHOR>
 * @date 2023/03/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MedicineTemplateVo", description="药品拉取模板实体")
@ToString(callSuper = true)
public class MedicineTemplateVo {

    private Long id;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "可用状态 0：待启用 1启用")
    private Integer usableState;

    @ApiModelProperty(value = "药品IdList")
    private List<Long> medicineIdList;

    @ApiModelProperty(value = "药品List")
    private List<MedicineTemplateMedicineVo> medicineList;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
