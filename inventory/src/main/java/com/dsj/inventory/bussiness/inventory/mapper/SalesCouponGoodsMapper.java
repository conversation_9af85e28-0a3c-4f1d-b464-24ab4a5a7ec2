package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.SalesCouponGoods;
import com.dsj.inventory.bussiness.inventory.vo.SalesCouponGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存优惠券商品明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface SalesCouponGoodsMapper extends BaseMapper<SalesCouponGoods> {

    /**
     * 获取优惠券商品
     * @param couponId
     * @return
     */
    List<SalesCouponGoodsVo> querySalesCouponGoodsList(Long couponId);

}
