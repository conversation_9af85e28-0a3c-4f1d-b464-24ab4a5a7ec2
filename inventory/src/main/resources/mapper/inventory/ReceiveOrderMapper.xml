<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.ReceiveOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.ReceiveOrder">
    <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="order_id" property="orderId" />
        <result column="code" property="code" />
        <result column="bill_date" property="billDate" />
        <result column="remark" property="remark" />
        <result column="deal_state" property="dealState" />
        <result column="purchaser_id" property="purchaserId" />
        <result column="receiver_id" property="receiverId" />
        <result column="organization_supplier_id" property="organizationSupplierId" />
        <result column="total_price" property="totalPrice" />
        <result column="discount_total_price" property="discountTotalPrice" />
        <result column="carrier" property="carrier" />
        <result column="start_address" property="startAddress" />
        <result column="start_date" property="startDate" />
        <result column="transport_way" property="transportWay" />
        <result column="transport_tool" property="transportTool" />
        <result column="invoice_date" property="invoiceDate" />
        <result column="invoice_code" property="invoiceCode" />
        <result column="invoice_price" property="invoicePrice" />
        <result column="goods_types" property="goodsTypes" />
        <result column="receive_order_number" property="receiveOrderNumber" />
        <result column="follow_goods_order_date" property="followGoodsOrderDate" />
        <result column="arrival_date" property="arrivalDate" />
        <result column="arrival_temp" property="arrivalTemp" />
        <result column="transport_humidity_record" property="transportHumidityRecord" />
        <result column="refrigerate_state" property="refrigerateState" />
        <result column="wholesaler_staff_number" property="wholesalerStaffNumber" />
        <result column="invoice_path" property="invoicePath" />
        <result column="follow_goods_order_path" property="followGoodsOrderPath" />
        <result column="check_report_path" property="checkReportPath" />
        <result column="purchase_content" property="purchaseContent" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        organization_id, order_id, code, bill_date, remark, deal_state, purchaser_id, receiver_id, organization_supplier_id, total_price, discount_total_price, carrier, start_address, start_date, transport_way, transport_tool, invoice_date, invoice_code, invoice_price, goods_types, receive_order_number, follow_goods_order_date, arrival_date, arrival_temp, transport_humidity_record, refrigerate_state, wholesaler_staff_number, invoice_path, follow_goods_order_path, check_report_path, purchase_content, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="queryReceiveOrderPage" resultType="com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderVo">
        SELECT distinct jro.id
        ,jro.organization_id organizationId
        ,jro.order_id orderId
        ,jro.order_code orderCode
        ,jro.code
        ,jro.remark
        ,jro.deal_state dealState
        ,jro.organization_supplier_id organizationSupplierId
        ,jos.name organizationSupplierName
        ,jos.code organizationSupplierCode
        ,jro.total_price totalPrice
        ,jro.total_retail_price totalRetailPrice
        ,jro.goods_types goodsTypes
        ,jro.purchase_content purchaseContent
        ,su.name receiverName
        ,jro.create_time createTime
        FROM jxc_receive_order jro
        LEFT JOIN sys_user su ON su.id = jro.receiver_id AND su.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jro.organization_supplier_id AND jos.is_delete = 0
        WHERE
        jro.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jro.organization_id = #{param.organizationId}
        </if>
        <if test="param.dealState != null ">
            AND jro.deal_state = #{param.dealState}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jro.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.receiverNameLike != null and param.receiverNameLike != '' ">
            AND su.name like CONCAT('%',#{param.receiverNameLike},'%')
        </if>
        <if test="param.organizationSupplierNameLike != null and param.organizationSupplierNameLike != '' ">
            AND jos.name like CONCAT('%',#{param.organizationSupplierNameLike},'%')
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jro.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        ORDER BY jro.create_time desc
    </select>

    <select id="getReceiveOrderById" resultType="com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderDetailVo">
        SELECT distinct jro.id
                      ,jro.organization_id organizationId
                      ,jro.order_id orderId
                      ,jro.order_code orderCode
                      ,jro.code
                      ,jro.bill_date billDate
                      ,jro.remark
                      ,jro.deal_state dealState
                      ,jro.organization_supplier_id organizationSupplierId
                      ,jos.name organizationSupplierName
                      ,jos.code organizationSupplierCode
                      ,jro.total_price totalPrice
                      ,jro.discount_total_price discountTotalPrice
                      ,jro.total_retail_price totalRetailPrice
                      ,jro.goods_types goodsTypes
                      ,jro.purchase_content purchaseContent
                      ,psu.name purchaserName
                      ,jro.purchaser_id purchaserId
                      ,su.name receiverName
                      ,jro.receiver_id receiverId
                      ,jro.discount_total_price discountTotalPrice
                      ,jro.carrier
                      ,jro.start_address startAddress
                      ,jro.start_date startDate
                      ,jro.transport_way transportWay
                      ,jro.transport_tool transportTool
                      ,jro.invoice_date invoiceDate
                      ,jro.invoice_code invoiceCode
                      ,jro.invoice_price invoicePrice
                      ,jro.invoice_number invoiceNumber
                      ,jro.goods_types goodsTypes
                      ,jro.receive_order_number receiveOrderNumber
                      ,jro.follow_goods_order_date followGoodsOrderDate
                      ,jro.arrival_date arrivalDate
                      ,jro.arrival_temp arrivalTemp
                      ,jro.transport_humidity_record transportHumidityRecord
                      ,jro.refrigerate_state refrigerateState
                      ,jro.wholesaler_staff_number wholesalerStaffNumber
                      ,jro.invoice_path invoicePath
                      ,jro.follow_goods_order_path followGoodsOrderPath
                      ,jro.check_report_path checkReportPath
                      ,jro.purchase_content purchaseContent
                      ,jro.create_time createTime
        FROM jxc_receive_order jro
        LEFT JOIN sys_user psu ON psu.id = jro.purchaser_id AND psu.is_delete = 0
        LEFT JOIN sys_user su ON su.id = jro.receiver_id AND su.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jro.organization_supplier_id AND jos.is_delete = 0
        WHERE
            jro.is_delete = 0
        AND jro.id = #{id}
    </select>

    <select id="queryReceiveOrderList" resultType="com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderExportVo">
        SELECT distinct jro.id
        ,jro.code
        ,jro.remark
        ,(CASE
        WHEN jro.deal_state = 0 THEN '暂存'
        WHEN jro.deal_state = 1 THEN '已复核'
        WHEN jro.deal_state = 2 THEN '已完成'
        END) dealState
        ,ROUND(jro.total_price/100,2) totalPrice
        ,ROUND(jro.total_retail_price/100,2) totalRetailPrice
        ,jro.goods_types goodsTypes
        ,jro.purchase_content purchaseContent
        ,jos.name organizationSupplierName
        ,jos.code organizationSupplierCode
        ,su.name receiverName
        ,jro.create_time createTime
        FROM jxc_receive_order jro
        LEFT JOIN sys_user su ON su.id = jro.receiver_id AND su.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jro.organization_supplier_id AND jos.is_delete = 0
        WHERE
        jro.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jro.organization_id = #{param.organizationId}
        </if>
        <if test="param.dealState != null ">
            AND jro.deal_state = #{param.dealState}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jro.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.receiverNameLike != null and param.receiverNameLike != '' ">
            AND su.name like CONCAT('%',#{param.receiverNameLike},'%')
        </if>
        <if test="param.organizationSupplierNameLike != null and param.organizationSupplierNameLike != '' ">
            AND jos.name like CONCAT('%',#{param.organizationSupplierNameLike},'%')
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jro.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        ORDER BY jro.create_time desc
    </select>

    <update id="deleteReceiveOrderByOrganizationId">
        UPDATE jxc_receive_order jro
        LEFT JOIN jxc_receive_order_goods jrog
            ON jro.id = jrog.order_id AND jrog.is_delete = 0
        SET
            jro.is_delete = 1,
            jro.update_time = NOW(),
            jro.update_user = #{currentUserId},
            jrog.is_delete = CASE WHEN jrog.id IS NOT NULL THEN 1 ELSE jrog.is_delete END,
            jrog.update_time = CASE WHEN jrog.id IS NOT NULL THEN NOW() ELSE jrog.update_time END,
            jrog.update_user = CASE WHEN jrog.id IS NOT NULL THEN #{currentUserId} ELSE jrog.update_user END
        WHERE
            jro.organization_id = #{organizationId}
            AND jro.is_delete = 0;
    </update>

    <select id="getReceiveOrderCount" resultType="java.lang.Integer">
        SELECT COUNT(1) number
        FROM jxc_receive_order jro
        WHERE
        DATE_FORMAT(jro.create_time,'%Y-%m-%d') = #{day}
        <if test="organizationId != null and organizationId != '' ">
            AND jro.organization_id = #{organizationId}
        </if>
        AND jro.is_delete = 0
    </select>

</mapper>
