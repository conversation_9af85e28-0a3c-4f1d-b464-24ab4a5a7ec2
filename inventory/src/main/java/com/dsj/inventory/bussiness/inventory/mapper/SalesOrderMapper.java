package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.SalesOrder;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.SalesCountPriceVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesOrderExportVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存销售单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface SalesOrderMapper extends BaseMapper<SalesOrder> {

    /**
     * 获取销售单分页列表
     * @param page
     * @param param
     * @return
     */
    List<SalesOrderDetailVo> querySalesOrderPage(@Param("page") Page page, @Param("param") QuerySalesOrderParam param);

    /**
     * 获取销售单详情
     * @param id
     * @return
     */
    SalesOrderDetailVo getSalesOrderById(Long id);

    /**
     * 导出销售单列表
     * @param param
     * @return
     */
    List<SalesOrderExportVo> querySalesOrderList(@Param("param") QuerySalesOrderParam param);

    /**
     * 获取收银员当天销售金额统计
     * @param cashierId
     * @return
     */
    SalesCountPriceVo countSalesPrice(Long cashierId);

    /**
     * 获取收银员当天退款金额统计
     * @param cashierId
     * @return
     */
    Integer countReturnPrice(Long cashierId);


    /**
     * 删除组织的销售单
     * @param organizationId
     */
    void deleteSalesOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天销售单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getSalesOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);

}
