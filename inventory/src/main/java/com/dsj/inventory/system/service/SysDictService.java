package com.dsj.inventory.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.system.entity.SysDict;

import java.util.List;

/**
 * 系统字典表 服务接口
 */
public interface SysDictService extends IService<SysDict> {
    
    /**
     * 根据字典类型查询字典数据
     *
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    List<SysDict> getByDictType(String dictType);
    
    /**
     * 查询某个字典下的所有直接子字典
     *
     * @param parentId 父级字典ID
     * @return 子字典列表
     */
    List<SysDict> getByParentId(Long parentId);
    
    /**
     * 根据字典编码查询字典信息
     *
     * @param dictCode 字典编码
     * @return 字典信息
     */
    SysDict getByDictCode(String dictCode);
    
    /**
     * 分页查询字典数据
     *
     * @param page 分页对象
     * @param dictName 字典名称
     * @param dictType 字典类型
     * @return 分页结果
     */
    IPage<SysDict> getDictPage(Page<SysDict> page, String dictName, String dictType);
    
    /**
     * 新增字典数据
     *
     * @param sysDict 字典数据
     * @return 是否成功
     */
    boolean saveDict(SysDict sysDict);
    
    /**
     * 更新字典数据
     *
     * @param sysDict 字典数据
     * @return 是否成功
     */
    boolean updateDict(SysDict sysDict);
    
    /**
     * 删除字典数据
     *
     * @param id 字典ID
     * @return 是否成功
     */
    boolean removeDict(Long id);
    
    /**
     * 批量删除字典数据
     *
     * @param ids 字典ID列表
     * @return 是否成功
     */
    boolean removeDictBatch(List<Long> ids);
} 