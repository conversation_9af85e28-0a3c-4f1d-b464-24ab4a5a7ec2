package com.dsj.inventory.common.utils.warm;

import com.dsj.inventory.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.Map;

/**
 * 暖心医生post请求工具类
 *
 * <AUTHOR>
 * @date 2023/9/11 8:28
 */
@Slf4j
public class PostUtil {

    /**
     * 发送暖心医生请求
     * @param url
     * @param params
     * @return
     */
    public static String doPost(String url, Map<String, Object> params) {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            if (CommonUtils.isNotEmpty(params)){
                url = url + "?";
                for (Map.Entry<String, Object> entry : params.entrySet()) {
                    url = url + entry.getKey() + "=" + entry.getValue() + "&";
                }
                url = url.substring(0,url.length()-1);
            }
            log.info("=============请求地址："+url);
            HttpPost httpPost = new HttpPost(url);
            response = httpClient.execute(httpPost);
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                response.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return resultString;
    }
}
