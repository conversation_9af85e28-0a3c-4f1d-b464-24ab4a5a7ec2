package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityTask;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesActivityTaskParam;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 销售活动任务 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface SalesActivityTaskMapper extends BaseMapper<SalesActivityTask> {

    /**
     * 获取销售活动任务分页列表
     * @param page
     * @param param
     * @return
     */
    List<SalesActivityTaskVo> querySalesActivityTaskPage(@Param("page") Page page, @Param("param") QuerySalesActivityTaskParam param);

    /**
     * 获取销售活动任务详情
     * @param id
     * @return
     */
    SalesActivityTaskVo getSalesActivityTaskById(Long id);

}
