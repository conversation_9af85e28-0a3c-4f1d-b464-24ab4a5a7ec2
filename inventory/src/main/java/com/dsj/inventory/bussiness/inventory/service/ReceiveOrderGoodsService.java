package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.ReceiveOrderGoods;

import java.util.List;

/**
 * <p>
 * 进销存收货单商品明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
public interface ReceiveOrderGoodsService extends IService<ReceiveOrderGoods> {

    /**
     * 根据订单ID删除商品明细
     * @param orderId 订单ID
     */
    void deleteByOrderId(Long orderId);

    /**
     * 根据订单ID查询商品明细列表
     * @param orderId 订单ID
     * @return 商品明细列表
     */
    List<ReceiveOrderGoods> listByOrderId(Long orderId);

    /**
     * 将商品明细标记为已删除
     * @param orderId 订单ID
     */
    void markAsDeletedByOrderId(Long orderId);

    /**
     * 根据订单ID和商品ID查询商品明细
     * @param orderId 订单ID
     * @param organizationMedicineId 组织商品ID
     * @return 采购订单商品明细
     */
    ReceiveOrderGoods getByOrderIdAndOrgMedicineId(Long orderId, Long organizationMedicineId);
}