package com.dsj.inventory.common.constant;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.dsj.inventory.common.entity.StrPool;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 用于同一管理和生成缓存的key， 避免多个项目使用的key重复
 * <p>
 * 使用@Cacheable时， 其value值一定要在此处指定
 *
 * <AUTHOR>
 * @date 2019/08/06
 */
public interface CacheKey {
  // 权限系统缓存 start

  /**
   * 验证码 前缀 完整key: captcha:{key} -> str
   */
  String CAPTCHA = "captcha";
  /**
   * token 前缀 完整key： token:{token} -> userid
   */
  String TOKEN = "token";

  /**
   * 菜单 前缀 完整key: menu:{menuId} -> obj
   */
  String MENU = "menu";
  /**
   * 组织 前缀 完整key: station:{stationId} -> obj
   */
  String ORG = "org";
  /**
   * 岗位 前缀 完整key: station:{stationId} -> obj
   */
  String STATION = "station";

  /**
   * 资源 前缀 完整key: resource:{resourceId} -> obj
   */
  String RESOURCE = "resource";

  /**
   * 角色 前缀 完整key: role:{roleId}
   */
  String ROLE = "role";
  /**
   * 角色拥有那些菜单 前缀 完整key: role_menu:{ROLE_ID} -> [MENU_ID, MENU_ID, ...]
   */
  String ROLE_MENU = "role_menu";
  /**
   * 角色拥有那些资源 前缀 完整key: role_resource:{ROLE_ID} -> [RESOURCE_ID, ...]
   */
  String ROLE_RESOURCE = "role_resource";
//    /**
//     * 角色拥有那些组织 前缀
//     * 完整key: role_org:{ROLE_ID} -> [ORG_ID, ...]
//     */
//    String ROLE_ORG = "role_org";

  /**
   * 用户 前缀 完整key: user:classTypeName:{USER_ID} -> [ROLE_ID, ...]
   */
  String USER = "user";

  /**
   * 用户拥有那些角色 前缀 完整key: user_role:{USER_ID} -> [ROLE_ID, ...]
   */
  String USER_ROLE = "user_role";
  /**
   * 用户拥有的菜单 前缀 完整key: user_menu:{userId} -> [MENU_ID, MENU_ID, ...]
   */
  String USER_MENU = "user_menu";
  /**
   * 用户拥有的资源 前缀 完整key: user_resource:{userId} -> [RESOURCE_ID, ...]
   */
  String USER_RESOURCE = "user_resource";


  /**
   * 系统URI 前缀
   * <p>
   * 完整key: system_api:{id} -> [ID, ...]
   */
  String SYSTEM_API = "system_api";

  /**
   * 登录总次数 login_log_total:{TENANT} -> Long
   */
  String LOGIN_LOG_TOTAL = "login_log_total";
  /**
   * 今日登录总次数 login_log_today:{TENANT}:{today} -> Long
   */
  String LOGIN_LOG_TODAY = "login_log_today";
  /**
   * 今日登录总ip login_log_todayip:{TENANT}:{today} -> Map
   */
  String LOGIN_LOG_TODAY_IP = "login_log_todayip";
  /**
   * 最近10访问记录 login_log_tenday:{TENANT}:{today}:{account} -> Map
   */
  String LOGIN_LOG_TEN_DAY = "login_log_tenday";
  /**
   * 登录总次数 login_log_browser:{TENANT} -> Map
   */
  String LOGIN_LOG_BROWSER = "login_log_browser";
  /**
   * 登录总次数 login_log_system{TENANT} -> Map
   */
  String LOGIN_LOG_SYSTEM = "login_log_system";

  /**
   * 地区 前缀 完整key: area:{id} -> obj
   */
  String AREA = "area";
  /**
   * 所有地区 前缀 完整key: area_all -> [AREA_ID]
   */
  String AREA_ALL = "area_all";

  /**
   * 字典 前缀 完整key: dictionary_item:{id} -> obj
   */
  String DICTIONARY_ITEM = "dictionary_item";

  /**
   * 参数 前缀 完整key: parameter:{id} -> obj
   */
  String PARAMETER = "parameter";
//    /**
//     * 用户登录的客户端 前缀： 用于记录用户在那几个设备上登录了
//     * 完整key: user_login_client:{userid} -> [client, client, ...] (Set)
//     */
//    String USER_LOGIN_CLIENT = "user_login_client";

  /**
   * 用户客户端token 前缀 完整key: user_client_token:{userid}:{client} -> token (String)
   */
  String USER_CLIENT_TOKEN = "user_client_token";

  /**
   * 用户token 前缀 完整key: user_token:{userid} -> token (String)
   */
  String USER_TOKEN = "user_token";

  /**
   * 用户token 前缀 完整key: token_user_id:{token} -> userid (Long)
   */
  String TOKEN_USER_ID = "token_user_id";


  /**
   * 租户 前缀 完整key: tenant:{id} -> obj
   */
  String TENANT = "tenant";
  // 权限系统缓存 end

  // 消息服务缓存 start
  /**
   * 用户注册 前缀 完整key: register:{注册类型}:{手机号}
   */
  String REGISTER_USER = "register";
  /**
   * 用户登录 前缀 完整key: login:{注册类型}:{手机号}
   */
  String LOGIN_USER = "login";
  // 消息服务缓存 end

  /**
   * 内容模块装修缓存前缀
   */
  String CMS_TEMPLATE = "cms_template";

  // 消息服务缓存 start
  /**
   * 收款帐号注册 完整key: register_collecting_account:{注册类型}:{手机号}
   */
  String REGISTER_COLLECTING_ACCOUNT = "register_collecting_account";

  /**
   * 商家入驻 完整key: tenant_apply:{注册类型}:{手机号}
   */
  String TENANT_APPLY = "tenant_apply";

  /**
   * 店铺访问统计人次key
   */
  String STORE_VISIT_CACHE_KEY = "store_visit_cache";

  /**
   * 加入购物车key
   */
  String ADD_CART_KEY = "add_cart_key";

  //========================单据编号相关========================
  /**
   *  问诊单编号 完整key：order_day_no_key：******** ->{序号}
   */
  String ORDER_DAY_NO_KEY = "order:order_day_no_key";

  /**
   *  外部问诊单编号 完整key：external_order_day_no_key：******** ->{序号}
   */
  String EXTERNAL_ORDER_DAY_NO_KEY = "order:external_order_day_no_key";

  /**
   *  处方编号 完整key：prescription_day_no_key：******** ->{序号}
   */
  String PRESCRIPTION_DAY_NO_KEY = "order:prescription_day_no_key";

  /**
   *  接口处方编号 完整key：external_prescription_day_no_key：******** ->{序号}
   */
  String EXTERNAL_PRESCRIPTION_DAY_NO_KEY = "order:external_prescription_day_no_key";

  //========================视频相关========================

  /**
   * 用户在线信息 完整key： user_online_info: -> {userId list}
   */
  String USER_ONLINE_INFO = "waiting:user_online_info";

//  /**
//   * 医生在线信息 完整key： doctor_online_info: -> {doctorId list}
//   */
//  String DOCTOR_OPEN_INFO = "waiting:doctor_open_info";

  /**
   * 用户视频任务信息 完整key： user_groups_info:userId -> {orderList}
   */
  String USER_GROUPS_INFO = "waiting:user_groups_info";

  /**
   * 用户视频任务计时器 完整key： group_timer:orderId -> {orderId}
   */
  String GROUP_TIMER = "waiting:group_timer";

  //========================  业务  ========================
  /**
   * 药品方法导入标识 完整key： medicine_import_method_key -> {randomId,其实这里随便放，只做开关记录}
   */
  String MEDICINE_IMPORT_METHOD_KEY = "business:medicine_import_method_key";

  /**
   * 异步调用id 完整key： async_response_id:randomId -> {AsyncResponseVo}
   */
  String ASYNC_RESPONSE_ID = "business:async_response_id";


  //========================用户登录相关========================

  /**
   * 用户登录失败次数记录 完整key： user_login_fail:phone -> {5}
   */
  String USER_LOGIN_FAIL = "business:user_login_fail";

  /**
   * 用户登录失败限时登录 完整key： user_login_limit:phone -> {true}
   */
  String USER_LOGIN_LIMIT = "business:user_login_limit";

  //========================订单支付相关========================

  /**
   * 挂号订单支付信息 完整key： registration_pay_info:{orderId} -> {WxPayUnifiedOrderV3Result.JsapiResult}
   */
  String REGISTRATION_PAY_INFO = "pay:registration_pay_info";

  /**
   * 购药单支付信息 完整key： medicine_order_pay_info:{orderId} -> {WxPayUnifiedOrderV3Result.JsapiResult}
   */
  String MEDICINE_ORDER_PAY_INFO = "pay:medicine_order_pay_info";
  /**
   * 购药单 下单后超时未支付 定时key 完整key： medicine_order_timer:{orderId} -> {orderId}
   */
  String MEDICINE_ORDER_TIMER = "business:medicine_order_timer";

  /**
   * 挂号单 下单后超时未支付 定时key 完整key： registration_order_timer:{orderId} -> {orderId}
   */
  String REGISTRATION_ORDER_TIMER = "business:registration_order_timer";

  /**
   * 暖心医生订单支付信息 完整key： warm_order_pay_info:{orderId} -> {WxPayUnifiedOrderV3Result.JsapiResult}
   */
  String WARM_ORDER_PAY_INFO = "pay:warm_order_pay_info";
  /**
   * 暖心医生订单 下单后超时未支付 定时key 完整key： warm_order_timer:{orderId} -> {orderId}
   */
  String WARM_ORDER_TIMER = "business:warm_order_timer";


  //========================辽宁 互联网医院推送相关========================
  /**
   * 互联网医院推送公共key
   */
  String BUS_INT_SYNC = "business:internet_sync";

  /**
   * 同步医疗机构备案信息 完整key： business:internet_sync:省份:organizationId:organization -> {organizationId list}
   */
  String SYNC_ORGANIZATION = "organization";

  /**
   * 同步网诊疗科目信息 完整key： business:internet_sync:省份:organizationId:department_class -> {departmentClassId list}
   */
  String SYNC_DEPARTMENT_CLASS = "department_class";

  /**
   * 同步科室备案信息 完整key： business:internet_sync:省份:organizationId:department -> {departmentId list}
   */
  String SYNC_DEPARTMENT = "department";

  /**
   * 同步医生备案信息 完整key： business:internet_sync:省份:organizationId:doctor -> {doctorId list}
   * 使用sys_user_r_role 的主键id
   */
  String SYNC_DOCTOR = "doctor";

  /**
   * 同步挂号信息 完整key： business:internet_sync:省份:organizationId:register_record -> {registerRecordId list}
   */
  String SYNC_REGISTER_RECORD = "register_record";

  /**
   * 同步取消挂号信息 完整key： business:internet_sync:省份:organizationId:cancel_register -> {registerRecordId list}
   */
  String SYNC_CANCEL_REGISTER = "cancel_register";

  /**
   * 同步就诊记录信息 完整key： business:internet_sync:省份:organizationId:medical_record -> {medicalRecordId list}
   */
  String SYNC_MEDICAL_RECORD = "medical_record";

  /**
   * 同步西医处方记录信息 完整key： business:internet_sync:省份:organizationId:prescriptionWm -> {prescriptionId list}
   */
  String SYNC_PRESCRIPTION_WM = "prescriptionWm";

  /**
   * 同步中医医处方记录信息 完整key： business:internet_sync:省份:organizationId:prescriptionWm -> {prescriptionId list}
   */
  String SYNC_PRESCRIPTION_CM = "prescriptionCm";

  /**
   * 同步撤销处方记录信息 完整key： business:internet_sync:省份:organizationId:cancel_prescription -> {prescriptionId list}
   */
  String SYNC_CANCEL_PRESCRIPTION = "cancel_prescription";

  /**
   * 同步审核处方记录信息 完整key： business:internet_sync:省份:organizationId:examine_prescription -> {prescriptionId list}
   */
  String SYNC_EXAMINE_PRESCRIPTION = "examine_prescription";

  /**
   * 同步流转处方记录信息 完整key： business:internet_sync:省份:organizationId:circulation_prescription -> {prescriptionId list}
   */
  String SYNC_CIRCULATION_PRESCRIPTION = "circulation_prescription";

  /**
   * 同步药品配送记录信息 完整key： business:internet_sync:省份:organizationId:medicine_deliver -> {medicineOrderId list}
   */
  String SYNC_MEDICINE_DELIVER = "medicine_deliver";

  /**
   * 同步收费/退费记录信息 完整key： business:internet_sync:省份:organizationId:deal_record -> {dealRecordId list}
   */
  String SYNC_DEAL_RECORD = "deal_record";

  /**
   * 同步记录信息 完整key： business:internet_sync:省份:organizationId:consult_record -> {consultRecordId list}
   */
  String SYNC_CONSULT_RECORD = "consult_record";

  /**
   * 同步服务评价记录信息 完整key： business:internet_sync:省份:organizationId:evaluate -> {evaluateId list}
   */
  String SYNC_EVALUATE = "evaluate";

  /**
   * 同步投诉记录信息 完整key： business:internet_sync:省份:organizationId:complaint -> {complaintId list}
   */
  String SYNC_COMPLAINT = "complaint";

//========================CA签名认证相关========================
  /**
   * 同步投诉记录信息 完整key： business:ca:socketKey:key值 -> {ApplySignParam }
   */
  String CA_SOCKET_KEY = "business:ca:socketKey";

  //========================搜索关键词相关========================
  /**
   *  寻医问药搜索 完整key：order_day_no_key：xxxxx ->{搜索词}
   */
  String SEARCH_MEDICAL_ADVICE_KEY = "search:search_medical_advice_key";

  //========================前端缓存redis========================
  String WEB_REDIS_STRING_PREFIX = "web:string";

  //========================消息通知相关==========================

  /**
   * 问诊单消息通知定时key(上午)
   */
  String DIAGNOSIS_AM_MESSAGE_TIMER = "message:diagnosis_am_message_timer";
  /**
   * 问诊单消息通知定时key(下午)
   */
  String DIAGNOSIS_PM_MESSAGE_TIMER = "message:diagnosis_pm_message_timer";

  //========================微信相关==========================================

  /**
   * 微信公众号access_token
   */
  String WX_ACCOUNT_ACCESS_TOKEN = "wx:account:access_token";

  String WX_ACCOUNT_LAST_OPEN_ID = "wx:account:last_open_id";

  //========================活动相关==========================

  /**
   * 活动任务变更活动生效状态
   */
  String ACTIVITY_TASK_ASSERT_STATE = "activity:task_assert_state";

  //========================重庆 处方推送相关========================
  /**
   * 互联网医院推送公共key
   */
  String BUS_INT_PRE = "business:prescription_sync";

  /**
   * key值
   */
  String BUS_INT_PRE_key = "prescriptionId";

  /**辽宁*/
  String LiaoNing = "210000";
  /**重庆*/
  String ChongQing = "500000";
  /**重庆*/
  Long ChongQingId = 1578226291620970496L;


  //========================直播门票支付========================

  /**
   * 门票支付信息 完整key： live_ticket_order_pay_info:{orderId} -> {WxPayUnifiedOrderV3Result.JsapiResult}
   */
  String LIVE_TICKET_ORDER_PAY_INFO = "pay:live_ticket_order_pay_info";

  /**
   * 门票支付 下单后超时未支付 定时key 完整key： live_ticket_order_timer:{orderId} -> {orderId}
   */
  String LIVE_TICKET_ORDER_TIMER = "business:live_ticket_order_timer";


  /**
   * 虚拟人信息 完整key： business:live_dummy:name -> []
   */
  String LIVE_DUMMY_NAME = "business:live_dummy:name";

  /**
   * 虚拟人信息 完整key： business:live_dummy:head -> []
   */
  String LIVE_DUMMY_HEAD = "business:live_dummy:head";


  //========================系统设置========================

  /**
   * 处方分方数量限制
   */
  String PRESCRIPTION_NUM_LIMIT = "system:prescription_num_limit";

  //========================腾讯云========================

  /**
   * 腾讯云视频取回时间
   */
  String TENCENT_VIDEO_RESTORE_DATETIME = "tencent:video:restore_datetime";

  /**
   * 腾讯云视频解冻后暂存时间
   */
  String TENCENT_VIDEO_RESTORE_TIME = "tencent:video:restore_time";


  /**
   * 构建没有租户信息的key
   */
  static String buildKey(Object... args) {
    if (args.length == 1) {
      return String.valueOf(args[0]);
    } else if (args.length > 0) {
      return StrUtil.join(StrPool.COLON, args);
    } else {
      return "";
    }
  }

  /**
   * 获取yyyy-MM-dd日期
   */
  static String getToday() {
    return LocalDateTime.now()
        .format(DateTimeFormatter.ofPattern(
            DatePattern.NORM_DATE_PATTERN));
  }

}
