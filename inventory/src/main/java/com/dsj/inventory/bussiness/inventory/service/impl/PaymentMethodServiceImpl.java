package com.dsj.inventory.bussiness.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.PaymentConfig;
import com.dsj.inventory.bussiness.inventory.entity.PaymentMethod;
import com.dsj.inventory.bussiness.inventory.mapper.PaymentMethodMapper;
import com.dsj.inventory.bussiness.inventory.param.PaymentConfigParam;
import com.dsj.inventory.bussiness.inventory.param.QueryPaymentMethodParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdatePaymentMethodParam;
import com.dsj.inventory.bussiness.inventory.service.PaymentConfigService;
import com.dsj.inventory.bussiness.inventory.service.PaymentMethodService;
import com.dsj.inventory.bussiness.inventory.vo.PaymentConfigVO;
import com.dsj.inventory.bussiness.inventory.vo.PaymentMethodVO;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.DeleteEnum;
import com.dsj.inventory.framework.exception.BizException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组织付款方式 Service 实现类
 */
@Service
@RequiredArgsConstructor
public class PaymentMethodServiceImpl extends ServiceImpl<PaymentMethodMapper, PaymentMethod> implements PaymentMethodService {

    private final PaymentConfigService paymentConfigService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdatePaymentMethod(SaveOrUpdatePaymentMethodParam param) {
        // 检查是否已存在相同组织下同一编码的付款方式
        LambdaQueryWrapper<PaymentMethod> queryWrapper = new LambdaQueryWrapper<PaymentMethod>()
                .eq(PaymentMethod::getOrganizationId, param.getOrganizationId())
                .eq(PaymentMethod::getMethodCode, param.getMethodCode())
                .ne(param.getId() != null, PaymentMethod::getId, param.getId());

        // 存在同组织下同编码且非自身的记录，则抛出异常
        if (baseMapper.selectCount(queryWrapper) > 0) {
            throw new BizException("同一组织下相同编码的付款方式已存在");
        }

        // 准备实体对象
        PaymentMethod paymentMethod = new PaymentMethod();
        BeanUtil.copyProperties(param, paymentMethod);

        // 获取当前操作用户ID
        Long userId = BaseContextHandler.getUserId();
        LocalDateTime now = LocalDateTime.now();

        boolean isNew = param.getId() == null;

        if (isNew) {
            // 新增操作
            paymentMethod.setCreateTime(now);
            paymentMethod.setCreateUser(userId);
            paymentMethod.setUpdateTime(now);
            paymentMethod.setUpdateUser(userId);
            this.save(paymentMethod);
        } else {
            // 更新操作
            paymentMethod.setUpdateTime(now);
            paymentMethod.setUpdateUser(userId);
            this.updateById(paymentMethod);
        }

        // 处理配置项
        if (CollUtil.isNotEmpty(param.getConfigList())) {
            paymentConfigService.replacePaymentConfigs(paymentMethod.getId(), param.getOrganizationId(), param.getConfigList());
        } else {
            // 如果配置列表为空，且是更新操作，则需要删除该付款方式下的所有配置
            if (!isNew) {
                paymentConfigService.deleteByPaymentMethodId(paymentMethod.getId());
            }
        }

        return paymentMethod.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deletePaymentMethod(Long id) {
        // 检查付款方式是否存在
        PaymentMethod paymentMethod = this.getById(id);
        if (paymentMethod == null) {
            throw new BizException("付款方式不存在");
        }

        // 先删除关联的配置项
        paymentConfigService.deleteByPaymentMethodId(id);

        // 再删除付款方式
        return this.removeById(id);
    }

    @Override
    public PaymentMethodVO getPaymentMethod(Long id) {
        // 查询付款方式
        PaymentMethod paymentMethod = this.getById(id);
        if (paymentMethod == null) {
            return null;
        }

        // 转换为VO对象
        PaymentMethodVO vo = new PaymentMethodVO();
        BeanUtil.copyProperties(paymentMethod, vo);

        // 查询配置项 (只查询启用的配置项进行展示)
        List<PaymentConfig> configList = paymentConfigService.listEnabledByPaymentMethodId(id);

        // 转换配置项为VO对象
        List<PaymentConfigVO> configVOList = configList.stream().map(config -> {
            PaymentConfigVO configVO = new PaymentConfigVO();
            BeanUtil.copyProperties(config, configVO);
            return configVO;
        }).collect(Collectors.toList());

        vo.setConfigList(configVOList);
        return vo;
    }

    @Override
    public Page<PaymentMethodVO> queryPaymentMethodPage(QueryPaymentMethodParam param) {
        // 构建查询条件
        LambdaQueryWrapper<PaymentMethod> queryWrapper = new LambdaQueryWrapper<PaymentMethod>()
                .eq(param.getOrganizationId() != null, PaymentMethod::getOrganizationId, param.getOrganizationId())
                .eq(StrUtil.isNotBlank(param.getMethodCode()), PaymentMethod::getMethodCode, param.getMethodCode())
                .like(StrUtil.isNotBlank(param.getMethodName()), PaymentMethod::getMethodName, param.getMethodName())
                .eq(param.getEnabled() != null, PaymentMethod::getEnabled, param.getEnabled())
                .orderByAsc(PaymentMethod::getSortOrder)
                .orderByDesc(PaymentMethod::getCreateTime);

        // 执行分页查询
        Page<PaymentMethod> page = new Page<>(param.getPage(), param.getSize());
        Page<PaymentMethod> resultPage = this.page(page, queryWrapper);

        // 转换为VO对象
        Page<PaymentMethodVO> voPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        if (CollUtil.isEmpty(resultPage.getRecords())) {
            voPage.setRecords(new ArrayList<>());
            return voPage;
        }

        // 获取所有付款方式ID
        Set<Long> methodIds = resultPage.getRecords().stream()
                .map(PaymentMethod::getId)
                .collect(Collectors.toSet());

        // 批量查询配置项 (查询所有状态的配置，因为可能是管理界面，需要看到所有)
        Map<Long, List<PaymentConfig>> configMap = paymentConfigService.mapAllConfigsByPaymentMethodIds(methodIds);

        // 组装VO对象
        List<PaymentMethodVO> voList = resultPage.getRecords().stream().map(entity -> {
            PaymentMethodVO vo = new PaymentMethodVO();
            BeanUtil.copyProperties(entity, vo);

            // 设置配置项列表
            List<PaymentConfig> configs = configMap.getOrDefault(entity.getId(), Collections.emptyList());
            List<PaymentConfigVO> configVOList = configs.stream().map(config -> {
                PaymentConfigVO configVO = new PaymentConfigVO();
                BeanUtil.copyProperties(config, configVO);
                return configVO;
            }).collect(Collectors.toList());
            vo.setConfigList(configVOList);

            return vo;
        }).collect(Collectors.toList());

        voPage.setRecords(voList);
        return voPage;
    }

    @Override
    public List<PaymentMethodVO> listEnabledByOrganization(Long organizationId, boolean withConfigs) {
        if (organizationId == null) {
            return Collections.emptyList();
        }

        // 查询启用的付款方式
        LambdaQueryWrapper<PaymentMethod> queryWrapper = new LambdaQueryWrapper<PaymentMethod>()
                .eq(PaymentMethod::getOrganizationId, organizationId)
                .eq(PaymentMethod::getEnabled, DeleteEnum.NOT_DELETE.getCode())
                .orderByAsc(PaymentMethod::getSortOrder)
                .orderByDesc(PaymentMethod::getCreateTime);

        List<PaymentMethod> methodList = this.list(queryWrapper);
        if (CollUtil.isEmpty(methodList)) {
            return Collections.emptyList();
        }

        // 转换为VO对象
        List<PaymentMethodVO> voList = methodList.stream().map(entity -> {
            PaymentMethodVO vo = new PaymentMethodVO();
            BeanUtil.copyProperties(entity, vo);
            return vo;
        }).collect(Collectors.toList());

        if (withConfigs && CollUtil.isNotEmpty(voList)) {
            // 获取所有付款方式ID
            Set<Long> methodIds = methodList.stream()
                    .map(PaymentMethod::getId)
                    .collect(Collectors.toSet());

            // 批量查询启用的配置项
            Map<Long, List<PaymentConfig>> configMap = paymentConfigService.mapEnabledConfigsByPaymentMethodIds(methodIds);

            // 设置配置项
            for (PaymentMethodVO vo : voList) {
                List<PaymentConfig> configs = configMap.getOrDefault(vo.getId(), Collections.emptyList());
                List<PaymentConfigVO> configVOList = configs.stream().map(config -> {
                    PaymentConfigVO configVO = new PaymentConfigVO();
                    BeanUtil.copyProperties(config, configVO);
                    return configVO;
                }).collect(Collectors.toList());
                vo.setConfigList(configVOList);
            }
        }

        return voList;
    }
} 