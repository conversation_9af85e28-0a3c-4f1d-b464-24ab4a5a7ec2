package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.MemberCoupon;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberCouponCountParam;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberCouponParam;
import com.dsj.inventory.bussiness.inventory.vo.MemberCouponCountVo;
import com.dsj.inventory.bussiness.inventory.vo.MemberCouponVo;

/**
 * <p>
 * 进销存会员优惠券 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface MemberCouponService extends IService<MemberCoupon> {

    /**
     * 获取会员优惠券分页列表
     * @param param
     * @return
     */
    Page<MemberCouponVo> queryMemberCouponPage(QueryMemberCouponParam param);

    /**
     * 获取会员优惠券详情
     * @param id
     * @return
     */
    MemberCouponVo getMemberCouponById(Long id);

    /**
     * 领取优惠券
     * @param couponId
     */
    void getCoupon(Long couponId);

    /**
     * 会员优惠券统计
     * @param param
     * @return
     */
    MemberCouponCountVo countMemberCoupon(QueryMemberCouponCountParam param);

}
