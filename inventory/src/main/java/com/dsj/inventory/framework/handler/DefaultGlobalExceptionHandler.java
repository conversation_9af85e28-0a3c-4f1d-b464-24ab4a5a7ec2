package com.dsj.inventory.framework.handler;

import cn.hutool.core.util.StrUtil;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.dsj.inventory.framework.exception.code.ExceptionCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.exceptions.PersistenceException;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.sql.SQLException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
public abstract class DefaultGlobalExceptionHandler {
    @ExceptionHandler(BizException.class)
    public ResponseEntity<String> bizException(BizException ex, HttpServletRequest request) {
        log.error("BizException:", ex);
        return Res.fail(HttpStatus.valueOf(ex.getCode()),ex.getMessage());
    }

    @ExceptionHandler(ParamException.class)
    public ResponseEntity paramException(ParamException ex, HttpServletRequest request) {
        log.error("ParamException:", ex);
        return Res.failNotAcceptable(ex.getMessage());
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity httpMessageNotReadableException(HttpMessageNotReadableException ex, HttpServletRequest request) {
        log.error("HttpMessageNotReadableException:", ex);
        String message = ex.getMessage();
        if (StrUtil.containsAny(message, "Could not read document:")) {
            String msg = String.format("无法正确的解析json类型的参数：%s", StrUtil.subBetween(message, "Could not read document:", " at "));
            return Res.failNotAcceptable(msg);
        }
        return Res.failNotAcceptable();
    }

    @ExceptionHandler(BindException.class)
    public ResponseEntity bindException(BindException ex, HttpServletRequest request) {
        log.error("BindException:", ex);
        try {
            String msgs = ex.getBindingResult().getFieldError().getDefaultMessage();
            if (StrUtil.isNotEmpty(msgs)) {
                return Res.failNotAcceptable(msgs);
            }
        } catch (Exception ee) {
        }
        StringBuilder msg = new StringBuilder();
        List<FieldError> fieldErrors = ex.getFieldErrors();
        fieldErrors.forEach((oe) ->
                msg.append("参数:[").append(oe.getObjectName())
                        .append(".").append(oe.getField())
                        .append("]的传入值:[").append(oe.getRejectedValue()).append("]与预期的字段类型不匹配.")
        );
        return Res.failNotAcceptable(msg.toString());
    }


    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity methodArgumentTypeMismatchException(MethodArgumentTypeMismatchException ex, HttpServletRequest request) {
        log.error("MethodArgumentTypeMismatchException:", ex);
        MethodArgumentTypeMismatchException eee = (MethodArgumentTypeMismatchException) ex;
        StringBuilder msg = new StringBuilder("参数：[").append(eee.getName())
                .append("]的传入值：[").append(eee.getValue())
                .append("]与预期的字段类型：[").append(eee.getRequiredType().getName()).append("]不匹配");
        return Res.failNotAcceptable(msg.toString());
    }

    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity illegalStateException(IllegalStateException ex, HttpServletRequest request) {
        log.error("IllegalStateException:", ex);
        return Res.failNotAcceptable();
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity missingServletRequestParameterException(MissingServletRequestParameterException ex, HttpServletRequest request) {
        log.error("MissingServletRequestParameterException:", ex);
        StringBuilder msg = new StringBuilder();
        msg.append("缺少必须的[").append(ex.getParameterType()).append("]类型的参数[").append(ex.getParameterName()).append("]");
        return Res.failNotAcceptable(msg.toString());
    }

    @ExceptionHandler(NullPointerException.class)
    public ResponseEntity nullPointerException(NullPointerException ex, HttpServletRequest request) {
        log.error("NullPointerException:", ex);
        return Res.fail();
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity illegalArgumentException(IllegalArgumentException ex, HttpServletRequest request) {
        log.error("IllegalArgumentException:", ex);
        return Res.fail();
    }

    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity httpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException ex, HttpServletRequest request) {
        log.error("HttpMediaTypeNotSupportedException:", ex);
        MediaType contentType = ex.getContentType();
        if (contentType != null) {
            StringBuilder msg = new StringBuilder();
            msg.append("请求类型(Content-Type)[").append(contentType.toString()).append("] 与实际接口的请求类型不匹配");
            return Res.failNotAcceptable(msg.toString());
        }
        return Res.failNotAcceptable("无效的Content-Type类型");
    }

    @ExceptionHandler(MissingServletRequestPartException.class)
    public ResponseEntity missingServletRequestPartException(MissingServletRequestPartException ex, HttpServletRequest request) {
        log.error("MissingServletRequestPartException:", ex);
        return Res.fail();
    }

    @ExceptionHandler(ServletException.class)
    public ResponseEntity servletException(ServletException ex, HttpServletRequest request) {
        log.error("ServletException:", ex);
        String msg = "UT010016: Not a multi part request";
        if (msg.equalsIgnoreCase(ex.getMessage())) {
            return Res.fail();
        }
        return Res.fail();
    }

    @ExceptionHandler(MultipartException.class)
    public ResponseEntity multipartException(MultipartException ex, HttpServletRequest request) {
        log.error("MultipartException:", ex);
        return Res.fail("文件参数异常");
    }

    /**
     * jsr 规范中的验证异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity constraintViolationException(ConstraintViolationException ex, HttpServletRequest request) {
        log.error("ConstraintViolationException:", ex);
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        String message = violations.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(";"));
        return Res.fail(message);
    }

    /**
     * spring 封装的参数验证异常， 在conttoller中没有写result参数时，会进入
     * @param ex
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity methodArgumentNotValidException(MethodArgumentNotValidException ex, HttpServletRequest request) {
        log.error("MethodArgumentNotValidException:", ex);
        return Res.fail(ex.getBindingResult().getFieldError().getDefaultMessage());
    }

    /**
     * 其他异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity otherExceptionHandler(Exception ex, HttpServletRequest request) {
        log.error("Exception:", ex);
        if (ex.getCause() instanceof BizException) {
          //  return this.bizException((BizException) ex.getCause(), request);
        }
        return Res.fail();
    }


    /**
     * 返回状态码:405
     */
    @ExceptionHandler({HttpRequestMethodNotSupportedException.class})
    public ResponseEntity handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex, HttpServletRequest request) {
        log.error("HttpRequestMethodNotSupportedException:", ex);
        return Res.common(HttpStatus.METHOD_NOT_ALLOWED);
    }


    @ExceptionHandler(PersistenceException.class)
    public ResponseEntity persistenceException(PersistenceException ex, HttpServletRequest request) {
        log.error("PersistenceException:", ex);
        if (ex.getCause() instanceof BizException) {
            BizException cause = (BizException) ex.getCause();
            return Res.fail(cause.getMessage());
        }
        return Res.fail(ExceptionCode.SQL_EX.getMsg());
    }

    @ExceptionHandler(MyBatisSystemException.class)
    public ResponseEntity myBatisSystemException(MyBatisSystemException ex, HttpServletRequest request) {
        log.error("PersistenceException:", ex);
        if (ex.getCause() instanceof PersistenceException) {
            return this.persistenceException((PersistenceException) ex.getCause(), request);
        }
        return Res.fail(ExceptionCode.SQL_EX.getMsg());
    }

    @ExceptionHandler(SQLException.class)
    public ResponseEntity sqlException(SQLException ex, HttpServletRequest request) {
        log.error("SQLException:", ex);
        return Res.fail(ExceptionCode.SQL_EX.getMsg());
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity dataIntegrityViolationException(DataIntegrityViolationException ex, HttpServletRequest request) {
        log.error("DataIntegrityViolationException:", ex);
        return Res.fail(ExceptionCode.SQL_EX.getMsg());
    }

}
