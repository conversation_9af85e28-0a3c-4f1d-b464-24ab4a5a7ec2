package com.dsj.inventory.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 系统字典表实体类
 */
@Data
@Accessors(chain = true)
@TableName("sys_dict")
public class SysDict extends Entity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字典名称
     */
    private String dictName;

    /**
     * 字典编号
     */
    private String dictCode;

    /**
     * 字典值
     */
    private String dictValue;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * 字典描述
     */
    private String dictDesc;

    /**
     * 删除标志,0：未删除  1：已删除
     */
    @TableLogic
    private Integer isDelete;
} 