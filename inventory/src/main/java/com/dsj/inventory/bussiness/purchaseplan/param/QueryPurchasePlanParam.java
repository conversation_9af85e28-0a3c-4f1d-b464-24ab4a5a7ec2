package com.dsj.inventory.bussiness.purchaseplan.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询采购计划
 * <AUTHOR>
 * @date 2023/04/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QueryPurchasePlanParam",description = "查询采购计划参数")
public class QueryPurchasePlanParam extends BasePage {

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "处理状态 0待执行 1已执行")
    private Integer dealState;

    @ApiModelProperty(value = "单号模糊搜索")
    private String codeLike;

    @ApiModelProperty(value = "创建人名称模糊搜索")
    private String createUserNameLike;

    @ApiModelProperty(value = "创建时间，查询条件--起始时间")
    private String createStart;

    @ApiModelProperty(value = "创建时间，查询条件--结束时间")
    private String createEnd;

}
