package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 销售单商品明细
 * <AUTHOR>
 * @date 2023/04/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SalesGoodsVo", description="销售单商品明细实体")
@ToString(callSuper = true)
public class SalesGoodsVo {

    private Long id;

    @ApiModelProperty(value = "销售单Id")
    private Long orderId;

    @ApiModelProperty(value = "销售员Id")
    private Long saleId;

    @ApiModelProperty(value = "销售员名称")
    private String saleName;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "通用名")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    private String generalName;

    @ApiModelProperty(value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(value = "包装单位")
    private String packUnit;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "剂型")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(value = "用法用量")
    private String usageDosage;

    @ApiModelProperty(value = "条形码")
    private String barCode;

    @ApiModelProperty(value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(value = "生产日期")
    private LocalDateTime produceDate;

    @ApiModelProperty(value = "有效期至")
    private LocalDateTime userDate;

    @ApiModelProperty(value = "批号")
    private String batchNumber;

    @ApiModelProperty(value = "货位Id")
    private Long shelfId;

    @ApiModelProperty(value = "货位名称")
    private String shelfName;

    @ApiModelProperty(value = "销售数量")
    private Double saleAmount;

    @ApiModelProperty(value = "赠送数量")
    private Double giveAmount;

    @ApiModelProperty(value = "单价")
    private Integer price;

    @ApiModelProperty(value = "折后单价")
    private Integer discountPrice;

    @ApiModelProperty(value = "优惠")
    private Integer preferentialPrice;

    @ApiModelProperty(value = "商品金额（销售数量*折后单价）")
    private Integer totalPrice;

    @ApiModelProperty(value = "零售价（私域商品价格）")
    private Integer salePrice;

    @ApiModelProperty(value = "会员价")
    private Integer memberPrice;

    @ApiModelProperty(value = "单品活动Id")
    private Long singleActivityId;

    @ApiModelProperty(value = "会员活动Id")
    private Long memberActivityId;

    @ApiModelProperty(value = "库存数量")
    private Double inventoryAmount;

    @ApiModelProperty(value = "商品主图")
    private String goodsFilePath;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
