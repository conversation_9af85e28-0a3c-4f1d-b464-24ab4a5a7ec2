package com.dsj.inventory.common.jcsh;


import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.dsj.inventory.common.jcsh.JcshProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.weaving.LoadTimeWeaverAware;
import org.springframework.instrument.classloading.LoadTimeWeaver;

import javax.annotation.PreDestroy;

@Slf4j
@EnableConfigurationProperties(JcshProperties.class)
@ConditionalOnProperty(prefix = "jsch",name = "enable",havingValue = "true",matchIfMissing = false)
@Configuration
public class SshChannelConfig implements LoadTimeWeaverAware{//LoadTimeWeaverAware 能保证这个类在数据库连接池之前初始化

	private Session session;
	
	/**
	 *  //最不安全的级别，相对安全的内网测试时建议使用。如果连接server的key在本地不存在，那么就自动添加到文件中（默认是known_hosts），并且给出一个警告。
		session.setConfig("StrictHostKeyChecking", "no");
		//默认的级别,如果连接和key不匹配，给出提示，并拒绝登录
		session.setConfig("StrictHostKeyChecking", "ask"); 
		//最安全的级别，如果连接与key不匹配，就拒绝连接，不会提示详细信息
		session.setConfig("StrictHostKeyChecking", "yes");

	 * @param jcshProperties
	 * @throws Exception
	 */
	public  SshChannelConfig(JcshProperties jcshProperties) throws Exception {
		if(jcshProperties==null||!jcshProperties.isEnable()||jcshProperties.getForwards().isEmpty()) {
			return ;
		}
        JSch jsch = new JSch();
//        jsch.addIdentity(sshPrvkey);//秘钥文件登录跳板机
        session = jsch.getSession(jcshProperties.getSshUser(),jcshProperties.getSshHost(), jcshProperties.getSshPort());
        session.setPassword(jcshProperties.getSshPassword());
        session.setConfig("StrictHostKeyChecking", "no");
        session.connect();
        log.warn("ssh通道连接,{}",session.getServerVersion());
        for(JcshProperties.ForwardConfig forward:jcshProperties.getForwards()) {
        	session.setPortForwardingL(forward.getJschlHost(), forward.getJschlPort(), forward.getJschrHost(), forward.getJschrPort());//端口映射 转发
        }
       
	}
	
	@Override
	public void setLoadTimeWeaver(LoadTimeWeaver loadTimeWeaver) {
		 log.warn("ssh通道配置loadTimeWeaver");
	}
	@PreDestroy
    public void disconnect() {
        if (session != null) {
        	session.disconnect();
        }
    }
}
