package com.dsj.inventory.framework.interceptor;

import cn.hutool.core.util.URLUtil;
import com.pocky.transport.bussiness.extraApp.entity.OrganizationApply;
import com.pocky.transport.bussiness.extraApp.service.IOrganizationApplyService;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.SignUtil;
import com.dsj.inventory.framework.annotations.ExtraAuth;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.jwt.TokenUtil;
import com.pocky.transport.framework.jwt.model.ExtraAuthInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.SortedMap;
import java.util.TreeMap;

import static com.dsj.inventory.common.context.BaseContextConstants.BEARER_HEADER_KEY;


/**
 *
 * 拦截器：针对指定接口，提供 定制化token 或签名认证
 */
@Slf4j
public class ExtraHandlerInterceptor extends HandlerInterceptorAdapter {

    @Autowired
    private TokenUtil tokenUtil;
    @Autowired
    private IOrganizationApplyService organizationApplyService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            log.info("not exec!!! url={}", request.getRequestURL());
            return super.preHandle(request, response, handler);
        }
        BaseContextHandler.setBoot(true);
        ExtraAuth methodExtraAuth = ((HandlerMethod)handler).getMethod().getAnnotation(ExtraAuth.class);
        if (methodExtraAuth != null && methodExtraAuth.blendAuth()) {
            String signMessage = signHandle(request);
            if (CommonUtils.isEmpty(signMessage)){
                return super.preHandle(request, response, handler);
            }else {
                String extraTokenMessage = extraTokenHandle(request);
                if (CommonUtils.isEmpty(extraTokenMessage)){
                    return super.preHandle(request, response, handler);
                }else {
                    log.error("外部接口认证拦截失败，{},{}",signMessage,extraTokenMessage);
                    throw new BizException("接口认证失败,失败原因参考,签名方式失败原因："+ signMessage + "；token方式失败原因： " + extraTokenMessage);
                }
            }
        }else if (methodExtraAuth != null && methodExtraAuth.signAuth()){
            String signMessage = signHandle(request);
            if (CommonUtils.isEmpty(signMessage)){
                return super.preHandle(request, response, handler);
            }else {
                log.error("外部接口认证拦截失败，{}",signMessage);
                throw new BizException("接口认证失败,失败原因参考,签名方式失败原因："+ signMessage);
            }
        }else if (methodExtraAuth != null && methodExtraAuth.extraTokenAuth()){
            String extraTokenMessage = extraTokenHandle(request);
            if (CommonUtils.isEmpty(extraTokenMessage)){
                return super.preHandle(request, response, handler);
            }else {
                log.error("外部接口认证拦截失败，{}",extraTokenMessage);
                throw new BizException("接口认证失败,失败原因参考" + "token方式失败原因： " + extraTokenMessage);
            }
        }
        return super.preHandle(request, response, handler);
    }

    public String signHandle(HttpServletRequest request) throws Exception {
        StringBuffer errorMessage = new StringBuffer();
        //走签名认证
        String secretId = getHeader("secretId", request);
        String timestamp = getHeader("timestamp", request);
        String nonestr = getHeader("nonestr", request);
        String sign = getHeader("sign", request);
        if(CommonUtils.isEmpty(sign) || CommonUtils.isEmpty(nonestr) ||CommonUtils.isEmpty(timestamp) ||CommonUtils.isEmpty(secretId)){
            errorMessage.append("签名验证失败。缺少必要参数");
            return errorMessage.toString();
        }
        OrganizationApply apply = organizationApplyService.lambdaQuery().eq(OrganizationApply::getAppSecretId, secretId).one();
        if (CommonUtils.isEmpty(apply)){
            errorMessage.append("签名验证失败。secretId不存在");
            return errorMessage.toString();
        }

        SortedMap<String, String> sortedMap = new TreeMap<>();
        sortedMap.put("secretId", secretId);
        sortedMap.put("timestamp", timestamp);
        sortedMap.put("sign", sign);
        sortedMap.put("secretKey", apply.getAppSecretKey());
        Boolean isOk = SignUtil.isCorrectSign(sortedMap, apply.getAppSecretKey(), nonestr);
        if (isOk){
            BaseContextHandler.set(BaseContextConstants.EXTRA_APP_APP_ID, apply.getId());
        }else {
            errorMessage.append("签名验证失败");
            return errorMessage.toString();
        }
        return errorMessage.toString();
    }

    public String extraTokenHandle(HttpServletRequest request) {
        StringBuffer errorMessage = new StringBuffer();
        String token = getHeader(BEARER_HEADER_KEY, request);
        if(CommonUtils.isEmpty(token)){
            errorMessage.append("token验证失败，未获取到token信息。");
            return errorMessage.toString();
        }
        ExtraAuthInfo authInfo = tokenUtil.getExtraTokenAuthInfo(token);
        if(CommonUtils.isEmpty(authInfo)){
            errorMessage.append("token验证失败，请重新获取token信息。");
            return errorMessage.toString();
        }
        //验证应用是否存在
        OrganizationApply apply = organizationApplyService.getById(authInfo.getAppId());
        if(CommonUtils.isEmpty(apply)) {
            errorMessage.append("token验证失败，应用信息不存在。");
            return errorMessage.toString();
        }
        BaseContextHandler.set(BaseContextConstants.EXTRA_APP_APP_ID, apply.getId());
        if(CommonUtils.isNotEmpty(token)){
            BaseContextHandler.setToken(token);
        }
        return errorMessage.toString();
    }

    private String getHeader(String name, HttpServletRequest request) {
        String value = request.getHeader(name);
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        return URLUtil.decode(value);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                                Object handler, Exception ex) throws Exception {
        BaseContextHandler.remove();
        super.afterCompletion(request, response, handler, ex);
    }
}
