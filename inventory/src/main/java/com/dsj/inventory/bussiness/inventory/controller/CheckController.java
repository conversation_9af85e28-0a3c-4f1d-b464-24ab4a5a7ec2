package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.CompleteCheckParam;
import com.dsj.inventory.bussiness.inventory.param.QueryCheckParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateCheckParam;
import com.dsj.inventory.bussiness.inventory.service.CheckService;
import com.dsj.inventory.bussiness.inventory.vo.CheckDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.CheckExportVo;
import com.dsj.inventory.bussiness.inventory.vo.CheckVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.EasyExcelUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 进销存盘点计划 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@RestController
@Api(value = "CheckController", tags = "进销存盘点计划api")
public class CheckController {

    @Autowired
    private CheckService checkService;

    /*新增盘点计划*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-check"})
    @PostMapping("/check")
    @ApiOperation(value = "新增盘点计划", notes = "新增盘点计划")
    public ResponseEntity<CheckDetailVo> saveCheck(@RequestBody SaveOrUpdateCheckParam param){
        CheckDetailVo checkVo = checkService.saveOrUpdateCheck(param);
        return Res.success(checkVo);
    }

    /*编辑盘点计划*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-check"})
    @PutMapping("/check/{id}")
    @ApiOperation(value = "编辑盘点计划", notes = "编辑盘点计划")
    public ResponseEntity<CheckDetailVo> updateCheck(@PathVariable Long id, @RequestBody SaveOrUpdateCheckParam param){
        param.setId(id);
        CheckDetailVo checkVo = checkService.saveOrUpdateCheck(param);
        return Res.success(checkVo);
    }

    /*删除盘点计划*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-check"})
    @DeleteMapping("/check/{id}")
    @ApiOperation(value = "删除盘点计划", notes = "删除盘点计划")
    public ResponseEntity<Void> deleteCheck(@PathVariable Long id){
        checkService.deleteCheck(id);
        return Res.success();
    }

    /*获取盘点计划列表*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-check"})
    @GetMapping("/check")
    @ApiOperation(value = "获取盘点计划列表", notes = "获取盘点计划列表")
    public ResponseEntity<List<CheckVo>> queryCheckList(QueryCheckParam param){
        Page<CheckVo> page = checkService.queryCheckPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取盘点计划详情*/
    @GetMapping("/check/{id}")
    @ApiOperation(value = "获取盘点计划详情", notes = "获取盘点计划详情")
    public ResponseEntity<CheckDetailVo> queryCheck(@PathVariable Long id){
        return Res.success(checkService.getCheckById(id));
    }

    /*提交盘点计划*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-check"})
    @PostMapping("/check/{id}/action-submit")
    @ApiOperation(value = "提交盘点计划", notes = "提交盘点计划")
    public ResponseEntity<Void> submitCheck(@PathVariable Long id){
        checkService.submitCheck(id);
        return Res.success();
    }

    /*盘点计划完成*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-check"})
    @PostMapping("/check/{id}/action-complete")
    @ApiOperation(value = "盘点计划完成", notes = "盘点计划完成")
    public ResponseEntity<CheckDetailVo> completeCheck(@PathVariable Long id,@RequestBody CompleteCheckParam param){
        param.setId(id);
        CheckDetailVo checkVo = checkService.completeCheck(param);
        return Res.success(checkVo);
    }

    /*盘点计划导出*/
    @GetMapping("/check/action-export")
    @ApiOperation(value = "导出盘点计划", notes = "导出盘点计划")
    public ResponseEntity<Void> exportCheck(HttpServletResponse response, QueryCheckParam param) throws IOException {
        param.setNeedExport(true);
        List<CheckExportVo> list = checkService.queryCheckList(param);
        EasyExcelUtils.exportOrdinaryExcel(response,list,"盘点计划导出列表","盘点计划导出列表", CheckExportVo.class);
        return Res.success();
    }

    /*生成日盘点计划*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-check"})
    @PostMapping("/check/{organizationId}/action-day")
    @ApiOperation(value = "生成日盘点计划", notes = "生成日盘点计划")
    public ResponseEntity<CheckDetailVo> saveDayCheck(@PathVariable Long organizationId){
        CheckDetailVo checkDetailVo = checkService.saveDayCheck(organizationId);
        return Res.success(checkDetailVo);
    }

}
