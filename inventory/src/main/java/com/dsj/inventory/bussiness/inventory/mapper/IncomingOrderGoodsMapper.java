package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.IncomingOrderGoods;
import com.dsj.inventory.bussiness.inventory.vo.IncomingOrderGoodsVo;
import com.dsj.inventory.common.enumeration.TrueEnum;

import java.util.List;

/**
 * <p>
 * 进销存入库单商品明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface IncomingOrderGoodsMapper extends BaseMapper<IncomingOrderGoods> {

    /**
     * 获取入库商品明细列表
     * @param orderId
     * @return
     */
    List<IncomingOrderGoodsVo> getIncomingOrderGoodsList(Long orderId);

    /**
     * 统计私域商品已入库数据
     * @param organizationMedicineId
     * @return
     */
    List<IncomingOrderGoodsVo> countIncomingOrderGoods(Long organizationMedicineId);

    /**
     * 根据订单ID将商品明细标记为已删除
     * @param orderId 订单ID
     * @return 更新记录数
     */
    default int markAsDeletedByOrderId(Long orderId) {
        LambdaUpdateWrapper<IncomingOrderGoods> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(IncomingOrderGoods::getOrderId, orderId)
                .set(IncomingOrderGoods::getIsDelete, TrueEnum.TRUE.getCode());
        return this.update(new IncomingOrderGoods(), updateWrapper);
    }

}
