package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 采购订单商品明细
 * <AUTHOR>
 * @date 2023/04/06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdatePurchaseOrderGoodsParam", description="新增or编辑 采购订单商品明细参数")
public class SaveOrUpdatePurchaseOrderGoodsParam {

    @ApiModelProperty(hidden = true,value = "订单Id")
    private Long orderId;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(hidden = true,value = "通用名")
    private String goodsName;

    @ApiModelProperty(hidden = true,value = "商品名称")
    private String generalName;

    @ApiModelProperty(hidden = true,value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(hidden = true,value = "包装单位")
    private String packUnit;

    @ApiModelProperty(hidden = true,value = "规格型号")
    private String specification;

    @ApiModelProperty(hidden = true,value = "剂型")
    private String drugType;

    @ApiModelProperty(hidden = true,value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(hidden = true,value = "上市许可持有人")
    private String launchPermitHolder;

    @ApiModelProperty(hidden = true,value = "当前零售价（私域商品价格）")
    private Integer price;

    @ApiModelProperty(hidden = true,value = "当前库存")
    private Double repertory;

    @ApiModelProperty(value = "含税价（进货价）")
    private Integer purchasePrice;

    @ApiModelProperty(value = "采购数量")
    @Min(value = 1, message = "采购数量不能小于1")
    @NotNull(message = "采购数量不能为空")
    private Double purchaseNumber;

    @ApiModelProperty(value = "含税金额（进货总金额）")
    private Integer totalPurchasePrice;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
