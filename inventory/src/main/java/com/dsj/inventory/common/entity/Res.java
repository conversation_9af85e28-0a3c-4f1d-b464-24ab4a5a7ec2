package com.dsj.inventory.common.entity;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName : Res
 * @Description : 统一返回实体
 * @Date : 2022-02-25 13:36
 */
public class Res<T> extends ResponseEntity<T> {

    public static final String ERROR_KEY = "error";
    public static final Map<String,String > errorMap = new HashMap<String, String>(){{put(ERROR_KEY,"");}};
    /**
     * @author: guojia
     * @description: 200 请求成功
     * @date: 2022/2/25
     */
    public static <E> ResponseEntity<E> success(E data) {
        return ResponseEntity.ok(data);
    }

    public static <E> ResponseEntity<List<E>> successPage(Page<E> page) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Access-Control-Expose-Headers","total,size,current,pages");
        headers.add("total", String.valueOf(page.getTotal()));
        headers.add("size", String.valueOf(page.getSize()));
        headers.add("current", String.valueOf(page.getCurrent()));
        headers.add("pages", String.valueOf(page.getPages()));
        return ResponseEntity.ok().headers(headers).body(page.getRecords());
    }

    public static ResponseEntity success() {
        return ResponseEntity.ok(new Object());
    }

    public static ResponseEntity fail() {
        String DEF_ERROR_MESSAGE = "服务器出错！！！";
        errorMap.put(ERROR_KEY,DEF_ERROR_MESSAGE);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorMap);
    }

    public static ResponseEntity fail(HttpStatus status,String msg) {
        errorMap.put(ERROR_KEY,msg);
        return ResponseEntity.status(status).body(errorMap);
    }

    public static ResponseEntity fail(String msg) {
        errorMap.put(ERROR_KEY,msg);
        return common(HttpStatus.INTERNAL_SERVER_ERROR,errorMap);
    }

    public static <E> ResponseEntity<E> common(HttpStatus status,E data) {
        return ResponseEntity.status(status).body(data);
    }
    public static ResponseEntity common(HttpStatus status) {
        return ResponseEntity.status(status).build();
    }

    /**
     * @author: guojia
     * @description:   401  用户未认证
     * @date: 2022/2/25
     */
    public static ResponseEntity failUnauthorized() {
        return fail(HttpStatus.UNAUTHORIZED,"用户未认证");
    }

    /**
     * @author: guojia
     * @description:   403  用户没有权限
     * @date: 2022/2/25
     */
    public static ResponseEntity failForbidden() {
        return fail(HttpStatus.FORBIDDEN,"用户没有权限");
    }
    /**
     * @author: guojia
     * @description:    406 请求格式或请求参数有误
     * @date: 2022/2/25
     */
    public static ResponseEntity failNotAcceptable(String msg) {
        return fail(HttpStatus.NOT_ACCEPTABLE,"请求格式或参数有误:"+msg);
    }

    public static ResponseEntity failNotAcceptable() {
        return fail(HttpStatus.NOT_ACCEPTABLE,"请求格式或参数有误:");
    }
    /**
     * @author: guojia
     * @description:  407 请求不完全，还需要后续请求
     * @date: 2022/2/25
     */
    public static <E> ResponseEntity<E> requiredAgain(E data) {
        return common(HttpStatus.PROXY_AUTHENTICATION_REQUIRED,data);
    }


    public Res(HttpStatus status) {
        super(status);
    }

    public Res(T body, HttpStatus status) {
        super(body, status);
    }

}
