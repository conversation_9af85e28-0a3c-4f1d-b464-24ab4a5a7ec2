package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @ClassName : MoveGoodsParam
 * @Description : 货架移动商品参数
 * @Date : 2023-04-18
 */
@Data
@Builder
@Accessors(chain = true)
@ApiModel(value = "MoveGoodsParam", description = "货架移动商品参数")
public class MoveGoodsParam {

    @ApiModelProperty(value = "库存Id（商品库存列表的id）")
    @NotNull(message = "库存Id不能为空")
    private Long inventoryId;

    @ApiModelProperty(value = "移动数量")
    @NotNull(message = "移动数量")
    private Double amount;
}
