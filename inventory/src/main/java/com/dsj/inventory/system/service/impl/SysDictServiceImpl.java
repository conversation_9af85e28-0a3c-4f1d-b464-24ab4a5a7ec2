package com.dsj.inventory.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.system.entity.SysDict;
import com.dsj.inventory.system.mapper.SysDictMapper;
import com.dsj.inventory.system.service.SysDictService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 系统字典表 服务实现类
 */
@Service
public class SysDictServiceImpl extends ServiceImpl<SysDictMapper, SysDict> implements SysDictService {

    @Override
    public List<SysDict> getByDictType(String dictType) {
        return baseMapper.selectByDictType(dictType);
    }

    @Override
    public List<SysDict> getByParentId(Long parentId) {
        return baseMapper.selectByParentId(parentId);
    }

    @Override
    public SysDict getByDictCode(String dictCode) {
        return baseMapper.selectByDictCode(dictCode);
    }

    @Override
    public IPage<SysDict> getDictPage(Page<SysDict> page, String dictName, String dictType) {
        return baseMapper.selectDictPage(page, dictName, dictType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDict(SysDict sysDict) {
        // 校验字典编码是否已存在
        SysDict existDict = getByDictCode(sysDict.getDictCode());
        if (existDict != null) {
            throw new RuntimeException("字典编码已存在");
        }

        // 如果父ID为空，设置为0（表示顶级字典）
        if (sysDict.getParentId() == null) {
            sysDict.setParentId(0L);
        }

        // 保存字典
        return save(sysDict);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDict(SysDict sysDict) {
        // 校验字典编码是否已被其他记录使用
        SysDict existDict = getByDictCode(sysDict.getDictCode());
        if (existDict != null && !existDict.getId().equals(sysDict.getId())) {
            throw new RuntimeException("字典编码已被其他记录使用");
        }

        // 更新字典
        return updateById(sysDict);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeDict(Long id) {
        // 查询是否有子字典
        List<SysDict> children = getByParentId(id);
        if (children != null && !children.isEmpty()) {
            throw new RuntimeException("该字典下存在子字典，不能删除");
        }

        // 删除字典
        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeDictBatch(List<Long> ids) {
        // 检查每个字典是否有子字典
        for (Long id : ids) {
            List<SysDict> children = getByParentId(id);
            if (children != null && !children.isEmpty()) {
                throw new RuntimeException("ID为" + id + "的字典下存在子字典，不能删除");
            }
        }

        // 批量删除字典
        return removeByIds(ids);
    }
} 