package com.dsj.inventory.bussiness.quality.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 药品首营审批表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_medicine_first_business")
@ApiModel(value="MedicineFirstBusinessEntity对象", description="药品首营审批表")
public class MedicineFirstBusinessEntity extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关联组织药品私域表ID")
    private Long orgMedicineMappingId;
    
    @ApiModelProperty(value = "关联供应商ID")
    private Long supplierId;
    
    @ApiModelProperty(value = "审批类型 (1-首营审批, 2-修改审批)")
    private Integer approvalType;
    
    @ApiModelProperty(value = "审批步骤 (1-采购经理审核, 2-质量经理审核, 3-质量负责人审核)")
    private Integer approvalStep;
    
    @ApiModelProperty(value = "操作类型 (0-创建, 1-提交, 2-通过, 3-驳回, 4-撤回)")
    private Integer actionType;
    
    @ApiModelProperty(value = "审批序号，同一药品的审批记录序号")
    private Integer approvalSequence;

    @ApiModelProperty(value = "申请时间")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "审核人ID (关联 sys_user 表)")
    private Long reviewerId;
    
    @ApiModelProperty(value = "当前审批角色代码")
    private String currentApproverRole;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime reviewTime;

    @ApiModelProperty(value = "审批状态 (0-草稿, 1-审核中, 2-已通过, 3-已驳回, 4-已撤回)")
    private Integer status;

    @ApiModelProperty(value = "审批意见")
    private String approvalOpinion;
    
    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;
} 