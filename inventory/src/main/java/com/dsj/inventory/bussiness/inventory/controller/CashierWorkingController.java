package com.dsj.inventory.bussiness.inventory.controller;


import com.dsj.inventory.bussiness.inventory.service.CashierWorkingService;
import com.dsj.inventory.bussiness.inventory.vo.CashierWorkingVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 进销存收银员值班记录 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@RestController
@Api(value = "CashierWorkingController", tags = "进销存收银员值班记录api")
public class CashierWorkingController {

    @Autowired
    private CashierWorkingService cashierWorkingService;

    /*收银员交班*/
    @OrgAuthMode(strict = true,strictUserMenus = false)
    @PostMapping("/cashier-working/action-turn/{organizationId}")
    @ApiOperation(value = "收银员交班", notes = "收银员交班")
    public ResponseEntity<CashierWorkingVo> turnCashierWorking(@PathVariable Long organizationId){
        CashierWorkingVo cashierWorkingVo = cashierWorkingService.turnCashierWorking(organizationId);
        return Res.success(cashierWorkingVo);
    }

}
