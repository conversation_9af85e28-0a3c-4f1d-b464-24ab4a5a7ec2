package com.dsj.inventory.bussiness.purchaseplan.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 进销存采购计划单商品明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_purchase_plan_goods")
@ApiModel(value="PurchasePlanGoods", description="进销存采购计划单商品明细")
public class PurchasePlanGoods extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "计划Id")
    @TableField("plan_id")
    private Long planId;

    @ApiModelProperty(value = "私域商品Id")
    @TableField("organization_medicine_id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "供应商ID，关联jxc_organization_supplier表")
    @TableField("organization_supplier_id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "通用名")
    @TableField("goods_name")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    @TableField("general_name")
    private String generalName;

    @ApiModelProperty(value = "商品编号")
    @TableField("goods_code")
    private String goodsCode;

    @ApiModelProperty(value = "包装单位")
    @TableField("pack_unit")
    private String packUnit;

    @ApiModelProperty(value = "规格型号")
    @TableField("specification")
    private String specification;

    @ApiModelProperty(value = "剂型")
    @TableField("drug_type")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    @TableField("manufacturer")
    private String manufacturer;

    @ApiModelProperty(value = "上市许可持有人")
    @TableField("launch_permit_holder")
    private String launchPermitHolder;

    @ApiModelProperty(value = "当前零售价（私域商品价格）")
    @TableField("price")
    private Integer price;

    @ApiModelProperty(value = "当前库存")
    @TableField("repertory")
    private Double repertory;

    @ApiModelProperty(value = "在途数量，已下单未入库的数量")
    @TableField("in_transit_quantity")
    private Double inTransitQuantity;

    @ApiModelProperty(value = "日均销量，用于计算库存上下限")
    @TableField("daily_avg_sales")
    private Double dailyAvgSales;

    @ApiModelProperty(value = "库存上限，上限天数×日均销量")
    @TableField("stock_upper_limit")
    private Double stockUpperLimit;

    @ApiModelProperty(value = "库存下限，下限天数×日均销量")
    @TableField("stock_lower_limit")
    private Double stockLowerLimit;

    @ApiModelProperty(value = "推荐采购数量，系统自动计算的建议采购量")
    @TableField("recommend_quantity")
    private Double recommendQuantity;

    @ApiModelProperty(value = "价格策略：1-按最低进价，2-按最新供应商，3-按上次进价")
    @TableField("price_strategy")
    private Integer priceStrategy;

    @ApiModelProperty(value = "含税价（进货价）")
    @TableField("purchase_price")
    private Integer purchasePrice;

    @ApiModelProperty(value = "采购数量")
    @TableField("purchase_number")
    private Double purchaseNumber;

    @ApiModelProperty(value = "含税金额（进货总金额）")
    @TableField("total_purchase_price")
    private Integer totalPurchasePrice;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
