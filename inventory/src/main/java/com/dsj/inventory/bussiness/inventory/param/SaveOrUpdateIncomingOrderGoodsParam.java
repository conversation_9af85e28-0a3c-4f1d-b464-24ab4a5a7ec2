package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 入库单商品明细
 * <AUTHOR>
 * @date 2023/04/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateIncomingOrderGoodsParam", description="新增or编辑 入库单商品明细参数")
public class SaveOrUpdateIncomingOrderGoodsParam {

    @ApiModelProperty(hidden = true,value = "入库单Id")
    private Long orderId;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(hidden = true,value = "通用名")
    private String goodsName;

    @ApiModelProperty(hidden = true,value = "商品名称")
    private String generalName;

    @ApiModelProperty(hidden = true,value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(hidden = true,value = "包装单位")
    private String packUnit;

    @ApiModelProperty(hidden = true,value = "规格型号")
    private String specification;

    @ApiModelProperty(hidden = true,value = "剂型")
    private String drugType;

    @ApiModelProperty(hidden = true,value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(hidden = true,value = "上市许可持有人")
    private String launchPermitHolder;

    @ApiModelProperty(hidden = true,value = "产地")
    private String producePlace;

    @ApiModelProperty(hidden = true,value = "当前零售价（私域商品价格）")
    private Integer price;

    @ApiModelProperty(hidden = true,value = "会员价（私域商品会员价）")
    private Integer memberPrice;

    @ApiModelProperty(hidden = true,value = "当前库存")
    private Double repertory;

    @ApiModelProperty(value = "含税价（进货价）")
    private Integer purchasePrice;

    @ApiModelProperty(value = "折后单价（含税价折后）")
    private Integer discountPrice;

    @ApiModelProperty(value = "采购数量")
    private Double purchaseNumber;

    @ApiModelProperty(value = "含税金额（进货总金额）")
    private Integer totalPurchasePrice;

    @ApiModelProperty(value = "折后金额（含税金额折后）")
    private Integer totalDiscountPrice;

    @ApiModelProperty(value = "生产日期")
    private LocalDateTime produceDate;

    @ApiModelProperty(value = "有效期至")
    private LocalDateTime userDate;

    @ApiModelProperty(value = "收货数量")
    private Double receiveAmount;

    @ApiModelProperty(value = "赠送数量")
    private Double giveAmount = 0.00;

    @ApiModelProperty(value = "合格数量")
    private Double qualifiedAmount;

    @ApiModelProperty(hidden = true,value = "不合格数量")
    private Double unqualifiedAmount;

    @ApiModelProperty(value = "不合格原因(入库环节拒收原因)")
    private String unqualifiedReason;

    @ApiModelProperty(value = "入库数量")
    private Double incomingAmount;

    @ApiModelProperty(value = "货位Id")
    private Long shelfId;

    @ApiModelProperty(hidden = true,value = "成本金额")
    private Integer costPrice;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
