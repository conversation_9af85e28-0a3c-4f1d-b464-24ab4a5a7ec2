package com.dsj.inventory.bussiness.quality.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "SupplierFirstBusinessUpdateParam", description = "供应商首营审批更新参数")
public class SupplierFirstBusinessUpdateParam {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "ID不能为空")
    private Long id;

    @ApiModelProperty(value = "关联 jxc_organization_supplier 表ID，表示审批的供应商")
    private Long supplierId;

    @ApiModelProperty(value = "申请时间")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "审核人ID (关联 sys_user 表)")
    private Long reviewerId;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime reviewTime;

    @ApiModelProperty(value = "审批状态 (例如: 0-待审批, 1-审批通过, 2-审批驳回)")
    private Integer status;

    @ApiModelProperty(value = "审批意见")
    private String approvalOpinion;

    @ApiModelProperty(value = "备注")
    private String remark;
} 