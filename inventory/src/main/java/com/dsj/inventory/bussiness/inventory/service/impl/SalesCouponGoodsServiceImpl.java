package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.SalesCouponGoods;
import com.dsj.inventory.bussiness.inventory.mapper.SalesCouponGoodsMapper;
import com.dsj.inventory.bussiness.inventory.service.SalesCouponGoodsService;
import com.dsj.inventory.bussiness.inventory.vo.SalesCouponGoodsVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 进销存优惠券商品明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Service
public class SalesCouponGoodsServiceImpl extends ServiceImpl<SalesCouponGoodsMapper, SalesCouponGoods> implements SalesCouponGoodsService {

    @Override
    public List<SalesCouponGoodsVo> querySalesCouponGoodsList(Long activityId) {
        return this.baseMapper.querySalesCouponGoodsList(activityId);
    }
}
