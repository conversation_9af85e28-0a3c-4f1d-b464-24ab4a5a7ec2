package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 采购订单处理状态 0暂存 1待收货 2已完成 3部分收货
 * <AUTHOR>
 */

@Getter
public enum PurchaseOrderStateEnum {

    TEMPORARY(0, "暂存"),
    /**
     * 订单已提交，等待首次收货或后续收货。
     * 当订单部分商品已收货但未全部完成时，状态会变为 PARTIALLY_RECEIVED。
     */
    WAITING(1, "待收货"),
    /**
     * 订单所有商品均已按采购数量完全收货。
     */
    COMPLETED(2, "已完成"),
    /**
     * 订单中部分商品已收货，但并非所有商品都已满足采购数量。
     */
    PARTIALLY_RECEIVED(3, "部分收货"),
    ;

    private Integer code;

    private String desc;

    PurchaseOrderStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (PurchaseOrderStateEnum a : PurchaseOrderStateEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
