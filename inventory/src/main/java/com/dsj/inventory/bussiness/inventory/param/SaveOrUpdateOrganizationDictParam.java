package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 组织字典参数保存或更新参数
 */
@Data
@ApiModel(description = "组织字典参数保存或更新参数")
public class SaveOrUpdateOrganizationDictParam {

    @ApiModelProperty(value = "主键ID（更新时必填）")
    private Long id;

    @ApiModelProperty(value = "组织ID", required = true)
    @NotNull(message = "组织ID不能为空")
    private Long organizationId;

    @ApiModelProperty(value = "字典名称", required = true)
    @NotBlank(message = "字典名称不能为空")
    private String dictName;

    @ApiModelProperty(value = "字典编码", required = true)
    @NotBlank(message = "字典编码不能为空")
    private String dictCode;

    @ApiModelProperty(value = "字典实际值", required = true)
    @NotBlank(message = "字典实际值不能为空")
    private String dictValue;

    @ApiModelProperty(value = "字典类型", required = true)
    @NotBlank(message = "字典类型不能为空")
    private String dictType;

    @ApiModelProperty(value = "字典描述")
    private String dictDesc;

    @ApiModelProperty(value = "父级ID")
    private Long parentId;
} 