package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询预警记录
 * <AUTHOR>
 * @date 2023/04/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QueryWarningLogParam",description = "查询预警记录参数")
public class QueryWarningLogParam extends BasePage {

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "商品信息（商品编号/通用名称）")
    private String goodsLike;

    @ApiModelProperty(value = "预警类型 1缺货 2积货 3滞销 4近效期")
    private Integer type;

    @ApiModelProperty(value = "滞销天数")
    private Integer unsalableDay;

    @ApiModelProperty(value = "零售价最小值")
    private Integer priceMin;

    @ApiModelProperty(value = "零售价最大值")
    private Integer priceMax;

    @ApiModelProperty(value = "库存数量最小值")
    private Integer inventoryAmountMin;

    @ApiModelProperty(value = "库存数量最大值")
    private Integer inventoryAmountMax;

    @ApiModelProperty(value = "近效期类型：0近效期 1已过期")
    private Integer becomeDueType;

    @ApiModelProperty(value = "距失效天数最小值")
    private Integer becomeDueMin;

    @ApiModelProperty(value = "距失效天数最大值")
    private Integer becomeDueMax;

}
