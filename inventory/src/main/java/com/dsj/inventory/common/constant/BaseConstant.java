package com.dsj.inventory.common.constant;

/**
 * 常量
 *
 * <AUTHOR>
 * @date 2022/8/8 9:59
 */
public interface BaseConstant {

    /**
     * 初始密码 cac12345
     * Dsj@2022
     */
    String INITIAL_PWD = "8ab6a9d7bedbe46ab97387caaba2c8c1";

    /**
     * 密码有效期天数 30天
     */
    Long PWD_EXPIRATION_DAYS = 30L;
    //=============================================分页 开始=============================================
    /**
     * 密码失败次数 5次
     */
    Integer PWD_FILE_NUMBER = 5;

    /**
     * 分页查询最大查询页数
     */
    Integer PAGE_SIZE_MAX = 50;
    //=============================================微信支付 开始=============================================
    /**
     * 交易描述 dealDescription
     */
    String DEAL_DESCRIPTION_REGISTRATION = "挂号费用";

    String DEAL_DESCRIPTION_BUY_MEDICINE = "购药费用";

    String DEAL_DESCRIPTION_WARM_ORDER = "暖心医生订单费用";

    String DEAL_LIVE_TICKET_ORDER = "直播门票费用";

    /**
     * 支付订单有效时间 30分钟
     */
    Integer DEAL_TIME_LIMIT = 30;

    //=============================================系统金额 开始=============================================
    /**
     * 系统金额单位转换
     */
    int MONEY_CONVERT = 100;

    /**
     * 系统金额 计算小数点位数
     */
    int MONEY_DECIMAL_COUNT = 2;

    /** 一次性读取 缓存数量*/
    int GET_COUNT = 50;

    //=============================================IM 消息相关 开始=============================================
//    data:  notice（大类）
//    description:  type: 系统公告（小类）
//    extension:  {标题，内容}（内容）

    /**
     * IM 消息描述 DESC
     */

    /** 发货*/
    String IM_DESC_DELIVER = "order_deliver";
    /** 收货*/
    String IM_DESC_RECEIVED = "order_received";
    /** 新增*/
    String IM_DESC_ORDER_NEW = "order_new";
    /** 申请退款*/
    String IM_DESC_ORDER_RETURN = "order_return";
    /** 拒单*/
    String IM_DESC_REJECT = "order_reject";
    /** 系统*/
    String IM_DESC_SYSTEM = "system";
    /** 支付成功*/
    String IM_DESC_PAY_SUCCESS = "pay";
    /** 超时*/
    String IM_MSG_TIMEOUT = "timeout";
    /** 超时取消*/
    String IM_MSG_TIMEOUT_CANCEL = "order_timeout_cancel";
    /** 自动收货*/
    String IM_MSG_RECEIVED_AUTO = "order_received_auto";
    /** CA签名 消息*/
    String IM_MSG_PAYLOAD = "payload";
    /** 公告*/
    String IM_MSG_NOTICE = "notice";
    /** 回访提醒*/
    String IM_MSG_RETURN_REMIND = "return_remind";
    /** 缺货*/
    String IM_DESC_INVENTORY_LACK = "inventory_lack";
    /** 积货*/
    String IM_DESC_INVENTORY_EXCEED = "inventory_exceed";
    /** 滞销*/
    String IM_DESC_INVENTORY_UNSALABLE = "inventory_unsalable";
    /** 近效期*/
    String IM_DESC_INVENTORY_BECOME_DUE = "inventory_become_due";
    /** 待问诊--专家问诊*/
    String IM_DESC_DIAGNOSE_WAIT_VISIT_SPECIALIST = "diagnose_wait_visit_specialist";
    /** 待接诊--专家问诊*/
    String IM_DESC_DIAGNOSE_WAIT_ACCEPT_SPECIALIST = "diagnose_wait_accept_specialist";
    /** 电子处方功能到期*/
    String IM_DESC_FUNCTION_TERM_USABLE_DATE = "function_term_usable_date";
    /** 进销存功能到期*/
    String IM_DESC_FUNCTION_TERM_INVENTORY_DATE = "function_term_inventory_date";

    /** 处方待审批 */
    String IM_DESC_PRESCRIPTION_APPROVE = "prescription_approve";

    /**
     *  "data": "大类"
     */

    /** orderinfo 订单相关通知*/
    String IM_DATE_ORDER_INFO = "orderInfo";

    /** poll  如果是通道轮训时  description 是xxx-${id} 成功 ：caSign-1610523752481685504 失败加_error caSign-1610523752481685504_error*/
    String IM_DATE_POLL = "poll";

    /** notice 通知公告*/
    String IM_DATE_NOTICE = "systemNotice";

    /** inventoryWarning 库存预警相关通知*/
    String IM_DATE_INVENTORY_WARNING = "inventoryWarning";

    /** prescriptionInfo 处方通知*/
    String IM_DATE_PRESCRIPTION_INFO = "prescriptionInfo";

    /** diagnose 待问诊相关通知*/
    String IM_DATE_DIAGNOSE_WAIT_VISIT = "diagnoseWaitVisit";

    /** diagnose 待接诊相关通知*/
    String IM_DATE_DIAGNOSE_WAIT_ACCEPT = "diagnoseWaitAccept";

    /** functionTerm 功能到期相关通知*/
    String IM_DATE_FUNCTION_TERM = "functionTerm";

//=============================================CA签名认证=============================================
    /**
     * 原文签名
     */
    String SIGN_TYPE_STR = "str";

    /**
     * 文件签名
     */
    String SIGN_TYPE_FILE = "file";

    /**
     * 相应码
     */
    int CA_RESPONSE_SUCCESS = 1001;
    int CA_RESPONSE_FAIL = 1002;
    int CA_RESPONSE_ERROR = 1003;
    int CA_RESPONSE_PARAM_ERROR = 1008;

    //=============================================his系统=============================================

    /** his系统 获取病人病历请求路径 */
    String MEDICAL_RECORD = "medical_record";

    //=============================================系统history=============================================

    /** 系统history库问诊单详情请求路径 */
    String DIAGNOSE_HISTORY = "diagnosisOrderHistory";

    /** 系统history库挂号单详情请求路径 */
    String REGISTRATION_HISTORY = "registrationHistory";

    /** 系统history库咨询单详情请求路径 */
    String CONSULT_DIAGNOSE_HISTORY = "consultDiagnoseHistory";

    /** 系统history库问诊单详情请求路径 */
    String EXTERNAL_DIAGNOSE_HISTORY = "externalDiagnosisOrder/action-history";

    /** 系统history库问诊单获取视频地址请求路径 */
    String DIAGNOSE_VIDEO_HISTORY = "diagnosisOrderHistory/action-video";

    /** 系统history库处方单详情请求路径 */
    String PRESCRIPTION_HISTORY = "prescriptionHistory";

    /** 系统history库外部处方单详情请求路径 */
    String EXTERNAL_PRESCRIPTION_HISTORY = "externalPrescription/action-history";

    /** 系统history库处方单视频请求路径 */
    String PRESCRIPTION_VIDEO_HISTORY = "prescriptionHistory/action-video";

    /** 系统history库处方单刷新请求路径 */
    String PRESCRIPTION_REFRESH_HISTORY = "prescriptionHistory/action-refresh";

    /** 系统history库处方单刷新请求路径 */
    String EXTERNAL_PRESCRIPTION_REFRESH_HISTORY = "externalPrescription/action-history/action-refresh";

    //=============================================分方参数 开始=============================================
    /**
     * 分方上限数量
     * split_upper_limit
     */
    int SPLIT_UPPER_LIMIT = 50;

    //=============================================时间格式化传 开始=============================================
    String START_H_M_S = "00:00:00";
    String END_H_M_S = "23:59:59";

    //=============================================字典码=============================================
    String CANCEL_ORDER_TIME = "cancel_order_time";

    /**
     * 作废图片路径
     */
    String R_ABANDON_PNG = "classpath:resources/abandon.png";

    /**
     * 已使用图片路径
     */
    String R_USED_PNG = "classpath:resources/used.png";

    //===========================================暖心医生接口路径============================================

    String WARM_ACCESS_KEY = "10023050516";
    String WARM_ACCESS_SECRET = "DDC1D583CFB69EADC5772BAC5D9215C3";
    String WARM_BASE_URL = "https://partner.warmdoctor.cn/";

    String WARM_SUCCESS_CODE = "9000200";

    /**
     * 医生
     */
    String WARM_DOCTOR = "doctor/";
    /**
     * 获取地区信息
     */
    String WARM_GET_HOSPITAL_REGION = "getHospitalRegion.htm";
    /**
     * 获取科室信息
     */
    String WARM_GET_DEPARTMENTS = "getDepartments.htm";
    /**
     * 获取医生列表
     */
    String WARM_GET_DOCTOR_INFO_LIST = "getDoctorInfoList.htm";
    /**
     * 获取医生详细信息
     */
    String WARM_GET_DOCTOR_INFO = "getDoctorInfo.htm";
    /**
     * 获取医生附件信息
     */
    String WARM_GET_DOCTOR_ATTACHMENT = "getAttachmentByDoctId.htm";
    /**
     * 获取医生可预约时间列表
     */
    String WARM_GET_DOCTOR_SCHEDULE = "getScheduleByDocId.htm";

    /**
     * 病患
     */
    String WARM_PATIENT = "patient/";
    /**
     * 获取与本人关系
     */
    String WARM_GET_PATIENT_RELATION = "getRelationForSelf.htm";
    /**
     * 获取视频目的
     */
    String WARM_GET_PATIENT_PURPOSE = "getPurpose.htm";
    /**
     * 填写病历
     */
    String WARM_GET_PATIENT_ADD = "addPatient.htm";

    /**
     * 腾讯
     */
    String WARM_TENCENT = "tencent/";
    /**
     * 获取userId
     */
    String WARM_GET_TENCENT_USER_ID = "getUserId.htm";
    /**
     * 获取签名
     */
    String WARM_GET_TENCENT_USER_SIG = "getGenSig.htm";
    /**
     * 获取房间号
     */
    String WARM_GET_TENCENT_ROOM_NO = "getRoomNo.htm";

    /**
     * 视频卡
     */
    String WARM_CARD = "proxyusercard/";
    /**
     * 视频卡激活
     */
    String WARM_CARD_EXECUTE = "executeActivateProxyCard.htm";

    /**
     * 预约单
     */
    String WARM_ORDER = "order/";
    /**
     * 创建预约单
     */
    String WARM_ORDER_ADD = "addOrder.htm";
    /**
     * 支付预约单（锁定）
     */
    String WARM_ORDER_PAY = "payOrder.htm";


}
