package com.dsj.inventory.common.generator;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * 基于Redis的编号生成器
 * 利用Redis的原子操作保证多服务器环境下编号的唯一性
 */
public class RedisNumberGenerator extends AbstractNumberGenerator {

    private final RedisTemplate<String, Object> redisTemplate;
    private final int sequenceLength;

    /**
     * 构造方法
     *
     * @param type          编号类型
     * @param redisTemplate Redis模板
     * @param sequenceLength 序列号长度，用于前导零填充
     */
    public RedisNumberGenerator(NumberType type, RedisTemplate<String, Object> redisTemplate, int sequenceLength) {
        super(type);
        this.redisTemplate = redisTemplate;
        this.sequenceLength = sequenceLength;
    }

    /**
     * 构造方法，默认序列号长度为6
     *
     * @param type          编号类型
     * @param redisTemplate Redis模板
     */
    public RedisNumberGenerator(NumberType type, RedisTemplate<String, Object> redisTemplate) {
        this(type, redisTemplate, 6);
    }

    @Override
    protected String getSequencePart() {
        String date = getDatePart();
        String key = String.format("number_generator:%s:%s", type.getPrefix(), date);
        
        // 使用Redis的原子操作获取并递增序列号
        RedisAtomicLong counter = new RedisAtomicLong(key, redisTemplate.getConnectionFactory());
        
        // 设置过期时间（例如，保留3天）
        counter.expire(3, TimeUnit.DAYS);
        
        // 获取递增后的值
        long sequence = counter.incrementAndGet();
        
        // 格式化序列号，添加前导零
        return String.format("%0" + sequenceLength + "d", sequence);
    }

    /**
     * 重置指定日期的序列号计数器（主要用于测试）
     *
     * @param date 日期
     */
    public void resetCounter(LocalDate date) {
        String dateStr = date.format(DATE_FORMATTER);
        String key = String.format("number_generator:%s:%s", type.getPrefix(), dateStr);
        redisTemplate.delete(key);
    }
} 