package com.dsj.inventory.bussiness.quality.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.quality.mapper.RejectionOrderGoodsMapper;
import com.dsj.inventory.bussiness.quality.entity.RejectionOrderGoods;
import com.dsj.inventory.bussiness.quality.service.RejectionOrderGoodsService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 拒收单商品明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Service
public class RejectionOrderGoodsServiceImpl extends ServiceImpl<RejectionOrderGoodsMapper, RejectionOrderGoods> implements RejectionOrderGoodsService {

    @Override
    public List<RejectionOrderGoods> listByRejectionOrderId(Long rejectionOrderId) {
        return baseMapper.selectListByRejectionOrderId(rejectionOrderId);
    }
} 