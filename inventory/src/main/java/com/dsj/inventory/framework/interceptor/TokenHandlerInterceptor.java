package com.dsj.inventory.framework.interceptor;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pocky.transport.bussiness.auth.entity.User;
import com.pocky.transport.bussiness.auth.enumeration.RoleEnum;
import com.pocky.transport.bussiness.auth.enumeration.UserEnum;
import com.pocky.transport.bussiness.auth.service.IUserService;
import com.pocky.transport.bussiness.auth.service.OrgRModuleService;
import com.pocky.transport.bussiness.auth.vo.UserROrganizationVo;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.entity.R;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.framework.annotations.LoginMode;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.code.ExceptionCode;
import com.dsj.inventory.framework.jwt.TokenUtil;
import com.dsj.inventory.framework.properties.IgnoreTokenProperties;
import com.pocky.transport.framework.jwt.model.AuthInfo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.dsj.inventory.common.context.BaseContextConstants.BEARER_HEADER_KEY;


/**
 * 网关： 获取token，并解析，然后将所有的用户、应用信息封装到请求头
 * <p>
 * 拦截器： 解析请求头数据， 将用户信息、应用信息封装到BaseContextHandler 考虑请求来源是否网关（ip等）
 * <p>
 * Created by pocky on 2017/9/10.
 *
 * <AUTHOR>
 * @date 2019-06-20 22:22
 */
@Slf4j
public class TokenHandlerInterceptor extends HandlerInterceptorAdapter {

    private final IgnoreTokenProperties ignoreTokenProperties;

    @Autowired
    private TokenUtil tokenUtil;
    @Autowired
    private IUserService userService;
    @Autowired
    private OrgRModuleService orgRModuleService;

    public TokenHandlerInterceptor(IgnoreTokenProperties ignoreTokenProperties) {
        this.ignoreTokenProperties = ignoreTokenProperties;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            log.info("not exec!!! url={}", request.getRequestURL());
            return super.preHandle(request, response, handler);
        }
        BaseContextHandler.setBoot(true);
        String uri = request.getRequestURI();
        AuthInfo authInfo = null;

        //判断是否要强制登录，如果不需要则可以放行
        LoginMode classLoginMode = ((HandlerMethod)handler).getMethod().getDeclaringClass().getAnnotation(LoginMode.class);
        if (classLoginMode != null && !classLoginMode.strict()) {
            return super.preHandle(request, response, handler);
        }
        LoginMode methodLoginMode = ((HandlerMethod)handler).getMethod().getAnnotation(LoginMode.class);
        if (methodLoginMode != null && !methodLoginMode.strict()) {
            return super.preHandle(request, response, handler);
        }
        try {
            // 忽略 token 认证的接口
            if (isIgnoreToken(uri)) {
                log.debug("access filter not execute");
                return super.preHandle(request, response, handler);
            }
            //获取token， 解析，然后想信息放入 heade
            //3, 获取token
            String token = getHeader(BEARER_HEADER_KEY, request);
            // 4, 解析 并 验证 token
            if (authInfo == null && CommonUtils.isNotEmpty(token)) {
                authInfo = tokenUtil.getAuthInfo(token);
            }
            if(CommonUtils.isNotEmpty(token)){
                BaseContextHandler.setToken(token);
            }
        } catch (BizException e) {
            log.error("验证token异常",e);
            throw BizException.wrap(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("验证token异常",e);
            throw BizException.wrap(R.FAIL_CODE, "验证token出错");
        }
        //5, 转换，将 token 解析出来的用户身份 和 解码后的tenant、Authorization 重新封装到请求头
        if (authInfo != null) {
            //判断用户是否存在
            User user = userService.getOne(new QueryWrapper<User>().lambda()
                    .eq(User::getId, authInfo.getUserId()));
            //判断用户是否需要踢登录
            if(CommonUtils.isNotEmpty(user)) {
                if(UserEnum.NEED_RELOGIN.getCode().equals(user.getNeedReLogin())) {
                    //设置不需要再踢登录
                    user.setNeedReLogin(UserEnum.NOT_NEED_RELOGIN.getCode());
                    userService.updateById(user);
                    //踢掉
                    throw BizException.wrap(ExceptionCode.JWT_TOKEN_EXPIRED.getCode(), "请重新登录");
                }else if (TrueEnum.FALSE.getCode().equals(user.getStatus())){
                    //禁用踢掉
                    throw BizException.wrap(ExceptionCode.JWT_USER_ENABLED.getCode(), ExceptionCode.JWT_USER_ENABLED.getMsg());
                }
                BaseContextHandler.setUserId(authInfo.getUserId());
                BaseContextHandler.setName(authInfo.getName());
                MDC.put(BaseContextConstants.JWT_KEY_USER_ID, String.valueOf(authInfo.getUserId()));
            } else {
                //踢掉
                throw BizException.wrap(ExceptionCode.JWT_TOKEN_EXPIRED.getCode(), "请重新登录");
            }
        } else {
            //踢掉
            throw BizException.wrap(ExceptionCode.JWT_TOKEN_EXPIRED.getCode(), "请重新登录");
        }
        //6 判断是否需要校验组织权限，判断人员菜单权限,有userRole 按传的来，没有生效组织 就按user 来
        boolean isSuperAdmin = false;
        String moduleType = getHeader("moduleType", request);
        String userRole = getHeader(BaseContextConstants.USER_ROLE, request);
        BaseContextHandler.set(BaseContextConstants.MODULE_TYPE, moduleType);
        BaseContextHandler.set(BaseContextConstants.USER_ROLE, userRole);
        String orgId = null;
        Integer organizationType = null;
        List<UserROrganizationVo> userROrganizationVos = authInfo.getUserROrganizationVos();
        if (CommonUtils.isNotEmpty(userROrganizationVos)){
            Optional<UserROrganizationVo> first = CommonUtils.isEmpty(userROrganizationVos) ? null :userROrganizationVos.stream().filter(uro -> uro.getOrgEnable().equals(TrueEnum.TRUE.getCode())).findFirst();
            if (first.isPresent()){
                orgId = first.get().getOrganizationId().toString();
                if (first.get().getOrganizationType().equals(OrganizationEnum.PLATFORM.getCode()) && first.get().getOrgEnable().equals(TrueEnum.TRUE.getCode())){
                    isSuperAdmin = true;
                }
                organizationType = first.get().getOrganizationType();
                BaseContextHandler.set(BaseContextConstants.USER_ORG_ID, orgId);
                BaseContextHandler.set(BaseContextConstants.USER_ORG_TYPE, organizationType);
            }else {
                BaseContextHandler.set(BaseContextConstants.USER_ROLE, RoleEnum.USER.getCode());
            }
        }else {
            BaseContextHandler.set(BaseContextConstants.USER_ROLE, RoleEnum.USER.getCode());
        }
        OrgAuthMode methodOrgAuthMode = ((HandlerMethod) handler).getMethod().getAnnotation(OrgAuthMode.class);
        if (methodOrgAuthMode != null) {
            if (isSuperAdmin){

            }else {
                if (methodOrgAuthMode.strict()) {
                    if (CommonUtils.isEmpty(moduleType) || CommonUtils.isEmpty(orgId)||CommonUtils.isEmpty(organizationType)) {
                        log.error("操作失败，该功能需要校验组织权限，缺少必要参数，请联系管理员。");
                    throw new BizException("操作失败，该功能需要校验组织权限，缺少必要参数，请联系管理员。");
                    } else {
                        if (Arrays.asList(1, 2, 3).contains(organizationType)) {
                            boolean isOk = orgRModuleService.checkPermission(orgId, moduleType);
                            if (!isOk) {
                                log.error("操作失败，该组织无对应模块操作权限，请联系管理员。");
                            throw new BizException("操作失败，该组织无对应模块操作权限，请联系管理员。");
                            }
                        }
                    }
                }
                if (methodOrgAuthMode.strictUserMenus() && CommonUtils.isNotEmpty(methodOrgAuthMode.menuCodeArray())) {
                    if (CommonUtils.isNotEmpty(authInfo.getUserMenuVos())){
                        boolean anyMatch = authInfo.getUserMenuVos().stream().anyMatch(um -> ListUtil.toList(methodOrgAuthMode.menuCodeArray()).contains(um.getMenuCode()));
                        if (!anyMatch) {
                            log.error("操作失败，操作人员无权限，请联系管理员。");
                    throw new BizException("操作失败，操作人员无权限，请联系管理员。");
                        }
                    }else {
                        log.error("操作失败，操作人员无权限，请联系管理员。");
                    throw new BizException("操作失败，操作人员无权限，请联系管理员。");
                    }
                }
            }
        }
        return super.preHandle(request, response, handler);
    }

    private String getHeader(String name, HttpServletRequest request) {
        String value = request.getHeader(name);
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        return URLUtil.decode(value);
    }

    /**
     * 忽略应用级token
     */
    protected boolean isIgnoreToken(String uri) {
        return ignoreTokenProperties.isIgnoreToken(uri);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                                Object handler, Exception ex) throws Exception {
        BaseContextHandler.remove();
        super.afterCompletion(request, response, handler, ex);
    }

}
