package com.dsj.inventory.common.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.common.entity.BasePage;
import cn.hutool.core.bean.BeanUtil;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页工具类
 */
public class PageUtils {

    /**
     * 将查询参数转换为MyBatis Plus的Page对象
     *
     * @param basePage 分页查询参数
     * @param <T>      实体类型
     * @return Page对象
     */
    public static <T> Page<T> toPage(BasePage basePage) {
        return new Page<>(basePage.getPage(), basePage.getSize());
    }

    /**
     * 将MyBatis Plus的Page对象转换为包含VO的Page对象
     *
     * @param page     MyBatis Plus Page对象 (包含Entity)
     * @param voClass  VO类
     * @param <E>      Entity类型
     * @param <V>      VO类型
     * @return Page对象 (包含VO)
     */
    public static <E, V> Page<V> toPageVO(Page<E> page, Class<V> voClass) {
        Page<V> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<V> voList = page.getRecords().stream()
                .map(entity -> BeanUtil.copyProperties(entity, voClass))
                .collect(Collectors.toList());
        voPage.setRecords(voList);
        return voPage;
    }
    
    /**
     * 将MyBatis Plus的Page对象转换为包含VO的Page对象，使用自定义转换函数
     *
     * @param page      MyBatis Plus Page对象 (包含Entity)
     * @param converter 自定义的实体到VO的转换函数
     * @param <E>       Entity类型
     * @param <V>       VO类型
     * @return Page对象 (包含VO)
     */
    public static <E, V> Page<V> toPageVO(Page<E> page, Function<E, V> converter) {
        Page<V> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<V> voList = page.getRecords().stream()
                .map(converter)
                .collect(Collectors.toList());
        voPage.setRecords(voList);
        return voPage;
    }
} 