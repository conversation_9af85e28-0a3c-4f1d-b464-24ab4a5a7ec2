package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询销售活动任务
 * <AUTHOR>
 * @date 2023/04/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QuerySalesActivityTaskParam",description = "查询销售活动任务参数")
public class QuerySalesActivityTaskParam extends BasePage {

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(hidden = true,value = "模块分类，1 云药快 ；2 外部；3 在线医院；4 商城；5进销存")
    private Integer moduleType;

    @ApiModelProperty(value = "任务名称模糊搜索")
    private String taskNameLike;

    @ApiModelProperty(value = "活动名称模糊搜索")
    private String activityNameLike;

    @ApiModelProperty(value = "活动类别，1单品活动 2会员日活动 3整单活动")
    private Integer activityCategory;

}
