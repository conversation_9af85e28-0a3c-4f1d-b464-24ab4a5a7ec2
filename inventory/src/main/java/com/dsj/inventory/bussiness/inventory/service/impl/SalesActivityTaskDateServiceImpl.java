package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityTaskDate;
import com.dsj.inventory.bussiness.inventory.mapper.SalesActivityTaskDateMapper;
import com.dsj.inventory.bussiness.inventory.service.SalesActivityTaskDateService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 销售活动任务日期 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Service
public class SalesActivityTaskDateServiceImpl extends ServiceImpl<SalesActivityTaskDateMapper, SalesActivityTaskDate> implements SalesActivityTaskDateService {

}
