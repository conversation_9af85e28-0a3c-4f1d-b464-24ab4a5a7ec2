package com.dsj.inventory.bussiness.inventory.enumeration;

/**
 * 付款方式代码枚举
 * 定义 jxc_payment_method 表中 method_code 字段的预设值
 */
public enum PaymentMethodCodeEnum {

    CASH("CASH", "现金支付"),
    WECHAT_PAY("WECHAT_PAY", "微信支付"),
    ALI_PAY("ALI_PAY", "支付宝支付"),
    POS("POS", "POS刷卡"),
    BANK_TRANSFER("BANK_TRANSFER", "银行转账");

    private final String code;
    private final String description;

    PaymentMethodCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
    
    /**
     * 根据code获取枚举值
     */
    public static PaymentMethodCodeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (PaymentMethodCodeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        
        return null;
    }
} 