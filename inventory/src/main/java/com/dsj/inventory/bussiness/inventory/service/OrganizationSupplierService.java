package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.quality.enumeration.DocumentStatusEnum;
import com.pocky.transport.bussiness.diagnose.param.DisableParam;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationSupplier;
import com.dsj.inventory.bussiness.inventory.param.QueryOrganizationSupplierParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateOrganizationSupplierParam;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationSupplierExportVo;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationSupplierVo;

import java.util.List;

/**
 * <p>
 * 供应商组织私域 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
public interface OrganizationSupplierService extends IService<OrganizationSupplier> {

    /**
     * 新增、编辑 供应商组织私域
     * @param param
     * @return
     */
    OrganizationSupplierVo saveOrUpdateOrganizationSupplier(SaveOrUpdateOrganizationSupplierParam param);

    /**
     * 删除 供应商组织私域
     * @param id
     */
    void deleteOrganizationSupplier(Long id);

    /**
     * 获取供应商组织私域分页列表
     * @param param
     * @return
     */
    Page<OrganizationSupplierVo> queryOrganizationSupplierPage(QueryOrganizationSupplierParam param);

    /**
     * 获取供应商组织私域列表详情
     * @param id
     * @return
     */
    OrganizationSupplierVo getOrganizationSupplierById(Long id);

    /**
     * 变更供应商组织私域可用状态
     * @param param
     */
    void disableOrganizationSupplier(DisableParam param);

    /**
     * 供应商组织私域绑定公域供应商
     * @param serialNo 公域供应商邀请码
     * @param organizationId 组织Id
     */
    void bindOrganizationSupplier(String serialNo, Long organizationId);

    /**
     * 导出供应商组织私域列表
     * @param param
     * @return
     */
    List<OrganizationSupplierExportVo> queryOrganizationSupplierExportList(QueryOrganizationSupplierParam param);

    // 更新 审批状态
    void updateApprovalStatus(Long id, DocumentStatusEnum status);

    /**
     * 根据id查询供应商名称
     *
     * @param id
     * @return
     */
    String getOrganizationSupplierNameById(Long id);
}
