package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryPurchaseOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdatePurchaseOrderParam;
import com.dsj.inventory.bussiness.inventory.service.PurchaseOrderService;
import com.dsj.inventory.bussiness.inventory.vo.PurchaseOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchaseOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchaseOrderVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.EasyExcelUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 进销存采购订单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
@RestController
@Api(value = "PurchaseOrderController", tags = "进销存采购订单api")
public class PurchaseOrderController {

    @Autowired
    private PurchaseOrderService purchaseOrderService;

    /*新增采购订单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-2"})
    @PostMapping("/purchase-order")
    @ApiOperation(value = "新增采购订单", notes = "新增采购订单")
    public ResponseEntity<PurchaseOrderDetailVo> savePurchaseOrder(@RequestBody SaveOrUpdatePurchaseOrderParam param){
        PurchaseOrderDetailVo purchaseOrderVo = purchaseOrderService.saveOrUpdatePurchaseOrder(param);
        return Res.success(purchaseOrderVo);
    }

    /*编辑采购订单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-2"})
    @PutMapping("/purchase-order/{id}")
    @ApiOperation(value = "编辑采购订单", notes = "编辑采购订单")
    public ResponseEntity<PurchaseOrderDetailVo> updatePurchaseOrder(@PathVariable Long id, @RequestBody SaveOrUpdatePurchaseOrderParam param){
        param.setId(id);
        PurchaseOrderDetailVo purchaseOrderVo = purchaseOrderService.saveOrUpdatePurchaseOrder(param);
        return Res.success(purchaseOrderVo);
    }

    /*删除采购订单*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-2"})
    @DeleteMapping("/purchase-order/{id}")
    @ApiOperation(value = "删除采购订单", notes = "删除采购订单")
    public ResponseEntity<Void> deletePurchaseOrder(@PathVariable Long id){
        purchaseOrderService.deletePurchaseOrder(id);
        return Res.success();
    }

    /*获取采购订单列表*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-2"})
    @GetMapping("/purchase-order")
    @ApiOperation(value = "获取采购订单列表", notes = "获取采购订单列表")
    public ResponseEntity<List<PurchaseOrderVo>> queryPurchaseOrderList(QueryPurchaseOrderParam param){
        Page<PurchaseOrderVo> page = purchaseOrderService.queryPurchaseOrderPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取采购订单详情*/
    @GetMapping("/purchase-order/{id}")
    @ApiOperation(value = "获取采购订单详情", notes = "获取采购订单详情")
    public ResponseEntity<PurchaseOrderDetailVo> queryPurchaseOrder(@PathVariable Long id){
        return Res.success(purchaseOrderService.getPurchaseOrderById(id));
    }

    /*提交采购订单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-purchase-2"})
    @PostMapping("/purchase-order/{id}/action-submit")
    @ApiOperation(value = "提交采购订单", notes = "提交采购订单")
    public ResponseEntity<Void> submitPurchaseOrder(@PathVariable Long id){
        purchaseOrderService.submitPurchaseOrder(id);
        return Res.success();
    }

    /*采购订单导出*/
    @GetMapping("/purchase-order/action-export")
    @ApiOperation(value = "导出采购订单", notes = "导出采购订单")
    public ResponseEntity<Void> exportPurchaseOrder(HttpServletResponse response, QueryPurchaseOrderParam param) throws IOException {
        param.setNeedExport(true);
        List<PurchaseOrderExportVo> list = purchaseOrderService.queryPurchaseOrderList(param);
        EasyExcelUtils.exportOrdinaryExcel(response,list,"采购订单导出列表","采购订单导出列表", PurchaseOrderExportVo.class);
        return Res.success();
    }
}
