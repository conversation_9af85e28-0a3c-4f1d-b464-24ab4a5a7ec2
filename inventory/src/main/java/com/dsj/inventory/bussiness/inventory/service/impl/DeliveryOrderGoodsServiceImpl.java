package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.DeliveryOrderGoods;
import com.dsj.inventory.bussiness.inventory.mapper.DeliveryOrderGoodsMapper;
import com.dsj.inventory.bussiness.inventory.service.DeliveryOrderGoodsService;
import com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderGoodsVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 进销存退货单商品明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Service
public class DeliveryOrderGoodsServiceImpl extends ServiceImpl<DeliveryOrderGoodsMapper, DeliveryOrderGoods> implements DeliveryOrderGoodsService {

    @Override
    public List<DeliveryOrderGoodsVo> getDeliveryOrderGoodsByOrderId(Long orderId) {
        return this.baseMapper.getDeliveryOrderGoodsByOrderId(orderId);
    }
}
