package com.dsj.inventory.bussiness.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationDict;
import com.dsj.inventory.bussiness.inventory.mapper.OrganizationDictMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryOrganizationDictParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateOrganizationDictParam;
import com.dsj.inventory.bussiness.inventory.service.OrganizationDictService;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationDictVO;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.framework.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组织字典参数 Service 实现类
 */
@Service
public class OrganizationDictServiceImpl extends ServiceImpl<OrganizationDictMapper, OrganizationDict> implements OrganizationDictService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdateOrganizationDict(SaveOrUpdateOrganizationDictParam param) {
        // 检查是否已存在相同组织下同一类型、同一编码的字典项
        Integer duplicateCount = baseMapper.countDuplicateDict(
                param.getOrganizationId(),
                param.getDictType(),
                param.getDictCode(),
                param.getId()
        );

        // 存在同组织下同类型、同编码且非自身的记录，则抛出异常
        if (duplicateCount > 0) {
            throw new BizException("同一组织下相同类型和编码的字典项已存在");
        }

        // 准备实体对象
        OrganizationDict organizationDict = new OrganizationDict();
        BeanUtil.copyProperties(param, organizationDict);

        // 获取当前操作用户ID
        Long userId = BaseContextHandler.getUserId();

        if (param.getId() != null) {
            // 更新操作
            organizationDict.setUpdateTime(LocalDateTime.now());
            organizationDict.setUpdateUser(userId);
            this.updateById(organizationDict);
        } else {
            // 新增操作
            organizationDict.setCreateTime(LocalDateTime.now());
            organizationDict.setCreateUser(userId);
            organizationDict.setUpdateTime(LocalDateTime.now());
            organizationDict.setUpdateUser(userId);
            this.save(organizationDict);
        }

        return organizationDict.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteOrganizationDict(Long id) {
        // 检查是否存在
        OrganizationDict organizationDict = this.getById(id);
        if (organizationDict == null) {
            throw new BizException("组织字典参数不存在");
        }

        // 检查是否有子节点
        if (baseMapper.countChildren(id) > 0) {
            throw new BizException("存在子字典项，请先删除子项");
        }

        // 执行逻辑删除
        return this.removeById(id);
    }

    @Override
    public OrganizationDictVO getOrganizationDict(Long id) {
        OrganizationDict organizationDict = this.getById(id);
        if (organizationDict == null) {
            return null;
        }

        OrganizationDictVO vo = new OrganizationDictVO();
        BeanUtil.copyProperties(organizationDict, vo);
        return vo;
    }

    @Override
    public Page<OrganizationDictVO> queryOrganizationDictPage(QueryOrganizationDictParam param) {
        // 执行分页查询
        Page<OrganizationDict> page = new Page<>(param.getPage(), param.getSize());
        Page<OrganizationDict> resultPage = baseMapper.selectPageByParam(page, param);

        // 转换为VO对象
        Page<OrganizationDictVO> voPage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        List<OrganizationDictVO> voList = resultPage.getRecords().stream().map(entity -> {
            OrganizationDictVO vo = new OrganizationDictVO();
            BeanUtil.copyProperties(entity, vo);
            return vo;
        }).collect(Collectors.toList());
        voPage.setRecords(voList);

        return voPage;
    }

    @Override
    public List<OrganizationDictVO> listByOrgAndType(Long organizationId, String dictType) {
        if (organizationId == null || StrUtil.isBlank(dictType)) {
            return new ArrayList<>();
        }

        // 查询指定组织和字典类型的字典项
        List<OrganizationDict> list = baseMapper.selectByOrgAndType(organizationId, dictType);

        // 转换为VO对象
        return list.stream().map(entity -> {
            OrganizationDictVO vo = new OrganizationDictVO();
            BeanUtil.copyProperties(entity, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public OrganizationDictVO getByOrgTypeAndCode(Long organizationId, String dictType, String dictCode) {
        if (organizationId == null || StrUtil.isBlank(dictType) || StrUtil.isBlank(dictCode)) {
            return null;
        }

        // 查询指定组织、字典类型和编码的字典项
        OrganizationDict entity = baseMapper.selectByOrgTypeAndCode(organizationId, dictType, dictCode);
        if (entity == null) {
            return null;
        }

        // 转换为VO对象
        OrganizationDictVO vo = new OrganizationDictVO();
        BeanUtil.copyProperties(entity, vo);
        return vo;
    }
} 