package com.dsj.inventory.bussiness.quality.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.quality.entity.RejectionOrder;
import com.dsj.inventory.bussiness.quality.param.RejectionAuditParam;
import com.dsj.inventory.bussiness.quality.param.RejectionOrderQueryParam;
import com.dsj.inventory.bussiness.quality.vo.RejectionOrderVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.quality.param.RejectionCreateParam;

/**
 * <p>
 * 拒收单主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface RejectionOrderService extends IService<RejectionOrder> {
    /**
     * 分页查询拒收单
     * @param param 查询参数
     * @return 分页结果
     */
    Page<RejectionOrderVO> queryPage(RejectionOrderQueryParam param);

    /**
     * 获取拒收单详情
     * @param id 拒收单ID
     * @return 详情VO
     */
    RejectionOrderVO getDetail(Long id);

    /**
     * 审核拒收单
     * @param id 拒收单ID
     * @param param 审核参数
     * @return 是否成功
     */
    boolean audit(Long id, RejectionAuditParam param);

    /**
     * 从源单据创建拒收单
     *
     * @param param 包含源单据信息和被拒收商品列表的参数对象
     * @return 创建成功的拒收单ID
     */
    Long createFromSource(RejectionCreateParam param);
} 