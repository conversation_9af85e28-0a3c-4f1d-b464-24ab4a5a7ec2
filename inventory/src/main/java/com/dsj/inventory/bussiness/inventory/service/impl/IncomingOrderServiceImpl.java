package com.dsj.inventory.bussiness.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.*;
import com.dsj.inventory.bussiness.inventory.enumeration.*;
import com.dsj.inventory.bussiness.inventory.mapper.IncomingOrderMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryIncomingOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateIncomingOrderGoodsParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateIncomingOrderParam;
import com.dsj.inventory.bussiness.inventory.service.*;
import com.dsj.inventory.bussiness.inventory.vo.*;
import com.dsj.inventory.bussiness.purchaseplan.entity.PurchasePlan;
import com.dsj.inventory.bussiness.purchaseplan.entity.PurchasePlanGoods;
import com.dsj.inventory.bussiness.purchaseplan.enumeration.PurchasePlanStateEnum;
import com.dsj.inventory.bussiness.quality.param.RejectedGoodsItem;
import com.dsj.inventory.bussiness.quality.param.RejectionCreateParam;
import com.dsj.inventory.bussiness.quality.service.RejectionOrderService;
import com.dsj.inventory.common.constant.BaseConstant;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.generator.NumberService;
import com.dsj.inventory.common.generator.NumberType;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.common.utils.TencentIMUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.entity.UserROrganization;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.diagnose.service.UserROrganizationService;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineExpand;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.enumeration.GoodsTypeEnum;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineExpandService;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 进销存入库单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Slf4j
@Service
public class IncomingOrderServiceImpl extends ServiceImpl<IncomingOrderMapper, IncomingOrder> implements IncomingOrderService {

    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @Autowired
    private IncomingOrderGoodsService incomingOrderGoodsService;
    @Autowired
    private AcceptOrderService acceptOrderService;
    @Autowired
    private AcceptOrderGoodsService acceptOrderGoodsService;
    @Autowired
    private InventoryService inventoryService;
    @Autowired
    private InventoryChangeLogService inventoryChangeLogService;
    @Autowired
    private PurchasePlanService purchasePlanService;
    @Autowired
    private PurchasePlanGoodsService purchasePlanGoodsService;
    @Autowired
    private PurchaseOrderService purchaseOrderService;
    @Autowired
    private PurchaseOrderGoodsService purchaseOrderGoodsService;
    @Autowired
    private ReceiveOrderService receiveOrderService;
    @Autowired
    private ReceiveOrderGoodsService receiveOrderGoodsService;
    @Autowired
    private WarningLogService warningLogService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private TencentIMUtils tencentIMUtils;
    @Autowired
    private UserROrganizationService userROrganizationService;
    @Autowired
    private OrganizationMedicineExpandService organizationMedicineExpandService;
    @Autowired
    private OrganizationMedicineMappingExtService organizationMedicineMappingExtService;
    @Autowired
    private NumberService numberService;
    @Autowired
    private RejectionOrderService rejectionOrderService;
    @Autowired
    private OrganizationSupplierServiceImpl organizationSupplierService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IncomingOrderDetailVo saveOrUpdateIncomingOrder(SaveOrUpdateIncomingOrderParam param) {
        if (CommonUtils.isEmpty(param)) {
            throw new ParamException("参数异常");
        }
        Organization organization = organizationService.getById(param.getOrganizationId());
        if (CommonUtils.isEmpty(organization)) {
            throw new BizException("组织不存在");
        }
        if (CommonUtils.isEmpty(organization.getInventoryDate())) {
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        if (CommonUtils.isNotEmpty(param.getId())) {
            IncomingOrder byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            //删除商品明细
            incomingOrderGoodsService.markAsDeletedByOrderId(param.getId());
        } else {
            //生成入库单号：RK+年后两位+月日四位+药房识别码六位+三位
            String day = DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_FORMAT);
            String dayStr = day.replaceAll("-", "").substring(2, 8);
            Integer count = this.getIncomingOrderCount(organization.getId(), day);
            count = count + 1;
            param.setCode("RK" + dayStr + organization.getSerialNo() + String.format("%03d", count));
        }

        if (CommonUtils.isNotEmpty(param.getOrderId())) {
            AcceptOrder acceptOrder = acceptOrderService.getById(param.getOrderId());
            if (CommonUtils.isEmpty(acceptOrder)) {
                throw new BizException("操作失败，验收单不存在");
            }
            int count = this.baseMapper.countByOrderIdExcludeIncomingOrderId(param.getOrderId(), param.getId());
            if (count > 0) {
                throw new BizException("操作失败，验收单已绑定，不能重复绑定");
            }
            param.setOrderCode(acceptOrder.getCode());
        } else {
            param.setPurchaserId(BaseContextHandler.getUserId());
            param.setAcceptId(BaseContextHandler.getUserId());
        }

        StringBuilder purchaseContent = new StringBuilder();
        Set<String> goodsTypeSet = new HashSet<>();
        BigDecimal totalPrice = BigDecimal.valueOf(0);
        BigDecimal totalDiscountPrice = BigDecimal.valueOf(0);
        AcceptOrder acceptOrder = null;
        if (CommonUtils.isNotEmpty(param.getOrderId())) {
            acceptOrder = acceptOrderService.getById(param.getOrderId());
        }

        if (CommonUtils.isNotEmpty(param.getGoodsList())) {
            List<SaveOrUpdateIncomingOrderGoodsParam> goodsList = param.getGoodsList();
            // 由于批号将由后端生成或继承，移除按批号分组的逻辑
            goodsList = goodsList.stream().collect(Collectors.groupingBy(SaveOrUpdateIncomingOrderGoodsParam::getOrganizationMedicineId)).values().stream().map(d -> d.get(0)).collect(Collectors.toList());

            if (goodsList.size() != param.getGoodsList().size()) {
                throw new BizException("操作失败，存在重复商品");
            }
            List<IncomingGoodsCostVo> goodsDistinctList = param.getGoodsList().stream().map(item -> {
                IncomingGoodsCostVo costVo = BeanUtil.toBean(item, IncomingGoodsCostVo.class);
                // 处理赠送数量为null的情况
                if (costVo.getGiveAmount() == null) {
                    costVo.setGiveAmount(0.0);
                }
                return costVo;
            }).collect(Collectors.toList());
            goodsDistinctList = goodsDistinctList.stream().collect(Collectors.groupingBy(g -> g.getOrganizationMedicineId())).values().stream().map(d -> {
                IncomingGoodsCostVo goods = d.get(0);
                goods.setOrganizationMedicineId(d.get(0).getOrganizationMedicineId());
                goods.setPurchaseNumber(d.stream().mapToDouble(item -> item.getPurchaseNumber() != null ? item.getPurchaseNumber() : 0.0).sum());
                goods.setReceiveAmount(d.stream().mapToDouble(item -> item.getReceiveAmount() != null ? item.getReceiveAmount() : 0.0).sum());
                goods.setIncomingAmount(d.stream().mapToDouble(item -> item.getIncomingAmount() != null ? item.getIncomingAmount() : 0.0).sum());
                goods.setTotalDiscountPrice(d.stream().mapToInt(item -> item.getTotalDiscountPrice() != null ? item.getTotalDiscountPrice() : 0).sum());
                return goods;
            }).collect(Collectors.toList());
            for (SaveOrUpdateIncomingOrderGoodsParam goodsParam : param.getGoodsList()) {
                // 处理赠送数量为null的情况
                if (goodsParam.getGiveAmount() == null) {
                    goodsParam.setGiveAmount(0.0);
                }
                OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(goodsParam.getOrganizationMedicineId());
                if (CommonUtils.isEmpty(organizationMedicine)) {
                    throw new BizException("操作失败，商品Id" + goodsParam.getOrganizationMedicineId() + "不存在");
                }
                if (CommonUtils.isNotEmpty(param.getOrderId())) {
                    AcceptOrderGoods acceptOrderGoods = acceptOrderGoodsService.getByOrderIdAndOrgMedicineId(param.getOrderId(), organizationMedicine.getId());
                    if (CommonUtils.isEmpty(acceptOrderGoods)) {
                        throw new BizException("操作失败，验收单不存在" + organizationMedicine.getName());
                    }
                    goodsParam.setGoodsName(acceptOrderGoods.getGoodsName());
                    goodsParam.setGeneralName(acceptOrderGoods.getGeneralName());
                    goodsParam.setGoodsCode(acceptOrderGoods.getGoodsCode());
                    goodsParam.setPackUnit(acceptOrderGoods.getPackUnit());
                    goodsParam.setSpecification(acceptOrderGoods.getSpecification());
                    goodsParam.setDrugType(acceptOrderGoods.getDrugType());
                    goodsParam.setManufacturer(acceptOrderGoods.getManufacturer());
                    goodsParam.setLaunchPermitHolder(acceptOrderGoods.getLaunchPermitHolder());
                    goodsParam.setProducePlace(acceptOrderGoods.getProducePlace());
                    goodsParam.setPrice(acceptOrderGoods.getPrice());
                    goodsParam.setMemberPrice(acceptOrderGoods.getMemberPrice());
                    goodsParam.setPurchasePrice(acceptOrderGoods.getPurchasePrice());
                    goodsParam.setDiscountPrice(acceptOrderGoods.getDiscountPrice());
                    goodsParam.setPurchaseNumber(acceptOrderGoods.getPurchaseNumber());
                    goodsParam.setProduceDate(acceptOrderGoods.getProduceDate());
                    goodsParam.setUserDate(acceptOrderGoods.getUserDate());
                    goodsParam.setReceiveAmount(acceptOrderGoods.getReceiveAmount());
                    goodsParam.setQualifiedAmount(acceptOrderGoods.getQualifiedAmount());
                    goodsParam.setUnqualifiedAmount(acceptOrderGoods.getUnqualifiedAmount());
                } else {
                    goodsParam.setGoodsName(organizationMedicine.getName());
                    goodsParam.setGeneralName(organizationMedicine.getGeneralName());
                    goodsParam.setGoodsCode(organizationMedicine.getGoodsCode());
                    goodsParam.setPackUnit(organizationMedicine.getPackUnit());
                    goodsParam.setSpecification(organizationMedicine.getSpecification());
                    goodsParam.setDrugType(organizationMedicine.getDrugType());
                    goodsParam.setManufacturer(organizationMedicine.getManufacturer());
                    OrganizationMedicineExpand medicineExpand = organizationMedicineExpandService.getById(organizationMedicine.getId());
                    if (CommonUtils.isNotEmpty(medicineExpand)) {
                        goodsParam.setLaunchPermitHolder(medicineExpand.getLaunchPermitHolder());
                    }
                    goodsParam.setProducePlace(organizationMedicine.getProducePlace());
                    goodsParam.setPrice(organizationMedicine.getOfflinePrice());
                    goodsParam.setMemberPrice(organizationMedicine.getMemberPrice());
                    goodsParam.setUnqualifiedAmount(BigDecimal.valueOf(goodsParam.getReceiveAmount()).subtract(BigDecimal.valueOf(goodsParam.getQualifiedAmount())).doubleValue());
                }
                BigDecimal amount = BigDecimal.valueOf(goodsParam.getIncomingAmount()).subtract(BigDecimal.valueOf(goodsParam.getGiveAmount()));
                BigDecimal totalPurchasePrice = BigDecimal.valueOf(goodsParam.getPurchasePrice()).multiply(amount);
                totalPurchasePrice = totalPurchasePrice.setScale(0, BigDecimal.ROUND_HALF_UP);
                if (totalPurchasePrice.intValue() != goodsParam.getTotalPurchasePrice()) {
                    throw new BizException("操作失败，商品" + goodsParam.getGoodsName() + "小计金额计算不一致");
                }
                BigDecimal goodsTotalDiscountPrice = BigDecimal.valueOf(goodsParam.getDiscountPrice()).multiply(amount);
                goodsTotalDiscountPrice = goodsTotalDiscountPrice.setScale(0, BigDecimal.ROUND_HALF_UP);
                if (goodsTotalDiscountPrice.intValue() != goodsParam.getTotalDiscountPrice()) {
                    throw new BizException("操作失败，商品" + goodsParam.getGoodsName() + "折后金额计算不一致");
                }

                goodsParam.setRepertory(organizationMedicine.getMedicineRepertory());
                if (CommonUtils.isNotEmpty(purchaseContent) && purchaseContent.indexOf(goodsParam.getGoodsName()) == -1) {
                    purchaseContent.append(goodsParam.getGoodsName() + "*" + goodsParam.getPurchaseNumber() + goodsParam.getPackUnit() + ",");
                }
                goodsTypeSet.add(GoodsTypeEnum.getDesc(organizationMedicine.getGoodsType()));
                totalPrice = totalPrice.add(totalPurchasePrice);
                totalDiscountPrice = totalDiscountPrice.add(goodsTotalDiscountPrice);
                for (IncomingGoodsCostVo goodsCost : goodsDistinctList) {
                    if (goodsCost.getOrganizationMedicineId().equals(goodsParam.getOrganizationMedicineId())) {
                        //计算进货成本
                        BigDecimal costPrice = BigDecimal.valueOf(goodsCost.getTotalDiscountPrice()).divide(BigDecimal.valueOf(goodsCost.getIncomingAmount()), 0, BigDecimal.ROUND_HALF_UP);
                        goodsParam.setCostPrice(costPrice.intValue());
                        //合并采购数量、收货数量
                        if (CommonUtils.isEmpty(param.getOrderId()) && IncomingOrderStateEnum.COMPLETED.getCode().equals(param.getDealState())) {
                            goodsParam.setPurchaseNumber(goodsCost.getPurchaseNumber());
                            goodsParam.setReceiveAmount(goodsCost.getReceiveAmount());
                        }
                        break;
                    }
                }
            }
            param.setPurchaseContent(purchaseContent.substring(0, purchaseContent.length() - 1));
            param.setGoodsTypes(String.join(",", goodsTypeSet));
            if (!Integer.valueOf(totalPrice.intValue()).equals(param.getTotalPrice())) {
                throw new BizException("操作失败，总金额计算不一致");
            }
            if (!Integer.valueOf(totalDiscountPrice.intValue()).equals(param.getDiscountTotalPrice())) {
                throw new BizException("操作失败，折后金额计算不一致");
            }
            param.setTotalPrice(totalPrice.intValue());
            param.setDiscountTotalPrice(totalDiscountPrice.intValue());
        }
        if (CommonUtils.isEmpty(param.getIncomingId())) {
            param.setIncomingId(BaseContextHandler.getUserId());
        }
        IncomingOrder incomingOrder = new IncomingOrder();
        BeanUtil.copyProperties(param, incomingOrder);
        this.saveOrUpdate(incomingOrder);
        List<IncomingOrderGoods> savedGoodsList = new ArrayList<>();
        if (CommonUtils.isNotEmpty(param.getGoodsList())) {
            List<IncomingOrderGoods> goodsList = BeanUtil.copyToList(param.getGoodsList(), IncomingOrderGoods.class);
            goodsList.forEach(g -> {
                g.setOrderId(incomingOrder.getId());
                g.setBatchNumber(numberService.generateNumber(NumberType.BATCH_NUMBER));
            });
            incomingOrderGoodsService.saveBatch(goodsList);
            savedGoodsList.addAll(goodsList);
        }

        // --- 开始处理拒收 ---
        List<TempRejectedInfo> tempRejectedInfos = new ArrayList<>();
        if (CommonUtils.isNotEmpty(savedGoodsList)) {
            List<SaveOrUpdateIncomingOrderGoodsParam> originalParams = param.getGoodsList();
            for (int i = 0; i < savedGoodsList.size(); i++) {
                IncomingOrderGoods entity = savedGoodsList.get(i);
                SaveOrUpdateIncomingOrderGoodsParam p = originalParams.get(i);

                double qualifiedAmount = entity.getQualifiedAmount() != null ? entity.getQualifiedAmount() : 0.0;
                double incomingAmount = entity.getIncomingAmount() != null ? entity.getIncomingAmount() : 0.0;
                double rejectionAmount = qualifiedAmount - incomingAmount;

                if (rejectionAmount > 0) {
                    tempRejectedInfos.add(new TempRejectedInfo(entity, p, rejectionAmount));
                }
            }
        }

        if (!tempRejectedInfos.isEmpty()) {
            createRejectionOrderFromSource(incomingOrder, acceptOrder, tempRejectedInfos);
        }
        // --- 拒收处理结束 ---

        IncomingOrderDetailVo detailVo = this.getIncomingOrderById(incomingOrder.getId());
        saveInventory(detailVo);
        //绑定验收单为已完成
        if (CommonUtils.isNotEmpty(param.getOrderId())) {
            acceptOrderService.completeAcceptOrder(param.getOrderId());
        }
        return detailVo;
    }

    private void createRejectionOrderFromSource(IncomingOrder incomingOrder, AcceptOrder acceptOrder, List<TempRejectedInfo> rejectedInfos) {
        RejectionCreateParam rejectionParam = new RejectionCreateParam();
        rejectionParam.setSourceId(incomingOrder.getId());
        rejectionParam.setSourceCode(incomingOrder.getCode());
        rejectionParam.setSourceType(com.dsj.inventory.bussiness.quality.enumeration.RejectionSourceTypeEnum.INCOMING);

        // 如果有关联的验收单，则从验收单的源头（收货单）获取供应商信息
        if (acceptOrder != null) {
            ReceiveOrder receiveOrder = receiveOrderService.getById(acceptOrder.getOrderId());
            if (receiveOrder != null) {
                rejectionParam.setSupplierId(receiveOrder.getOrganizationSupplierId());
                rejectionParam.setSupplierName(organizationSupplierService.getOrganizationSupplierNameById(receiveOrder.getOrganizationSupplierId()));
            }
        }
        rejectionParam.setRemark(incomingOrder.getRemark());

        List<RejectedGoodsItem> rejectedItems = rejectedInfos.stream().map(info -> {
            IncomingOrderGoods entity = info.getEntity();
            SaveOrUpdateIncomingOrderGoodsParam p = info.getParam();

            RejectedGoodsItem rejectedItem = new RejectedGoodsItem();
            rejectedItem.setSourceOrderGoodsId(entity.getId());
            rejectedItem.setGoodsId(entity.getOrganizationMedicineId());
            rejectedItem.setGoodsName(entity.getGoodsName());
            rejectedItem.setBatchNo(entity.getBatchNumber());
            if (entity.getPurchasePrice() != null) {
                rejectedItem.setPurchasePrice(new BigDecimal(entity.getPurchasePrice()));
            }
            rejectedItem.setRejectionQuantity(BigDecimal.valueOf(info.getRejectionAmount()));
            rejectedItem.setRejectionReason(p.getUnqualifiedReason());
            return rejectedItem;
        }).collect(Collectors.toList());

        rejectionParam.setRejectedItems(rejectedItems);

        log.info("入库单[{}]创建关联拒收单...", incomingOrder.getCode());
        rejectionOrderService.createFromSource(rejectionParam);
    }

    @Override
    public void deleteIncomingOrder(Long id) {
        IncomingOrder byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        if (!byId.getDealState().equals(IncomingOrderStateEnum.TEMPORARY.getCode())) {
            throw new BizException("操作失败，入库单当前状态不允许删除");
        }
        //删除商品明细
        incomingOrderGoodsService.markAsDeletedByOrderId(id);
        this.baseMapper.deleteById(id);
    }

    /**
     * 商品入库
     *
     * @param data
     */
    private void saveInventory(IncomingOrderDetailVo data) {
        LocalDateTime now = LocalDateTime.now();
        List<IncomingOrderGoodsVo> goodsList = data.getGoodsList();
        //是否发送积货预警消息
        boolean exceedFlag = false;
        for (IncomingOrderGoodsVo goods : goodsList) {
            OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(goods.getOrganizationMedicineId());
            if (CommonUtils.isEmpty(organizationMedicine)) {
                throw new BizException("操作失败，" + goods.getGoodsName() + "不存在");
            }
            //商品入库
            Inventory inventory = Inventory.builder().organizationId(data.getOrganizationId()).organizationMedicineId(goods.getOrganizationMedicineId()).produceDate(goods.getProduceDate()).userDate(goods.getUserDate()).inventoryAmount(goods.getIncomingAmount()).costPrice(goods.getCostPrice()).batchNumber(goods.getBatchNumber()).shelfId(goods.getShelfId()).batch(data.getCode()).incomingGoodsId(goods.getId()).incomingDate(now).build();
            inventoryService.save(inventory);
            //库存变动记录
            InventoryChangeLog inventoryChangeLog = InventoryChangeLog.builder().organizationId(data.getOrganizationId()).organizationMedicineId(goods.getOrganizationMedicineId()).inventoryId(inventory.getId()).orderId(data.getId()).orderType(InventoryOrderTypeEnum.INCOMING.getCode()).changeType(InventoryChangeTypeEnum.INCREASE.getCode()).changeAmount(goods.getIncomingAmount()).batchNumber(goods.getBatchNumber()).shelfId(goods.getShelfId()).batch(data.getCode()).costPrice(goods.getCostPrice()).executeId(BaseContextHandler.getUserId()).build();
            inventoryChangeLogService.save(inventoryChangeLog);
            //商品表库存更新，成本均价更新
            BigDecimal medicineRepertory = BigDecimal.valueOf(organizationMedicine.getMedicineRepertory());
            medicineRepertory = medicineRepertory.add(BigDecimal.valueOf(goods.getIncomingAmount()));
            List<IncomingOrderGoodsVo> incomingGoodsList = incomingOrderGoodsService.countIncomingOrderGoods(goods.getOrganizationMedicineId());
            BigDecimal goodsTotalAmount = BigDecimal.valueOf(0.00);
            BigDecimal costTotalPrice = BigDecimal.valueOf(0);
            for (IncomingOrderGoodsVo incomingGoods : incomingGoodsList) {
                BigDecimal goodsAmount = BigDecimal.valueOf(incomingGoods.getIncomingAmount()).subtract(BigDecimal.valueOf(incomingGoods.getReturnedAmount()));
                BigDecimal goodsPrice = goodsAmount.multiply(BigDecimal.valueOf(incomingGoods.getCostPrice()));
                costTotalPrice = costTotalPrice.add(goodsPrice);
                goodsTotalAmount = goodsTotalAmount.add(goodsAmount);
            }
            BigDecimal averageCost = costTotalPrice.divide(goodsTotalAmount, 0, BigDecimal.ROUND_HALF_UP);

            // 更新商品库存信息
            boolean needUpdateInventoryShelve = TrueEnum.FALSE.getCode().equals(organizationMedicine.getInventoryShelve()) && medicineRepertory.doubleValue() > 0.00;
            organizationMedicineMappingExtService.updateMedicineInventoryInfo(organizationMedicine.getId(), medicineRepertory.doubleValue(), averageCost.intValue(), now, needUpdateInventoryShelve, organizationMedicine.getLastSaleTime() == null ? now : null);
            //处理库存预警
            if (CommonUtils.isNotEmpty(organizationMedicine.getRepertoryLowerLimit())) {
                //缺货
                WarningLog lowerWarningLog = warningLogService.findWarningLogByTypeAndMedicine(WarningTypeEnum.LACK.getCode(), organizationMedicine.getOrganizationId(), organizationMedicine.getId());

                //库存下限
                Integer lowerLimit = organizationMedicine.getRepertoryLowerLimit();
                if (medicineRepertory.doubleValue() > lowerLimit && CommonUtils.isNotEmpty(lowerWarningLog)) {
                    //删除预警记录
                    warningLogService.markWarningLogAsDeleted(lowerWarningLog.getId());
                }
            }
            if (CommonUtils.isNotEmpty(organizationMedicine.getRepertoryUpperLimit())) {
                //积货
                WarningLog upperWarningLog = warningLogService.findWarningLogByTypeAndMedicine(WarningTypeEnum.EXCEED.getCode(), organizationMedicine.getOrganizationId(), organizationMedicine.getId());

                //库存上限
                Integer upperLimit = organizationMedicine.getRepertoryUpperLimit();
                if (medicineRepertory.doubleValue() > upperLimit && CommonUtils.isEmpty(upperWarningLog)) {
                    //新增积货预警
                    WarningLog warningLog = WarningLog.builder().organizationId(organizationMedicine.getOrganizationId()).organizationMedicineId(organizationMedicine.getId()).type(WarningTypeEnum.EXCEED.getCode()).build();
                    warningLogService.save(warningLog);
                    exceedFlag = true;
                }
            }
        }
        //发送预警消息
        if (exceedFlag) {
            log.info("============向商家发送积货预警消息============");
            List<UserROrganization> uroList = userROrganizationService.lambdaQuery().eq(UserROrganization::getOrganizationId, data.getOrganizationId()).list();
            if (CommonUtils.isNotEmpty(uroList)) {
                List<String> userIdList = uroList.stream().map(s -> s.getUserId().toString()).collect(Collectors.toList());
                tencentIMUtils.adminSendBatchSingleMsg(userIdList, BaseConstant.IM_DATE_INVENTORY_WARNING, BaseConstant.IM_DESC_INVENTORY_EXCEED, "");
            }
        }
    }

    /**
     * 生成采购计划、采购订单、收货单、验收单
     *
     * @param data
     */
    private void createInventoryProcess(IncomingOrderDetailVo data) {
        BigDecimal purchaseTotalPrice = BigDecimal.valueOf(0);
        BigDecimal receiveTotalPrice = BigDecimal.valueOf(0);
        BigDecimal receiveDiscountTotalPrice = BigDecimal.valueOf(0);
        BigDecimal totalRetailPrice = BigDecimal.valueOf(0);
        BigDecimal acceptTotalPrice = BigDecimal.valueOf(0);
        BigDecimal acceptDiscountTotalPrice = BigDecimal.valueOf(0);
        List<PurchasePlanGoods> planGoodsList = new ArrayList<>();
        List<ReceiveOrderGoods> receiveGoodsList = new ArrayList<>();
        List<AcceptOrderGoods> acceptGoodsList = new ArrayList<>();
        for (IncomingOrderGoodsVo goods : data.getGoodsList()) {
            //验收单商品明细
            AcceptOrderGoods acceptGoods = BeanUtil.toBean(goods, AcceptOrderGoods.class);
            acceptGoods.setId(null);
            BigDecimal amount = BigDecimal.valueOf(goods.getQualifiedAmount()).subtract(BigDecimal.valueOf(goods.getGiveAmount()));
            BigDecimal acceptGoodsTotalPrice = BigDecimal.valueOf(goods.getPurchasePrice()).multiply(amount);
            acceptGoodsTotalPrice = acceptGoodsTotalPrice.setScale(0, BigDecimal.ROUND_HALF_UP);
            acceptGoods.setTotalPurchasePrice(acceptGoodsTotalPrice.intValue());
            acceptTotalPrice = acceptTotalPrice.add(acceptGoodsTotalPrice);
            BigDecimal acceptGoodsDiscountTotalPrice = BigDecimal.valueOf(goods.getDiscountPrice()).multiply(amount);
            acceptGoodsDiscountTotalPrice = acceptGoodsDiscountTotalPrice.setScale(0, BigDecimal.ROUND_HALF_UP);
            acceptGoods.setTotalDiscountPrice(acceptGoodsDiscountTotalPrice.intValue());
            acceptDiscountTotalPrice = acceptDiscountTotalPrice.add(acceptGoodsDiscountTotalPrice);
            acceptGoodsList.add(acceptGoods);
        }
        //同一商品合并
        List<IncomingOrderGoodsVo> goodsDistinctList = data.getGoodsList().stream().map(item -> BeanUtil.toBean(item, IncomingOrderGoodsVo.class)).collect(Collectors.toList());
        goodsDistinctList = goodsDistinctList.stream().collect(Collectors.groupingBy(g -> g.getOrganizationMedicineId())).values().stream().map(d -> {
            IncomingOrderGoodsVo goods = d.get(0);
            goods.setOrganizationMedicineId(d.get(0).getOrganizationMedicineId());
            goods.setGiveAmount(d.stream().mapToDouble(item -> item.getGiveAmount() != null ? item.getGiveAmount() : 0.0).sum());
            goods.setQualifiedAmount(d.stream().mapToDouble(item -> item.getQualifiedAmount() != null ? item.getQualifiedAmount() : 0.0).sum());
            goods.setIncomingAmount(d.stream().mapToDouble(item -> item.getIncomingAmount() != null ? item.getIncomingAmount() : 0.0).sum());
            return goods;
        }).collect(Collectors.toList());
        for (IncomingOrderGoodsVo goods : goodsDistinctList) {
            // 安全处理可能为null的字段
            Integer purchasePrice = goods.getPurchasePrice() != null ? goods.getPurchasePrice() : 0;
            Double purchaseNumber = goods.getPurchaseNumber() != null ? goods.getPurchaseNumber() : 0.0;
            Integer price = goods.getPrice() != null ? goods.getPrice() : 0;
            Double receiveAmount = goods.getReceiveAmount() != null ? goods.getReceiveAmount() : 0.0;
            Double giveAmount = goods.getGiveAmount() != null ? goods.getGiveAmount() : 0.0;
            Integer discountPrice = goods.getDiscountPrice() != null ? goods.getDiscountPrice() : 0;

            // 使用安全处理后的值
            BigDecimal goodsTotalPrice = BigDecimal.valueOf(purchasePrice).multiply(BigDecimal.valueOf(purchaseNumber));
            goodsTotalPrice = goodsTotalPrice.setScale(0, BigDecimal.ROUND_HALF_UP);
            purchaseTotalPrice = purchaseTotalPrice.add(goodsTotalPrice);
            //采购计划商品明细
            PurchasePlanGoods planGoods = BeanUtil.toBean(goods, PurchasePlanGoods.class);
            planGoods.setId(null);
            planGoods.setTotalPurchasePrice(goodsTotalPrice.intValue());
            planGoodsList.add(planGoods);
            //收货单商品明细
            BigDecimal retailPrice = BigDecimal.valueOf(price).multiply(BigDecimal.valueOf(receiveAmount));
            totalRetailPrice = totalRetailPrice.add(retailPrice);
            ReceiveOrderGoods receiveGoods = BeanUtil.toBean(goods, ReceiveOrderGoods.class);
            receiveGoods.setId(null);
            receiveGoods.setArrivalAmount(receiveAmount);
            receiveGoods.setRefuseAmount(0.00);
            receiveGoods.setGiveAmount(giveAmount);
            BigDecimal amount = BigDecimal.valueOf(receiveAmount).subtract(BigDecimal.valueOf(giveAmount));
            BigDecimal receiveGoodsTotalPrice = BigDecimal.valueOf(purchasePrice).multiply(amount);
            receiveGoodsTotalPrice = receiveGoodsTotalPrice.setScale(0, BigDecimal.ROUND_HALF_UP);
            receiveGoods.setTotalPurchasePrice(receiveGoodsTotalPrice.intValue());
            receiveTotalPrice = receiveTotalPrice.add(receiveGoodsTotalPrice);
            BigDecimal receiveGoodsDiscountTotalPrice = BigDecimal.valueOf(discountPrice).multiply(amount);
            receiveGoodsDiscountTotalPrice = receiveGoodsDiscountTotalPrice.setScale(0, BigDecimal.ROUND_HALF_UP);
            receiveGoods.setTotalDiscountPrice(receiveGoodsDiscountTotalPrice.intValue());
            receiveDiscountTotalPrice = receiveDiscountTotalPrice.add(receiveGoodsDiscountTotalPrice);
            receiveGoodsList.add(receiveGoods);
        }
        Organization organization = organizationService.getById(data.getOrganizationId());
        String day = DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_FORMAT);
        String dayStr = day.replaceAll("-", "").substring(2, 8);
        //生成采购计划
        PurchasePlan purchasePlan = BeanUtil.toBean(data, PurchasePlan.class);
        purchasePlan.setId(null);
        purchasePlan.setDealState(PurchasePlanStateEnum.EXECUTED.getCode());
        purchasePlan.setTotalPrice(purchaseTotalPrice.intValue());
        purchasePlan.setExpectReceiveDate(data.getArrivalDate());
        purchasePlan.setCreateTime(null);
        purchasePlan.setCreateUser(null);
        //生成采购计划单号：JH+年后两位+月日四位+药房识别码六位+三位
        if (CommonUtils.isEmpty(organization)) {
            throw new BizException("操作失败，组织不存在");
        }
        Integer count = purchasePlanService.getPurchasePlanCount(organization.getId(), day);
        count = count + 1;
        purchasePlan.setCode("JH" + dayStr + organization.getSerialNo() + String.format("%03d", count));
        purchasePlanService.save(purchasePlan);
        planGoodsList.stream().map(g -> g.setPlanId(purchasePlan.getId())).collect(Collectors.toList());
        purchasePlanGoodsService.saveBatch(planGoodsList);
        //生成采购订单
        PurchaseOrder purchaseOrder = BeanUtil.toBean(purchasePlan, PurchaseOrder.class);
        purchaseOrder.setId(null);
        purchaseOrder.setPlanId(purchasePlan.getId());
        purchaseOrder.setOrderCode(purchasePlan.getCode());
        purchaseOrder.setPurchaserId(data.getPurchaserId());
        purchaseOrder.setDealState(PurchaseOrderStateEnum.COMPLETED.getCode());
        purchaseOrder.setCreateTime(null);
        purchaseOrder.setCreateUser(null);
        //生成采购订单单号：CG+年后两位+月日四位+药房识别码六位+三位
        count = purchaseOrderService.getPurchaseOrderCount(organization.getId(), day);
        count = count + 1;
        purchaseOrder.setCode("CG" + dayStr + organization.getSerialNo() + String.format("%03d", count));
        purchaseOrderService.save(purchaseOrder);
        List<PurchaseOrderGoods> orderGoodsList = planGoodsList.stream().map(item -> BeanUtil.toBean(item, PurchaseOrderGoods.class)).collect(Collectors.toList());
        orderGoodsList.stream().forEach(g -> {
            g.setOrderId(purchaseOrder.getId());
            g.setId(null);
        });
        purchaseOrderGoodsService.saveBatch(orderGoodsList);
        //生成收货单
        ReceiveOrder receiveOrder = BeanUtil.toBean(data, ReceiveOrder.class);
        receiveOrder.setId(null);
        receiveOrder.setOrderId(purchaseOrder.getId());
        receiveOrder.setOrderCode(purchaseOrder.getCode());
        receiveOrder.setDealState(ReceiveOrderStateEnum.COMPLETED.getCode());
        receiveOrder.setReceiverId(data.getAcceptId());
        receiveOrder.setTotalRetailPrice(totalRetailPrice.intValue());
        receiveOrder.setTotalPrice(receiveTotalPrice.intValue());
        receiveOrder.setDiscountTotalPrice(receiveDiscountTotalPrice.intValue());
        receiveOrder.setCreateTime(null);
        receiveOrder.setCreateUser(null);
        //生成收货单号：FH+年后两位+月日四位+药房识别码六位+三位
        count = receiveOrderService.getReceiveOrderCount(organization.getId(), day);
        count = count + 1;
        receiveOrder.setCode("FH" + dayStr + organization.getSerialNo() + String.format("%03d", count));
        receiveOrderService.save(receiveOrder);
        receiveGoodsList.stream().forEach(g -> {
            g.setOrderId(receiveOrder.getId());
            g.setId(null);
        });
        receiveOrderGoodsService.saveBatch(receiveGoodsList);
        //生成验收单
        AcceptOrder acceptOrder = BeanUtil.toBean(data, AcceptOrder.class);
        acceptOrder.setId(null);
        acceptOrder.setOrderId(receiveOrder.getId());
        acceptOrder.setOrderCode(receiveOrder.getCode());
        acceptOrder.setDealState(AcceptOrderStateEnum.COMPLETED.getCode());
        acceptOrder.setTotalPrice(acceptTotalPrice.intValue());
        acceptOrder.setDiscountTotalPrice(acceptDiscountTotalPrice.intValue());
        acceptOrder.setCreateTime(null);
        acceptOrder.setCreateUser(null);
        //生成验收单号：YS+年后两位+月日四位+药房识别码六位+三位
        count = acceptOrderService.getAcceptOrderCount(organization.getId(), day);
        count = count + 1;
        acceptOrder.setCode("YS" + dayStr + organization.getSerialNo() + String.format("%03d", count));
        acceptOrderService.saveOrUpdate(acceptOrder);
        acceptGoodsList.stream().map(g -> g.setOrderId(acceptOrder.getId())).collect(Collectors.toList());
        acceptOrderGoodsService.saveBatch(acceptGoodsList);
        //绑定验收单到入库单
        this.baseMapper.updateOrderIdAndCode(data.getId(), acceptOrder.getId(), acceptOrder.getCode());
    }

    @Override
    public Page<IncomingOrderVo> queryIncomingOrderPage(QueryIncomingOrderParam param) {
        if (CommonUtils.isEmpty(param)) {
            throw new ParamException("参数异常");
        }
        if (!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE))) && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))) {
            throw new BizException("组织无权限");
        } else if (OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))) {
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        Page page = new Page<>(param.getPage(), param.getSize());
        List<IncomingOrderVo> list = this.baseMapper.queryIncomingOrderPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public IncomingOrderDetailVo getIncomingOrderById(Long id) {
        IncomingOrderDetailVo vo = this.baseMapper.getIncomingOrderById(id);
        if (CommonUtils.isNotEmpty(vo)) {
            List<IncomingOrderGoodsVo> goodsList = incomingOrderGoodsService.getIncomingOrderGoodsList(vo.getId());
            if (CommonUtils.isNotEmpty(goodsList)) {
                vo.setGoodsList(goodsList);
            }
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitIncomingOrder(Long id) {
        IncomingOrderDetailVo incomingOrderDetailVo = this.getIncomingOrderById(id);
        if (CommonUtils.isEmpty(incomingOrderDetailVo)) {
            throw new BizException("操作失败，入库单不存在");
        }
        Organization organization = organizationService.getById(incomingOrderDetailVo.getOrganizationId());
        if (CommonUtils.isEmpty(organization)) {
            throw new BizException("组织不存在");
        }
        if (CommonUtils.isEmpty(organization.getInventoryDate())) {
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        this.baseMapper.updateStateToCompleted(id);
        //商品入库、库存变动记录、商品表库存更新，成本均价更新
        this.saveInventory(incomingOrderDetailVo);
        if (CommonUtils.isEmpty(incomingOrderDetailVo.getOrderId())) {
            //生成采购计划、采购订单、收货单、验收单
            this.createInventoryProcess(incomingOrderDetailVo);
        }
    }

    @Override
    public List<IncomingOrderExportVo> queryIncomingOrderList(QueryIncomingOrderParam param) {
        if (!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE))) && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))) {
            throw new BizException("组织无权限");
        } else if (OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))) {
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        return this.baseMapper.queryIncomingOrderList(param);
    }

    @Override
    public void deleteIncomingOrderByOrganizationId(Long organizationId) {
        this.baseMapper.deleteIncomingOrderByOrganizationId(organizationId);
    }

    @Override
    public Integer getIncomingOrderCount(Long organizationId, String day) {
        return this.baseMapper.getIncomingOrderCount(organizationId, day);
    }

    @Getter
    @AllArgsConstructor
    private static class TempRejectedInfo {
        private final IncomingOrderGoods entity;
        private final SaveOrUpdateIncomingOrderGoodsParam param;
        private final double rejectionAmount;
    }
}
