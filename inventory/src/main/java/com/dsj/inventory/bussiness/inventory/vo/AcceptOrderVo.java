package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 验收单
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AcceptOrderVo", description="验收单实体")
@ToString(callSuper = true)
public class AcceptOrderVo {

    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "收货单Id")
    private Long orderId;

    @ApiModelProperty(value = "收货单号")
    private String orderCode;

    @ApiModelProperty(value = "验收单号")
    private String code;

    @ApiModelProperty(value = "开票日期")
    private LocalDateTime billDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "处理状态 0暂存 1已验收 2已完成")
    private Integer dealState;

    @ApiModelProperty(value = "验收员Id")
    private Long acceptId;

    @ApiModelProperty(value = "验收员名称")
    private String acceptName;

    @ApiModelProperty(value = "私域供应商Id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "供应商名称")
    private String organizationSupplierName;

    @ApiModelProperty(value = "供应商编号")
    private String organizationSupplierCode;

    @ApiModelProperty(value = "总金额")
    private Integer totalPrice;

    @ApiModelProperty(value = "商品种类")
    private String goodsTypes;

    @ApiModelProperty(value = "采购内容")
    private String purchaseContent;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
