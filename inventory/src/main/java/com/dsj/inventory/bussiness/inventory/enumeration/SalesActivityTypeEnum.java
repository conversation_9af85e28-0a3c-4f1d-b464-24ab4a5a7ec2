package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 活动类型 1逢倍折扣 2满件减 3单品特价 4满元减元
 * <AUTHOR>
 */

@Getter
public enum SalesActivityTypeEnum {

    ACTIVITY_1(1, "每逢倍折"),
    ACTIVITY_2(2, "满件减元"),
    ACTIVITY_3(3, "单品特价"),
    ACTIVITY_4(4, "满元减元"),
    ACTIVITY_5(5, "满件赠件"),
    ;

    private Integer code;

    private String desc;

    SalesActivityTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (SalesActivityTypeEnum a : SalesActivityTypeEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
