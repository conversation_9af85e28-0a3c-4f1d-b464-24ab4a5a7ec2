package com.dsj.inventory.common.utils.ca;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.Security;
import java.security.spec.InvalidKeySpecException;
import java.util.Base64;

/**
 * @Title: 加密算法/分组加密模式/分组填充方式
 * @ClassName: com.lnyt.sm4.controller.Sm4Helper.java
 * @Description:以8个字节为一组进行分组加密
 * 分组填充方式使用：分组填充方式有多种，包含PKCS5Padding、PKCS7Padding、ZeroBytePadding等等
 *
 * @Copyright 2016-2021 公司名称 - Powered By 研发中心
 * @author: 陈延龙
 * @date:  2022-08-29 9:39
 * @version V1.0
 */
public class Sm4Helper {
   // private static final Logger logger = LogManager.getLogger(Sm4Helper.class);
//    public static final String ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding";
//    public static final String ALGORITHM_NAME_ECB_NOPADDING = "SM4/ECB/NoPadding";
//    public static final String ALGORITHM_NAME_CBC_PADDING = "SM4/CBC/PKCS5Padding";
//    public static final String ALGORITHM_NAME_CBC_NOPADDING = "SM4/CBC/NoPadding";

    public static final String ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding";
    public static final String ALGORITHM_NAME_ECB_NOPADDING = "SM4/ECB/NoPadding";
    public static final String ALGORITHM_NAME_CBC_PADDING = "SM4/CBC/PKCS5Padding";
    public static final String ALGORITHM_NAME_CBC_NOPADDING = "SM4/CBC/NoPadding";

    public static final String ECB = "ECB";
    public static final String CBC = "CBC";


    private static final String FILE_MODE_READ = "r";

    private static final String FILE_MODE_READ_WRITE = "rw";

    private static final String PBK_SHA1 = "PBKDF2WithHmacSHA1";

    private static final String ALGORITHM_SM4 = "SM4";

    private static final int KEY_DEFAULT_SIZE = 128;

    private static final int blockSize = 1024*10;

    private static final int ENCRYPT_BUFFER_SIZE = 1024;

    private static final int DECRYPT_BUFFER_SIZE = 1040;


    static{
        try{
            Security.addProvider(new BouncyCastleProvider());
        }catch(Exception e){
            e.printStackTrace();
        }
    }




    /**
     * 大字符串分段加密
     * @param content 需要加密的内容
     * @param seed 种子
     * @return 加密后内容
     * @throws Exception 异常
     */
    public static String sm4EncryptLarge(String content, String seed,String seedLv,String algorithmName) throws Exception {
        if(algorithmName==null||algorithmName==""){
            throw new RuntimeException("填充模式不能为空");
        }

        if(!algorithmName.equals(ALGORITHM_NAME_ECB_PADDING)&&!algorithmName.equals(ALGORITHM_NAME_ECB_NOPADDING)&&!algorithmName.equals(ALGORITHM_NAME_CBC_PADDING)&&!algorithmName.equals(ALGORITHM_NAME_CBC_NOPADDING)){
            throw new RuntimeException("填充模式非法");
        }
        byte[] data = content.getBytes(StandardCharsets.UTF_8);
        byte[] rawKey = getRawKey(seed);
        byte[] rawKeyLv = null;
        Cipher mCipher =null;
        if(algorithmName.equals(ALGORITHM_NAME_ECB_PADDING)||algorithmName.equals(ALGORITHM_NAME_ECB_NOPADDING)){
            mCipher = generateEcbCipher(Cipher.ENCRYPT_MODE, rawKey,algorithmName);
        }else if(algorithmName.equals(ALGORITHM_NAME_CBC_PADDING)||algorithmName.equals(ALGORITHM_NAME_CBC_NOPADDING)){
            if(seedLv==null||seedLv==""){
                throw new RuntimeException("seedLv在【"+algorithmName+"】填充模式下，不可为空");
            }
            rawKeyLv = getRawKey(seedLv);
            mCipher = generateCbcCipher(Cipher.ENCRYPT_MODE, rawKey,rawKeyLv,algorithmName);
        }else{
            throw new RuntimeException("填充模式非法");
        }
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > ENCRYPT_BUFFER_SIZE) {
                cache = mCipher.doFinal(data, offSet, ENCRYPT_BUFFER_SIZE);
            } else {
                cache = mCipher.doFinal(data, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * ENCRYPT_BUFFER_SIZE;
        }
        byte[] decryptedData = out.toByteArray();
        out.close();
        return Base64.getEncoder().encodeToString(decryptedData);
    }

    /**
     * 大字符串分段解密
     * @param content 密文
     * @param seed 种子
     * @return 解密后内容
     * @throws Exception 异常
     */
    public static String sm4DecryptLarge(String content, String seed,String seedLv,String algorithmName) throws Exception {
        if(algorithmName==null||algorithmName==""){
            throw new RuntimeException("填充模式不能为空");
        }

        if(!algorithmName.equals(ALGORITHM_NAME_ECB_PADDING)&&!algorithmName.equals(ALGORITHM_NAME_ECB_NOPADDING)&&!algorithmName.equals(ALGORITHM_NAME_CBC_PADDING)&&!algorithmName.equals(ALGORITHM_NAME_CBC_NOPADDING)){
            throw new RuntimeException("填充模式非法");
        }
        byte[] data = Base64.getDecoder().decode(content);
        byte[] rawKey = getRawKey(seed);
        byte[] rawKeyLv = null;
        Cipher mCipher =null;
        if(algorithmName.equals(ALGORITHM_NAME_ECB_PADDING)||algorithmName.equals(ALGORITHM_NAME_ECB_NOPADDING)){
            mCipher = generateEcbCipher(Cipher.DECRYPT_MODE, rawKey,algorithmName);
        }else if(algorithmName.equals(ALGORITHM_NAME_CBC_PADDING)||algorithmName.equals(ALGORITHM_NAME_CBC_NOPADDING)){
            if(seedLv==null||seedLv==""){
                throw new RuntimeException("seedLv在【"+algorithmName+"】填充模式下，不可为空");
            }
            rawKeyLv = getRawKey(seedLv);
            mCipher = generateCbcCipher(Cipher.DECRYPT_MODE, rawKey,rawKeyLv,algorithmName);
        }else{
            throw new RuntimeException("填充模式非法");
        }
        int inputLen = data.length;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] cache;
        int i = 0;
        // 对数据分段解密
        while (inputLen - offSet > 0) {
            if (inputLen - offSet > DECRYPT_BUFFER_SIZE) {
                cache = mCipher.doFinal(data, offSet, DECRYPT_BUFFER_SIZE);
            } else {
                cache = mCipher.doFinal(data, offSet, inputLen - offSet);
            }
            out.write(cache, 0, cache.length);
            i++;
            offSet = i * DECRYPT_BUFFER_SIZE;
        }
        byte[] decryptedData = out.toByteArray();
        out.close();
        return new String(decryptedData, StandardCharsets.UTF_8);
    }


    /**
     * 字符串加密
     * @param data 字符串
     * @param seed 种子
     * @return 加密后内容
     * @throws Exception 异常
     */
    public static String sm4Encrypt(String data, String seed,String seedLv,String algorithmName) throws Exception {
        if(algorithmName==null||algorithmName==""){
            throw new RuntimeException("填充模式不能为空");
        }

        if(!algorithmName.equals(ALGORITHM_NAME_ECB_PADDING)&&!algorithmName.equals(ALGORITHM_NAME_ECB_NOPADDING)&&!algorithmName.equals(ALGORITHM_NAME_CBC_PADDING)&&!algorithmName.equals(ALGORITHM_NAME_CBC_NOPADDING)){
            throw new RuntimeException("填充模式非法");
        }
        byte[] rawKey = getRawKey(seed);
        byte[] rawKeyLv = null;
        Cipher mCipher =null;
        if(algorithmName.equals(ALGORITHM_NAME_ECB_PADDING)||algorithmName.equals(ALGORITHM_NAME_ECB_NOPADDING)){
            mCipher = generateEcbCipher(Cipher.ENCRYPT_MODE, rawKey,algorithmName);
        }else if(algorithmName.equals(ALGORITHM_NAME_CBC_PADDING)||algorithmName.equals(ALGORITHM_NAME_CBC_NOPADDING)){
            if(seedLv==null||seedLv==""){
                throw new RuntimeException("seedLv在【"+algorithmName+"】填充模式下，不可为空");
            }
            rawKeyLv = getRawKey(seedLv);
            mCipher = generateCbcCipher(Cipher.ENCRYPT_MODE, rawKey,rawKeyLv,algorithmName);
        }else{
            throw new RuntimeException("填充模式非法");
        }
        byte[] bytes = mCipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 字符串解密
     * @param data 加密字符串
     * @param seed 种子
     * @return 解密后内容
     * @throws Exception 异常
     */
    public static String sm4Decrypt(String data, String seed,String seedLv,String algorithmName) throws Exception {
        if(algorithmName==null||algorithmName==""){
            throw new RuntimeException("填充模式不能为空");
        }

        if(!algorithmName.equals(ALGORITHM_NAME_ECB_PADDING)&&!algorithmName.equals(ALGORITHM_NAME_ECB_NOPADDING)&&!algorithmName.equals(ALGORITHM_NAME_CBC_PADDING)&&!algorithmName.equals(ALGORITHM_NAME_CBC_NOPADDING)){
            throw new RuntimeException("填充模式非法");
        }
        byte[] rawKey = getRawKey(seed);
        byte[] rawKeyLv = null;
        Cipher mCipher =null;
        if(algorithmName.equals(ALGORITHM_NAME_ECB_PADDING)||algorithmName.equals(ALGORITHM_NAME_ECB_NOPADDING)){
            mCipher = generateEcbCipher(Cipher.DECRYPT_MODE, rawKey,algorithmName);
        }else if(algorithmName.equals(ALGORITHM_NAME_CBC_PADDING)||algorithmName.equals(ALGORITHM_NAME_CBC_NOPADDING)){
            if(seedLv==null||seedLv==""){
                throw new RuntimeException("seedLv在【"+algorithmName+"】填充模式下，不可为空");
            }
            rawKeyLv = getRawKey(seedLv);
            mCipher = generateCbcCipher(Cipher.DECRYPT_MODE, rawKey,rawKeyLv,algorithmName);
        }else{
            throw new RuntimeException("填充模式非法");
        }
        byte[] bytes = mCipher.doFinal(Base64.getDecoder().decode(data));
        return new String(bytes, StandardCharsets.UTF_8);
    }








    /**
     * 使用一个安全的随机数来产生一个密匙,密匙加密使用的
     * @param seed 种子
     * @return 随机数组
     * @throws NoSuchAlgorithmException 模式错误
     */
    public static byte[] getRawKey(String seed) throws NoSuchAlgorithmException, InvalidKeySpecException {

//        int count = 1000;
//        int keyLen = KEY_DEFAULT_SIZE;
//        int saltLen = keyLen / 8;
//        SecureRandom random = new SecureRandom();
//        byte[] salt = new byte[saltLen];
//        random.setSeed(seed.getBytes());
//        random.nextBytes(salt);
//        KeySpec keySpec = new PBEKeySpec(seed.toCharArray(), salt, count, keyLen);
//        SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance(PBK_SHA1);
//        return secretKeyFactory.generateSecret(keySpec).getEncoded();
        String seeds = MD5Encode(seed, "UTF_8").substring(8, 24);
        return seeds.getBytes(StandardCharsets.UTF_8);
    }

    private static String byteArrayToHexString(byte b[]) {
        StringBuffer resultSb = new StringBuffer();
        for (int i = 0; i < b.length; i++)
            resultSb.append(byteToHexString(b[i]));

        return resultSb.toString();
    }

    private static String byteToHexString(byte b) {
        int n = b;
        if (n < 0)
            n += 256;
        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigits[d1] + hexDigits[d2];
    }

    public static String MD5Encode(String origin, String charsetname) {
        String resultString = null;
        try {
            resultString = new String(origin);
            MessageDigest md = MessageDigest.getInstance("MD5");
            if (charsetname == null || "".equals(charsetname))
                resultString = byteArrayToHexString(md.digest(resultString
                        .getBytes()));
            else
                resultString = byteArrayToHexString(md.digest(resultString
                        .getBytes(charsetname)));
        } catch (Exception exception) {
        }
        return resultString;
    }

    public static String getMD5Code(String origin) {
        String resultString = null;
        try {
            resultString = new String(origin);
            MessageDigest md = MessageDigest.getInstance("MD5");
            resultString = byteArrayToHexString(md.digest(resultString
                    .getBytes("utf-8")));
        } catch (Exception exception) {
        }
        return resultString;
    }
    private static final String hexDigits[] = { "0", "1", "2", "3", "4", "5",
            "6", "7", "8", "9", "a", "b", "c", "d", "e", "f" };

    /**
     * 生成ecb模式密码工具
     * @param mode 模式
     * @param key 密钥
     * @return 密码工具
     * @throws Exception 异常
     */
    private static Cipher generateEcbCipher(int mode, byte[] key,String algorithmName) throws Exception{
        Cipher cipher = Cipher.getInstance(algorithmName, BouncyCastleProvider.PROVIDER_NAME);
        Key sm4Key = new SecretKeySpec(key, ALGORITHM_SM4);
        cipher.init(mode, sm4Key);
        return cipher;
    }

    /**
     * 生成Cbc模式密码工具
     * @param mode 模式
     * @param key 密钥
     * @return 密码工具
     * @throws Exception 异常
     */
    private static Cipher generateCbcCipher(int mode, byte[] key, byte[] iv,String algorithmName) throws Exception{
        Cipher cipher = Cipher.getInstance(algorithmName, BouncyCastleProvider.PROVIDER_NAME);
        Key sm4Key = new SecretKeySpec(key, ALGORITHM_SM4);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
        cipher.init(mode, sm4Key, ivParameterSpec);
        return cipher;
    }










}
