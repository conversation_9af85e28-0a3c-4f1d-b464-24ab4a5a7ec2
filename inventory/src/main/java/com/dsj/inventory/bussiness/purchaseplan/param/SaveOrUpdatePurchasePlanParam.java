package com.dsj.inventory.bussiness.purchaseplan.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购计划
 * <AUTHOR>
 * @date 2023/04/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdatePurchasePlanParam", description="新增or编辑 采购计划参数")
public class SaveOrUpdatePurchasePlanParam {

    @ApiModelProperty(hidden = true, value = "id,更新是传入")
    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(hidden = true,value = "计划单号")
    private String code;

    @ApiModelProperty(value = "开票日期")
    private LocalDateTime billDate;

    @ApiModelProperty(value = "预计收货日期")
    private LocalDateTime expectReceiveDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "处理状态 0待执行 1已执行")
    private Integer dealState;

    @ApiModelProperty(hidden = true,value = "私域供应商Id")
    private Long organizationSupplierId;

    @ApiModelProperty(hidden = true,value = "总金额")
    private Integer totalPrice;

    @ApiModelProperty(hidden = true,value = "商品种类")
    private String goodsTypes;

    @ApiModelProperty(hidden = true,value = "采购内容")
    private String purchaseContent;

    @ApiModelProperty(value = "商品明细")
    private List<SaveOrUpdatePurchasePlanGoodsParam> goodsList;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
