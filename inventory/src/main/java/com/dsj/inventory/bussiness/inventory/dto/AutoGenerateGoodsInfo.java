package com.dsj.inventory.bussiness.inventory.dto;

import lombok.Data;

/**
 * 自动生成商品信息DTO
 * 用于自动生成采购计划时的商品信息传递
 */
@Data
public class AutoGenerateGoodsInfo {
    
    private Long organizationMedicineId;    // 私域商品ID
    private String goodsName;               // 商品名称
    private String goodsCode;               // 商品编号
    private String generalName;             // 通用名
    private String specification;           // 规格型号
    private String packUnit;                // 包装单位
    private String manufacturer;            // 生产厂家
    
    private Double currentStock;            // 当前库存
    private Double inTransitQuantity;       // 在途数量
    private Double dailyAvgSales;           // 日均销量
    private Double stockUpperLimit;         // 库存上限
    private Double stockLowerLimit;         // 库存下限
    private Double recommendQuantity;       // 推荐采购数量
    
    private Long recommendedSupplierId;     // 推荐供应商ID
    private String recommendedSupplierName; // 推荐供应商名称
    private Integer recommendedPrice;       // 推荐价格（分）
    private Integer priceStrategy;          // 价格策略
    
    private Boolean isLowStock;             // 是否低库存
    private Integer stockOutDays;           // 断货天数
    
    // 计算是否需要采购
    public Boolean needPurchase() {
        return recommendQuantity != null && recommendQuantity > 0;
    }
    
    // 计算可用库存（当前库存+在途数量）
    public Double getAvailableStock() {
        double current = currentStock != null ? currentStock : 0.0;
        double inTransit = inTransitQuantity != null ? inTransitQuantity : 0.0;
        return current + inTransit;
    }
}