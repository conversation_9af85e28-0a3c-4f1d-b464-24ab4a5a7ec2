package com.dsj.inventory.bussiness.quality.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.quality.entity.SupplierFirstBusinessEntity;
import com.dsj.inventory.bussiness.quality.enumeration.ApprovalTypeEnum;
import com.dsj.inventory.bussiness.quality.param.SupplierFirstBusinessApprovalParam;
import com.dsj.inventory.bussiness.quality.param.SupplierFirstBusinessQueryParam;
import com.dsj.inventory.bussiness.quality.service.SupplierFirstBusinessService;
import com.dsj.inventory.bussiness.quality.vo.SupplierFirstBusinessVO;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 供应商首营审批表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@RestController
@RequestMapping("/api/quality/supplier-first-business")
@Api(tags = "供应商首营审批")
@Validated
public class SupplierFirstBusinessController {

    @Autowired
    private SupplierFirstBusinessService supplierFirstBusinessService;

    @ApiOperation("获取供应商首营审批详情")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-supplier-first"})
    @GetMapping("/{id}")
    public ResponseEntity<SupplierFirstBusinessVO> getSupplierFirstBusinessById(@PathVariable Long id) {
        SupplierFirstBusinessVO vo = supplierFirstBusinessService.getSupplierFirstBusinessById(id);
        return Res.success(vo);
    }

    @ApiOperation("分页查询供应商首营审批")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-supplier-first"})
    @GetMapping("/page")
    public ResponseEntity<?> querySupplierFirstBusinessPage(SupplierFirstBusinessQueryParam param) {
        Page<SupplierFirstBusinessVO> page = supplierFirstBusinessService.querySupplierFirstBusinessPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())) {
            return Res.successPage(page);
        } else {
            return Res.success(page.getRecords());
        }
    }

    @ApiOperation("审批供应商（首营/修改）")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-supplier-first-approve"})
    @PostMapping("/approve")
    public ResponseEntity<Long> approveSupplierFirstBusiness(@Valid @RequestBody SupplierFirstBusinessApprovalParam param) {
        // 获取要审批的记录
        SupplierFirstBusinessEntity entity = supplierFirstBusinessService.getById(param.getId());
        if (entity == null) {
            return Res.fail("审批记录不存在");
        }

        Long id;
        // 根据审批类型调用不同的服务方法
        if (ApprovalTypeEnum.MODIFICATION.getCode().equals(entity.getApprovalType())) {
            id = supplierFirstBusinessService.approveSupplierModification(param);
        } else {
            // 默认或首营审批
            id = supplierFirstBusinessService.approveSupplierFirstBusiness(param);
        }
        return Res.success(id);
    }

    @ApiOperation("撤回供应商首营审批")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-supplier-first"})
    @PostMapping("/withdraw/{supplierId}")
    public ResponseEntity<Long> withdrawSupplierFirstBusiness(@PathVariable Long supplierId) {
        Long id = supplierFirstBusinessService.withdrawSupplierFirstBusiness(supplierId);
        return Res.success(id);
    }

    @ApiOperation("获取供应商首营审批最新状态")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-supplier-first"})
    @GetMapping("/latest/{supplierId}")
    public ResponseEntity<SupplierFirstBusinessVO> getLatestSupplierFirstBusiness(@PathVariable Long supplierId) {
        SupplierFirstBusinessVO vo = supplierFirstBusinessService.getLatestSupplierFirstBusiness(supplierId);
        return Res.success(vo);
    }

    @ApiOperation("获取供应商首营审批历史记录")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-supplier-first"})
    @GetMapping("/history/{supplierId}")
    public ResponseEntity<List<SupplierFirstBusinessVO>> getSupplierFirstBusinessHistory(@PathVariable Long supplierId) {
        List<SupplierFirstBusinessVO> list = supplierFirstBusinessService.getSupplierFirstBusinessHistory(supplierId);
        return Res.success(list);
    }

    @ApiOperation("查询待我审批的供应商首营记录")
    @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"quality-supplier-first"})
    @GetMapping("/pending")
    public ResponseEntity<?> queryPendingApprovalPage(SupplierFirstBusinessQueryParam param) {
        Page<SupplierFirstBusinessVO> page = supplierFirstBusinessService.queryPendingApprovalPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())) {
            return Res.successPage(page);
        } else {
            return Res.success(page.getRecords());
        }
    }
} 