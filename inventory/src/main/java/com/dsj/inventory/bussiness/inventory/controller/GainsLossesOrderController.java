package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryGainsLossesOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateGainsLossesOrderParam;
import com.dsj.inventory.bussiness.inventory.service.GainsLossesOrderService;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesOrderVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.EasyExcelUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 进销存损溢单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@RestController
@Api(value = "GainsLossesOrderController", tags = "进销存损溢单api")
public class GainsLossesOrderController {

    @Autowired
    private GainsLossesOrderService gainsLossesOrderService;

    /*新增损溢单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-gains-losses"})
    @PostMapping("/gains-losses-order")
    @ApiOperation(value = "新增损溢单", notes = "新增损溢单")
    public ResponseEntity<GainsLossesOrderDetailVo> saveGainsLossesOrder(@RequestBody SaveOrUpdateGainsLossesOrderParam param){
        GainsLossesOrderDetailVo gainsLossesOrderVo = gainsLossesOrderService.saveOrUpdateGainsLossesOrder(param);
        return Res.success(gainsLossesOrderVo);
    }

    /*编辑损溢单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-gains-losses"})
    @PutMapping("/gains-losses-order/{id}")
    @ApiOperation(value = "编辑损溢单", notes = "编辑损溢单")
    public ResponseEntity<GainsLossesOrderDetailVo> updateGainsLossesOrder(@PathVariable Long id, @RequestBody SaveOrUpdateGainsLossesOrderParam param){
        param.setId(id);
        GainsLossesOrderDetailVo gainsLossesOrderVo = gainsLossesOrderService.saveOrUpdateGainsLossesOrder(param);
        return Res.success(gainsLossesOrderVo);
    }

    /*删除损溢单*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-gains-losses"})
    @DeleteMapping("/gains-losses-order/{id}")
    @ApiOperation(value = "删除损溢单", notes = "删除损溢单")
    public ResponseEntity<Void> deleteGainsLossesOrder(@PathVariable Long id){
        gainsLossesOrderService.deleteGainsLossesOrder(id);
        return Res.success();
    }

    /*获取损溢单列表*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-gains-losses"})
    @GetMapping("/gains-losses-order")
    @ApiOperation(value = "获取损溢单列表", notes = "获取损溢单列表")
    public ResponseEntity<List<GainsLossesOrderVo>> queryGainsLossesOrderList(QueryGainsLossesOrderParam param){
        Page<GainsLossesOrderVo> page = gainsLossesOrderService.queryGainsLossesOrderPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取损溢单详情*/
    @GetMapping("/gains-losses-order/{id}")
    @ApiOperation(value = "获取损溢单详情", notes = "获取损溢单详情")
    public ResponseEntity<GainsLossesOrderDetailVo> queryGainsLossesOrder(@PathVariable Long id){
        return Res.success(gainsLossesOrderService.getGainsLossesOrderById(id));
    }

    /*提交损溢单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-gains-losses"})
    @PostMapping("/gains-losses-order/{id}/action-submit")
    @ApiOperation(value = "提交损溢单", notes = "提交损溢单")
    public ResponseEntity<Void> submitGainsLossesOrder(@PathVariable Long id){
        gainsLossesOrderService.submitGainsLossesOrder(id);
        return Res.success();
    }

    /*损溢单导出*/
    @GetMapping("/gains-losses-order/action-export")
    @ApiOperation(value = "导出损溢单", notes = "导出损溢单")
    public ResponseEntity<Void> exportGainsLossesOrder(HttpServletResponse response, QueryGainsLossesOrderParam param) throws IOException {
        param.setNeedExport(true);
        List<GainsLossesOrderExportVo> list = gainsLossesOrderService.queryGainsLossesOrderList(param);
        EasyExcelUtils.exportOrdinaryExcel(response,list,"损溢单导出列表","损溢单导出列表", GainsLossesOrderExportVo.class);
        return Res.success();
    }
    
}
