package com.dsj.inventory.bussiness.quality.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.quality.entity.MedicineFirstBusinessEntity;
import com.dsj.inventory.bussiness.quality.param.MedicineFirstBusinessApprovalParam;
import com.dsj.inventory.bussiness.quality.param.MedicineFirstBusinessCreateParam;
import com.dsj.inventory.bussiness.quality.param.MedicineFirstBusinessQueryParam;
import com.dsj.inventory.bussiness.quality.vo.MedicineFirstBusinessVO;

import java.util.List;

/**
 * <p>
 * 药品首营审批表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
public interface MedicineFirstBusinessService extends IService<MedicineFirstBusinessEntity> {

    /**
     * 创建药品首营审批
     * 如果已经存在该药品和供应商组合的草稿记录，则直接返回现有记录ID
     *
     * @param param 创建参数
     * @return 主键ID
     */
    Long createMedicineFirstBusiness(MedicineFirstBusinessCreateParam param);

    /**
     * 获取药品首营审批详情
     *
     * @param id 主键ID
     * @return 详情VO
     */
    MedicineFirstBusinessVO getMedicineFirstBusinessById(Long id);

    /**
     * 分页查询药品首营审批
     *
     * @param param 查询参数
     * @return 分页结果
     */
    Page<MedicineFirstBusinessVO> queryMedicineFirstBusinessPage(MedicineFirstBusinessQueryParam param);

    /**
     * 审批（通过/驳回）
     *
     * @param param 审批参数
     * @return 审批后的记录ID
     */
    Long approveMedicineFirstBusiness(MedicineFirstBusinessApprovalParam param);

    /**
     * 撤回药品首营审批
     *
     * @param id 审批记录ID
     * @return 撤回后的记录ID
     */
    Long withdrawMedicineFirstBusiness(Long id);

    /**
     * 获取药品首营审批最新状态
     *
     * @param orgMedicineMappingId 药品私域ID
     * @param supplierId           供应商ID
     * @return 详情VO
     */
    MedicineFirstBusinessVO getLatestMedicineFirstBusiness(Long orgMedicineMappingId, Long supplierId);

    /**
     * 获取药品首营审批历史记录
     *
     * @param orgMedicineMappingId 药品私域ID
     * @param supplierId           供应商ID
     * @return 历史记录列表
     */
    List<MedicineFirstBusinessVO> getMedicineFirstBusinessHistory(Long orgMedicineMappingId, Long supplierId);

    /**
     * 查询待我审批的药品首营记录
     *
     * @param param 查询参数
     * @return 分页结果
     */
    Page<MedicineFirstBusinessVO> queryPendingApprovalPage(MedicineFirstBusinessQueryParam param);

    /**
     * 检查药品首营状态
     *
     * @param orgMedicineMappingId 药品私域ID
     * @param supplierId           供应商ID
     * @return 是否通过首营审批
     */
    boolean checkMedicineFirstBusinessStatus(Long orgMedicineMappingId, Long supplierId);

    /**
     * 为待审核的药品创建修改审批流程
     *
     * @param orgMedicineMappingId 状态为"审核中"的药品私域ID
     * @param supplierId 供应商ID
     * @return 主键ID
     */
    Long createMedicineModificationApproval(Long orgMedicineMappingId, Long supplierId);

    /**
     * 审批药品修改（通过/驳回）
     *
     * @param param 审批参数
     * @return 审批后的记录ID
     */
    Long approveMedicineModification(MedicineFirstBusinessApprovalParam param);
} 