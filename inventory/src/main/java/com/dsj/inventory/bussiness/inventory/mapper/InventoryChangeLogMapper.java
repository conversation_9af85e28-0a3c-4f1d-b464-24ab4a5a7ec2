package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.InventoryChangeLog;
import com.dsj.inventory.bussiness.inventory.vo.CheckGoodsVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 进销存库存变动记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
public interface InventoryChangeLogMapper extends BaseMapper<InventoryChangeLog> {

    /**
     * 获取固定时间盘点商品明细
     * @param organizationId
     * @param startTime
     * @param endTime
     * @return
     */
    List<CheckGoodsVo> getDayCheckGoods(@Param("organizationId") Long organizationId,
                                        @Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

}
