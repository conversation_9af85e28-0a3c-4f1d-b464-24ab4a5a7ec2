package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 供应商组织私域销售人员
 * <AUTHOR>
 * @date 2023/03/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateOrganizationSupplierMemberParam", description="新增or编辑 供应商组织私域销售人员参数")
public class SaveOrUpdateOrganizationSupplierMemberParam {

    @ApiModelProperty(hidden = true, value = "id,更新是传入")
    private Long id;

    @ApiModelProperty(value = "私域供应商Id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证号码")
    private String card;

    @ApiModelProperty(value = "身份证有效期")
    private LocalDateTime cardUseDate;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "授权区域")
    private String authorizeArea;

    @ApiModelProperty(value = "授权书号")
    private String authorizeCode;

    @ApiModelProperty(value = "授权书号有效期")
    private LocalDateTime authorizeCodeDate;

    @ApiModelProperty(value = "授权品种，字典")
    private String authorizeScope;

    @ApiModelProperty(value = "身份证附件")
    private String cardFrontPath;

    @ApiModelProperty(value = "授权书附件")
    private String authorizePath;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
