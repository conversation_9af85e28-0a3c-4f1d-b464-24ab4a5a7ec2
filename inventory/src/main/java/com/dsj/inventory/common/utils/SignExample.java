package com.dsj.inventory.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;
import java.util.*;

@Slf4j
public class SignExample {
    /**
     * secretId:  6746599545093953
     * secretKey: gt4o2ywc3i6wegc76fhu
     * timestamp: 1702886983675
     * 盐:        40289041
     * 加密前：48secretId=6746599545093953&timestamp=1702886983675&secretKey=gt4o2ywc3i6wegc76fhu02
     * 加密后：41b437a5201d147eb462831312f3eb4c
     */
    public static String createSign(SortedMap<String, String> params, String key,String nonestr){
        StringBuilder sb = new StringBuilder();
        Set<Map.Entry<String, String>> es =  params.entrySet();
        Iterator<Map.Entry<String,String>> it =  es.iterator();
        //生成
        while (it.hasNext()){
            Map.Entry<String,String> entry = it.next();
            String k = entry.getKey();
            String v = entry.getValue();
            if(null != v && !"".equals(v) && !"sign".equals(k) && !"secretKey".equals(k)){
                sb.append(k+"="+v+"&");
            }
        }
        sb.append("secretKey=").append(key);
        System.out.println("待加密串" +("" +nonestr.charAt(0) + nonestr.charAt(3) + sb.toString() + nonestr.charAt(5) + nonestr.charAt(2)));
        String sign = MD5(("" +nonestr.charAt(0) + nonestr.charAt(3) + sb.toString() + nonestr.charAt(5) + nonestr.charAt(2)));
        return sign;
    }

    /**
     * md5工具类
     */
    public static String MD5(String data){
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte [] array = md5.digest(data.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte item : array) {
                sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
            }
            return sb.toString().toLowerCase();
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        //用户端发起请求，生成签名后发送请求
        String secretKey = "gt4o2ywc3i6wegc76fhu";
        String secretId = "6746599545093953";
        String timestamp = "1702886983675";
        String nonestr = "40289041";
        //生成签名
        SortedMap<String, String> sortedMap = new TreeMap<>();
        sortedMap.put("secretId", secretId);
        sortedMap.put("timestamp", timestamp);
        System.out.println("签名："+ com.dsj.inventory.common.utils.SignExample.createSign(sortedMap, secretKey,nonestr));
    }

}
