package com.dsj.inventory.common.utils;

import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.StrUtil;
import com.obs.services.ObsClient;
import com.obs.services.model.*;
import com.dsj.inventory.common.entity.PutObjectResultVo;
import com.dsj.inventory.common.entity.StrPool;
import com.dsj.inventory.common.utils.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.Charset;

/**
 * <AUTHOR>
 * @ClassName : ObsUtils
 * @Description : OBS 工具类
 * @Date : 2022-07-25 16:25
 */
@Component
public class ObsUtils {

    private static ObsClient obsClient;

    private static String bucketName;

    private static String parentDir;

    private static String endPoint;

    private static String domainName;

    //静态属性注入
    @Autowired
    public void setObsClient(ObsClient obsClient) {
        com.dsj.inventory.common.utils.ObsUtils.obsClient = obsClient;
    }
    @Value("${huawei.obs.bucketName}")
    public void setBucketName(String bucketName) {
        com.dsj.inventory.common.utils.ObsUtils.bucketName = bucketName;
    }
    @Value("${huawei.obs.parentDir}")
    public void setParentDir(String parentDir) {
        com.dsj.inventory.common.utils.ObsUtils.parentDir = parentDir;
    }
    @Value("${huawei.obs.upload.endPoint}")
    public void setEndPoint(String endPoint) {
        com.dsj.inventory.common.utils.ObsUtils.endPoint = endPoint;
    }
    @Value("${huawei.obs.domainName}")
    public void setDomainName(String domainName) {
        com.dsj.inventory.common.utils.ObsUtils.domainName = domainName;
    }

    /**
     * @author: guojia
     * @description: 获取OBS 上传文件信息
     * @date: 2022/7/26
     */
    public static PutObjectResultVo getAvailableObsResult(PutObjectResult putObjectResult){
        String url = putObjectResult.getObjectUrl();
        url = URLDecoder.decode(url, Charset.forName("utf-8"));
        url = StrUtil.replace(url,bucketName + StrPool.DOT + endPoint,domainName);
        return PutObjectResultVo.builder().objectUrl(url).build();
    }

    /**
     * @author: guojia
     * @description: 通过内网读取OBS 将 https://filet.syqbshop.com:443 替换为 http://diagnose.obs.cn-north-4.myhuaweicloud.com
     * @date: 2022/7/26
     */
    public static String getObsInnerUrl(String url){
        url = URLDecoder.decode(url, Charset.forName("utf-8"));
        url = StrUtil.replace(url,StrPool.HTTPS + domainName + StrPool.COLON + StrPool.HTTPS_port,
                StrPool.HTTP + bucketName + StrPool.DOT + endPoint);
        return url;
    }

    /**
     * 上传文件
     *
     * @param multipartFile 文件
     * @param objectKey     文件名，如果桶中有文件夹的话，如往test文件上传test.txt文件，那么objectKey就是test/test.txt
     * @throws Exception
     */
    public static PutObjectResult uploadFile(MultipartFile multipartFile, String objectKey) throws Exception {
        InputStream inputStream = multipartFile.getInputStream();
        PutObjectResult putObjectResult = obsClient.putObject(bucketName, parentDir+"/"+objectKey, inputStream);
        inputStream.close();
        //obsClient.close();
        return putObjectResult;
    }

    /**
     * 上传文件
     *
     * @param file      文件
     * @param objectKey 文件名，如果桶中有文件夹的话，如往test文件上传test.txt文件，那么objectKey就是test/test.txt
     * @throws Exception
     */
    public static PutObjectResult uploadFileByFile(File file,String newParentDir,String objectKey) throws Exception {
        String dir = parentDir;
        if (CommonUtils.isNotEmpty(newParentDir)){
            dir = newParentDir;
        }
        InputStream inputStream = new FileInputStream(file);
        PutObjectResult putObjectResult = obsClient.putObject(bucketName, dir+"/"+objectKey, inputStream);
        inputStream.close();
        // obsClient.close();

        return putObjectResult;
    }

    /**
     * 根据播放地址上传文件
     * @param path
     * @param newParentDir
     * @param objectKey
     * @return
     * @throws Exception
     */
    public static PutObjectResult uploadFileByUrl(String path,String newParentDir,String objectKey) throws Exception {
        String dir = parentDir;
        if (CommonUtils.isNotEmpty(newParentDir)){
            dir = newParentDir;
        }
        URL url = new URL(path);
        URLConnection urlConnection = (URLConnection) url.openConnection();
        InputStream inputStream = urlConnection.getInputStream();
        PutObjectResult putObjectResult = obsClient.putObject(bucketName, dir+"/"+objectKey, inputStream);
        inputStream.close();
        // obsClient.close();

        return putObjectResult;
    }

    /**
     * 上传文件
     *
     * @param inputStream      文件
     * @param objectKey 文件名，如果桶中有文件夹的话，如往test文件上传test.txt文件，那么objectKey就是test/test.txt
     * @throws Exception
     */
    public static PutObjectResult uploadFileByInputStream(InputStream inputStream,String newParentDir, String objectKey) throws Exception {
        String dir = parentDir;
        if (CommonUtils.isNotEmpty(newParentDir)){
            dir = newParentDir;
        }
        PutObjectResult putObjectResult = obsClient.putObject(bucketName, dir+"/"+objectKey, inputStream);
        inputStream.close();
        // obsClient.close();
        return putObjectResult;
    }

    /**
     * 上传文件
     *
     * @param inputStream      文件
     * @param objectKey 文件名，如果桶中有文件夹的话，如往test文件上传test.txt文件，那么objectKey就是test/test.txt
     * @throws Exception
     */
    public static PutObjectResult uploadFileByInputStream(InputStream inputStream,String newParentDir, String objectKey,String ContentType) throws Exception {
        String dir = parentDir;
        if (CommonUtils.isNotEmpty(newParentDir)){
            dir = newParentDir;
        }
        ObjectMetadata objectMetadata = null;
        if (CommonUtils.isNotEmpty(ContentType)){
            objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(ContentType);
        }
        PutObjectResult putObjectResult = obsClient.putObject(bucketName, dir+"/"+objectKey, inputStream,objectMetadata);
        inputStream.close();
        // obsClient.close();
        return putObjectResult;
    }


    /**
     * 文件下载
     * @param objectKey 文件名
     * @return
     */
    public static ObsObject downFile(String objectKey){
        ObsObject obsObject = obsClient.getObject(bucketName, parentDir+"/"+objectKey);
        // InputStream ins = obsObject.getObjectContent();

        return obsObject;
    }

    /**
     * 文件下载
     * @param bucketName 桶
     * @param objectKey 文件名
     * @return
     */
    public static ObsObject downFile(String bucketName, String objectKey){
        ObsObject obsObject = obsClient.getObject(bucketName, objectKey);
        // InputStream ins = obsObject.getObjectContent();

        return obsObject;
    }

    /**
     * 文件下载
     * @param bucketName 桶
     * @param parentDir 父级目录
     * @param objectKey 文件名
     * @return
     */
    public static ObsObject downFile(String bucketName, String parentDir, String objectKey){
        if(CommonUtils.isEmpty(bucketName)){
            bucketName = com.dsj.inventory.common.utils.ObsUtils.bucketName;
        }
        ObsObject obsObject = obsClient.getObject(bucketName, parentDir+"/"+objectKey);
        // InputStream ins = obsObject.getObjectContent();

        return obsObject;
    }
    /**
     * 删除文件
     *
     * @param objectKey 文件名，如果桶中有文件夹的话，如往test文件上传test.txt文件，那么objectKey就是test/test.txt
     * @throws Exception
     */
    public static DeleteObjectResult deleteFile(String objectKey) throws Exception {
        DeleteObjectResult deleteObjectResult = obsClient.deleteObject(bucketName, objectKey);
        //obsClient.close();
        return deleteObjectResult;
    }

    /**
     * 归档文件恢复至
     */
    public static RestoreObjectResult restoreObject(String bucketName,String objectKey) throws Exception {
        RestoreObjectRequest request = new RestoreObjectRequest();
        request.setBucketName(bucketName);
        request.setObjectKey(objectKey);
        request.setDays(1);
        request.setRestoreTier(RestoreTierEnum.EXPEDITED);
        return obsClient.restoreObjectV2(request);
    }
}
