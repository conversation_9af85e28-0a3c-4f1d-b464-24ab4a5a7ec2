package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 销售单处理状态 1挂单 2已完成 3已退单
 * <AUTHOR>
 */

@Getter
public enum SalesOrderStateEnum {

    HANG(1, "挂单"),
    COMPLETED(2, "已完成"),
    RETURN(3, "已退单"),
    ;

    private Integer code;

    private String desc;

    SalesOrderStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (SalesOrderStateEnum a : SalesOrderStateEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
