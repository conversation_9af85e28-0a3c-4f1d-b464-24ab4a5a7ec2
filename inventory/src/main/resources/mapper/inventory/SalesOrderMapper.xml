<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.SalesOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.SalesOrder">
    <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="code" property="code" />
        <result column="total_price" property="totalPrice" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        organization_id, code, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="querySalesOrderPage" resultType="com.dsj.inventory.bussiness.inventory.vo.SalesOrderDetailVo">
        SELECT distinct jso.id
        ,jso.organization_id organizationId
        ,jso.code
        ,jso.order_state orderState
        ,jso.order_type orderType
        ,jso.sale_id saleId
        ,su.name saleName
        ,jso.cashier_id cashierId
        ,csu.name cashierName
        ,jso.pay_way payWay
        ,jso.member_id memberId
        ,jso.receivable_price receivablePrice
        ,jso.preferential_price preferentialPrice
        ,jso.deduct_price deductPrice
        ,jso.total_price totalPrice
        ,jso.actual_price actualPrice
        ,jso.give_change_price giveChangePrice
        ,jso.activity_reduce_price activityReducePrice
        ,jso.coupon_price couponPrice
        ,jso.integral
        ,jso.goods_info goodsInfo
        ,jso.create_time createTime
        FROM jxc_sales_order jso
        LEFT JOIN sys_user csu ON csu.id = jso.cashier_id AND csu.is_delete = 0
        LEFT JOIN sys_user su ON su.id = jso.sale_id AND su.is_delete = 0
        WHERE
        jso.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jso.organization_id = #{param.organizationId}
        </if>
        <if test="param.cashierId != null and param.cashierId != '' ">
            AND jso.cashier_id = #{param.cashierId}
        </if>
        <if test="param.memberId != null and param.memberId != '' ">
            AND jso.member_id = #{param.memberId}
        </if>
        <if test="param.orderState != null">
            AND jso.order_state = #{param.orderState}
        </if>
        <if test="param.orderState == null">
            AND jso.order_state in (2,3)
        </if>
        <if test="param.orderType != null">
            AND jso.order_type = #{param.orderType}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jso.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.goodsNameLike != null and param.goodsNameLike != '' ">
            AND jso.goods_info like CONCAT('%',#{param.goodsNameLike},'%')
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jso.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        ORDER BY jso.create_time desc
    </select>

    <select id="getSalesOrderById" resultType="com.dsj.inventory.bussiness.inventory.vo.SalesOrderDetailVo">
        SELECT distinct jso.id
                      ,jso.organization_id organizationId
                      ,jso.code
                      ,jso.order_state orderState
                      ,jso.order_type orderType
                      ,jso.return_date returnDate
                      ,jso.return_reason returnReason
                      ,jso.hang_date hangDate
                      ,jso.hang_remark hangRemark
                      ,jso.cashier_id cashierId
                      ,csu.name cashierName
                      ,jso.receivable_price receivablePrice
                      ,jso.preferential_price preferentialPrice
                      ,jso.deduct_price deductPrice
                      ,jso.total_price totalPrice
                      ,jso.actual_price actualPrice
                      ,jso.give_change_price giveChangePrice
                      ,jso.goods_amount goodsAmount
                      ,jso.integral
                      ,jso.deduct_integral deductIntegral
                      ,jso.prescription_type prescriptionType
                      ,jso.prescription_ids prescriptionIds
                      ,jso.settlement_date settlementDate
                      ,jso.member_id memberId
                      ,jso.activity_id activityId
                      ,jso.activity_reduce_price activityReducePrice
                      ,jso.coupon_id couponId
                      ,jso.coupon_price couponPrice
                      ,jso.pay_way payWay
                      ,jso.pay_info payInfo
                      ,jso.stored_pay storedPay
                      ,jso.integral_pay integralPay
                      ,jso.goods_info goodsInfo
                      ,jso.sale_id saleId
                      ,su.name saleName
                      ,jso.create_time createTime
        FROM jxc_sales_order jso
        LEFT JOIN sys_user csu ON csu.id = jso.cashier_id AND csu.is_delete = 0
        LEFT JOIN sys_user su ON su.id = jso.sale_id AND su.is_delete = 0
        WHERE
            jso.is_delete = 0
        AND jso.id = #{id}
    </select>

    <select id="querySalesOrderList" resultType="com.dsj.inventory.bussiness.inventory.vo.SalesOrderExportVo">
        SELECT distinct jso.id
        ,jso.code
        ,(CASE
        WHEN jso.order_state = 1 THEN '挂单'
        WHEN jso.order_state = 2 THEN '已完成'
        WHEN jso.order_state = 3 THEN '已退单'
        END) orderState
        ,csu.name cashierName
        ,jso.pay_way payWay
        ,ROUND(jso.receivable_price/100,2) receivablePrice
        ,ROUND(jso.actual_price/100,2) actualPrice
        ,jso.goods_info goodsInfo
        ,jso.create_time createTime
        FROM jxc_sales_order jso
        LEFT JOIN sys_user csu ON csu.id = jso.cashier_id AND csu.is_delete = 0
        WHERE
        jso.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jso.organization_id = #{param.organizationId}
        </if>
        <if test="param.cashierId != null and param.cashierId != '' ">
            AND jso.cashier_id = #{param.cashierId}
        </if>
        <if test="param.memberId != null and param.memberId != '' ">
            AND jso.member_id = #{param.memberId}
        </if>
        <if test="param.orderState != null">
            AND jso.order_state = #{param.orderState}
        </if>
        <if test="param.orderState == null">
            AND jso.order_state in (2,3)
        </if>
        <if test="param.orderType != null">
            AND jso.order_type = #{param.orderType}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jso.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.goodsNameLike != null and param.goodsNameLike != '' ">
            AND jso.goods_info like CONCAT('%',#{param.goodsNameLike},'%')
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jso.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        ORDER BY jso.create_time desc
    </select>

    <select id="countSalesPrice" resultType="com.dsj.inventory.bussiness.inventory.vo.SalesCountPriceVo">
        SELECT
              SUM(jso.receivable_price) receivablePrice
             ,SUM(jso.preferential_price) preferentialPrice
             ,SUM(jso.deduct_price) deductPrice
             ,SUM(jso.total_price) totalPrice
             ,SUM(jso.actual_price) actualPrice
             ,SUM(jso.give_change_price) giveChangePrice
             ,SUM(jso.activity_reduce_price) activityReducePrice
             ,SUM(jso.coupon_price) couponPrice
             ,SUM(jso.stored_pay) storedPay
             ,SUM(jso.integral_pay) integralPay
        FROM jxc_sales_order jso
        WHERE
            jso.is_delete = 0
        AND jso.order_state in (2,3)
        AND DATE_FORMAT(jso.settlement_date,"%Y-%m-%d") = DATE_FORMAT(now(),"%Y-%m-%d")
        AND jso.cashier_id = #{cashierId}
    </select>

    <select id="countReturnPrice" resultType="java.lang.Integer">
        SELECT
            SUM(jso.actual_price) totalPrice
        FROM jxc_sales_order jso
        WHERE
            jso.is_delete = 0
        AND jso.order_state = 3
        AND DATE_FORMAT(jso.return_date,"%Y-%m-%d") = DATE_FORMAT(now(),"%Y-%m-%d")
        AND jso.cashier_id = #{cashierId}
    </select>

    <update id="deleteSalesOrderByOrganizationId">
        UPDATE jxc_sales_order jso, jxc_sales_goods jsg
        SET jso.is_delete = 1, jsg.is_delete = 1
        WHERE jso.id = jsg.order_id
          AND jso.is_delete = 0
          AND jsg.is_delete = 0
          AND jso.organization_id = #{organizationId}
    </update>

    <select id="getSalesOrderCount" resultType="java.lang.Integer">
        SELECT COUNT(1) number
        FROM jxc_sales_order jso
        WHERE
        DATE_FORMAT(jso.create_time,'%Y-%m-%d') = #{day}
        <if test="organizationId != null and organizationId != '' ">
            AND jso.organization_id = #{organizationId}
        </if>
    </select>

</mapper>
