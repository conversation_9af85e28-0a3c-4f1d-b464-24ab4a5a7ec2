package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.WarningLog;
import com.dsj.inventory.bussiness.inventory.enumeration.WarningTypeEnum;
import com.dsj.inventory.bussiness.inventory.mapper.WarningLogMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryWarningLogParam;
import com.dsj.inventory.bussiness.inventory.service.InventoryService;
import com.dsj.inventory.bussiness.inventory.service.WarningLogService;
import com.dsj.inventory.bussiness.inventory.vo.InventoryVo;
import com.dsj.inventory.bussiness.inventory.vo.WarningLogVo;
import com.dsj.inventory.common.constant.BaseConstant;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.common.utils.TencentIMUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.auth.param.QueryOrgModuleParam;
import com.pocky.transport.bussiness.auth.service.OrgRModuleService;
import com.pocky.transport.bussiness.auth.vo.OrgRModuleVo;
import com.pocky.transport.bussiness.diagnose.entity.UserROrganization;
import com.pocky.transport.bussiness.diagnose.enumeration.ModuleEnum;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.diagnose.service.UserROrganizationService;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 进销存预警记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Slf4j
@Service
public class WarningLogServiceImpl extends ServiceImpl<WarningLogMapper, WarningLog> implements WarningLogService {

    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @Autowired
    private WarningLogService warningLogService;
    @Autowired
    private InventoryService inventoryService;
    @Autowired
    private OrgRModuleService orgRModuleService;
    @Autowired
    private TencentIMUtils tencentIMUtils;
    @Autowired
    private UserROrganizationService userROrganizationService;

    @Override
    public Page<WarningLogVo> queryWarningLogPage(QueryWarningLogParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<WarningLogVo> list = this.baseMapper.queryWarningLogPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public void batchHandleWarningLog() {
        //获取拥有进销存权限的组织，并且设置了滞销
        QueryOrgModuleParam unsalableParam = QueryOrgModuleParam.builder().moduleType(ModuleEnum.INVENTORY.getCode())
                .salesDayLimitFlag(TrueEnum.TRUE.getCode()).build();
        List<OrgRModuleVo> unsalableOrganizationList = orgRModuleService.queryOrgRModuleList(unsalableParam);
        //发送滞销预警消息组织IdList
        List<Long> unsalableOrganizationIdList = new ArrayList<>();
        if(CommonUtils.isNotEmpty(unsalableOrganizationList)){
            for(OrgRModuleVo orgRModule : unsalableOrganizationList){
                Integer unsalableDay = orgRModule.getSalesDayLimit();
                //删除已失效的滞销预警
                this.baseMapper.deleteUnsalableWarningLog(orgRModule.getOrganizationId(),unsalableDay);
                //获取需要新增滞销预警的私域商品Id
                List<Long> organizationMedicineIdList = organizationMedicineMappingService
                        .queryUnsalableOrganizationMedicineList(orgRModule.getOrganizationId(),unsalableDay);
                if(CommonUtils.isEmpty(organizationMedicineIdList)){
                    continue;
                }
                List<WarningLog> warningLogList = new ArrayList<>();
                for(Long organizationMedicineId : organizationMedicineIdList){
                    WarningLog warningLog = WarningLog.builder().organizationId(orgRModule.getOrganizationId())
                            .organizationMedicineId(organizationMedicineId).type(WarningTypeEnum.UNSALABLE.getCode()).build();
                    warningLogList.add(warningLog);
                }
                warningLogService.saveBatch(warningLogList);
                unsalableOrganizationIdList.add(orgRModule.getOrganizationId());
            }
        }
        //获取拥有进销存权限的组织，并且设置了近效期
        QueryOrgModuleParam becomeDueParam = QueryOrgModuleParam.builder().moduleType(ModuleEnum.INVENTORY.getCode())
                .becomeDueLimitFlag(TrueEnum.TRUE.getCode()).build();
        List<OrgRModuleVo> becomeDueOrganizationList = orgRModuleService.queryOrgRModuleList(becomeDueParam);
        //发送滞销预警消息组织IdList
        List<Long> becomeDueOrganizationIdList = new ArrayList<>();
        if(CommonUtils.isNotEmpty(becomeDueOrganizationList)){
            for(OrgRModuleVo orgRModule : becomeDueOrganizationList){
                Integer becomeDueDay = orgRModule.getBecomeDueLimit();
                //删除已失效的近效期预警
                this.baseMapper.deleteBecomeDueWarningLog(orgRModule.getOrganizationId(),becomeDueDay);
                //获取需要新增近效期预警的库存Id
                List<InventoryVo> inventoryList = inventoryService.queryBecomeDueInventoryList(orgRModule.getOrganizationId(),becomeDueDay);
                if(CommonUtils.isEmpty(inventoryList)){
                    continue;
                }
                List<WarningLog> warningLogList = new ArrayList<>();
                for(InventoryVo inventory : inventoryList){
                    WarningLog warningLog = WarningLog.builder().organizationId(orgRModule.getOrganizationId()).inventoryId(inventory.getId())
                            .organizationMedicineId(inventory.getOrganizationMedicineId()).type(WarningTypeEnum.BECOME_DUE.getCode()).build();
                    warningLogList.add(warningLog);
                }
                warningLogService.saveBatch(warningLogList);
                becomeDueOrganizationIdList.add(orgRModule.getOrganizationId());
            }
        }
        //发送预警消息
        unsalableOrganizationIdList = unsalableOrganizationIdList.stream().distinct().collect(Collectors.toList());
        becomeDueOrganizationIdList = becomeDueOrganizationIdList.stream().distinct().collect(Collectors.toList());
        if(CommonUtils.isNotEmpty(unsalableOrganizationIdList)){
            for(Long organizationId : unsalableOrganizationIdList){
                List<UserROrganization> uroList = userROrganizationService.lambdaQuery()
                        .eq(UserROrganization::getOrganizationId,organizationId).list();
                if (CommonUtils.isNotEmpty(uroList)){
                    List<String> userIdList = uroList.stream().map(s -> s.getUserId().toString()).collect(Collectors.toList());
                    tencentIMUtils.adminSendBatchSingleMsg(userIdList, BaseConstant.IM_DATE_INVENTORY_WARNING,BaseConstant.IM_DESC_INVENTORY_UNSALABLE,"");
                }
            }
            log.info("{}向商家发送近效期预警消息", DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
        if(CommonUtils.isNotEmpty(becomeDueOrganizationIdList)){
            for(Long organizationId : becomeDueOrganizationIdList){
                List<UserROrganization> uroList = userROrganizationService.lambdaQuery()
                        .eq(UserROrganization::getOrganizationId,organizationId).list();
                if (CommonUtils.isNotEmpty(uroList)){
                    List<String> userIdList = uroList.stream().map(s -> s.getUserId().toString()).collect(Collectors.toList());
                    tencentIMUtils.adminSendBatchSingleMsg(userIdList, BaseConstant.IM_DATE_INVENTORY_WARNING,BaseConstant.IM_DESC_INVENTORY_BECOME_DUE,"");
                }
            }
            log.info("{}向商家发送滞销预警消息", DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
    }

    @Override
    public WarningLog findWarningLogByTypeAndMedicine(Integer type, Long organizationId, Long organizationMedicineId) {
        return this.baseMapper.findWarningLogByTypeAndMedicine(type, organizationId, organizationMedicineId);
    }

    @Override
    public boolean markWarningLogAsDeleted(Long id) {
        return this.baseMapper.markWarningLogAsDeleted(id) > 0;
    }
}
