package com.dsj.inventory.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 校验工具
 *
 * <AUTHOR>
 * @date 2023/9/8 16:41
 */
public class CheckUtils {

    /**
     * 校验手机号码是否符合规范
     * @param phone
     * @return
     */
    public static boolean checkPhone(String phone) {
        String regex = "^1[3456789]\\d{9}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(phone);
        return matcher.matches();
    }

}
