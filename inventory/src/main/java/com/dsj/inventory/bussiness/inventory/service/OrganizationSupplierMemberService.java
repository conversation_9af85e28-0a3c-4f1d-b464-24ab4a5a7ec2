package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationSupplierMember;
import com.dsj.inventory.bussiness.inventory.param.QueryOrganizationSupplierMemberParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateOrganizationSupplierMemberParam;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationSupplierMemberVo;

import java.util.List;

/**
 * <p>
 * 供应商组织私域销售人员 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-24
 */
public interface OrganizationSupplierMemberService extends IService<OrganizationSupplierMember> {

    /**
     * 新增、编辑、删除 供应商组织私域销售人员
     * @param param
     * @return
     */
    OrganizationSupplierMemberVo saveOrUpdateOrganizationSupplierMember(SaveOrUpdateOrganizationSupplierMemberParam param);

    /**
     * 获取供应商组织私域销售人员分页列表
     * @param param
     * @return
     */
    Page<OrganizationSupplierMemberVo> queryOrganizationSupplierMemberPage(QueryOrganizationSupplierMemberParam param);

    /**
     * 供应商组织私域批量处理销售人员 --新增、编辑、删除
     * @param organizationSupplierId
     * @param paramList
     * @return
     */
    List<OrganizationSupplierMemberVo> batchChangeOrganizationSupplierMember(Long organizationSupplierId, List<SaveOrUpdateOrganizationSupplierMemberParam> paramList);

}
