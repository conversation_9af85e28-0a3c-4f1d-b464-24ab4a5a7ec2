package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.PurchaseOrder;
import com.dsj.inventory.bussiness.inventory.param.QueryPurchaseOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.PurchaseOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchaseOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.PurchaseOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存采购订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
public interface PurchaseOrderMapper extends BaseMapper<PurchaseOrder> {


    /**
     * 获取采购订单分页列表
     * @param page
     * @param param
     * @return
     */
    List<PurchaseOrderVo> queryPurchaseOrderPage(@Param("page") Page page, @Param("param") QueryPurchaseOrderParam param);

    /**
     * 获取采购订单详情
     * @param id
     * @return
     */
    PurchaseOrderDetailVo getPurchaseOrderById(Long id);

    /**
     * 导出采购订单列表
     * @param param
     * @return
     */
    List<PurchaseOrderExportVo> queryPurchaseOrderList(@Param("param") QueryPurchaseOrderParam param);

    /**
     * 删除组织的采购订单
     * @param organizationId
     */
    void deletePurchaseOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天采购订单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getPurchaseOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);

    /**
     * 根据计划ID统计采购单数量，可排除指定采购单
     * @param planId 计划ID
     * @param excludeOrderId 要排除的采购单ID (可为null)
     * @return 采购单数量
     */
    default int countByPlanId(Long planId, Long excludeOrderId) {
        LambdaQueryWrapper<PurchaseOrder> wrapper = new LambdaQueryWrapper<PurchaseOrder>()
                .eq(PurchaseOrder::getPlanId, planId);
        if (excludeOrderId != null) {
            wrapper.ne(PurchaseOrder::getId, excludeOrderId);
        }
        return this.selectCount(wrapper);
    }

    /**
     * 更新采购订单状态
     * @param orderId 订单ID
     * @param targetState 目标状态
     */
    default void updateState(Long orderId, Integer targetState) {
        this.update(null, new LambdaUpdateWrapper<PurchaseOrder>()
                .eq(PurchaseOrder::getId, orderId)
                .set(PurchaseOrder::getDealState, targetState));
    }

    /**
     * 更新采购订单状态，带原始状态校验
     * @param orderId 订单ID
     * @param currentState 当前状态
     * @param targetState 目标状态
     */
    default void updateStateWithCondition(Long orderId, Integer currentState, Integer targetState) {
        this.update(null, new LambdaUpdateWrapper<PurchaseOrder>()
                .eq(PurchaseOrder::getId, orderId)
                .eq(PurchaseOrder::getDealState, currentState)
                .set(PurchaseOrder::getDealState, targetState));
    }

}
