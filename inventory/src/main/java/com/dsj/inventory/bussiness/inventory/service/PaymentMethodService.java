package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.PaymentMethod;
import com.dsj.inventory.bussiness.inventory.param.QueryPaymentMethodParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdatePaymentMethodParam;
import com.dsj.inventory.bussiness.inventory.vo.PaymentMethodVO;

import java.util.List;

/**
 * 组织付款方式 Service 接口
 */
public interface PaymentMethodService extends IService<PaymentMethod> {

    /**
     * 保存或更新组织付款方式（包含配置项）
     *
     * @param param 保存或更新参数
     * @return 组织付款方式ID
     */
    Long saveOrUpdatePaymentMethod(SaveOrUpdatePaymentMethodParam param);

    /**
     * 删除组织付款方式（同时删除相关配置项）
     *
     * @param id 组织付款方式ID
     * @return 是否成功
     */
    Boolean deletePaymentMethod(Long id);

    /**
     * 获取组织付款方式详情（包含配置项）
     *
     * @param id 组织付款方式ID
     * @return 组织付款方式视图对象
     */
    PaymentMethodVO getPaymentMethod(Long id);

    /**
     * 分页查询组织付款方式
     *
     * @param param 查询参数
     * @return 分页结果
     */
    Page<PaymentMethodVO> queryPaymentMethodPage(QueryPaymentMethodParam param);

    /**
     * 获取组织下所有启用的付款方式
     *
     * @param organizationId 组织ID
     * @param withConfigs 是否包含配置项
     * @return 组织付款方式列表
     */
    List<PaymentMethodVO> listEnabledByOrganization(Long organizationId, boolean withConfigs);
} 