package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 进销存损溢单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_gains_losses_order")
@ApiModel(value="GainsLossesOrder", description="进销存损溢单")
public class GainsLossesOrder extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织Id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "损溢单号")
    @TableField("code")
    private String code;

    @ApiModelProperty(value = "处理状态 0暂存 1完成")
    @TableField("deal_state")
    private Integer dealState;

    @ApiModelProperty(value = "损溢类型 1损2溢")
    @TableField("type")
    private Integer type;

    @ApiModelProperty(value = "损溢总数")
    @TableField("total_amount")
    private Double totalAmount;

    @ApiModelProperty(value = "成本金额（成本均价总额）")
    @TableField("cost_total_price")
    private Integer costTotalPrice;

    @ApiModelProperty(value = "移动加权平均成本价总额")
    @TableField("average_total_cost")
    private Integer averageTotalCost;

    @ApiModelProperty(value = "零售总金额（私域商品总金额）")
    @TableField("total_price")
    private Integer totalPrice;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty(value = "损溢商品名")
    @TableField("goods_names")
    private String goodsNames;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
