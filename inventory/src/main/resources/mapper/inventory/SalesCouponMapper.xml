<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.SalesCouponMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.SalesCoupon">
    <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="coupon_type" property="couponType" />
        <result column="name" property="name" />
        <result column="coupon_condition" property="couponCondition" />
        <result column="reduce_price" property="reducePrice" />
        <result column="percentage" property="percentage" />
        <result column="assert_day" property="assertDay" />
        <result column="use_day" property="useDay" />
        <result column="amount_limit" property="amountLimit" />
        <result column="apply_to" property="applyTo" />
        <result column="goods_limit" property="goodsLimit" />
        <result column="grant_amount" property="grantAmount" />
        <result column="use_amount" property="useAmount" />
        <result column="expire_amount" property="expireAmount" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        organization_id, coupon_type, name, coupon_condition, reduce_price, percentage, assert_day, use_day, amount_limit, apply_to, goods_limit, grant_amount, use_amount, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="querySalesCouponPage" resultType="com.dsj.inventory.bussiness.inventory.vo.SalesCouponVo">
        SELECT distinct jsc.id
        ,jsc.organization_id organizationId
        ,jsc.module_type moduleType
        ,jsc.coupon_type couponType
        ,jsc.coupon_mode couponMode
        ,jsc.coupon_state couponState
        ,jsc.name
        ,jsc.coupon_condition couponCondition
        ,jsc.reduce_price reducePrice
        ,jsc.percentage
        ,jsc.assert_day assertDay
        ,jsc.use_day useDay
        ,jsc.start_date startDate
        ,jsc.end_date endDate
        ,jsc.get_start_date getStartDate
        ,jsc.get_end_date getEndDate
        ,jsc.get_amount_limit getAmountLimit
        ,jsc.get_group getGroup
        ,jsc.amount_limit amountLimit
        ,jsc.goods_limit goodsLimit
        ,jsc.grant_amount grantAmount
        ,jsc.use_amount useAmount
        ,jsc.expire_amount expireAmount
        ,(CASE
        WHEN jsc.coupon_mode = 1 THEN (jsc.grant_amount-jsc.use_amount-jsc.expire_amount)
        WHEN jsc.coupon_mode = 2 AND jsc.get_amount_limit is not null THEN (jsc.get_amount_limit-jsc.grant_amount)
        END) surplusAmount
        ,jsc.apply_to applyTo
        ,su.name createUserName
        ,jsc.create_time createTime
        FROM jxc_sales_coupon jsc
        LEFT JOIN sys_user su ON su.id = jsc.create_user AND su.is_delete = 0
        <if test="param.goodsIds != null and param.goodsIds != ''">
        LEFT JOIN jxc_sales_coupon_goods jscg ON jscg.coupon_id = jsc.id AND jscg.is_delete = 0
        </if>
        WHERE
        jsc.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jsc.organization_id = #{param.organizationId}
        </if>
        <if test="param.moduleType != null ">
            AND jsc.module_type = #{param.moduleType}
        </if>
        <if test="param.couponType != null ">
            AND jsc.coupon_type = #{param.couponType}
        </if>
        <if test="param.couponMode != null ">
            AND jsc.coupon_mode = #{param.couponMode}
        </if>
        <if test="param.couponState != null ">
            AND jsc.coupon_state = #{param.couponState}
        </if>
        <if test="param.getGroup != null ">
            AND jsc.get_group = #{param.getGroup}
        </if>
        <if test="param.getScreen">
            AND now() BETWEEN jsc.get_start_date AND jsc.get_end_date
            AND (jsc.get_amount_limit is null OR (jsc.get_amount_limit - jsc.grant_amount) > 1)
        </if>
        <if test="param.nameLike != null and param.nameLike != '' ">
            AND jsc.name like CONCAT('%',#{param.nameLike},'%')
        </if>
        <if test="param.createUserNameLike != null and param.createUserNameLike != '' ">
            AND su.name like CONCAT('%',#{param.createUserNameLike},'%')
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jsc.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        <if test="param.getDateStart != null and param.getDateEnd != '' ">
            AND DATE_FORMAT(jsc.get_start_date,"%Y-%m-%d") BETWEEN #{param.getDateStart} AND #{param.getDateEnd}
        </if>
        <if test="param.goodsIds != null and param.goodsIds != ''">
            AND (jscg.organization_medicine_id in
            <foreach item="item" index="index" collection="param.goodsIds.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR jsc.goods_limit = 1)
        </if>
        GROUP BY jsc.id
        ORDER BY jsc.create_time desc
    </select>

    <select id="getSalesCouponById" resultType="com.dsj.inventory.bussiness.inventory.vo.SalesCouponVo">
        SELECT jsc.id
             ,jsc.organization_id organizationId
             ,jsc.module_type moduleType
             ,jsc.coupon_type couponType
             ,jsc.coupon_mode couponMode
             ,jsc.coupon_state couponState
             ,jsc.name
             ,jsc.coupon_condition couponCondition
             ,jsc.reduce_price reducePrice
             ,jsc.percentage
             ,jsc.assert_day assertDay
             ,jsc.use_day useDay
             ,jsc.start_date startDate
             ,jsc.end_date endDate
             ,jsc.get_start_date getStartDate
             ,jsc.get_end_date getEndDate
             ,jsc.get_amount_limit getAmountLimit
             ,jsc.get_group getGroup
             ,jsc.amount_limit amountLimit
             ,jsc.apply_to applyTo
             ,jsc.goods_limit goodsLimit
             ,jsc.grant_amount grantAmount
             ,jsc.use_amount useAmount
             ,jsc.expire_amount expireAmount
             ,(CASE
             WHEN jsc.coupon_mode = 1 THEN (jsc.grant_amount-jsc.use_amount-jsc.expire_amount)
             WHEN jsc.coupon_mode = 2 AND jsc.get_amount_limit is not null THEN (jsc.get_amount_limit-jsc.grant_amount)
             END) surplusAmount
             ,su.name createUserName
             ,jsc.create_time createTime
        FROM jxc_sales_coupon jsc
        LEFT JOIN sys_user su ON su.id = jsc.create_user AND su.is_delete = 0
        WHERE
            jsc.is_delete = 0
        AND jsc.id = #{id}
    </select>

    <select id="querySalesCouponMemberDetailPage" resultType="com.dsj.inventory.bussiness.inventory.vo.SalesCouponDetailVo">
        SELECT distinct jmc.id
        ,jm.name
        ,jm.phone
        ,jmc.coupon_state couponState
        ,jmc.start_date startDate
        ,jmc.end_date endDate
        ,jmc.create_time createTime
        FROM jxc_member_coupon jmc
        LEFT JOIN jxc_member jm ON jm.id = jmc.member_id AND jm.is_delete = 0
        WHERE
        jmc.is_delete = 0
        <if test="param.couponId != null and param.couponId != '' ">
            AND jmc.coupon_id = #{param.couponId}
        </if>
        ORDER BY jmc.create_time desc
    </select>

    <select id="querySalesCouponUserDetailPage" resultType="com.dsj.inventory.bussiness.inventory.vo.SalesCouponDetailVo">
        SELECT distinct jmc.id
        ,su.name
        ,su.phone
        ,jmc.coupon_state couponState
        ,jmc.start_date startDate
        ,jmc.end_date endDate
        ,do.name getOrganizationName
        ,jmc.create_time createTime
        FROM jxc_member_coupon jmc
        LEFT JOIN sys_user su ON su.id = jmc.user_id AND su.is_delete = 0
        LEFT JOIN di_organization do ON do.id = jmc.get_organization_id AND do.is_delete = 0
        WHERE
        jmc.is_delete = 0
        <if test="param.couponId != null and param.couponId != '' ">
            AND jmc.coupon_id = #{param.couponId}
        </if>
        ORDER BY jmc.create_time desc
    </select>


</mapper>
