package com.dsj.inventory.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * IM 群组list信息返回实体
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class IMGroupListResponseVo {

    /**
     * 请求处理的结果，OK 表示处理成功，FAIL 表示失败
     */
    private String ActionStatus;

    /**
     * 错误码，0表示成功，非0表示失败
     */
    private Integer ErrorCode;

    /**
     * 错误信息
     */
    private String ErrorInfo;

    /**
     * 群组总数
     */
    private Integer TotalCount;


    /*** 本群组的群成员总数 */
    private List<GroupIdS> GroupIdList;

    /**
     * 分页拉取的标志
     */
    private Integer Next;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class GroupIdS{
        /**
         * id
         */
        private String GroupId;
    }


}
