package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 退货单处理状态 0暂存 1退货中 2已退货 3已取消
 * <AUTHOR>
 */

@Getter
public enum DeliveryOrderStateEnum {

    TEMPORARY(0, "暂存"),
    RETURNING(1, "退货中"),
    RETURNED(2, "已退货"),
    CANCEL(3, "已取消"),
    ;

    private Integer code;

    private String desc;

    DeliveryOrderStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (DeliveryOrderStateEnum a : DeliveryOrderStateEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
