package com.dsj.inventory.bussiness.inventory.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.dsj.inventory.framework.config.easyExcel.LocalDateTimeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.time.LocalDateTime;

/**
 * 损溢单导出
 * <AUTHOR>
 * @date 2023/04/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="GainsLossesOrderExportVo", description="损溢单导出实体")
@ToString(callSuper = true)
@ColumnWidth(25)
@HeadRowHeight(20)
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER)//内容样式
public class GainsLossesOrderExportVo {

    @ApiModelProperty(value = "损溢单号")
    @ExcelProperty(value = "单号")
    private String code;

    @ApiModelProperty(value = "创建人名称")
    @ExcelProperty(value = "报损报溢人")
    private String createUserName;

    @ApiModelProperty(value = "处理状态 0暂存 1完成")
    @ExcelProperty(value = "处理状态")
    private String dealState;

    @ApiModelProperty(value = "损溢类型 1损2溢")
    @ExcelProperty(value = "损溢类型")
    private String type;

    @ApiModelProperty(value = "损溢商品名")
    @ExcelProperty(value = "损溢商品名")
    private String goodsNames;

    @ExcelProperty(value = "报损总数")
    private String lossesAmount;

    @ExcelProperty(value = "报溢总数")
    private String gainsAmount;

    @ApiModelProperty(value = "成本金额（成本均价总额）")
    @ExcelProperty(value = "成本金额")
    private String costTotalPrice;

    @ApiModelProperty(value = "零售总金额（私域商品总金额）")
    @ExcelProperty(value = "零售总金额")
    private String totalPrice;

    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "创建时间",converter = LocalDateTimeConverter.class)
    private LocalDateTime createTime;

}
