package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 优惠券类型 1代金券 2折扣券
 * <AUTHOR>
 */

@Getter
public enum SalesCouponTypeEnum {

    VOUCHER(1, "代金券"),
    COUPON(2, "折扣券"),
    ;

    private Integer code;

    private String desc;

    SalesCouponTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (SalesCouponTypeEnum a : SalesCouponTypeEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
