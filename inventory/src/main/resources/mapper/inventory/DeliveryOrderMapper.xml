<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.DeliveryOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.DeliveryOrder">
    <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="order_id" property="orderId" />
        <result column="code" property="code" />
        <result column="bill_date" property="billDate" />
        <result column="remark" property="remark" />
        <result column="deal_state" property="dealState" />
        <result column="organization_supplier_id" property="organizationSupplierId" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        organization_id, order_id, code, bill_date, remark, deal_state, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="queryDeliveryOrderPage" resultType="com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderVo">
        SELECT distinct jdo.id
        ,jdo.organization_id organizationId
        ,jdo.order_id orderId
        ,jdo.order_code orderCode
        ,jdo.code
        ,jdo.remark
        ,jdo.deal_state dealState
        ,jdo.organization_supplier_id organizationSupplierId
        ,jos.name organizationSupplierName
        ,jos.code organizationSupplierCode
        ,jdo.organization_supplier_member_id organizationSupplierMemberId
        ,josm.name organizationSupplierMemberName
        ,jdo.return_total_price returnTotalPrice
        ,jdo.return_cost_total_price returnCostTotalPrice
        ,jdo.disparity_price disparityPrice
        ,jdo.reason
        ,jdo.create_user createUser
        ,su.name createUserName
        ,jdo.create_time createTime
        FROM jxc_delivery_order jdo
        LEFT JOIN sys_user su ON su.id = jdo.create_user AND su.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jdo.organization_supplier_id AND jos.is_delete = 0
        LEFT JOIN jxc_organization_supplier_member josm ON josm.id = jdo.organization_supplier_member_id AND josm.is_delete = 0
        WHERE
        jdo.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jdo.organization_id = #{param.organizationId}
        </if>
        <if test="param.dealState != null ">
            AND jdo.deal_state = #{param.dealState}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jdo.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.createUserNameLike != null and param.createUserNameLike != '' ">
            AND su.name like CONCAT('%',#{param.createUserNameLike},'%')
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jdo.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        ORDER BY jdo.create_time desc
    </select>

    <select id="getDeliveryOrderById" resultType="com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderDetailVo">
        SELECT distinct jdo.id
                      ,jdo.organization_id organizationId
                      ,jdo.order_id orderId
                      ,jdo.order_code orderCode
                      ,jdo.code
                      ,jdo.bill_date billDate
                      ,jdo.remark
                      ,jdo.deal_state dealState
                      ,jdo.organization_supplier_id organizationSupplierId
                      ,jos.name organizationSupplierName
                      ,jos.code organizationSupplierCode
                      ,jdo.organization_supplier_member_id organizationSupplierMemberId
                      ,josm.name organizationSupplierMemberName
                      ,jdo.return_total_price returnTotalPrice
                      ,jdo.return_cost_total_price returnCostTotalPrice
                      ,jdo.disparity_price disparityPrice
                      ,jdo.transport_way transportWay
                      ,jdo.transport_order_code transportOrderCode
                      ,jdo.reason
                      ,jdo.create_user createUser
                      ,su.name createUserName
                      ,jdo.create_time createTime
        FROM jxc_delivery_order jdo
        LEFT JOIN jxc_incoming_order jio ON jio.id = jdo.order_id AND jio.is_delete = 0
        LEFT JOIN sys_user su ON su.id = jdo.create_user AND su.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jdo.organization_supplier_id AND jos.is_delete = 0
        LEFT JOIN jxc_organization_supplier_member josm ON josm.id = jdo.organization_supplier_member_id AND josm.is_delete = 0
        WHERE
            jdo.is_delete = 0
        AND jdo.id = #{id}
    </select>

    <select id="queryDeliveryOrderList" resultType="com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderExportVo">
        SELECT distinct jdo.id
        ,jdo.code
        ,jdo.remark
        ,(CASE
        WHEN jdo.deal_state = 0 THEN '暂存'
        WHEN jdo.deal_state = 1 THEN '退货中'
        WHEN jdo.deal_state = 2 THEN '已退货'
        WHEN jdo.deal_state = 3 THEN '已取消'
        END) dealState
        ,josm.name organizationSupplierMemberName
        ,ROUND(jdo.return_total_price/100,2) returnTotalPrice
        ,ROUND(jdo.return_cost_total_price/100,2) returnCostTotalPrice
        ,ROUND(jdo.disparity_price/100,2) disparityPrice
        ,jdo.reason
        ,jos.name organizationSupplierName
        ,jos.code organizationSupplierCode
        ,su.name createUserName
        ,jdo.create_time createTime
        FROM jxc_delivery_order jdo
        LEFT JOIN sys_user su ON su.id = jdo.create_user AND su.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jdo.organization_supplier_id AND jos.is_delete = 0
        LEFT JOIN jxc_organization_supplier_member josm ON josm.id = jdo.organization_supplier_member_id AND josm.is_delete = 0
        WHERE
        jdo.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jdo.organization_id = #{param.organizationId}
        </if>
        <if test="param.dealState != null ">
            AND jdo.deal_state = #{param.dealState}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jdo.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.createUserNameLike != null and param.createUserNameLike != '' ">
            AND su.name like CONCAT('%',#{param.createUserNameLike},'%')
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jdo.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        ORDER BY jdo.create_time desc
    </select>

    <select id="countReturnedAmount" resultType="java.lang.Double">
        SELECT
        ROUND(SUM(jdog.return_amount),2) returnAmount
        FROM
        jxc_delivery_order_goods jdog
        INNER JOIN jxc_delivery_order jdo ON jdog.order_id = jdo.id
        WHERE jdog.is_delete = 0
        AND jdo.is_delete = 0
        AND jdo.deal_state = 2
        AND jdog.incoming_goods_id = #{incomingGoodsId}
    </select>

    <update id="deleteDeliveryOrderByOrganizationId">
        UPDATE jxc_delivery_order jdo, jxc_delivery_order_goods jdog
        SET jdo.is_delete = 1, jdog.is_delete = 1
        WHERE jdo.id = jdog.order_id
          AND jdo.is_delete = 0
          AND jdog.is_delete = 0
          AND jdo.organization_id = #{organizationId}
    </update>

    <select id="getDeliveryOrderCount" resultType="java.lang.Integer">
        SELECT COUNT(1) number
        FROM jxc_delivery_order jdo
        WHERE
        DATE_FORMAT(jdo.create_time,'%Y-%m-%d') = #{day}
        <if test="organizationId != null and organizationId != '' ">
            AND jdo.organization_id = #{organizationId}
        </if>
    </select>

</mapper>
