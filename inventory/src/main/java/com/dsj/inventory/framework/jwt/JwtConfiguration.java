package com.dsj.inventory.framework.jwt;


import com.dsj.inventory.common.cache.CacheRepository;
import com.dsj.inventory.framework.jwt.JwtProperties;
import com.dsj.inventory.framework.jwt.TokenUtil;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * 认证服务端配置
 *
 * <AUTHOR>
 * @date 2018/11/20
 */
@EnableConfigurationProperties(value = {
        JwtProperties.class,
})
public class JwtConfiguration {

    @Bean
    public TokenUtil getTokenUtil(JwtProperties authServerProperties, CacheRepository cacheRepository) {
        return new TokenUtil(authServerProperties,cacheRepository);
    }
}
