package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 进销存会员充值记录信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_member_recharge_record")
@ApiModel(value="MemberRechargeRecord", description="进销存会员充值记录信息")
public class MemberRechargeRecord extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "会员Id")
    @TableField("member_id")
    private Long memberId;

    @ApiModelProperty(value = "支付方式 1现金")
    @TableField("pay_way")
    private Integer payWay;

    @ApiModelProperty(value = "充值类型 1金额")
    @TableField("recharge_type")
    private Integer rechargeType;

    @ApiModelProperty(value = "充值金额")
    @TableField("recharge_price")
    private Integer rechargePrice;

    @ApiModelProperty(value = "赠送金额")
    @TableField("give_price")
    private Integer givePrice;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
