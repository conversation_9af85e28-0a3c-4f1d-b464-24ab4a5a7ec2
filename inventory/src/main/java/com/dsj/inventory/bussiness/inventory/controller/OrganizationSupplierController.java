package com.dsj.inventory.bussiness.inventory.controller;


import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pocky.transport.bussiness.diagnose.param.DisableParam;
import com.dsj.inventory.bussiness.inventory.param.QueryOrganizationSupplierParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateOrganizationSupplierParam;
import com.dsj.inventory.bussiness.inventory.service.OrganizationSupplierService;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationSupplierExportVo;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationSupplierVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.EasyExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 供应商组织私域 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@RestController
@Api(value = "OrganizationSupplierController", tags = "供应商组织私域api")
public class OrganizationSupplierController {

    @Autowired
    private OrganizationSupplierService organizationSupplierService;

    /*新增供应商组织私域*/
    @PostMapping("/organization-supplier")
    @ApiOperation(value = "新增供应商组织私域", notes = "新增供应商组织私域")
    public ResponseEntity<OrganizationSupplierVo> saveOrganizationSupplier(@RequestBody SaveOrUpdateOrganizationSupplierParam param){
        OrganizationSupplierVo organizationSupplierVo = organizationSupplierService.saveOrUpdateOrganizationSupplier(param);
        return Res.success(organizationSupplierVo);
    }

    /*编辑供应商组织私域*/
    @PutMapping("/organization-supplier/{id}")
    @ApiOperation(value = "编辑供应商组织私域", notes = "编辑供应商组织私域")
    public ResponseEntity<OrganizationSupplierVo> updateOrganizationSupplier(@PathVariable Long id, @RequestBody SaveOrUpdateOrganizationSupplierParam param){
        param.setId(id);
        OrganizationSupplierVo organizationSupplierVo = organizationSupplierService.saveOrUpdateOrganizationSupplier(param);
        return Res.success(organizationSupplierVo);
    }

    /*删除供应商组织私域*/
    @DeleteMapping("/organization-supplier/{id}")
    @ApiOperation(value = "删除供应商组织私域", notes = "删除供应商组织私域")
    public ResponseEntity<Void> deleteOrganizationSupplier(@PathVariable Long id){
        organizationSupplierService.deleteOrganizationSupplier(id);
        return Res.success();
    }

    /*获取供应商组织私域列表*/
    @GetMapping("/organization-supplier")
    @ApiOperation(value = "获取供应商组织私域列表", notes = "获取供应商组织私域列表")
    public ResponseEntity<List<OrganizationSupplierVo>> queryOrganizationSupplierList(QueryOrganizationSupplierParam param){
        Page<OrganizationSupplierVo> page = organizationSupplierService.queryOrganizationSupplierPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取供应商组织私域详情*/
    @GetMapping("/organization-supplier/{id}")
    @ApiOperation(value = "获取供应商组织私域详情", notes = "获取供应商组织私域详情")
    public ResponseEntity<OrganizationSupplierVo> queryOrganizationSupplier(@PathVariable Long id){
        return Res.success(organizationSupplierService.getOrganizationSupplierById(id));
    }

    /*变更供应商组织私域禁用状态 --禁用或开启*/
    @PostMapping("/organization-supplier/{id}/action-disable")
    @ApiOperation(value = "变更供应商组织私域禁用状态", notes = "变更供应商组织私域禁用状态 --禁用或开启")
    public ResponseEntity<Void> disableOrganizationSupplier(@PathVariable Long id, @RequestBody DisableParam param){
        param.setIds(ListUtil.toList(id));
        organizationSupplierService.disableOrganizationSupplier(param);
        return Res.success();
    }

    /*供应商组织私域绑定*/
    @PostMapping("/organization-supplier/{serialNo}/action-bind/{organizationId}")
    @ApiOperation(value = "供应商组织私域绑定", notes = "供应商组织私域绑定 --绑定公域供应商")
    public ResponseEntity<Void> bindOrganizationSupplier(@PathVariable String serialNo,@PathVariable Long organizationId){
        organizationSupplierService.bindOrganizationSupplier(serialNo,organizationId);
        return Res.success();
    }

    /*供应商组织私域导出*/
    @GetMapping("/organization-supplier/action-export")
    @ApiOperation(value = "导出供应商组织私域", notes = "导出供应商组织私域")
    public ResponseEntity<Void> exportOrganizationSupplier(HttpServletResponse response, QueryOrganizationSupplierParam param) throws IOException {
        param.setNeedExport(true);
        List<OrganizationSupplierExportVo> list = organizationSupplierService.queryOrganizationSupplierExportList(param);
        EasyExcelUtils.exportOrdinaryExcel(response,list,"供应商组织私域导出列表","供应商组织私域导出列表", OrganizationSupplierExportVo.class);
        return Res.success();
    }

}
