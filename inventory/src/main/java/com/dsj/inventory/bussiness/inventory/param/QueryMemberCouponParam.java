package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询会员优惠券
 * <AUTHOR>
 * @date 2023/04/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QueryMemberCouponParam",description = "查询会员优惠券参数")
public class QueryMemberCouponParam extends BasePage {

    @ApiModelProperty(value = "模块分类，1 云药快 ；2 外部；3 在线医院；4 商城；5进销存")
    private Integer moduleType;

    @ApiModelProperty(value = "会员Id")
    private Long memberId;

    @ApiModelProperty(value = "系统用户Id")
    private Long userId;

    @ApiModelProperty(value = "优惠券Id")
    private Long couponId;

    @ApiModelProperty(value = "优惠券状态 0未使用 1已使用 2已过期")
    private Integer couponState;

    @ApiModelProperty(value = "商品goodsIds")
    private String goodsIds;

    @ApiModelProperty(value = "领取时用户所属组织")
    private Long getOrganizationId;

    @ApiModelProperty(value = "优惠券组织Id")
    private Long organizationId;
}
