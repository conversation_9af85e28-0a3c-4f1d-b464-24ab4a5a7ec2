package com.dsj.inventory.bussiness.quality.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "拒收单查询参数")
public class RejectionOrderQueryParam extends BasePage {

    @ApiModelProperty(value = "来源单据号")
    private String sourceOrderCode;

    @ApiModelProperty(value = "供应商ID")
    private Long supplierId;

    @ApiModelProperty(value = "状态 (1:待审核, 2:审核通过, 3:审核驳回)")
    private Integer status;

    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;
} 