package com.dsj.inventory.framework.exception;


import com.dsj.inventory.framework.exception.BaseUncheckedException;

/**
 * 业务中校验参数异常
 * 用于在处理业务逻辑时，进行抛出的异常。
 * <AUTHOR>
 */
public class ParamException extends BaseUncheckedException {

    private static final long serialVersionUID = -3843907364558373817L;

    public ParamException(String message) {
        super(406, message);
    }

    public ParamException(int code, String message) {
        super(code, message);
    }


    @Override
    public String toString() {
        return "BizException [message=" + message + ", code=" + code + "]";
    }

}
