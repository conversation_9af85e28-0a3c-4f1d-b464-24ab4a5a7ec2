package com.dsj.inventory.common.entity;

import com.dsj.inventory.common.entity.IMMemberInfoVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * IM 群成员信息返回实体
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class IMGroupMemberResponseVo {

    /**
     * 请求处理的结果，OK 表示处理成功，FAIL 表示失败
     */
    private String ActionStatus;

    /**
     * 错误码，0表示成功，非0表示失败
     */
    private Integer ErrorCode;

    /**
     * 错误信息
     */
    private String ErrorInfo;


    /*** 本群组的群成员总数 */
    private Integer MemberNum;

    /*** 本群组的群成员总数 */
    private List<IMMemberInfoVo> MemberList;

}
