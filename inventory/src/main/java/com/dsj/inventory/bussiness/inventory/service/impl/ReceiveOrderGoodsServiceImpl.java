package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.ReceiveOrderGoods;
import com.dsj.inventory.bussiness.inventory.mapper.ReceiveOrderGoodsMapper;
import com.dsj.inventory.bussiness.inventory.service.ReceiveOrderGoodsService;
import com.dsj.inventory.common.enumeration.TrueEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 进销存收货单商品明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
@Service
public class ReceiveOrderGoodsServiceImpl extends ServiceImpl<ReceiveOrderGoodsMapper, ReceiveOrderGoods> implements ReceiveOrderGoodsService {

    @Autowired
    private ReceiveOrderGoodsMapper receiveOrderGoodsMapper;

    @Override
    public void deleteByOrderId(Long orderId) {
        receiveOrderGoodsMapper.markAsDeletedByOrderId(orderId);
    }

    @Override
    public List<ReceiveOrderGoods> listByOrderId(Long orderId) {
        return receiveOrderGoodsMapper.selectListByOrderId(orderId);
    }

    @Override
    public void markAsDeletedByOrderId(Long orderId) {
        receiveOrderGoodsMapper.markAsDeletedByOrderId(orderId);
    }

    @Override
    public ReceiveOrderGoods getByOrderIdAndOrgMedicineId(Long orderId, Long organizationMedicineId) {
        return receiveOrderGoodsMapper.selectByOrderIdAndOrgMedicineId(orderId, organizationMedicineId);
    }
}
