# 采购计划功能增强开发任务清单

## 项目概述
基于OrganizationDict实现采购计划参数管理，增强采购计划功能，包括自动生成、价格策略、GSP校验等核心功能。

---

## 第一阶段：基础功能完善 (2-3天)

### task001 - 参数常量类定义
**任务名称**: 创建采购计划参数常量类
**任务描述**: 在`com.dsj.inventory.bussiness.purchaseplan.constant`包下创建`PurchasePlanParamConstants`类，定义所有采购计划相关的参数常量。
**版本状态**: 完成

**验收标准清单**:
- [ ] 定义字典类型常量`DICT_TYPE_PURCHASE_PLAN = "PURCHASE_PLAN_CONFIG"`
- [ ] 定义所有参数编码常量（PRICE_STRATEGY、LOWEST_PRICE_DAYS等）
- [ ] 定义所有参数描述常量
- [ ] 定义所有参数默认值常量
- [ ] 提供`getDefaultParameterMap()`方法返回默认参数映射
- [ ] 提供`getParameterDescriptionMap()`方法返回参数描述映射
- [ ] 添加完整的JavaDoc注释

**注意事项**:
- 参数编码使用大写字母和下划线命名规范
- 默认值必须是字符串类型，便于存储到OrganizationDict
- 确保所有参数都有对应的描述信息
- 参考现有的枚举类风格进行编写

### task002 - 参数管理服务接口
**任务名称**: 创建采购计划参数管理服务接口
**任务描述**: 在`com.dsj.inventory.bussiness.purchaseplan.service`包下创建`PurchasePlanParameterService`接口。
**版本状态**: 完成

**验收标准清单**:
- [ ] 定义基础参数操作方法（获取、更新、初始化、检查存在性）
- [ ] 定义便捷方法获取具体业务参数（价格策略、天数配置等）
- [ ] 所有方法都有完整的JavaDoc注释
- [ ] 参数校验和异常处理说明清晰
- [ ] 方法命名符合业务语义

**注意事项**:
- 接口方法要考虑参数为空的情况
- 便捷方法要覆盖所有业务参数
- 返回值类型要明确（Integer、String、Boolean）
- 参考现有Service接口的命名风格

### task003 - 参数管理服务实现
**任务名称**: 实现采购计划参数管理服务  
**任务描述**: 在`com.dsj.inventory.bussiness.purchaseplan.service.impl`包下创建`PurchasePlanParameterServiceImpl`类。  
**版本状态**: 计划中

**验收标准清单**:
- [ ] 注入OrganizationDictService依赖
- [ ] 实现所有接口方法
- [ ] 添加参数类型转换和异常处理
- [ ] 实现参数初始化逻辑，避免重复创建
- [ ] 添加日志记录（debug和info级别）
- [ ] 实现参数缓存机制（可选）

**注意事项**:
- 使用@Service注解和@Slf4j日志
- 参数转换失败时要有降级处理（返回默认值）
- 初始化参数时要检查是否已存在
- 更新参数时要区分新增和修改场景
- 参考现有ServiceImpl的代码风格

### task004 - 数据库表结构扩展
**任务名称**: 扩展采购计划相关表字段  
**任务描述**: 为jxc_purchase_plan和jxc_purchase_plan_goods表添加新功能所需的字段。  
**版本状态**: 计划中

**验收标准清单**:
- [ ] 扩展jxc_purchase_plan表（auto_generated、gsp_check_status、check_message）
- [ ] 扩展jxc_purchase_plan_goods表（supplier_id、in_transit_quantity等）
- [ ] 添加必要的索引优化查询性能
- [ ] 编写数据库迁移脚本
- [ ] 测试脚本在开发和测试环境的执行

**注意事项**:
- 新增字段要有默认值，确保兼容性
- 索引设计要考虑查询场景
- 字段注释要清晰明确
- 迁移脚本要支持回滚

### task005 - 实体类字段扩展
**任务名称**: 扩展PurchasePlan和PurchasePlanGoods实体类  
**任务描述**: 为实体类添加新增的数据库字段对应的属性。  
**版本状态**: 计划中

**验收标准清单**:
- [ ] 在PurchasePlan类中添加auto_generated、gsp_check_status、check_message字段
- [ ] 在PurchasePlanGoods类中添加所有新增字段
- [ ] 添加@ApiModelProperty注解和字段注释
- [ ] 确保字段类型和数据库一致
- [ ] 更新相关的VO和Param类

**注意事项**:
- 保持现有的注解风格（@Data、@Builder等）
- 字段命名使用驼峰命名法
- @TableField注解要指定数据库字段名
- 新增字段要考虑序列化兼容性

---

## 第二阶段：核心业务功能 (4-5天)

### task006 - 进货单价策略服务接口
**任务名称**: 创建进货单价策略服务接口  
**任务描述**: 在service包下创建`PurchasePriceStrategyService`接口，定义三种价格策略的方法。  
**版本状态**: 计划中

**验收标准清单**:
- [ ] 定义根据策略获取进货单价的主方法
- [ ] 定义三种具体策略方法（最低进价、最新供应商、上次进价）
- [ ] 定义获取商品最近使用供应商的方法
- [ ] 所有方法都有详细的参数说明和返回值说明
- [ ] 定义价格策略枚举或常量

**注意事项**:
- 价格单位统一使用分（Integer类型）
- 方法要考虑商品或供应商不存在的情况
- 策略选择要支持参数化配置
- 接口设计要便于扩展新的价格策略

### task007 - 进货单价策略服务实现
**任务名称**: 实现进货单价策略服务  
**任务描述**: 实现PurchasePriceStrategyService接口，编写三种价格策略的具体逻辑。  
**版本状态**: 计划中

**验收标准清单**:
- [ ] 实现按最低进价策略（查询N天内最低价格）
- [ ] 实现按最新供应商策略（查询最近采购记录的价格）
- [ ] 实现按上次进价策略（查询同供应商上次价格）
- [ ] 实现供应商推荐逻辑
- [ ] 添加数据库查询优化
- [ ] 添加异常处理和日志记录

**注意事项**:
- 查询要考虑组织隔离
- 时间范围查询要使用参数配置
- 价格为空时要有默认处理逻辑
- 查询性能要优化，避免大数据量查询
- 要注入PurchasePlanParameterService获取配置

### task008 - 库存计算服务接口
**任务名称**: 创建库存计算服务接口  
**任务描述**: 在service包下创建`InventoryCalculationService`接口，定义库存相关计算方法。  
**版本状态**: 计划中

**验收标准清单**:
- [ ] 定义在途数量计算方法
- [ ] 定义日均销量计算方法
- [ ] 定义库存上下限计算方法
- [ ] 定义推荐采购数量计算方法
- [ ] 定义获取当前库存方法
- [ ] 所有计算方法都有明确的算法说明

**注意事项**:
- 计算方法要考虑断货天数的影响
- 数量类型统一使用Double
- 计算结果要进行合理性校验
- 接口要支持批量计算优化性能

### task009 - 库存计算服务实现
**任务名称**: 实现库存计算服务  
**任务描述**: 实现InventoryCalculationService接口，编写各种库存计算的具体算法。  
**版本状态**: 计划中

**验收标准清单**:
- [ ] 实现在途数量统计（查询已下单未入库的数量）
- [ ] 实现日均销量计算（考虑断货天数）
- [ ] 实现库存上下限计算（天数×日均销量）
- [ ] 实现推荐采购数量计算（上限-当前-在途）
- [ ] 实现当前库存查询
- [ ] 添加计算结果的边界值处理

**注意事项**:
- 销量统计要排除异常数据
- 断货天数计算要准确
- 计算结果不能为负数
- 要考虑商品停售、下架等状态
- 大批量计算要考虑性能优化

### task010 - 自动生成采购计划服务接口
**任务名称**: 创建自动生成采购计划服务接口  
**任务描述**: 在service包下创建`AutoGeneratePurchasePlanService`接口。  
**版本状态**: 计划中

**验收标准清单**:
- [ ] 定义自动生成采购计划主方法
- [ ] 定义获取低库存商品列表方法
- [ ] 定义批量计算采购计划明细方法
- [ ] 定义自动生成商品信息DTO类
- [ ] 定义生成规则配置方法

**注意事项**:
- 自动生成要支持按供应商分组
- 要考虑商品的采购权限和状态
- 生成结果要支持用户确认和修改
- 接口要支持预览模式（不实际保存）

### task011 - 自动生成采购计划服务实现
**任务名称**: 实现自动生成采购计划服务
**任务描述**: 实现AutoGeneratePurchasePlanService接口，编写自动生成的核心算法。
**版本状态**: 计划中

**验收标准清单**:
- [ ] 实现低库存商品筛选逻辑
- [ ] 实现采购计划自动计算算法
- [ ] 实现按供应商分组生成多个计划单
- [ ] 实现商品信息自动填充
- [ ] 实现生成结果的数据校验
- [ ] 添加生成过程的日志记录

**注意事项**:
- 要集成价格策略服务和库存计算服务
- 生成的计划单要符合业务规则
- 要处理商品无供应商的情况
- 生成数量要合理，避免过量采购
- 要考虑组织的采购权限和限制

---

## 第三阶段：校验和导入功能 (3-4天)

### task012 - GSP校验服务接口
**任务名称**: 创建GSP合规校验服务接口
**任务描述**: 在service包下创建`GspComplianceService`接口，定义GSP校验相关方法。
**版本状态**: 计划中

**验收标准清单**:
- [ ] 定义采购计划GSP校验主方法
- [ ] 定义供应商资质校验方法
- [ ] 定义商品资质校验方法
- [ ] 定义经营范围匹配校验方法
- [ ] 定义GSP校验结果DTO类
- [ ] 定义批量校验方法

**注意事项**:
- 校验规则要支持参数化配置
- 校验结果要包含详细的错误信息
- 要支持不同的校验模式（拦截/提示/忽略）
- 接口要支持异步校验

### task013 - GSP校验服务实现
**任务名称**: 实现GSP合规校验服务
**任务描述**: 实现GspComplianceService接口，编写各种GSP校验逻辑。
**版本状态**: 计划中

**验收标准清单**:
- [ ] 实现供应商证照效期校验
- [ ] 实现商品证照效期校验
- [ ] 实现供应商经营范围校验
- [ ] 实现企业经营范围校验
- [ ] 实现校验结果汇总和处理
- [ ] 添加校验缓存机制

**注意事项**:
- 证照效期要考虑提前预警
- 经营范围匹配要精确
- 校验失败要有明确的提示信息
- 要集成参数管理服务获取校验模式
- 校验性能要优化，支持批量处理

### task014 - Excel导入功能实现
**任务名称**: 实现采购计划Excel导入功能
**任务描述**: 在service包下创建`PurchasePlanImportService`，实现Excel导入采购计划功能。
**版本状态**: 计划中

**验收标准清单**:
- [ ] 设计Excel导入模板格式
- [ ] 实现Excel文件解析逻辑
- [ ] 实现数据校验和清洗
- [ ] 实现导入结果反馈
- [ ] 实现导入失败的错误处理
- [ ] 提供导入模板下载功能

**注意事项**:
- 模板格式要简单易用
- 数据校验要全面（商品存在性、供应商匹配等）
- 导入要支持大批量数据
- 错误信息要定位到具体行列
- 导入过程要有进度反馈

### task015 - 控制器接口扩展
**任务名称**: 扩展PurchasePlanController接口
**任务描述**: 在现有控制器中添加新功能的API接口。
**版本状态**: 计划中

**验收标准清单**:
- [ ] 添加自动生成采购计划接口
- [ ] 添加GSP校验接口
- [ ] 添加Excel导入接口
- [ ] 添加参数配置管理接口
- [ ] 添加价格策略测试接口
- [ ] 完善API文档注解

**注意事项**:
- 接口要有权限控制注解
- 参数校验要完整
- 返回结果要统一格式
- 要添加操作日志记录
- API文档要详细清晰

---

## 第四阶段：测试和优化 (2-3天)

### task016 - 单元测试编写
**任务名称**: 编写核心服务的单元测试
**任务描述**: 为所有新增的服务类编写完整的单元测试。
**版本状态**: 计划中

**验收标准清单**:
- [ ] 参数管理服务测试（覆盖率>80%）
- [ ] 价格策略服务测试（覆盖率>80%）
- [ ] 库存计算服务测试（覆盖率>80%）
- [ ] 自动生成服务测试（覆盖率>80%）
- [ ] GSP校验服务测试（覆盖率>80%）
- [ ] 异常场景测试覆盖

**注意事项**:
- 使用Mock对象模拟依赖
- 测试用例要覆盖边界值
- 异常测试要全面
- 测试数据要真实可信
- 测试要独立，不依赖外部环境

### task017 - 集成测试验证
**任务名称**: 编写集成测试验证整体功能
**任务描述**: 编写端到端的集成测试，验证各个服务协同工作。
**版本状态**: 计划中

**验收标准清单**:
- [ ] 自动生成采购计划端到端测试
- [ ] 价格策略集成测试
- [ ] GSP校验流程测试
- [ ] Excel导入完整流程测试
- [ ] 参数配置生效测试
- [ ] 性能基准测试

**注意事项**:
- 测试环境要接近生产环境
- 测试数据要充分
- 性能测试要有明确指标
- 测试要可重复执行
- 要测试并发场景

### task018 - 性能优化和调优
**任务名称**: 性能优化和系统调优
**任务描述**: 对新增功能进行性能分析和优化。
**版本状态**: 计划中

**验收标准清单**:
- [ ] 数据库查询优化（索引、SQL调优）
- [ ] 缓存策略实施
- [ ] 批量操作优化
- [ ] 内存使用优化
- [ ] 响应时间优化
- [ ] 并发性能测试

**注意事项**:
- 优化要基于实际性能测试结果
- 不能为了性能牺牲数据一致性
- 缓存策略要考虑数据更新
- 要监控系统资源使用情况
- 优化后要重新测试功能正确性

### task019 - 文档完善和代码审查
**任务名称**: 完善技术文档和代码审查
**任务描述**: 完善所有技术文档，进行代码审查和质量检查。
**版本状态**: 计划中

**验收标准清单**:
- [ ] API接口文档完善
- [ ] 数据库设计文档更新
- [ ] 部署文档编写
- [ ] 用户操作手册更新
- [ ] 代码审查通过
- [ ] 代码质量检查通过

**注意事项**:
- 文档要及时更新，保持同步
- 代码要符合团队规范
- 注释要清晰完整
- 要有版本变更说明
- 部署脚本要经过验证

### task020 - 生产环境部署准备
**任务名称**: 生产环境部署准备和验证
**任务描述**: 准备生产环境部署所需的所有资源和验证。
**版本状态**: 计划中

**验收标准清单**:
- [ ] 数据库迁移脚本准备
- [ ] 配置文件准备
- [ ] 部署脚本编写
- [ ] 回滚方案准备
- [ ] 生产环境验证测试
- [ ] 监控告警配置

**注意事项**:
- 部署要支持灰度发布
- 回滚方案要经过验证
- 要有详细的部署检查清单
- 监控要覆盖关键指标
- 要准备应急响应预案

---

## 总结

**总任务数**: 20个
**预计总工期**: 11-15天
**关键里程碑**:
- 第一阶段完成：基础参数管理功能可用
- 第二阶段完成：核心业务功能可用
- 第三阶段完成：完整功能可用
- 第四阶段完成：生产就绪

**风险控制**:
- 每个阶段完成后进行功能验证
- 关键功能要有备选方案
- 性能问题要及时发现和解决
- 数据安全要重点关注
