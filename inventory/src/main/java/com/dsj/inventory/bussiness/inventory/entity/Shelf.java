package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 进销存货架信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_shelf")
@ApiModel(value="Shelf", description="进销存货架信息")
public class Shelf extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "货架名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "父级id")
    @TableField("parent_id")
    private Long parentId;

    @ApiModelProperty(value = "根父级级到本级id串")
    @TableField("level_ids")
    private String levelIds;

    @ApiModelProperty(value = "货架类型 0普通货架1特殊货架")
    @TableField("type")
    private Integer type;

    @ApiModelProperty(value = "层级")
    @TableField("level")
    private Integer level;

    @ApiModelProperty(value = "是否允许删除，0否1是")
    @TableField("delete_flag")
    private Integer deleteFlag;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
