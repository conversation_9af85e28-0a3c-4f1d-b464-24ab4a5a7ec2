package com.dsj.inventory.bussiness.quality.enumeration;

import lombok.Getter;

/**
 * 供应商首营审批步骤枚举
 */
@Getter
public enum SupplierFirstApprovalStepEnum {
    
    PURCHASE_MANAGER_REVIEW(1, "采购经理审核"),
    QUALITY_MANAGER_REVIEW(2, "质量经理审核"),
    QUALITY_SUPERVISOR_REVIEW(3, "质量负责人审核"),
    COMPLETED(4, "审批完成");
    
    private final Integer code;
    private final String desc;
    
    SupplierFirstApprovalStepEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return "";
        }
        for (SupplierFirstApprovalStepEnum step : values()) {
            if (step.getCode().equals(code)) {
                return step.getDesc();
            }
        }
        return "";
    }
} 