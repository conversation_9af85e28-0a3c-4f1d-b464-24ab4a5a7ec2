<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.CheckMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.Check">
    <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="code" property="code" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        organization_id, code, remark, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="queryCheckPage" resultType="com.dsj.inventory.bussiness.inventory.vo.CheckVo">
        SELECT distinct jc.id
        ,jc.organization_id organizationId
        ,jc.code
        ,jc.name
        ,jc.check_state checkState
        ,jc.remark
        ,jc.shelf_ids shelfIds
        ,(CASE WHEN jc.shelf_ids = '1' THEN '全部' ELSE GROUP_CONCAT(distinct js.name) END) shelfNames
        ,su.name createUserName
        ,jc.check_ids checkIds
        ,GROUP_CONCAT(distinct csu.name) checkNames
        ,jc.check_date checkDate
        ,jc.create_time createTime
        FROM jxc_check jc
        LEFT JOIN sys_user su ON su.id = jc.create_user AND su.is_delete = 0
        LEFT JOIN jxc_shelf js ON FIND_IN_SET(js.id,jc.shelf_ids) AND js.is_delete = 0
        LEFT JOIN sys_user csu ON FIND_IN_SET(csu.id,jc.check_ids) AND csu.is_delete = 0
        WHERE
        jc.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jc.organization_id = #{param.organizationId}
        </if>
        <if test="param.checkState != null ">
            AND jc.check_state = #{param.checkState}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jc.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.nameLike != null and param.nameLike != '' ">
            AND jc.name like CONCAT('%',#{param.nameLike},'%')
        </if>
        <if test="param.createUserNameLike != null and param.createUserNameLike != '' ">
            AND su.name like CONCAT('%',#{param.createUserNameLike},'%')
        </if>
        <if test="param.checkNameLike != null and param.checkNameLike != '' ">
            AND csu.name like CONCAT('%',#{param.checkNameLike},'%')
        </if>
        <if test="param.checkId != null and param.checkId != '' ">
            AND FIND_IN_SET (#{param.checkId},jc.check_ids)
        </if>
        <choose>
            <when test="param.shelfId != null and param.shelfId != '1'  ">
                AND (FIND_IN_SET (#{param.shelfId},jc.shelf_ids) OR jc.shelf_ids = '1')
            </when>
            <when test="param.shelfId != null and param.shelfId == '1' ">
                AND jc.shelf_ids = #{param.shelfId}
            </when>
        </choose>
        <if test="param.createTime != null and param.createTime != '' ">
            AND DATE_FORMAT(jc.create_time,"%Y-%m-%d") = #{param.createTime}
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jc.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        GROUP BY jc.id
        ORDER BY jc.create_time desc
    </select>

    <select id="getCheckById" resultType="com.dsj.inventory.bussiness.inventory.vo.CheckDetailVo">
        SELECT distinct jc.id
                      ,jc.organization_id organizationId
                      ,jc.code
                      ,jc.name
                      ,jc.check_state checkState
                      ,jc.remark
                      ,jc.shelf_ids shelfIds
                      ,(CASE WHEN jc.shelf_ids = '1' THEN '全部' ELSE GROUP_CONCAT(distinct js.name) END) shelfNames
                      ,su.name createUserName
                      ,jc.check_ids checkIds
                      ,GROUP_CONCAT(distinct csu.name) checkNames
                      ,jc.check_date checkDate
                      ,jc.gains_id gainsId
                      ,jc.losses_id lossesId
                      ,jc.create_time createTime
        FROM jxc_check jc
        LEFT JOIN sys_user su ON su.id = jc.create_user AND su.is_delete = 0
        LEFT JOIN jxc_shelf js ON FIND_IN_SET(js.id,jc.shelf_ids) AND js.is_delete = 0
        LEFT JOIN sys_user csu ON FIND_IN_SET(csu.id,jc.check_ids) AND csu.is_delete = 0
        WHERE
            jc.is_delete = 0
        AND jc.id = #{id}
    </select>

    <select id="queryCheckList" resultType="com.dsj.inventory.bussiness.inventory.vo.CheckExportVo">
        SELECT distinct jc.id
        ,jc.code
        ,jc.name
        ,(CASE
        WHEN jc.check_state = 0 THEN '暂存'
        WHEN jc.check_state = 1 THEN '盘点中'
        WHEN jc.check_state = 2 THEN '已完成'
        END) checkState
        ,jc.remark
        ,(CASE WHEN jc.shelf_ids = '1' THEN '全部' ELSE GROUP_CONCAT(distinct js.name) END) shelfNames
        ,su.name createUserName
        ,GROUP_CONCAT(distinct csu.name) checkNames
        ,jc.check_date checkDate
        ,jc.create_time createTime
        FROM jxc_check jc
        LEFT JOIN sys_user su ON su.id = jc.create_user AND su.is_delete = 0
        LEFT JOIN jxc_shelf js ON FIND_IN_SET(js.id,jc.shelf_ids) AND js.is_delete = 0
        LEFT JOIN sys_user csu ON FIND_IN_SET(csu.id,jc.check_ids) AND csu.is_delete = 0
        WHERE
        jc.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jc.organization_id = #{param.organizationId}
        </if>
        <if test="param.checkState != null ">
            AND jc.check_state = #{param.checkState}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jc.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.createUserNameLike != null and param.createUserNameLike != '' ">
            AND su.name like CONCAT('%',#{param.createUserNameLike},'%')
        </if>
        <if test="param.checkNameLike != null and param.checkNameLike != '' ">
            AND csu.name like CONCAT('%',#{param.checkNameLike},'%')
        </if>
        <if test="param.checkId != null and param.checkId != '' ">
            AND FIND_IN_SET (#{param.checkId},jc.check_ids)
        </if>
        <choose>
            <when test="param.shelfId != null and param.shelfId != '1'  ">
                AND (FIND_IN_SET (#{param.shelfId},jc.shelf_ids) OR jc.shelf_ids = '1')
            </when>
            <when test="param.shelfId != null and param.shelfId == '1' ">
                AND jc.shelf_ids = #{param.shelfId}
            </when>
        </choose>
        <if test="param.createTime != null and param.createTime != '' ">
            AND DATE_FORMAT(jc.create_time,"%Y-%m-%d") = #{param.createTime}
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jc.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        GROUP BY jc.id
        ORDER BY jc.create_time desc
    </select>

    <update id="deleteCheckByOrganizationId">
        UPDATE jxc_check jc, jxc_check_goods jcg
        SET jc.is_delete = 1, jcg.is_delete = 1
        WHERE jc.id = jcg.check_id
          AND jc.is_delete = 0
          AND jcg.is_delete = 0
          AND jc.organization_id = #{organizationId}
    </update>

    <select id="getCheckCount" resultType="java.lang.Integer">
        SELECT COUNT(1) number
        FROM jxc_check jc
        WHERE
        DATE_FORMAT(jc.create_time,'%Y-%m-%d') = #{day}
        <if test="organizationId != null and organizationId != '' ">
            AND jc.organization_id = #{organizationId}
        </if>
    </select>

</mapper>
