package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.MedicineTemplate;
import com.dsj.inventory.bussiness.inventory.entity.MedicineTemplateRMedicine;
import com.dsj.inventory.bussiness.inventory.mapper.MedicineTemplateMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryMedicineTemplateParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateMedicineTemplateParam;
import com.dsj.inventory.bussiness.inventory.service.MedicineTemplateRMedicineService;
import com.dsj.inventory.bussiness.inventory.service.MedicineTemplateService;
import com.dsj.inventory.bussiness.inventory.vo.MedicineTemplateMedicineVo;
import com.dsj.inventory.bussiness.inventory.vo.MedicineTemplateVo;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DozerUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.auth.entity.Log;
import com.pocky.transport.bussiness.auth.enumeration.LogModuleEnum;
import com.pocky.transport.bussiness.auth.enumeration.LogTypeEnum;
import com.pocky.transport.bussiness.auth.service.ILogService;
import com.pocky.transport.bussiness.diagnose.param.DisableParam;
import com.pocky.transport.bussiness.diagnose.service.MedicineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 药品拉取模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@Service
public class MedicineTemplateServiceImpl extends ServiceImpl<MedicineTemplateMapper, MedicineTemplate> implements MedicineTemplateService {

    @Autowired
    private DozerUtils dozerUtils;
    @Autowired
    private ILogService logService;
    @Autowired
    private MedicineTemplateRMedicineService medicineTemplateRMedicineService;
    @Autowired
    private MedicineService medicineService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MedicineTemplateVo saveOrUpdateMedicineTemplate(SaveOrUpdateMedicineTemplateParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(CommonUtils.isNotEmpty(param.getMedicineIdList())){
            List<Long> medicineIdList = param.getMedicineIdList().stream().distinct().collect(Collectors.toList());
            if(medicineIdList.size() != param.getMedicineIdList().size()){
                throw new BizException("存在重复商品Id");
            }
        }
        String type = LogTypeEnum.INSERT.getCode();
        if(CommonUtils.isNotEmpty(param.getId())) {
            MedicineTemplate byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            //删除药品
            medicineTemplateRMedicineService.lambdaUpdate().set(MedicineTemplateRMedicine::getIsDelete, TrueEnum.TRUE.getCode())
                    .eq(MedicineTemplateRMedicine::getTemplateId, param.getId()).update(new MedicineTemplateRMedicine());
            type = LogTypeEnum.UPDATE.getCode();
            if (CommonUtils.isNotEmpty(param.getIsDelete()) && TrueEnum.TRUE.getCode().equals(param.getIsDelete())){
                this.baseMapper.deleteById(param.getId());
                //增加系统操作日志
                logService.save(Log.builder().moduleName(LogModuleEnum.MEDICINE.getCode()).type(LogTypeEnum.DELETE.getCode())
                        .logInfo(LogTypeEnum.DELETE.getDesc() + "药品模板" + byId.getTitle()).build());
                return null;
            }
        }

        MedicineTemplate medicineTemplate = dozerUtils.map(param, MedicineTemplate.class);
        this.saveOrUpdate(medicineTemplate);
        if(CommonUtils.isNotEmpty(param.getMedicineIdList())){
            List<MedicineTemplateRMedicine> medicineList = new ArrayList<>();
            for(Long medicineId : param.getMedicineIdList()){
                MedicineTemplateRMedicine rMedicine = MedicineTemplateRMedicine.builder()
                        .templateId(medicineTemplate.getId()).medicineId(medicineId).build();
                medicineList.add(rMedicine);
            }
            medicineTemplateRMedicineService.saveBatch(medicineList);
        }
        //增加系统操作日志
        logService.save(Log.builder().moduleName(LogModuleEnum.MEDICINE.getCode()).type(type)
                .logInfo(LogTypeEnum.getDesc(type) + "药品模板" + param.getTitle()).build());
        return this.getMedicineTemplateById(medicineTemplate.getId());
    }

    @Override
    public Page<MedicineTemplateVo> queryMedicineTemplatePage(QueryMedicineTemplateParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<MedicineTemplateVo> list = this.baseMapper.queryMedicineTemplatePage(page, param);
        return page.setRecords(list);
    }

    @Override
    public MedicineTemplateVo getMedicineTemplateById(Long id) {
        MedicineTemplateVo vo = this.baseMapper.getMedicineTemplateById(id);
        if(CommonUtils.isNotEmpty(vo)){
            List<MedicineTemplateMedicineVo> medicineList = medicineTemplateRMedicineService.getMedicineTemplateMedicineList(vo.getId());
            if(CommonUtils.isNotEmpty(medicineList)){
                List<Long> medicineIdList = medicineList.stream().map(m -> m.getMedicineId()).collect(Collectors.toList());
                vo.setMedicineIdList(medicineIdList);
                vo.setMedicineList(medicineList);
            }
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableMedicineTemplate(DisableParam param) {
        List<Long> ids = param.getIds();
        if (CommonUtils.isNotEmpty(ids)){
            ids.forEach(id->{
                MedicineTemplate byId = this.getById(id);
                this.lambdaUpdate().eq(MedicineTemplate::getId,id)
                        .set(MedicineTemplate::getUsableState,param.getUsableState()).update(new MedicineTemplate());
                //增加系统操作日志
                String type = LogTypeEnum.ENABLE.getCode();
                if(TrueEnum.FALSE.getCode().equals(param.getUsableState())){
                    type = LogTypeEnum.DISABLE.getCode();
                }
                logService.save(Log.builder().moduleName(LogModuleEnum.MEDICINE.getCode()).type(type)
                        .logInfo(LogTypeEnum.getDesc(type) + "药品模板" + byId.getTitle()).build());
            });
        }
    }


}
