package com.dsj.inventory.common.utils;

import com.itextpdf.awt.geom.Rectangle2D;
import com.itextpdf.text.Document;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.*;
import com.itextpdf.text.pdf.parser.PdfReaderContentParser;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import com.pocky.transport.bussiness.external.event.TextRenderListener;
import com.dsj.inventory.common.entity.AddImagePdfParam;
import com.dsj.inventory.common.utils.CommonUtils;

import java.io.*;
import java.util.List;

/**
 * 通过模板转换pdf
 *
 * <AUTHOR>
 * @date 2022/4/10 9:12
 */
public class PdfUtils {

    private static final String FONT = "simhei.ttf";

    public static void createPdfForBsDebitNote(OutputStream stream, String html,String hospitalseal) throws Exception{

        //创建文件
        Document doc = new Document(PageSize.A4,0,0,0,0);
        //输出流：资源到前端一个文件的过程
        //package com.itextpdf.text.pdf; 添加到此文档的内容将被写入outputstream.
        PdfWriter pdfWriter = PdfWriter.getInstance(doc,stream);
        pdfWriter.setPageEvent(new PdfPageEventHelper(){
            @Override
            public void onEndPage(PdfWriter writer, Document document) {
                PdfContentByte cb = writer.getDirectContent();
                try{
                    cb.setFontAndSize(BaseFont.createFont(FONT,BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED),6);
                } catch (com.itextpdf.text.DocumentException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                cb.saveState();
                cb.beginText();
                cb.endText();
                cb.restoreState();
            }
        });

        doc.setMargins(10, 10, 10, 10);
        doc.open();
        //以下可以使用itextpdf带有的丰富的方法去写，去画，但我们使用并做好了模板，因此把模板搞成字节输入流取代那些方法
        ByteArrayInputStream byteArrayInputStream=new ByteArrayInputStream(html.getBytes());
        //这种将XHTML/CSS或XML流解析为PDF的是被itextpdf接收的并且itextpdf提供了帮助类XMLWorkerHelper，使用后直接搞定
        XMLWorkerHelper.getInstance().parseXHtml(pdfWriter,doc,byteArrayInputStream);
        if (CommonUtils.isNotEmpty(hospitalseal)){
            Image image =Image.getInstance(hospitalseal);
            image.setAbsolutePosition(330,200);
            image.scaleToFit(210,210);
            doc.add(image);
        }
        doc.close();
        stream.close();
    }

    public static void addImagePdf(File file, File targetFile, List<AddImagePdfParam> imageParamList) throws Exception {
        // 插入页
        int pageIndex = 1;
        // 读取模板文件
        InputStream input = new FileInputStream(file);
        PdfReader reader = new PdfReader(input);
        PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(targetFile));
        // 提取pdf中的表单
        AcroFields form = stamper.getAcroFields();
        form.addSubstitutionFont(BaseFont.createFont("STSong-Light","UniGB-UCS2-H", BaseFont.NOT_EMBEDDED));

        // 通过域名获取所在页和坐标，左下角为起点
        PdfReaderContentParser parser = new PdfReaderContentParser(reader);
        for(AddImagePdfParam param : imageParamList){
            int x = param.getX();
            int y = param.getY();
            for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                //新建一个TextRenderListener对象，该对象实现了RenderListener接口，作为处理PDF的主要类
                TextRenderListener listener = new TextRenderListener();
                //解析PDF，并处理里面的文字
                parser.processContent(i, listener);
                //获取文字的矩形边框
                List<Rectangle2D.Float> rectText = listener.rectText;
                List<String> textList = listener.textList;
                for (String textStr : textList) {
                    if (textStr.contains(param.getFieldName())) {
                        pageIndex = i;
                        int index = textList.indexOf(textStr);
                        Rectangle2D.Float position = rectText.get(index);
                        x = (int) (position.x + x);
                        y = (int) (position.y + y);
                        break;
                    }
                }
            }

            // 读图片
            Image image = Image.getInstance(param.getImagePath());
            // 获取操作的页面
            PdfContentByte page = stamper.getOverContent(pageIndex);
            // 图片位置
            image.setAbsolutePosition(x, y);
            // 图片宽高
            image.scaleAbsolute(param.getWidth(), param.getHeight());
            // 添加图片
            page.addImage(image);
        }

        stamper.close();
        reader.close();
        input.close();
    }






}
