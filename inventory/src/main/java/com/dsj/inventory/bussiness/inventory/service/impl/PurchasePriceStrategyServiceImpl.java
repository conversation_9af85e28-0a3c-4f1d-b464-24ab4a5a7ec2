package com.dsj.inventory.bussiness.inventory.service.impl;

import com.dsj.inventory.bussiness.inventory.mapper.IncomingOrderGoodsMapper;
import com.dsj.inventory.bussiness.inventory.mapper.PurchaseOrderGoodsMapper;
import com.dsj.inventory.bussiness.inventory.mapper.PurchasePlanGoodsMapper;
import com.dsj.inventory.bussiness.inventory.service.PurchasePlanParameterService;
import com.dsj.inventory.bussiness.inventory.service.PurchasePriceStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 进货单价策略服务实现
 * 实现三种价格生成策略
 */
@Service
@Slf4j
public class PurchasePriceStrategyServiceImpl implements PurchasePriceStrategyService {

    @Autowired
    private PurchasePlanGoodsMapper purchasePlanGoodsMapper;

    @Autowired
    private PurchaseOrderGoodsMapper purchaseOrderGoodsMapper;

    @Autowired
    private IncomingOrderGoodsMapper incomingOrderGoodsMapper;

    @Autowired
    private PurchasePlanParameterService purchasePlanParameterService;

    @Override
    public Integer getPurchasePrice(Long organizationMedicineId, Long organizationSupplierId, Long organizationId) {
        log.debug("根据策略获取进货单价 - organizationMedicineId: {}, organizationSupplierId: {}, organizationId: {}", 
                organizationMedicineId, organizationSupplierId, organizationId);
        // TODO: 实现根据策略获取进货单价逻辑
        throw new UnsupportedOperationException("待实现：根据策略获取进货单价");
    }

    @Override
    public Integer getPriceByLowest(Long organizationMedicineId, Integer days, Long organizationId) {
        log.debug("按最低进价策略获取价格 - organizationMedicineId: {}, days: {}, organizationId: {}", 
                organizationMedicineId, days, organizationId);
        // TODO: 实现按最低进价策略获取价格逻辑
        throw new UnsupportedOperationException("待实现：按最低进价策略获取价格");
    }

    @Override
    public Integer getPriceByLatestSupplier(Long organizationMedicineId, Long organizationId) {
        log.debug("按最新供应商策略获取价格 - organizationMedicineId: {}, organizationId: {}", 
                organizationMedicineId, organizationId);
        // TODO: 实现按最新供应商策略获取价格逻辑
        throw new UnsupportedOperationException("待实现：按最新供应商策略获取价格");
    }

    @Override
    public Integer getPriceByLastPrice(Long organizationMedicineId, Long organizationSupplierId, Long organizationId) {
        log.debug("按上次进价策略获取价格 - organizationMedicineId: {}, organizationSupplierId: {}, organizationId: {}", 
                organizationMedicineId, organizationSupplierId, organizationId);
        // TODO: 实现按上次进价策略获取价格逻辑
        throw new UnsupportedOperationException("待实现：按上次进价策略获取价格");
    }

    @Override
    public Long getLastUsedSupplier(Long organizationMedicineId, Long organizationId) {
        log.debug("获取商品最近使用的供应商 - organizationMedicineId: {}, organizationId: {}", 
                organizationMedicineId, organizationId);
        // TODO: 实现获取商品最近使用的供应商逻辑
        throw new UnsupportedOperationException("待实现：获取商品最近使用的供应商");
    }

    @Override
    public Long getRecommendedSupplier(Long organizationMedicineId, Long organizationId) {
        log.debug("根据配置的策略获取推荐供应商 - organizationMedicineId: {}, organizationId: {}", 
                organizationMedicineId, organizationId);
        // TODO: 实现根据配置的策略获取推荐供应商逻辑
        throw new UnsupportedOperationException("待实现：根据配置的策略获取推荐供应商");
    }

    @Override
    public Integer[] getPriceRange(Long organizationMedicineId, Integer days, Long organizationId) {
        log.debug("获取商品的历史采购价格范围 - organizationMedicineId: {}, days: {}, organizationId: {}", 
                organizationMedicineId, days, organizationId);
        // TODO: 实现获取商品的历史采购价格范围逻辑
        throw new UnsupportedOperationException("待实现：获取商品的历史采购价格范围");
    }

    @Override
    public Boolean hasSupplierHistory(Long organizationMedicineId, Long organizationSupplierId, Long organizationId) {
        log.debug("验证供应商是否有该商品的采购历史 - organizationMedicineId: {}, organizationSupplierId: {}, organizationId: {}", 
                organizationMedicineId, organizationSupplierId, organizationId);
        // TODO: 实现验证供应商是否有该商品的采购历史逻辑
        throw new UnsupportedOperationException("待实现：验证供应商是否有该商品的采购历史");
    }
}