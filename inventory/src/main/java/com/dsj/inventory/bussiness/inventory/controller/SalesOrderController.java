package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesOrderParam;
import com.dsj.inventory.bussiness.inventory.param.ReturnSalesOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateSalesOrderParam;
import com.dsj.inventory.bussiness.inventory.service.SalesOrderService;
import com.dsj.inventory.bussiness.inventory.vo.SalesOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesOrderExportVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.EasyExcelUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 进销存销售单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@RestController
@Api(value = "SalesOrderController", tags = "进销存销售单api")
public class SalesOrderController {

    @Autowired
    private SalesOrderService salesOrderService;

    /*新增销售单*/
    @OrgAuthMode(strict = true,strictUserMenus = false,menuCodeArray = {"drugstore-erp-stock-stock","drugstore-erp-sale"})
    @PostMapping("/sales-order")
    @ApiOperation(value = "新增销售单", notes = "新增销售单")
    public ResponseEntity<SalesOrderDetailVo> saveSalesOrder(@RequestBody SaveOrUpdateSalesOrderParam param){
        SalesOrderDetailVo salesVo = salesOrderService.saveOrUpdateSalesOrder(param);
        return Res.success(salesVo);
    }

    /*编辑销售单*/
    @PutMapping("/sales-order/{id}")
    @ApiOperation(value = "编辑销售单", notes = "编辑销售单")
    public ResponseEntity<SalesOrderDetailVo> updateSalesOrder(@PathVariable Long id, @RequestBody SaveOrUpdateSalesOrderParam param){
        param.setId(id);
        SalesOrderDetailVo salesVo = salesOrderService.saveOrUpdateSalesOrder(param);
        return Res.success(salesVo);
    }

    /*删除销售单*/
    @DeleteMapping("/sales-order/{id}")
    @ApiOperation(value = "删除销售单", notes = "删除销售单")
    public ResponseEntity<Void> deleteSalesOrder(@PathVariable Long id){
        salesOrderService.deleteSalesOrder(id);
        return Res.success();
    }

    /*获取销售单列表*/
    @GetMapping("/sales-order")
    @ApiOperation(value = "获取销售单列表", notes = "获取销售单列表")
    public ResponseEntity<List<SalesOrderDetailVo>> querySalesOrderList(QuerySalesOrderParam param){
        Page<SalesOrderDetailVo> page = salesOrderService.querySalesOrderPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取销售单详情*/
    @GetMapping("/sales-order/{id}")
    @ApiOperation(value = "获取销售单详情", notes = "获取销售单详情")
    public ResponseEntity<SalesOrderDetailVo> querySalesOrder(@PathVariable Long id){
        return Res.success(salesOrderService.getSalesOrderById(id));
    }

    /*销售单退货*/
    @OrgAuthMode(strict = true,strictUserMenus = false,menuCodeArray = {"drugstore-erp-stock-stock","drugstore-erp-sale"})
    @PostMapping("/sales-order/{id}/action-return")
    @ApiOperation(value = "销售单退货", notes = "销售单退货")
    public ResponseEntity<Void> returnSalesOrder(@PathVariable Long id, @RequestBody ReturnSalesOrderParam param){
        param.setId(id);
        salesOrderService.returnSalesOrder(param);
        return Res.success();
    }

    /*销售单导出*/
    @GetMapping("/sales-order/action-export")
    @ApiOperation(value = "导出销售单", notes = "导出销售单")
    public ResponseEntity<Void> exportSalesOrder(HttpServletResponse response, QuerySalesOrderParam param) throws IOException {
        param.setNeedExport(true);
        List<SalesOrderExportVo> list = salesOrderService.querySalesOrderList(param);
        EasyExcelUtils.exportOrdinaryExcel(response,list,"销售单导出列表","销售单导出列表", SalesOrderExportVo.class);
        return Res.success();
    }

    /*清理挂单*/
    @PostMapping("/sales-order/action-clear-timer")
    @ApiOperation(value = "清理挂单", notes = "清理前一天的挂单，定时器模拟")
    public ResponseEntity<Void> clearSalesOrderTimer(){
        salesOrderService.clearSalesOrder();
        return Res.success();
    }

}
