package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询药品拉取模板
 * <AUTHOR>
 * @date 2023/03/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QueryMedicineTemplateParam",description = "查询药品拉取模板参数")
public class QueryMedicineTemplateParam extends BasePage {

    @ApiModelProperty(value = "标题模糊搜索")
    private String titleLike;

    @ApiModelProperty(value = "创建人模糊搜索")
    private String createUserNameLike;

    @ApiModelProperty(value = "可用状态 0：待启用 1启用")
    private Integer usableState;

}
