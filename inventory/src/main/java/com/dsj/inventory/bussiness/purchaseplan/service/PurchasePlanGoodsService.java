package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.purchaseplan.entity.PurchasePlanGoods;

/**
 * <p>
 * 进销存采购计划单商品明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
public interface PurchasePlanGoodsService extends IService<PurchasePlanGoods> {
    /**
     * 根据计划ID将商品明细标记为已删除
     * @param planId 计划ID
     */
    void markAsDeletedByPlanId(Long planId);
}
