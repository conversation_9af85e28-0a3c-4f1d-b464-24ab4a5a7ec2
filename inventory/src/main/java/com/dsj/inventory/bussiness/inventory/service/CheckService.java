package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.Check;
import com.dsj.inventory.bussiness.inventory.param.CompleteCheckParam;
import com.dsj.inventory.bussiness.inventory.param.QueryCheckParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateCheckParam;
import com.dsj.inventory.bussiness.inventory.vo.CheckDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.CheckExportVo;
import com.dsj.inventory.bussiness.inventory.vo.CheckVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存盘点计划 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface CheckService extends IService<Check> {

    /**
     * 新增、编辑 盘点计划
     * @param param
     * @return
     */
    CheckDetailVo saveOrUpdateCheck(SaveOrUpdateCheckParam param);

    /**
     * 删除 盘点计划
     * @param id
     */
    void deleteCheck(Long id);

    /**
     * 获取盘点计划分页列表
     * @param param
     * @return
     */
    Page<CheckVo> queryCheckPage(QueryCheckParam param);

    /**
     * 获取盘点计划列表详情
     * @param id
     * @return
     */
    CheckDetailVo getCheckById(Long id);

    /**
     * 提交盘点计划
     * @param id
     */
    void submitCheck(Long id);

    /**
     * 完成盘点计划
     * @param param
     * @return
     */
    CheckDetailVo completeCheck(CompleteCheckParam param);

    /**
     * 导出盘点计划列表
     * @param param
     * @return
     */
    List<CheckExportVo> queryCheckList(QueryCheckParam param);

    /**
     * 删除组织的盘点计划
     * @param organizationId
     */
    void deleteCheckByOrganizationId(Long organizationId);

    /**
     * 统计组织当天盘点计划数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getCheckCount(@Param("organizationId") Long organizationId, @Param("day") String day);

    /**
     * 生成日盘点计划
     * @param organizationId
     */
    CheckDetailVo saveDayCheck(Long organizationId);

}
