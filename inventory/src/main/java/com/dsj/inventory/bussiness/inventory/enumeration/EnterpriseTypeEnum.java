package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 企业类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum EnterpriseTypeEnum {
    /**
     * 生产企业（GMP）
     */
    GMP(1, "生产企业"),
    
    /**
     * 经营企业（GSP）
     */
    GSP(2, "经营企业");

    /**
     * 编码
     */
    private final Integer code;
    
    /**
     * 描述
     */
    private final String desc;
    
    /**
     * 根据编码获取描述
     * 
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (EnterpriseTypeEnum enumItem : EnterpriseTypeEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getDesc();
            }
        }
        return null;
    }
} 