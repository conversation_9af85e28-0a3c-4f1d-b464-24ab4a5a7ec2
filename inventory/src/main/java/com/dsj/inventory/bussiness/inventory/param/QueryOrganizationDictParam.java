package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织字典参数查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "组织字典参数查询参数")
public class QueryOrganizationDictParam extends BasePage {

    @ApiModelProperty(value = "组织ID")
    private Long organizationId;

    @ApiModelProperty(value = "字典名称")
    private String dictName;

    @ApiModelProperty(value = "字典编码")
    private String dictCode;

    @ApiModelProperty(value = "字典类型")
    private String dictType;

    @ApiModelProperty(value = "父级ID")
    private Long parentId;
} 