package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.DeliveryOrder;
import com.dsj.inventory.bussiness.inventory.param.QueryDeliveryOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存退货单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface DeliveryOrderMapper extends BaseMapper<DeliveryOrder> {

    /**
     * 获取退货单分页列表
     * @param page
     * @param param
     * @return
     */
    List<DeliveryOrderVo> queryDeliveryOrderPage(@Param("page") Page page, @Param("param") QueryDeliveryOrderParam param);

    /**
     * 获取退货单详情
     * @param id
     * @return
     */
    DeliveryOrderDetailVo getDeliveryOrderById(Long id);

    /**
     * 导出退货单列表
     * @param param
     * @return
     */
    List<DeliveryOrderExportVo> queryDeliveryOrderList(@Param("param") QueryDeliveryOrderParam param);

    /**
     * 统计已退货数量
     * @param incomingGoodsId
     * @return
     */
    Double countReturnedAmount(Long incomingGoodsId);

    /**
     * 删除组织的退货单
     * @param organizationId
     */
    void deleteDeliveryOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天退货单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getDeliveryOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);

}
