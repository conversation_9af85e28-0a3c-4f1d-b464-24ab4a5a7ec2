package com.dsj.inventory.bussiness.purchaseplan.service.impl;

import com.dsj.inventory.bussiness.inventory.service.OrganizationDictService;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationDictVO;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateOrganizationDictParam;
import com.dsj.inventory.bussiness.purchaseplan.constant.PurchasePlanParamConstants;
import com.dsj.inventory.bussiness.purchaseplan.service.PurchasePlanParameterService;
import com.dsj.inventory.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * 采购计划参数管理服务实现类
 * 基于OrganizationDict实现参数配置管理
 * 
 * <AUTHOR>
 * @since 2025-06-23
 */
@Service
@Slf4j
public class PurchasePlanParameterServiceImpl implements PurchasePlanParameterService {

    @Autowired
    private OrganizationDictService organizationDictService;

    // ==================== 基础参数操作方法 ====================

    @Override
    public Integer getIntParameter(Long organizationId, String paramCode) {
        validateBasicParams(organizationId, paramCode);
        
        String value = getStringParameter(organizationId, paramCode);
        if (value != null) {
            try {
                return Integer.valueOf(value);
            } catch (NumberFormatException e) {
                log.warn("参数值转换失败 - organizationId: {}, paramCode: {}, value: {}, 使用默认值", 
                    organizationId, paramCode, value);
            }
        }
        // 返回默认值
        return getDefaultIntValue(paramCode);
    }

    @Override
    public String getStringParameter(Long organizationId, String paramCode) {
        validateBasicParams(organizationId, paramCode);
        
        log.debug("获取参数 - organizationId: {}, paramCode: {}", organizationId, paramCode);
        
        OrganizationDictVO dict = organizationDictService.getByOrgTypeAndCode(
            organizationId, PurchasePlanParamConstants.DICT_TYPE_PURCHASE_PLAN, paramCode);
        
        if (dict != null && CommonUtils.isNotEmpty(dict.getDictValue())) {
            log.debug("获取到参数值 - paramCode: {}, value: {}", paramCode, dict.getDictValue());
            return dict.getDictValue();
        }
        
        // 返回默认值
        String defaultValue = getDefaultStringValue(paramCode);
        log.debug("使用默认参数值 - paramCode: {}, defaultValue: {}", paramCode, defaultValue);
        return defaultValue;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initDefaultParameters(Long organizationId) {
        if (organizationId == null) {
            throw new IllegalArgumentException("组织ID不能为空");
        }
        
        log.info("初始化组织采购计划参数 - organizationId: {}", organizationId);
        
        // 获取默认参数和描述
        Map<String, String> defaultParams = PurchasePlanParamConstants.getDefaultParameterMap();
        Map<String, String> descriptions = PurchasePlanParamConstants.getParameterDescriptionMap();
        
        int createdCount = 0;
        for (Map.Entry<String, String> entry : defaultParams.entrySet()) {
            String paramCode = entry.getKey();
            String paramValue = entry.getValue();
            
            // 检查参数是否已存在
            if (!parameterExists(organizationId, paramCode)) {
                try {
                    SaveOrUpdateOrganizationDictParam param = new SaveOrUpdateOrganizationDictParam();
                    param.setOrganizationId(organizationId);
                    param.setDictType(PurchasePlanParamConstants.DICT_TYPE_PURCHASE_PLAN);
                    param.setDictCode(paramCode);
                    param.setDictValue(paramValue);
                    param.setDictName(descriptions.get(paramCode));
                    param.setDictDesc("采购计划模块参数配置");
                    
                    organizationDictService.saveOrUpdateOrganizationDict(param);
                    createdCount++;
                    log.debug("创建参数 - paramCode: {}, paramValue: {}", paramCode, paramValue);
                } catch (Exception e) {
                    log.error("创建参数失败 - organizationId: {}, paramCode: {}, error: {}", 
                        organizationId, paramCode, e.getMessage());
                    throw new RuntimeException("参数初始化失败: " + paramCode, e);
                }
            } else {
                log.debug("参数已存在，跳过创建 - paramCode: {}", paramCode);
            }
        }
        
        log.info("参数初始化完成 - organizationId: {}, 创建参数数量: {}", organizationId, createdCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateParameter(Long organizationId, String paramCode, String paramValue) {
        validateBasicParams(organizationId, paramCode);
        if (CommonUtils.isEmpty(paramValue)) {
            throw new IllegalArgumentException("参数值不能为空");
        }
        
        log.info("更新参数 - organizationId: {}, paramCode: {}, paramValue: {}", 
            organizationId, paramCode, paramValue);
            
        try {
            OrganizationDictVO existingDict = organizationDictService.getByOrgTypeAndCode(
                organizationId, PurchasePlanParamConstants.DICT_TYPE_PURCHASE_PLAN, paramCode);
                
            SaveOrUpdateOrganizationDictParam param = new SaveOrUpdateOrganizationDictParam();
            param.setOrganizationId(organizationId);
            param.setDictType(PurchasePlanParamConstants.DICT_TYPE_PURCHASE_PLAN);
            param.setDictCode(paramCode);
            param.setDictValue(paramValue);
            param.setDictDesc("采购计划模块参数配置");
            
            if (existingDict != null) {
                // 更新现有参数
                param.setId(existingDict.getId());
                param.setDictName(existingDict.getDictName());
                log.debug("更新现有参数 - id: {}", existingDict.getId());
            } else {
                // 创建新参数
                Map<String, String> descriptions = PurchasePlanParamConstants.getParameterDescriptionMap();
                param.setDictName(descriptions.getOrDefault(paramCode, paramCode));
                log.debug("创建新参数 - paramCode: {}", paramCode);
            }
            
            organizationDictService.saveOrUpdateOrganizationDict(param);
            log.info("参数更新成功 - paramCode: {}", paramCode);
        } catch (Exception e) {
            log.error("参数更新失败 - organizationId: {}, paramCode: {}, error: {}", 
                organizationId, paramCode, e.getMessage());
            throw new RuntimeException("参数更新失败: " + paramCode, e);
        }
    }

    @Override
    public Boolean parameterExists(Long organizationId, String paramCode) {
        validateBasicParams(organizationId, paramCode);
        
        OrganizationDictVO dict = organizationDictService.getByOrgTypeAndCode(
            organizationId, PurchasePlanParamConstants.DICT_TYPE_PURCHASE_PLAN, paramCode);
        return dict != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteParameter(Long organizationId, String paramCode) {
        validateBasicParams(organizationId, paramCode);
        
        log.info("删除参数 - organizationId: {}, paramCode: {}", organizationId, paramCode);
        
        OrganizationDictVO dict = organizationDictService.getByOrgTypeAndCode(
            organizationId, PurchasePlanParamConstants.DICT_TYPE_PURCHASE_PLAN, paramCode);
            
        if (dict != null) {
            try {
                Boolean result = organizationDictService.deleteOrganizationDict(dict.getId());
                log.info("参数删除{} - paramCode: {}", result ? "成功" : "失败", paramCode);
                return result;
            } catch (Exception e) {
                log.error("参数删除失败 - organizationId: {}, paramCode: {}, error: {}", 
                    organizationId, paramCode, e.getMessage());
                throw new RuntimeException("参数删除失败: " + paramCode, e);
            }
        }
        
        log.debug("参数不存在，无需删除 - paramCode: {}", paramCode);
        return true;
    }

    // ==================== 便捷方法 - 获取具体业务参数 ====================

    @Override
    public Integer getPriceStrategy(Long organizationId) {
        return getIntParameter(organizationId, PurchasePlanParamConstants.PRICE_STRATEGY);
    }

    @Override
    public Integer getLowestPriceDays(Long organizationId) {
        return getIntParameter(organizationId, PurchasePlanParamConstants.LOWEST_PRICE_DAYS);
    }

    @Override
    public Integer getInTransitDays(Long organizationId) {
        return getIntParameter(organizationId, PurchasePlanParamConstants.IN_TRANSIT_DAYS);
    }

    @Override
    public Integer getSalesStatDays(Long organizationId) {
        return getIntParameter(organizationId, PurchasePlanParamConstants.SALES_STAT_DAYS);
    }

    @Override
    public Integer getUpperLimitDays(Long organizationId) {
        return getIntParameter(organizationId, PurchasePlanParamConstants.UPPER_LIMIT_DAYS);
    }

    @Override
    public Integer getLowerLimitDays(Long organizationId) {
        return getIntParameter(organizationId, PurchasePlanParamConstants.LOWER_LIMIT_DAYS);
    }

    @Override
    public Integer getGspCheckMode(Long organizationId) {
        return getIntParameter(organizationId, PurchasePlanParamConstants.GSP_CHECK_MODE);
    }

    // ==================== 便捷方法 - 设置具体业务参数 ====================

    @Override
    public void setPriceStrategy(Long organizationId, Integer strategy) {
        validatePriceStrategy(strategy);
        updateParameter(organizationId, PurchasePlanParamConstants.PRICE_STRATEGY, strategy.toString());
    }

    @Override
    public void setLowestPriceDays(Long organizationId, Integer days) {
        validatePositiveInteger(days, "最低进价取样天数");
        updateParameter(organizationId, PurchasePlanParamConstants.LOWEST_PRICE_DAYS, days.toString());
    }

    @Override
    public void setInTransitDays(Long organizationId, Integer days) {
        validatePositiveInteger(days, "在途数量统计天数");
        updateParameter(organizationId, PurchasePlanParamConstants.IN_TRANSIT_DAYS, days.toString());
    }

    @Override
    public void setSalesStatDays(Long organizationId, Integer days) {
        validatePositiveInteger(days, "日均销量统计天数");
        updateParameter(organizationId, PurchasePlanParamConstants.SALES_STAT_DAYS, days.toString());
    }

    @Override
    public void setUpperLimitDays(Long organizationId, Integer days) {
        validatePositiveInteger(days, "库存上限天数");
        updateParameter(organizationId, PurchasePlanParamConstants.UPPER_LIMIT_DAYS, days.toString());
    }

    @Override
    public void setLowerLimitDays(Long organizationId, Integer days) {
        validatePositiveInteger(days, "库存下限天数");
        updateParameter(organizationId, PurchasePlanParamConstants.LOWER_LIMIT_DAYS, days.toString());
    }

    @Override
    public void setGspCheckMode(Long organizationId, Integer mode) {
        validateGspCheckMode(mode);
        updateParameter(organizationId, PurchasePlanParamConstants.GSP_CHECK_MODE, mode.toString());
    }

    // ==================== 批量操作方法 ====================

    @Override
    public Map<String, String> getAllParameters(Long organizationId) {
        if (organizationId == null) {
            throw new IllegalArgumentException("组织ID不能为空");
        }

        log.debug("获取组织所有采购计划参数 - organizationId: {}", organizationId);

        Map<String, String> result = new HashMap<>();
        Map<String, String> defaultParams = PurchasePlanParamConstants.getDefaultParameterMap();

        for (String paramCode : defaultParams.keySet()) {
            String value = getStringParameter(organizationId, paramCode);
            result.put(paramCode, value);
        }

        log.debug("获取到参数数量: {}", result.size());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateParameters(Long organizationId, Map<String, String> parameters) {
        if (organizationId == null) {
            throw new IllegalArgumentException("组织ID不能为空");
        }
        if (CommonUtils.isEmpty(parameters)) {
            throw new IllegalArgumentException("参数映射不能为空");
        }

        log.info("批量更新参数 - organizationId: {}, 参数数量: {}", organizationId, parameters.size());

        int successCount = 0;
        for (Map.Entry<String, String> entry : parameters.entrySet()) {
            String paramCode = entry.getKey();
            String paramValue = entry.getValue();

            if (CommonUtils.isEmpty(paramCode) || CommonUtils.isEmpty(paramValue)) {
                log.warn("跳过无效参数 - paramCode: {}, paramValue: {}", paramCode, paramValue);
                continue;
            }

            try {
                updateParameter(organizationId, paramCode, paramValue);
                successCount++;
            } catch (Exception e) {
                log.error("批量更新参数失败 - paramCode: {}, error: {}", paramCode, e.getMessage());
                throw new RuntimeException("批量更新参数失败: " + paramCode, e);
            }
        }

        log.info("批量更新参数完成 - 成功数量: {}", successCount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetToDefaultParameters(Long organizationId) {
        if (organizationId == null) {
            throw new IllegalArgumentException("组织ID不能为空");
        }

        log.info("重置组织参数为默认值 - organizationId: {}", organizationId);

        Map<String, String> defaultParams = PurchasePlanParamConstants.getDefaultParameterMap();
        updateParameters(organizationId, defaultParams);

        log.info("参数重置完成 - organizationId: {}", organizationId);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 校验基础参数
     */
    private void validateBasicParams(Long organizationId, String paramCode) {
        if (organizationId == null) {
            throw new IllegalArgumentException("组织ID不能为空");
        }
        if (CommonUtils.isEmpty(paramCode)) {
            throw new IllegalArgumentException("参数代码不能为空");
        }
    }

    /**
     * 校验正整数参数
     */
    private void validatePositiveInteger(Integer value, String paramName) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException(paramName + "必须大于0");
        }
    }

    /**
     * 校验价格策略参数
     */
    private void validatePriceStrategy(Integer strategy) {
        if (strategy == null) {
            throw new IllegalArgumentException("价格策略不能为空");
        }
        if (!PurchasePlanParamConstants.PRICE_STRATEGY_LOWEST.equals(strategy) &&
            !PurchasePlanParamConstants.PRICE_STRATEGY_LATEST_SUPPLIER.equals(strategy) &&
            !PurchasePlanParamConstants.PRICE_STRATEGY_LAST_PRICE.equals(strategy)) {
            throw new IllegalArgumentException("无效的价格策略: " + strategy + "，有效值: 1-最低进价, 2-最新供应商, 3-上次进价");
        }
    }

    /**
     * 校验GSP校验模式参数
     */
    private void validateGspCheckMode(Integer mode) {
        if (mode == null) {
            throw new IllegalArgumentException("GSP校验模式不能为空");
        }
        if (!PurchasePlanParamConstants.GSP_CHECK_MODE_BLOCK.equals(mode) &&
            !PurchasePlanParamConstants.GSP_CHECK_MODE_IGNORE.equals(mode) &&
            !PurchasePlanParamConstants.GSP_CHECK_MODE_WARN.equals(mode)) {
            throw new IllegalArgumentException("无效的GSP校验模式: " + mode + "，有效值: 1-拦截, 0-不拦截不提示, -1-提示不拦截");
        }
    }

    /**
     * 获取参数默认字符串值
     */
    private String getDefaultStringValue(String paramCode) {
        return PurchasePlanParamConstants.getDefaultParameterMap().get(paramCode);
    }

    /**
     * 获取参数默认整数值
     */
    private Integer getDefaultIntValue(String paramCode) {
        String defaultValue = getDefaultStringValue(paramCode);
        if (defaultValue != null) {
            try {
                return Integer.valueOf(defaultValue);
            } catch (NumberFormatException e) {
                log.warn("默认值转换失败 - paramCode: {}, defaultValue: {}", paramCode, defaultValue);
            }
        }
        return 0;
    }
}
