package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivity;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesActivityParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateSalesActivityParam;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityVo;

/**
 * <p>
 * 进销存销售活动 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface SalesActivityService extends IService<SalesActivity> {

    /**
     * 新增、编辑、删除 销售活动
     * @param param
     * @return
     */
    SalesActivityVo saveOrUpdateSalesActivity(SaveOrUpdateSalesActivityParam param);

    /**
     * 获取销售活动分页列表
     * @param param
     * @return
     */
    Page<SalesActivityVo> querySalesActivityPage(QuerySalesActivityParam param);

    /**
     * 获取销售活动列表详情
     * @param id
     * @return
     */
    SalesActivityVo getSalesActivityById(Long id);

    /**
     * 暂停/启用销售活动
     * @param id
     */
    void pauseSalesActivity(Long id);

    /**
     * 终止销售活动
     * @param id
     */
    void stopSalesActivity(Long id);

    /**
     * 批量处理销售活动状态
     */
    void batchHandleSalesActivityState();

}
