package com.dsj.inventory.bussiness.inventory.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.Inventory;
import com.dsj.inventory.bussiness.inventory.entity.InventoryChangeLog;
import com.dsj.inventory.bussiness.inventory.entity.Shelf;
import com.dsj.inventory.bussiness.inventory.entity.WarningLog;
import com.dsj.inventory.bussiness.inventory.enumeration.InventoryChangeTypeEnum;
import com.dsj.inventory.bussiness.inventory.enumeration.InventoryOrderTypeEnum;
import com.dsj.inventory.bussiness.inventory.mapper.InventoryMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryInventoryParam;
import com.dsj.inventory.bussiness.inventory.param.QueryInventorySaleParam;
import com.dsj.inventory.bussiness.inventory.service.*;
import com.dsj.inventory.bussiness.inventory.vo.InventoryExportVo;
import com.dsj.inventory.bussiness.inventory.vo.InventoryImportVo;
import com.dsj.inventory.bussiness.inventory.vo.InventorySaleVo;
import com.dsj.inventory.bussiness.inventory.vo.InventoryVo;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.common.utils.DozerUtils;
import com.dsj.inventory.common.utils.EasyExcelUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.auth.entity.Log;
import com.pocky.transport.bussiness.auth.enumeration.LogModuleEnum;
import com.pocky.transport.bussiness.auth.enumeration.LogTypeEnum;
import com.pocky.transport.bussiness.auth.service.ILogService;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.enumeration.GoodsTypeEnum;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import com.pocky.transport.bussiness.mul.entity.Video;
import com.pocky.transport.bussiness.mul.service.IVideoService;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 进销存商品库存 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Service
public class InventoryServiceImpl extends ServiceImpl<InventoryMapper, Inventory> implements InventoryService {

    @Autowired
    private InventoryChangeLogService inventoryChangeLogService;
    @Autowired
    private WarningLogService warningLogService;
    @Autowired
    private PurchasePlanService purchasePlanService;
    @Autowired
    private PurchaseOrderService purchaseOrderService;
    @Autowired
    private ReceiveOrderService receiveOrderService;
    @Autowired
    private AcceptOrderService acceptOrderService;
    @Autowired
    private IncomingOrderService incomingOrderService;
    @Autowired
    private DeliveryOrderService deliveryOrderService;
    @Autowired
    private CheckService checkService;
    @Autowired
    private GainsLossesOrderService gainsLossesOrderService;
    @Autowired
    private SalesOrderService salesOrderService;
    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @Autowired
    private DozerUtils dozerUtils;
    @Autowired
    private ShelfService shelfService;
    @Autowired
    private ILogService logService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private IVideoService videoService;

    @Override
    public Page<InventoryVo> queryInventoryPage(QueryInventoryParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<InventoryVo> list = this.baseMapper.queryInventoryPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public List<InventoryExportVo> queryInventoryList(QueryInventoryParam param) {
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        return this.baseMapper.queryInventoryList(param);
    }

    @Override
    public Page<InventorySaleVo> queryInventorySalePage(QueryInventorySaleParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<InventorySaleVo> list = this.baseMapper.queryInventorySalePage(page, param);
        return page.setRecords(list);
    }

    @Override
    public List<InventoryVo> queryBecomeDueInventoryList(Long organizationId, Integer becomeDueDay) {
        return this.baseMapper.queryBecomeDueInventoryList(organizationId,becomeDueDay);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initializeInventory(MultipartFile file,Long organizationId) throws IOException {
        //删除所有库存相关信息
        //删除采购计划以及商品明细
        purchasePlanService.deletePurchasePlanByOrganizationId(organizationId);
        //删除采购订单以及商品明细
        purchaseOrderService.deletePurchaseOrderByOrganizationId(organizationId);
        //删除收货单以及商品明细
        receiveOrderService.deleteReceiveOrderByOrganizationId(organizationId);
        //删除验收单以及商品明细
        acceptOrderService.deleteAcceptOrderByOrganizationId(organizationId);
        //删除入库单以及商品明细
        incomingOrderService.deleteIncomingOrderByOrganizationId(organizationId);
        //删除退货单以及商品明细
        deliveryOrderService.deleteDeliveryOrderByOrganizationId(organizationId);
        //删除盘点计划以及商品明细
        checkService.deleteCheckByOrganizationId(organizationId);
        //删除损益单以及商品明细
        gainsLossesOrderService.deleteGainsLossesOrderByOrganizationId(organizationId);
        //删除销售单以及商品明细
        salesOrderService.deleteSalesOrderByOrganizationId(organizationId);
        //删除商品库存
        this.lambdaUpdate().eq(Inventory::getOrganizationId,organizationId)
                .set(Inventory::getIsDelete, TrueEnum.TRUE.getCode())
                .update(new Inventory());
        //删除库存变更记录
        inventoryChangeLogService.lambdaUpdate().eq(InventoryChangeLog::getOrganizationId,organizationId)
                .set(InventoryChangeLog::getIsDelete, TrueEnum.TRUE.getCode())
                .update(new InventoryChangeLog());
        //删除库存预警记录
        warningLogService.lambdaUpdate().eq(WarningLog::getOrganizationId,organizationId)
                .set(WarningLog::getIsDelete, TrueEnum.TRUE.getCode())
                .update(new WarningLog());
        //清空商品库存、成本价格、最后销售时间、默认线上、线下下架
        LambdaUpdateWrapper<OrganizationMedicineMapping> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrganizationMedicineMapping::getOrganizationId,organizationId);
        updateWrapper.set(OrganizationMedicineMapping::getMedicineRepertory,0);
        updateWrapper.set(OrganizationMedicineMapping::getAverageCost,null);
        updateWrapper.set(OrganizationMedicineMapping::getIsShelve,TrueEnum.FALSE.getCode());
        updateWrapper.set(OrganizationMedicineMapping::getInventoryShelve,TrueEnum.FALSE.getCode());
        organizationMedicineMappingService.update(new OrganizationMedicineMapping(),updateWrapper);
        //线上商城下架同时删除短视频里的商品
        LambdaUpdateWrapper<Video> videoWrapper = new LambdaUpdateWrapper<>();
        videoWrapper.eq(Video::getOrganizationId, organizationId);
        videoWrapper.set(Video::getOrgMedicineId, null);
        videoService.update(new Video(),videoWrapper);
        //初始化库存
        Map<String, Object> map = new HashMap<>();
        map.put("organizationId",organizationId);
        AnalysisEventListener<InventoryImportVo> listener = EasyExcelUtils.getListenerBiConsumer((list, extMap) -> ((InventoryServiceImpl) AopContext.currentProxy()).batchInitializeInventory(list,extMap), Integer.MAX_VALUE, map);
        EasyExcel.read(new BufferedInputStream(file.getInputStream()), InventoryImportVo.class, listener).sheet().doRead();
    }

    @Transactional(rollbackFor = {Exception.class})
    public void batchInitializeInventory(List<InventoryImportVo> list, Map<String, Object> extendMap) {
        StringBuffer errorMessage = new StringBuffer();
        Set<Inventory> insertList = new HashSet<>();
        // 获取数据行数List
        Long organizationId = (Long) extendMap.get("organizationId");
        Organization organization = organizationService.getById(organizationId);
        for (int i = 0; i < list.size(); i++) {
            StringBuffer oneMessage = new StringBuffer();
            Inventory inventory = Inventory.builder().organizationId(organizationId).build();
            InventoryImportVo inventoryImportVo = dozerUtils.map(list.get(i),InventoryImportVo.class);
            if (CommonUtils.isEmpty(inventoryImportVo.getGoodsType())){
                oneMessage.append("第"+(i+1)+"行缺少商品分类内容,").append("</br>");
            }
            if (CommonUtils.isEmpty(inventoryImportVo.getName())){
                oneMessage.append("第"+(i+1)+"行缺少商品名称内容,").append("</br>");
            }
            if (CommonUtils.isEmpty(inventoryImportVo.getSpecification())){
                oneMessage.append("第"+(i+1)+"行缺少规格型号内容,").append("</br>");
            }
            LambdaQueryWrapper<OrganizationMedicineMapping> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrganizationMedicineMapping::getOrganizationId,organizationId);
            wrapper.eq(OrganizationMedicineMapping::getName,inventoryImportVo.getName());
            wrapper.eq(OrganizationMedicineMapping::getSpecification,inventoryImportVo.getSpecification());
            //1药品(含中成药)，2注射用药，3中药饮片，4养生中药，5中药材，6医疗器械，7保健品，8普通食品，9计生用品'，10化妆品，11日用百货，12其他，13商业赠品，14原料药
            if(inventoryImportVo.getGoodsType().equals(GoodsTypeEnum.GOODS_1.getCode())
                    || inventoryImportVo.getGoodsType().equals(GoodsTypeEnum.GOODS_2.getCode())
                    || inventoryImportVo.getGoodsType().equals(GoodsTypeEnum.GOODS_3.getCode())
                    || inventoryImportVo.getGoodsType().equals(GoodsTypeEnum.GOODS_4.getCode())
                    || inventoryImportVo.getGoodsType().equals(GoodsTypeEnum.GOODS_5.getCode())
                    || inventoryImportVo.getGoodsType().equals(GoodsTypeEnum.GOODS_15.getCode())){
                if (CommonUtils.isEmpty(inventoryImportVo.getApprovalNumber())){
                    oneMessage.append("第"+(i+1)+"行缺少批准文号内容,").append("</br>");
                }
                wrapper.eq(OrganizationMedicineMapping::getApprovalNumber,inventoryImportVo.getApprovalNumber());
                if(CommonUtils.isNotEmpty(inventoryImportVo.getInsuranceCode())){
                    wrapper.eq(OrganizationMedicineMapping::getInsuranceCode,inventoryImportVo.getInsuranceCode());
                }
                List<OrganizationMedicineMapping> organizationMedicineList = organizationMedicineMappingService.list(wrapper);
                if(CommonUtils.isEmpty(organizationMedicineList)){
                    oneMessage.append("第"+(i+1)+"行系统未获取到商品,").append("</br>");
                }else if(organizationMedicineList.size() > 1){
                    oneMessage.append("第"+(i+1)+"行系统存在多个商品,").append("</br>");
                }else {
                    inventory.setOrganizationMedicineId(organizationMedicineList.get(0).getId());
                }
            }else if(inventoryImportVo.getGoodsType().equals(GoodsTypeEnum.GOODS_6.getDesc())){
                if(CommonUtils.isEmpty(inventoryImportVo.getManufacturer())){
                    oneMessage.append("第"+(i+1)+"行缺少生产厂家内容,").append("</br>");
                }
                wrapper.eq(OrganizationMedicineMapping::getManufacturer,inventoryImportVo.getManufacturer());
                if(CommonUtils.isEmpty(inventoryImportVo.getRegistrationNumber())){
                    oneMessage.append("第"+(i+1)+"行缺少备案/注册证内容,").append("</br>");
                }
                wrapper.eq(OrganizationMedicineMapping::getRegistrationNumber,inventoryImportVo.getRegistrationNumber());
                if(CommonUtils.isEmpty(inventoryImportVo.getMedicalApparatusRegistration())){
                    oneMessage.append("第"+(i+1)+"行缺少医疗器械产品注册证内容,").append("</br>");
                }
                wrapper.eq(OrganizationMedicineMapping::getMedicalApparatusRegistration,inventoryImportVo.getMedicalApparatusRegistration());
                if(CommonUtils.isEmpty(inventoryImportVo.getProducePermit())){
                    oneMessage.append("第"+(i+1)+"行缺少生产许可证内容,").append("</br>");
                }
                wrapper.eq(OrganizationMedicineMapping::getProducePermit,inventoryImportVo.getProducePermit());
                List<OrganizationMedicineMapping> organizationMedicineList = organizationMedicineMappingService.list(wrapper);
                if(CommonUtils.isEmpty(organizationMedicineList)){
                    oneMessage.append("第"+(i+1)+"行系统未获取到商品,").append("</br>");
                }else if(organizationMedicineList.size() > 1){
                    oneMessage.append("第"+(i+1)+"行系统存在多个商品,").append("</br>");
                }else {
                    inventory.setOrganizationMedicineId(organizationMedicineList.get(0).getId());
                }
            }else {
                if (CommonUtils.isEmpty(inventoryImportVo.getApprovalNumber())){
                    oneMessage.append("第"+(i+1)+"行缺少批准文号内容,").append("</br>");
                }
                wrapper.eq(OrganizationMedicineMapping::getApprovalNumber,inventoryImportVo.getApprovalNumber());
                if (CommonUtils.isEmpty(inventoryImportVo.getManufacturer())){
                    oneMessage.append("第"+(i+1)+"行缺少生产厂家内容,").append("</br>");
                }
                wrapper.eq(OrganizationMedicineMapping::getManufacturer,inventoryImportVo.getManufacturer());
                List<OrganizationMedicineMapping> organizationMedicineList = organizationMedicineMappingService.list(wrapper);
                if(CommonUtils.isEmpty(organizationMedicineList)){
                    oneMessage.append("第"+(i+1)+"行系统未获取到商品,").append("</br>");
                }else if(organizationMedicineList.size() > 1){
                    oneMessage.append("第"+(i+1)+"行系统存在多个商品,").append("</br>");
                }else {
                    inventory.setOrganizationMedicineId(organizationMedicineList.get(0).getId());
                }
            }
            if (CommonUtils.isEmpty(inventoryImportVo.getProduceDate())){
                oneMessage.append("第"+(i+1)+"行缺少生产日期内容,").append("</br>");
            }else {
                boolean result = DateUtils.checkDateStr(inventoryImportVo.getProduceDate(),DateUtils.DEFAULT_DATE_FORMAT_SLASH);
                if(result){
                    String produceDateStr = DateUtils.formatDateStr(inventoryImportVo.getProduceDate());
                    if(CommonUtils.isEmpty(produceDateStr)){
                        oneMessage.append("第"+(i+1)+"行生产日期格式有误,").append("</br>");
                    }
                    inventory.setProduceDate(DateUtils.format(produceDateStr,DateUtils.DEFAULT_DATE_TIME_FORMAT));
                }else {
                    oneMessage.append("第"+(i+1)+"行生产日期格式有误,").append("</br>");
                }
            }
            if (CommonUtils.isEmpty(inventoryImportVo.getUserDate())){
                oneMessage.append("第"+(i+1)+"行缺少有效期至内容,").append("</br>");
            }else {
                boolean result = DateUtils.checkDateStr(inventoryImportVo.getUserDate(),DateUtils.DEFAULT_DATE_FORMAT_SLASH);
                if(result){
                    String useDateStr = DateUtils.formatDateStr(inventoryImportVo.getUserDate());
                    if(CommonUtils.isEmpty(useDateStr)){
                        oneMessage.append("第"+(i+1)+"行有效期至格式有误,").append("</br>");
                    }
                    inventory.setUserDate(DateUtils.format(useDateStr,DateUtils.DEFAULT_DATE_TIME_FORMAT));
                }else {
                    oneMessage.append("第"+(i+1)+"行有效期至格式有误,").append("</br>");
                }
            }
            if (CommonUtils.isEmpty(inventoryImportVo.getInventoryAmount())){
                oneMessage.append("第"+(i+1)+"行缺少库存数量内容,").append("</br>");
            }
            inventory.setInventoryAmount(Double.valueOf(inventoryImportVo.getInventoryAmount()));
            if (CommonUtils.isEmpty(inventoryImportVo.getBatchNumber())){
                oneMessage.append("第"+(i+1)+"行缺少批号内容,").append("</br>");
            }
            inventory.setBatchNumber(inventoryImportVo.getBatchNumber());
            if (CommonUtils.isEmpty(inventoryImportVo.getShelfName())){
                oneMessage.append("第"+(i+1)+"行缺少货位名称内容,").append("</br>");
            }
            String[] ShelfStr = inventoryImportVo.getShelfName().split(",");
            for(int j=0; j<ShelfStr.length; j++){
                int level = j+1;
                Shelf shelf = shelfService.lambdaQuery().eq(Shelf::getName,ShelfStr[j]).eq(Shelf::getLevel,level)
                        .eq(Shelf::getOrganizationId,organizationId).one();
                if(CommonUtils.isEmpty(shelf)){
                    oneMessage.append("第"+(i+1)+"行未查询到货位" + ShelfStr[j] + ",").append("</br>");
                }
                inventory.setShelfId(shelf.getId());
            }
            if (CommonUtils.isEmpty(inventoryImportVo.getBatch())){
                oneMessage.append("第"+(i+1)+"行缺少批次内容,").append("</br>");
            }
            inventory.setBatch(inventoryImportVo.getBatch());
            if (oneMessage.length() > 0) {
                errorMessage.append(oneMessage);
                continue;
            }
            insertList.add(inventory);
        }
        if(CommonUtils.isNotEmpty(errorMessage) && errorMessage.length() > 0){
            throw new BizException(errorMessage.toString());
        }
        if (CommonUtils.isNotEmpty(insertList)) {
            for(Inventory inventory : insertList){
                this.save(inventory);
                //增加库存变动记录
                InventoryChangeLog inventoryChangeLog = InventoryChangeLog.builder().organizationId(organizationId)
                        .organizationMedicineId(inventory.getOrganizationMedicineId()).inventoryId(inventory.getId())
                        .orderType(InventoryOrderTypeEnum.INITIALIZE.getCode()).changeType(InventoryChangeTypeEnum.INCREASE.getCode())
                        .changeAmount(inventory.getInventoryAmount()).batchNumber(inventory.getBatchNumber())
                        .shelfId(inventory.getShelfId()).batch(inventory.getBatch()).executeId(BaseContextHandler.getUserId())
                        .build();
                inventoryChangeLogService.save(inventoryChangeLog);
                //商品表库存更新
                OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(inventory.getOrganizationMedicineId());
                BigDecimal medicineRepertory = BigDecimal.valueOf(organizationMedicine.getMedicineRepertory())
                        .add(BigDecimal.valueOf(inventory.getInventoryAmount()));
                organizationMedicineMappingService.lambdaUpdate().eq(OrganizationMedicineMapping::getId,organizationMedicine.getId())
                        .set(OrganizationMedicineMapping::getMedicineRepertory,medicineRepertory.doubleValue())
                        .update(new OrganizationMedicineMapping());
            }
        }
        //增加系统操作日志
        logService.save(Log.builder().moduleName(LogModuleEnum.DRUGSTORE.getCode()).type(LogTypeEnum.REPERTORY.getCode())
                .logInfo(OrganizationEnum.getDescByCode(organization.getType()) + organization.getName() + "进销存模块初始化库存").build());
    }

}
