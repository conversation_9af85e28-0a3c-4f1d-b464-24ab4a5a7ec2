package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 收货单商品明细
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PurchaseOrderGoodsVo", description="收货单商品明细实体")
@ToString(callSuper = true)
public class ReceiveOrderGoodsVo {

    private Long id;

    @ApiModelProperty(value = "收货单Id")
    private Long orderId;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "通用名")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    private String generalName;

    @ApiModelProperty(value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(value = "包装单位")
    private String packUnit;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "剂型")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(value = "上市许可持有人")
    private String launchPermitHolder;

    @ApiModelProperty(value = "产地")
    private String producePlace;

    @ApiModelProperty(value = "当前零售价（私域商品价格）")
    private Integer price;

    @ApiModelProperty(value = "会员价（私域商品会员价）")
    private Integer memberPrice;

    @ApiModelProperty(value = "当前库存")
    private Double repertory;

    @ApiModelProperty(value = "含税价（进货价）")
    private Integer purchasePrice;

    @ApiModelProperty(value = "折后单价（含税价折后）")
    private Integer discountPrice;

    @ApiModelProperty(value = "采购数量")
    private Double purchaseNumber;

    @ApiModelProperty(value = "含税金额（进货总金额）")
    private Integer totalPurchasePrice;

    @ApiModelProperty(value = "折后金额（含税金额折后）")
    private Integer totalDiscountPrice;

    @ApiModelProperty(value = "到货数量")
    private Double arrivalAmount;

    @ApiModelProperty(value = "收货数量")
    private Double receiveAmount;

    @ApiModelProperty(value = "拒收数量")
    private Double refuseAmount;

    @ApiModelProperty(value = "赠送数量")
    private Double giveAmount;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
