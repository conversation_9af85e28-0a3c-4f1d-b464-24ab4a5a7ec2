package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 药品拉取模板药品信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("di_medicine_template_r_medicine")
@ApiModel(value="MedicineTemplateRMedicine", description="药品拉取模板药品信息表")
public class MedicineTemplateRMedicine extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "药品模板Id")
    @TableField("template_id")
    private Long templateId;

    @ApiModelProperty(value = "药品Id")
    @TableField("medicine_id")
    private Long medicineId;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
