package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 盘点计划处理状态 0暂存 1盘点中 2已完成
 * <AUTHOR>
 */

@Getter
public enum CheckStateEnum {

    TEMPORARY(0, "暂存"),
    CHECK(1, "盘点中"),
    COMPLETED(2, "已完成"),
    ;

    private Integer code;

    private String desc;

    CheckStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (CheckStateEnum a : CheckStateEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
