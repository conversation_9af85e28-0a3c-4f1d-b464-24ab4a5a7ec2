package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.SalesGoods;
import com.dsj.inventory.bussiness.inventory.mapper.SalesGoodsMapper;
import com.dsj.inventory.bussiness.inventory.service.SalesGoodsService;
import com.dsj.inventory.bussiness.inventory.vo.SalesGoodsVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 进销存销售单商品明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Service
public class SalesGoodsServiceImpl extends ServiceImpl<SalesGoodsMapper, SalesGoods> implements SalesGoodsService {

    @Override
    public List<SalesGoodsVo> getSalesGoodsListByOrderId(Long orderId) {
        return this.baseMapper.getSalesGoodsListByOrderId(orderId);
    }
}
