package com.dsj.inventory.framework.aspect;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 通过切面 记录请求日志
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class SysLogAspect {

    private final ThreadLocal<LocalDateTime> currentThread = new ThreadLocal<>();

    @Pointcut("@annotation(io.swagger.annotations.ApiOperation)")
    public void logPointCut() {
    }

    @Before("logPointCut() && @annotation(apiOperation)")
    public void invokeBefore(JoinPoint joinPoint, ApiOperation apiOperation) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        try {
            String param = getParams(joinPoint);
            log.info("请求地址:{} || 请求描述:{} || 请求方法:{} || 请求参数:{}", request.getMethod() + " " + request.getRequestURL().toString(), apiOperation.value(), joinPoint.getSignature(), param);
            currentThread.set(LocalDateTime.now());
        } catch (Exception e) {
            log.info("\033[1;33m日志切面出现错误:", e + "\033[0m");
        }
    }

    private String getParams(JoinPoint joinPoint) {
        String params = "";
        if (joinPoint.getArgs() != null && joinPoint.getArgs().length > 0) {
            for (int i = 0; i < joinPoint.getArgs().length; i++) {
                Object arg = joinPoint.getArgs()[i];
                if ((arg instanceof HttpServletResponse) || (arg instanceof HttpServletRequest)
                        || (arg instanceof MultipartFile) || (arg instanceof MultipartFile[])) {
                    continue;
                }
                try {
                    params +=JSONObject.toJSONString(joinPoint.getArgs()[i]);
                } catch (Exception e1) {
                    log.error("Exception:切面解析参数异常",e1);
                }
            }
        }
        return params;
    }


    @AfterReturning(returning = "obj", pointcut = "logPointCut()")
    public void invokeAfter(Object obj) {
        if (currentThread.get() != null) {
            log.info("本次耗时:{}毫秒", Duration.between(currentThread.get(), LocalDateTime.now()).toMillis());
        }
    }
}

