package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织付款方式实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("jxc_payment_method")
public class PaymentMethod extends Entity {

    /**
     * 组织ID
     */
    private Long organizationId;

    /**
     * 付款方式编码
     */
    private String methodCode;

    /**
     * 付款方式名称
     */
    private String methodName;

    /**
     * 图标URL
     */
    private String iconUrl;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 是否启用
     */
    private Integer enabled;

    /**
     * 备注
     */
    private String remark;
} 