package com.dsj.inventory.bussiness.inventory.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

/**
 * 退货单导出
 * <AUTHOR>
 * @date 2023/04/13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DeliveryOrderExportVo", description="退货单导出实体")
@ToString(callSuper = true)
@ColumnWidth(25)
@HeadRowHeight(20)
@HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER)//表头样式
@ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER)//内容样式
public class DeliveryOrderExportVo {

    @ApiModelProperty(value = "退货单号")
    @ExcelProperty(value = "退货单号")
    private String code;

    @ApiModelProperty(value = "供应商名称")
    @ExcelProperty(value = "供应商名称")
    private String organizationSupplierName;

    @ApiModelProperty(value = "供应商编号")
    @ExcelProperty(value = "供应商编号")
    private String organizationSupplierCode;

    @ApiModelProperty(value = "处理状态 0暂存 1退货中 2已退货 3已取消")
    @ExcelProperty(value = "处理状态")
    private String dealState;

    @ApiModelProperty(value = "私域供应商销售人员名称")
    @ExcelProperty(value = "销售员")
    private String organizationSupplierMemberName;

    @ApiModelProperty(value = "创建人名称")
    @ExcelProperty(value = "操作人")
    private String createUserName;

    @ApiModelProperty(value = "退货总额")
    @ExcelProperty(value = "退货总额")
    private String returnTotalPrice;

    @ApiModelProperty(value = "退货成本金额")
    @ExcelProperty(value = "退货成本总额")
    private String returnCostTotalPrice;

    @ApiModelProperty(value = "进退货差价（退货成本金额-退货总额）")
    @ExcelProperty(value = "进货退货差价总额")
    private String disparityPrice;

    @ApiModelProperty(value = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "退货原因")
    @ExcelProperty(value = "原因")
    private String reason;

}
