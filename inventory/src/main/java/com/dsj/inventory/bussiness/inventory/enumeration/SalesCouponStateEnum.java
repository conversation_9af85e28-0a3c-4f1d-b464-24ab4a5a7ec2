package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 优惠券状态 0未过期 1已过期
 * <AUTHOR>
 */

@Getter
public enum SalesCouponStateEnum {

    NOT_EXPIRED(0, "未过期"),
    EXPIRED(1, "已过期"),
    ;

    private Integer code;

    private String desc;

    SalesCouponStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (SalesCouponStateEnum a : SalesCouponStateEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
