package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 收货单处理状态 0暂存 1已复核 2已完成
 * <AUTHOR>
 */

@Getter
public enum ReceiveOrderStateEnum {

    TEMPORARY(0, "暂存"),
    REVIEWED(1, "已复核"),
    COMPLETED(2, "已完成"),
    ;

    private Integer code;

    private String desc;

    ReceiveOrderStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (ReceiveOrderStateEnum a : ReceiveOrderStateEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
