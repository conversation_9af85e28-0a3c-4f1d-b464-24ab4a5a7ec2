package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.Shelf;
import com.dsj.inventory.bussiness.inventory.param.QueryShelfParam;
import com.dsj.inventory.bussiness.inventory.vo.ShelfVo;
import com.pocky.transport.bussiness.diagnose.vo.OrganizationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存货架信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
public interface ShelfMapper extends BaseMapper<Shelf> {

    /**
     * 获取货架分页列表
     * @param page
     * @param param
     * @return
     */
    List<ShelfVo> queryShelfPage(@Param("page") Page page, @Param("param") QueryShelfParam param);

    /**
     * 获取需要初始化货架的组织
     * @return
     */
    List<OrganizationVo> queryInitializationOrganization();

}
