package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.AcceptOrder;
import com.dsj.inventory.bussiness.inventory.param.QueryAcceptOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateAcceptOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存验收单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface AcceptOrderService extends IService<AcceptOrder> {

    /**
     * 新增、编辑 验收单
     * @param param
     * @return
     */
    AcceptOrderDetailVo saveOrUpdateAcceptOrder(SaveOrUpdateAcceptOrderParam param);

    /**
     * 删除 验收单
     * @param id
     */
    void deleteAcceptOrder(Long id);

    /**
     * 获取验收单分页列表
     * @param param
     * @return
     */
    Page<AcceptOrderVo> queryAcceptOrderPage(QueryAcceptOrderParam param);

    /**
     * 获取验收单列表详情
     * @param id
     * @return
     */
    AcceptOrderDetailVo getAcceptOrderById(Long id);

    /**
     * 提交验收单
     * @param id
     */
    void submitAcceptOrder(Long id);

    /**
     * 导出验收单列表
     * @param param
     * @return
     */
    List<AcceptOrderExportVo> queryAcceptOrderList(QueryAcceptOrderParam param);

    /**
     * 删除组织的验收单
     * @param organizationId
     */
    void deleteAcceptOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天验收单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getAcceptOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);

    /**
     * 完成验收单
     * @param orderId
     */
    void completeAcceptOrder(Long orderId);

}
