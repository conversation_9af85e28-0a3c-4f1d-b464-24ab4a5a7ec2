package com.dsj.inventory.bussiness.purchaseplan.service;

/**
 * 采购计划参数管理服务接口
 * 基于OrganizationDict实现参数配置管理
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
public interface PurchasePlanParameterService {
    
    // ==================== 基础参数操作方法 ====================
    
    /**
     * 获取参数值（整型）
     * 
     * @param organizationId 组织ID，不能为空
     * @param paramCode 参数代码，不能为空
     * @return 参数值，如果不存在则返回默认值，转换失败返回默认值
     * @throws IllegalArgumentException 当organizationId或paramCode为空时抛出
     */
    Integer getIntParameter(Long organizationId, String paramCode);
    
    /**
     * 获取参数值（字符串）
     * 
     * @param organizationId 组织ID，不能为空
     * @param paramCode 参数代码，不能为空
     * @return 参数值，如果不存在则返回默认值
     * @throws IllegalArgumentException 当organizationId或paramCode为空时抛出
     */
    String getStringParameter(Long organizationId, String paramCode);
    
    /**
     * 初始化组织采购计划参数
     * 为指定组织创建所有默认参数配置，如果参数已存在则跳过
     * 
     * @param organizationId 组织ID，不能为空
     * @throws IllegalArgumentException 当organizationId为空时抛出
     * @throws RuntimeException 当参数初始化失败时抛出
     */
    void initDefaultParameters(Long organizationId);
    
    /**
     * 更新参数值
     * 如果参数不存在则创建，如果存在则更新
     * 
     * @param organizationId 组织ID，不能为空
     * @param paramCode 参数代码，不能为空
     * @param paramValue 参数值，不能为空
     * @throws IllegalArgumentException 当任何参数为空时抛出
     * @throws RuntimeException 当参数更新失败时抛出
     */
    void updateParameter(Long organizationId, String paramCode, String paramValue);
    
    /**
     * 检查参数是否存在
     * 
     * @param organizationId 组织ID，不能为空
     * @param paramCode 参数代码，不能为空
     * @return 是否存在，true-存在，false-不存在
     * @throws IllegalArgumentException 当organizationId或paramCode为空时抛出
     */
    Boolean parameterExists(Long organizationId, String paramCode);
    
    /**
     * 删除参数
     * 
     * @param organizationId 组织ID，不能为空
     * @param paramCode 参数代码，不能为空
     * @return 是否删除成功
     * @throws IllegalArgumentException 当organizationId或paramCode为空时抛出
     */
    Boolean deleteParameter(Long organizationId, String paramCode);
    
    // ==================== 便捷方法 - 获取具体业务参数 ====================
    
    /**
     * 获取价格策略
     * 
     * @param organizationId 组织ID，不能为空
     * @return 价格策略：1-最低进价，2-最新供应商，3-上次进价
     * @throws IllegalArgumentException 当organizationId为空时抛出
     */
    Integer getPriceStrategy(Long organizationId);
    
    /**
     * 获取最低进价取样天数
     * 用于价格策略为"按最低进价生成"时的取样天数
     * 
     * @param organizationId 组织ID，不能为空
     * @return 取样天数，默认180天
     * @throws IllegalArgumentException 当organizationId为空时抛出
     */
    Integer getLowestPriceDays(Long organizationId);
    
    /**
     * 获取在途数量统计天数
     * 用于计算商品在途数量的统计天数
     * 
     * @param organizationId 组织ID，不能为空
     * @return 统计天数，默认30天
     * @throws IllegalArgumentException 当organizationId为空时抛出
     */
    Integer getInTransitDays(Long organizationId);
    
    /**
     * 获取日均销量统计天数
     * 用于计算商品日均销量的统计天数
     * 
     * @param organizationId 组织ID，不能为空
     * @return 统计天数，默认30天
     * @throws IllegalArgumentException 当organizationId为空时抛出
     */
    Integer getSalesStatDays(Long organizationId);
    
    /**
     * 获取库存上限天数
     * 用于计算库存上限：上限天数 × 日均销量
     * 
     * @param organizationId 组织ID，不能为空
     * @return 上限天数，默认30天
     * @throws IllegalArgumentException 当organizationId为空时抛出
     */
    Integer getUpperLimitDays(Long organizationId);
    
    /**
     * 获取库存下限天数
     * 用于计算库存下限：下限天数 × 日均销量
     * 
     * @param organizationId 组织ID，不能为空
     * @return 下限天数，默认7天
     * @throws IllegalArgumentException 当organizationId为空时抛出
     */
    Integer getLowerLimitDays(Long organizationId);
    
    /**
     * 获取GSP校验模式
     * 
     * @param organizationId 组织ID，不能为空
     * @return 校验模式：1-拦截，0-不拦截不提示，-1-提示不拦截
     * @throws IllegalArgumentException 当organizationId为空时抛出
     */
    Integer getGspCheckMode(Long organizationId);
    
    // ==================== 便捷方法 - 设置具体业务参数 ====================
    
    /**
     * 设置价格策略
     * 
     * @param organizationId 组织ID，不能为空
     * @param strategy 价格策略：1-最低进价，2-最新供应商，3-上次进价
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    void setPriceStrategy(Long organizationId, Integer strategy);
    
    /**
     * 设置最低进价取样天数
     * 
     * @param organizationId 组织ID，不能为空
     * @param days 取样天数，必须大于0
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    void setLowestPriceDays(Long organizationId, Integer days);
    
    /**
     * 设置在途数量统计天数
     * 
     * @param organizationId 组织ID，不能为空
     * @param days 统计天数，必须大于0
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    void setInTransitDays(Long organizationId, Integer days);
    
    /**
     * 设置日均销量统计天数
     * 
     * @param organizationId 组织ID，不能为空
     * @param days 统计天数，必须大于0
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    void setSalesStatDays(Long organizationId, Integer days);
    
    /**
     * 设置库存上限天数
     * 
     * @param organizationId 组织ID，不能为空
     * @param days 上限天数，必须大于0
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    void setUpperLimitDays(Long organizationId, Integer days);
    
    /**
     * 设置库存下限天数
     * 
     * @param organizationId 组织ID，不能为空
     * @param days 下限天数，必须大于0
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    void setLowerLimitDays(Long organizationId, Integer days);
    
    /**
     * 设置GSP校验模式
     * 
     * @param organizationId 组织ID，不能为空
     * @param mode 校验模式：1-拦截，0-不拦截不提示，-1-提示不拦截
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    void setGspCheckMode(Long organizationId, Integer mode);
    
    // ==================== 批量操作方法 ====================
    
    /**
     * 批量获取组织的所有采购计划参数
     * 
     * @param organizationId 组织ID，不能为空
     * @return 参数映射，key为参数编码，value为参数值
     * @throws IllegalArgumentException 当organizationId为空时抛出
     */
    java.util.Map<String, String> getAllParameters(Long organizationId);
    
    /**
     * 批量更新组织的采购计划参数
     * 
     * @param organizationId 组织ID，不能为空
     * @param parameters 参数映射，key为参数编码，value为参数值
     * @throws IllegalArgumentException 当参数无效时抛出
     * @throws RuntimeException 当批量更新失败时抛出
     */
    void updateParameters(Long organizationId, java.util.Map<String, String> parameters);
    
    /**
     * 重置组织的采购计划参数为默认值
     * 
     * @param organizationId 组织ID，不能为空
     * @throws IllegalArgumentException 当organizationId为空时抛出
     * @throws RuntimeException 当重置失败时抛出
     */
    void resetToDefaultParameters(Long organizationId);
}
