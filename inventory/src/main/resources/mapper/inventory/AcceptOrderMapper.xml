<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.AcceptOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.AcceptOrder">
    <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="order_id" property="orderId" />
        <result column="code" property="code" />
        <result column="bill_date" property="billDate" />
        <result column="remark" property="remark" />
        <result column="deal_state" property="dealState" />
        <result column="purchaser_id" property="purchaserId" />
        <result column="accept_id" property="acceptId" />
        <result column="organization_supplier_id" property="organizationSupplierId" />
        <result column="total_price" property="totalPrice" />
        <result column="discount_total_price" property="discountTotalPrice" />
        <result column="carrier" property="carrier" />
        <result column="start_address" property="startAddress" />
        <result column="start_date" property="startDate" />
        <result column="transport_way" property="transportWay" />
        <result column="transport_tool" property="transportTool" />
        <result column="invoice_date" property="invoiceDate" />
        <result column="invoice_code" property="invoiceCode" />
        <result column="invoice_price" property="invoicePrice" />
        <result column="receive_order_number" property="receiveOrderNumber" />
        <result column="follow_goods_order_date" property="followGoodsOrderDate" />
        <result column="arrival_date" property="arrivalDate" />
        <result column="arrival_temp" property="arrivalTemp" />
        <result column="transport_humidity_record" property="transportHumidityRecord" />
        <result column="refrigerate_state" property="refrigerateState" />
        <result column="wholesaler_staff_number" property="wholesalerStaffNumber" />
        <result column="invoice_path" property="invoicePath" />
        <result column="follow_goods_order_path" property="followGoodsOrderPath" />
        <result column="check_report_path" property="checkReportPath" />
        <result column="goods_types" property="goodsTypes" />
        <result column="purchase_content" property="purchaseContent" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        organization_id, order_id, code, bill_date, remark, deal_state, purchaser_id, accept_id, organization_supplier_id, total_price, discount_total_price, carrier, start_address, start_date, transport_way, transport_tool, invoice_date, invoice_code, invoice_price, receive_order_number, follow_goods_order_date, arrival_date, arrival_temp, transport_humidity_record, refrigerate_state, wholesaler_staff_number, invoice_path, follow_goods_order_path, check_report_path, goods_types, purchase_content, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="queryAcceptOrderPage" resultType="com.dsj.inventory.bussiness.inventory.vo.AcceptOrderVo">
        SELECT distinct jao.id
        ,jao.organization_id organizationId
        ,jao.order_id orderId
        ,jao.order_code orderCode
        ,jao.code
        ,jao.bill_date billDate
        ,jao.remark
        ,jao.deal_state dealState
        ,jao.organization_supplier_id organizationSupplierId
        ,jos.name organizationSupplierName
        ,jos.code organizationSupplierCode
        ,jao.total_price totalPrice
        ,jao.goods_types goodsTypes
        ,jao.purchase_content purchaseContent
        ,su.name acceptName
        ,jao.create_time createTime
        FROM jxc_accept_order jao
        LEFT JOIN sys_user su ON su.id = jao.accept_id AND su.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jao.organization_supplier_id AND jos.is_delete = 0
        WHERE
        jao.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jao.organization_id = #{param.organizationId}
        </if>
        <if test="param.dealState != null ">
            AND jao.deal_state = #{param.dealState}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jao.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.acceptNameLike != null and param.acceptNameLike != '' ">
            AND su.name like CONCAT('%',#{param.acceptNameLike},'%')
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jao.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        ORDER BY jao.create_time desc
    </select>

    <select id="getAcceptOrderById" resultType="com.dsj.inventory.bussiness.inventory.vo.AcceptOrderDetailVo">
        SELECT distinct jao.id
                      ,jao.organization_id organizationId
                      ,jao.order_id orderId
                      ,jao.order_code orderCode
                      ,jao.code
                      ,jao.bill_date billDate
                      ,jao.remark
                      ,jao.deal_state dealState
                      ,jao.organization_supplier_id organizationSupplierId
                      ,jos.name organizationSupplierName
                      ,jos.code organizationSupplierCode
                      ,jao.total_price totalPrice
                      ,jao.goods_types goodsTypes
                      ,jao.purchase_content purchaseContent
                      ,psu.name purchaserName
                      ,jao.purchaser_id purchaserId
                      ,su.name acceptName
                      ,jao.accept_id acceptId
                      ,jao.discount_total_price discountTotalPrice
                      ,jao.carrier
                      ,jao.start_address startAddress
                      ,jao.start_date startDate
                      ,jao.transport_way transportWay
                      ,jao.transport_tool transportTool
                      ,jao.invoice_date invoiceDate
                      ,jao.invoice_code invoiceCode
                      ,jao.invoice_price invoicePrice
                      ,jao.invoice_number invoiceNumber
                      ,jao.goods_types goodsTypes
                      ,jao.receive_order_number receiveOrderNumber
                      ,jao.follow_goods_order_date followGoodsOrderDate
                      ,jao.arrival_date arrivalDate
                      ,jao.arrival_temp arrivalTemp
                      ,jao.transport_humidity_record transportHumidityRecord
                      ,jao.refrigerate_state refrigerateState
                      ,jao.wholesaler_staff_number wholesalerStaffNumber
                      ,jao.invoice_path invoicePath
                      ,jao.follow_goods_order_path followGoodsOrderPath
                      ,jao.check_report_path checkReportPath
                      ,jao.purchase_content purchaseContent
                      ,jao.create_time createTime
        FROM jxc_accept_order jao
                 LEFT JOIN sys_user psu ON psu.id = jao.purchaser_id AND psu.is_delete = 0
                 LEFT JOIN sys_user su ON su.id = jao.accept_id AND su.is_delete = 0
                 LEFT JOIN jxc_organization_supplier jos ON jos.id = jao.organization_supplier_id AND jos.is_delete = 0
        WHERE
            jao.is_delete = 0
          AND jao.id = #{id}
    </select>

    <select id="queryAcceptOrderList" resultType="com.dsj.inventory.bussiness.inventory.vo.AcceptOrderExportVo">
        SELECT distinct jao.id
        ,jao.code
        ,jao.remark
        ,(CASE
        WHEN jao.deal_state = 0 THEN '暂存'
        WHEN jao.deal_state = 1 THEN '已验收'
        WHEN jao.deal_state = 2 THEN '已完成'
        END) dealState
        ,ROUND(jao.total_price/100,2) totalPrice
        ,jao.goods_types goodsTypes
        ,jao.purchase_content purchaseContent
        ,jos.name organizationSupplierName
        ,jos.code organizationSupplierCode
        ,su.name acceptName
        ,jao.create_time createTime
        FROM jxc_accept_order jao
        LEFT JOIN sys_user su ON su.id = jao.accept_id AND su.is_delete = 0
        LEFT JOIN jxc_organization_supplier jos ON jos.id = jao.organization_supplier_id AND jos.is_delete = 0
        WHERE
        jao.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jao.organization_id = #{param.organizationId}
        </if>
        <if test="param.dealState != null ">
            AND jao.deal_state = #{param.dealState}
        </if>
        <if test="param.codeLike != null and param.codeLike != '' ">
            AND jao.code like CONCAT('%',#{param.codeLike},'%')
        </if>
        <if test="param.acceptNameLike != null and param.acceptNameLike != '' ">
            AND su.name like CONCAT('%',#{param.acceptNameLike},'%')
        </if>
        <if test="param.createStart != null and param.createEnd != '' ">
            AND DATE_FORMAT(jao.create_time,"%Y-%m-%d") BETWEEN #{param.createStart} AND #{param.createEnd}
        </if>
        ORDER BY jao.create_time desc
    </select>

    <update id="deleteAcceptOrderByOrganizationId">
        UPDATE jxc_accept_order jao, jxc_accept_order_goods jaog
        SET jao.is_delete = 1, jaog.is_delete = 1
        WHERE jao.id = jaog.order_id
          AND jao.is_delete = 0
          AND jaog.is_delete = 0
          AND jao.organization_id = #{organizationId}
    </update>

    <select id="getAcceptOrderCount" resultType="java.lang.Integer">
        SELECT COUNT(1) number
        FROM jxc_accept_order jao
        WHERE
        DATE_FORMAT(jao.create_time,'%Y-%m-%d') = #{day}
        <if test="organizationId != null and organizationId != '' ">
            AND jao.organization_id = #{organizationId}
        </if>
    </select>

</mapper>
