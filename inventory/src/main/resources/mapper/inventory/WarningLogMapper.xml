<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.WarningLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.WarningLog">
    <result column="id" property="id" />
        <result column="organization_id" property="organizationId" />
        <result column="organization_medicine_id" property="organizationMedicineId" />
        <result column="type" property="type" />
        <result column="inventory_id" property="inventoryId" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        organization_id, organization_medicine_id, type, inventory_id, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="queryWarningLogPage" resultType="com.dsj.inventory.bussiness.inventory.vo.WarningLogVo">
        SELECT distinct jwl.id
        ,jwl.organization_id organizationId
        ,jwl.organization_medicine_id organizationMedicineId
        ,homm.name goodsName
        ,homm.general_name generalName
        ,homm.goods_code goodsCode
        ,homm.pack_unit packUnit
        ,homm.specification
        ,homm.drug_type drugType
        ,homm.manufacturer
        ,homm.produce_place producePlace
        ,homm.approval_number approvalNumber
        ,homm.bar_code barCode
        ,homm.offline_price offlinePrice
        ,homm.member_price memberPrice
        ,homm.last_sale_time lastSaleTime
        ,homm.repertory_upper_limit repertoryUpperLimit
        ,homm.repertory_lower_limit repertoryLowerLimit
        <if test="param.type != null and param.type != 4">
        ,homm.medicine_repertory inventoryAmount
        ,homm.last_incoming_time incomingDate
        </if>
        <if test="param.type != null and param.type == 4">
        ,ji.produce_date produceDate
        ,ji.user_date userDate
        ,ji.inventory_amount inventoryAmount
        ,ji.batch_number batchNumber
        ,ji.shelf_id shelfId
        ,js.name shelfName
        ,ji.batch
        ,ji.incoming_date incomingDate
        ,(TO_DAYS(ji.user_date) - TO_DAYS(now())) becomeDueDay
        ,(CASE
         WHEN (TO_DAYS(ji.user_date) - TO_DAYS(now())) <![CDATA[>=]]> 0 THEN 0
         WHEN (TO_DAYS(ji.user_date) - TO_DAYS(now())) <![CDATA[<]]> 0 THEN 1
         END) becomeDueType
        </if>
        ,jwl.create_time createTime
        FROM jxc_warning_log jwl
        LEFT JOIN hos_organization_medicine_mapping homm ON homm.id = jwl.organization_medicine_id AND homm.is_delete = 0
        <if test="param.type != null and param.type == 4">
        LEFT JOIN jxc_inventory ji ON ji.id = jwl.inventory_id AND ji.is_delete = 0
        LEFT JOIN jxc_shelf js ON js.id = ji.shelf_id AND js.is_delete = 0
        </if>
        WHERE
        jwl.is_delete = 0
        <if test="param.organizationId != null and param.organizationId != '' ">
            AND jwl.organization_id = #{param.organizationId}
        </if>
        <if test="param.type != null ">
            AND jwl.type = #{param.type}
        </if>
        <if test="param.goodsLike != null and param.goodsLike != '' ">
            AND (homm.goods_code like CONCAT('%',#{param.goodsLike},'%') OR homm.name like CONCAT('%',#{param.goodsLike},'%'))
        </if>
        <if test="param.unsalableDay != null ">
            AND (TO_DAYS(now()) - TO_DAYS(homm.last_sale_time)) <![CDATA[>=]]> #{param.unsalableDay}
        </if>
        <if test="param.priceMin != null and param.priceMax != null ">
            AND homm.medicine_price between #{param.priceMin} and #{param.priceMax}
        </if>
        <if test="param.inventoryAmountMin != null and param.inventoryAmountMax != null ">
            AND homm.medicine_repertory between #{param.inventoryAmountMin} and #{param.inventoryAmountMax}
        </if>
        <choose>
            <when test="param.becomeDueType != null and param.becomeDueType == 0 ">
                AND (TO_DAYS(ji.user_date) - TO_DAYS(now())) <![CDATA[>=]]> 0
            </when>
            <when test="param.becomeDueType != null and param.becomeDueType == 1 ">
                AND (TO_DAYS(ji.user_date) - TO_DAYS(now())) <![CDATA[<]]> 0
            </when>
        </choose>
        <if test="param.becomeDueMin != null and param.becomeDueMax != null ">
            AND (TO_DAYS(ji.user_date) - TO_DAYS(now())) between #{param.becomeDueMin} and #{param.becomeDueMax}
        </if>
        ORDER BY jwl.create_time desc
    </select>

    <update id="deleteUnsalableWarningLog">
        UPDATE jxc_warning_log jwl, hos_organization_medicine_mapping homm
        SET jwl.is_delete = 1
        WHERE jwl.organization_medicine_id = homm.id
        AND jwl.is_delete = 0
        AND jwl.type = 3
        AND (TO_DAYS(now()) - TO_DAYS(homm.last_sale_time)) <![CDATA[<]]> #{unsalableDay}
        AND jwl.organization_id = #{organizationId}
    </update>

    <update id="deleteBecomeDueWarningLog">
        UPDATE jxc_warning_log jwl, jxc_inventory ji
        SET jwl.is_delete = 1
        WHERE jwl.inventory_id = ji.id
        AND jwl.is_delete = 0
        AND jwl.type = 4
        AND jwl.organization_id = #{organizationId}
        AND (ji.inventory_amount <![CDATA[<=]]> 0 OR (TO_DAYS(ji.user_date) - TO_DAYS(now())) <![CDATA[>]]> #{becomeDueDay})
    </update>

</mapper>
