package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 盘点计划商品明细
 * <AUTHOR>
 * @date 2023/04/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="SaveOrUpdateCheckGoodsParam", description="新增or编辑 盘点计划商品明细参数")
public class SaveOrUpdateCheckGoodsParam {

    @ApiModelProperty(hidden = true,value = "盘点计划Id")
    private Long checkId;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "库存Id")
    private Long inventoryId;

    @ApiModelProperty(hidden = true,value = "通用名")
    private String goodsName;

    @ApiModelProperty(hidden = true,value = "商品名称")
    private String generalName;

    @ApiModelProperty(hidden = true,value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(hidden = true,value = "包装单位")
    private String packUnit;

    @ApiModelProperty(hidden = true,value = "规格型号")
    private String specification;

    @ApiModelProperty(hidden = true,value = "剂型")
    private String drugType;

    @ApiModelProperty(hidden = true,value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(hidden = true,value = "条形码")
    private String barCode;

    @ApiModelProperty(hidden = true,value = "批准文号")
    private String approvalNumber;

    @ApiModelProperty(hidden = true,value = "生产日期")
    private LocalDateTime produceDate;

    @ApiModelProperty(hidden = true,value = "有效期至")
    private LocalDateTime userDate;

    @ApiModelProperty(hidden = true,value = "批号")
    private String batchNumber;

    @ApiModelProperty(hidden = true,value = "货位Id")
    private Long shelfId;

    @ApiModelProperty(hidden = true,value = "批次（入库单号）")
    private String batch;

    @ApiModelProperty(hidden = true,value = "当前零售价（私域商品价格）")
    private Integer price;

    @ApiModelProperty(hidden = true,value = "库存数量")
    private Double inventoryAmount;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;

}
