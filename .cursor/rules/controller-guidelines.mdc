---
description: 
globs: **/controller/**.java
alwaysApply: false
---
# Controller开发规范
始终用中文和我交流。
## 职责
- Controller层 **仅负责** 接收HTTP请求、参数校验、调用Service层方法处理业务逻辑，并将Service层返回的VO对象封装成统一的 `ResponseEntity` 格式返回给前端。
- **严禁** 在Controller层直接进行数据库操作或编写复杂的业务逻辑。

## 注解与路由
- **类注解**: 使用 `@RestController` 标识。
- **路由**: 使用 `@RequestMapping("/api/模块名")` 在类级别统一定义模块的基础路径。
- **HTTP方法**: 使用具体的HTTP方法注解，如 `@GetMapping`, `@PostMapping`, `@PutMapping`, `@DeleteMapping`。

## 权限控制注解 (如 @OrgAuthMode)
- **目的**: 用于在Controller方法级别声明接口所需的组织权限和用户菜单权限。
- **注解**: 使用 `@OrgAuthMode`。
- **参数说明**:
    - `strict` (boolean, 默认 `true`): 是否强制校验组织权限。为 `true` 时，通常要求请求头中包含如 `moduleType` 的参数。
    - `strictUserMenus` (boolean, 默认 `true`): 是否强制校验用户菜单权限。为 `true` 时，会检查用户是否拥有 `menuCodeArray` 中指定的菜单权限。
    - `menuCodeArray` (String[], 默认 `""`): 定义访问该接口所需的一个或多个菜单权限代码。
- **使用场景**:
    - 根据接口的敏感程度和业务需求，配置不同的权限校验级别。
    - 示例:
      ```java
      // 示例1: 强制组织和菜单权限
      @OrgAuthMode(strict = true, strictUserMenus = true, menuCodeArray = {"drugstore-erp-purchase-2"})
      @PostMapping("/purchase-order")
      public ResponseEntity<PurchaseOrderDetailVo> savePurchaseOrder(@RequestBody SaveOrUpdatePurchaseOrderParam param){
          // ...
      }

      // 示例2: 不强制组织权限，但强制菜单权限
      @OrgAuthMode(strict = false, strictUserMenus = true, menuCodeArray = {"drugstore-erp-stock-stock"})
      @GetMapping("/inventory")
      public ResponseEntity<List<InventoryVo>> queryInventoryList(QueryInventoryParam param){
          // ...
      }

      // 示例3: 强制组织权限，不强制菜单权限 (此时 menuCodeArray 通常为空或不设置)
      @OrgAuthMode(strict = true, strictUserMenus = false)
      @PostMapping("/inventory/action-initialize/{organizationId}")
      public ResponseEntity<Void> importIllness(@RequestParam(value = "file") MultipartFile file, @PathVariable Long organizationId) {
          // ...
      }
      ```
- **注意**: 未标记 `@OrgAuthMode` 注解的方法，其权限校验逻辑将依赖于项目配置的其他全局权限控制机制或不进行此特定类型的校验。

## 依赖注入
- **注入Service**: 必须通过 `@Autowired` 注入对应的 Service 接口，而不是实现类。
- **禁止注入Mapper**: Controller层 **不允许** 直接注入或使用 `Mapper` 接口。

## 请求处理
- **参数接收**:
    - 对于 `GET` 请求的简单参数，使用 `@RequestParam`。
    - 对于 `POST`, `PUT` 请求的复杂参数，使用封装好的 `Param` 对象，并使用 `@RequestBody` 注解。
    - 对于路径参数，使用 `@PathVariable`。
- **参数校验**:
    - 对 `Param` 对象使用 `@Valid` 或 `@Validated` 注解启用校验。
    - 在 `Param` 对象的字段上使用 JSR-303 (Bean Validation) 注解 (如 `@NotNull`, `@NotBlank`, `@Size`, `@Pattern` 等)。

## 响应处理
- **返回类型**: 所有Controller方法的返回类型必须是 `org.springframework.http.ResponseEntity<?>`。
- **成功响应**:
    - 对于非分页数据：调用Service层方法获取VO对象或简单列表，然后使用统一的响应工具 `com.dsj.inventory.common.entity.Res.success(voOrList)` 构建 `org.springframework.http.ResponseEntity`。
    - 对于分页查询：
        - Service 层通常返回 `com.baomidou.mybatisplus.extension.plugins.pagination.Page<VO>` 对象。
        - Controller 层根据请求参数对象中是否包含有效的分页参数（如 `page` 和 `size`，通常通过检查其是否为空或有意义的值）进行处理。
        - 若包含有效的分页参数，应使用项目统一的响应工具方法（例如 `com.dsj.inventory.common.entity.Res.successPage(pageObject)`）将 `Page` 对象包装成包含完整分页信息（如数据列表、总条数、总页数等）的 `ResponseEntity`。
        - 若不包含有效的分页参数（或业务场景需要返回所有数据），则通常从 `Page` 对象中提取所有记录（例如 `pageObject.getRecords()`），并使用项目统一的响应工具（例如 `Res.success(recordsList)`）构建仅包含数据列表的 `ResponseEntity`。
- **失败响应**: 异常应由全局异常处理器 (`GlobalExceptionHandler`) 统一处理并返回包含错误信息的 `ResponseEntity`。Controller层通常不需要显式处理业务异常，除非有特殊逻辑。

## API文档 (Knife4j)
- **类注解**: 使用 `@Api(tags = "模块描述")` 标注Controller类。
- **方法注解**: 使用 `@ApiOperation(value = "接口简要说明", notes = "接口详细描述")` 标注公开的API方法。
- **参数注解**:
    - 对简单类型参数使用 `@ApiParam(value = "参数说明", required = true/false, example = "示例值")`。
    - `@RequestBody` 标注的 `Param` 对象会自动被扫描，其内部字段需使用 `@ApiModelProperty` 标注。

## 示例
```java
import org.springframework.http.ResponseEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
// ... 其他 import

@Api(tags = "示例模块")
@RestController
@RequestMapping("/api/example")
public class ExampleController {

    @Autowired
    private ExampleService exampleService; // 注入Service接口

    @ApiOperation(value = "获取示例信息", notes = "根据ID获取示例信息")
    @GetMapping("/{id}")
    public ResponseEntity<ExampleVO> getExampleById(
            @ApiParam(value = "示例ID", required = true, example = "1") @PathVariable Long id) {
        ExampleVO vo = exampleService.getExampleInfo(id);
        return ResponseEntity.ok(vo);
    }

    @ApiOperation(value = "创建示例", notes = "创建一个新的示例")
    @PostMapping
    public ResponseEntity<Long> createExample(
            @Valid @RequestBody ExampleParam param) { // 使用@Valid校验Param对象
        Long newId = exampleService.createExample(param);
        return ResponseEntity.ok(newId);
    }

    @GetMapping("/list")
    @ApiOperation(value = "分页信息", notes = "获取信息的分页结果")
    Page<OrganizationSupplierVo> page = organizationSupplierService.queryOrganizationSupplierPage(param);
    if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
        return Res.successPage(page);
    }else {
        return Res.success(page.getRecords());
    }
}

@ApiModel(description = "示例请求参数")
@Data
public class ExampleParam {
    @ApiModelProperty(value = "示例名称", required = true, example = "测试示例")
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "示例类型", example = "TYPE1")
    private String type;
}

@ApiModel(description = "示例视图对象")
@Data
public class ExampleVO {
    @ApiModelProperty(value = "示例ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "示例名称", example = "测试示例")
    private String name;

    @ApiModelProperty(value = "创建时间", example = "2023-10-27 10:00:00")
    private LocalDateTime createTime;
}

```

