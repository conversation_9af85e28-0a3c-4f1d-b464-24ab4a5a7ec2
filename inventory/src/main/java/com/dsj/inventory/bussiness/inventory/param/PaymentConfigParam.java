package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 付款方式配置项参数
 */
@Data
@ApiModel(description = "付款方式配置项参数")
public class PaymentConfigParam {

    @ApiModelProperty(value = "主键ID（更新时必填）")
    private Long id;

    @ApiModelProperty(value = "配置项键", required = true)
    @NotBlank(message = "配置项键不能为空")
    private String configKey;

    @ApiModelProperty(value = "配置项值", required = true)
    @NotBlank(message = "配置项值不能为空")
    private String configValue;

    @ApiModelProperty(value = "配置项描述")
    private String description;

    @ApiModelProperty(value = "是否启用：1启用，0禁用")
    private Integer enabled = 1;
} 