package com.dsj.inventory.common.utils;

import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Map;

/**
 * 通用工具类
 * chenhao
 * 2021年4月22日21:01:03
 */
public class CommonUtils {

    /**
     * 判断空值
     *
     * @param obj
     * @return
     */
    public static boolean isEmpty(Object obj) {
        if (null == obj) {
            return true;
        }
        if (obj instanceof String) {
            String s = (String) obj;
            return s.trim().length() == 0 || "null".equals(s) || "NULL".equals(s) || "undefined".equals(s);
        }
        if (obj.getClass().isArray()) {
            return Array.getLength(obj) == 0;
        }
        if (obj instanceof Collection) {
            @SuppressWarnings("rawtypes")
            Collection c = (Collection) obj;
            return c.isEmpty();
        }
        if (obj instanceof Map) {
            @SuppressWarnings("rawtypes")
            Map m = (Map) obj;
            return m.isEmpty();
        }
        return false;
    }

    public static boolean isNotEmpty(Object obj) {
        return !isEmpty(obj);
    }


    private static double EARTH_RADIUS = 6378.137;

    private static double rad(double d) {
        return d * Math.PI / 180.0;
    }

    /**
     * 通过经纬度获取距离 不同的计算方式存在误差
     *
     * @param lat1  第一个点的纬度
     * @param lng1  第一个点的经度
     * @param lat2  第二个点的纬度
     * @param lng2  第二个点的经度
     * @return 距离 (单位：米)
     */
    public static double getDistance(double lat1, double lng1, double lat2,
                                     double lng2) {
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double a = radLat1 - radLat2;
        double b = rad(lng1) - rad(lng2);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2)
                + Math.cos(radLat1) * Math.cos(radLat2)
                * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;
        s = Math.round(s * 10000d) / 10000d;
        s = s * 1000;
        return s;
    }

    public static void main(String[] args) {
        double distance = getDistance(29.490295, 106.486654,
                29.615467, 106.581515);
        System.out.println(distance);
    }

    /**
     * 通过身份证获取性别
     * @param card
     * @return
     */
    public static Integer getGenderByCard(String card) {
        int gender = 0;
        if(card.length() == 18){
            //如果身份证号18位，取身份证号倒数第二位
            char c = card.charAt(card.length() - 2);
            gender = Integer.parseInt(String.valueOf(c));
        }else{
            //如果身份证号15位，取身份证号最后一位
            char c = card.charAt(card.length() - 1);
            gender = Integer.parseInt(String.valueOf(c));
        }
        if(gender % 2 == 1){
            return 1;
        }else{
            return 2;
        }
    }
}
