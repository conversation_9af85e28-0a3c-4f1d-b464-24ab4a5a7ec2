package com.dsj.inventory.framework.jwt.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * jwt 存储的 内容
 *
 * <AUTHOR>
 * @date 2018/11/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class JwtUserInfo implements Serializable {
    /**
     * 账号id
     */
    private Long userId;
    /**
     * 账号
     */
    private String account;
    /**
     * 姓名
     */
    private String name;
    /**
     * 商户号
     */
    private String tenantCode;

    private String mobile;

    public JwtUserInfo(Long userId, String account, String name) {
        this.userId = userId;
        this.account = account;
        this.name = name;
    }

    public JwtUserInfo(Long userId, String account, String name, String tenantCode) {
        this.userId = userId;
        this.account = account;
        this.name = name;
        this.tenantCode = tenantCode;
    }
}
