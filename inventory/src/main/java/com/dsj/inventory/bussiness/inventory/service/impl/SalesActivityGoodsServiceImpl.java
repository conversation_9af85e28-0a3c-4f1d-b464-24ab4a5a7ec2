package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityGoods;
import com.dsj.inventory.bussiness.inventory.mapper.SalesActivityGoodsMapper;
import com.dsj.inventory.bussiness.inventory.service.SalesActivityGoodsService;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityGoodsVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 进销存销售活动商品明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Service
public class SalesActivityGoodsServiceImpl extends ServiceImpl<SalesActivityGoodsMapper, SalesActivityGoods> implements SalesActivityGoodsService {

    @Override
    public List<SalesActivityGoodsVo> querySalesActivityGoodsList(Long activityId) {
        return this.baseMapper.querySalesActivityGoodsList(activityId);
    }
}
