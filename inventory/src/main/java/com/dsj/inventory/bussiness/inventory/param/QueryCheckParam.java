package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询盘点计划
 * <AUTHOR>
 * @date 2023/04/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QueryCheckParam",description = "查询盘点计划参数")
public class QueryCheckParam extends BasePage {

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "盘点状态：0暂存 1盘点中 2已完成")
    private Integer checkState;

    @ApiModelProperty(value = "盘点计划号模糊搜索")
    private String codeLike;

    @ApiModelProperty(value = "盘点计划名称模糊搜索")
    private String nameLike;

    @ApiModelProperty(value = "创建时间，查询条件--起始时间")
    private String createStart;

    @ApiModelProperty(value = "创建时间，查询条件--结束时间")
    private String createEnd;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "创建人模糊搜索")
    private String createUserNameLike;

    @ApiModelProperty(value = "盘点人模糊搜索")
    private String checkNameLike;

    @ApiModelProperty(value = "盘点人")
    private Long checkId;

    @ApiModelProperty(value = "货位Id")
    private Long shelfId;



}
