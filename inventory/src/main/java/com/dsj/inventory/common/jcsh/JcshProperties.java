package com.dsj.inventory.common.jcsh;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * 
 * 在使用的类上面加如下注解
 * @EnableConfigurationProperties(JcshProperties.class)
 * 或者在当前类上加
 * @Component
 * 
 * <AUTHOR>
 *
 */
@Data
@ConfigurationProperties(prefix="jsch")
public class JcshProperties {
	
	private boolean enable;
	
	private String sshHost;
	
	private int sshPort;
	
	private String sshUser;
	
	private String sshPassword;
	
	private List<ForwardConfig> forwards;
	
	@Data
	public static class ForwardConfig {
		private String jschlHost;
		
		private int jschlPort;
		
		private String jschrHost;
		
		private int jschrPort;
	}

}
