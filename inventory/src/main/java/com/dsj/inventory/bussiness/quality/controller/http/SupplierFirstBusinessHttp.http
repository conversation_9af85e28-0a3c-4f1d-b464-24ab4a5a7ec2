###
# Create Supplier First Business Record
POST {{baseUrl}}/api/quality/supplier-first-business
Content-Type: application/json
token: {{token}}

{
  "supplierId": 101,
  "applyTime": "2024-07-28T10:00:00",
  "status": 0,
  "approvalOpinion": "Initial application for New Supplier Inc.",
  "remark": "Awaiting QA review"
}

###
# Get Supplier First Business Record by ID
# @name getSupplierFirstBusinessById
GET {{baseUrl}}/api/quality/supplier-first-business/{{createdId}}
Content-Type: application/json
token: {{token}}

###
# Update Supplier First Business Record
PUT {{baseUrl}}/api/quality/supplier-first-business/{{createdId}}
Content-Type: application/json
token: {{token}}

{
  "status": 1, # Approved
  "reviewerId": 55,
  "reviewTime": "2024-07-29T14:30:00",
  "approvalOpinion": "Approved after review. All documents are valid.",
  "remark": "Supplier is now active for procurement."
}

###
# Query Supplier First Business Records (Page)
GET {{baseUrl}}/api/quality/supplier-first-business/page?page=1&size=5&status=1
Content-Type: application/json
token: {{token}}

###
# Query All Supplier First Business Records (No Pagination)
GET {{baseUrl}}/api/quality/supplier-first-business/page?status=0
Content-Type: application/json
token: {{token}}

###
# Delete Supplier First Business Record
DELETE {{baseUrl}}/api/quality/supplier-first-business/{{createdId}}
Content-Type: application/json
token: {{token}}

###
# Try to Get Deleted Record (should be null or error)
GET {{baseUrl}}/api/quality/supplier-first-business/{{createdId}}
Content-Type: application/json
token: {{token}} 