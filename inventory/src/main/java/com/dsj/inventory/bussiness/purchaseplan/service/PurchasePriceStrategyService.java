package com.dsj.inventory.bussiness.purchaseplan.service;

import java.util.List;
import java.util.Map;

/**
 * 进货单价策略服务接口
 * 提供三种价格策略的计算方法：最低进价、最新供应商、上次进价
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
public interface PurchasePriceStrategyService {
    
    // ==================== 主要策略方法 ====================
    
    /**
     * 根据策略获取进货单价
     * 
     * @param organizationId 组织ID，不能为空
     * @param organizationMedicineId 组织商品ID，不能为空
     * @param organizationSupplierId 供应商ID，可为空（策略2和3需要）
     * @param strategy 价格策略：1-最低进价，2-最新供应商，3-上次进价
     * @return 进货单价（分），如果无法获取则返回null
     * @throws IllegalArgumentException 当必要参数为空或策略无效时抛出
     */
    Integer getPurchasePriceByStrategy(Long organizationId, Long organizationMedicineId, 
                                     Long organizationSupplierId, Integer strategy);
    
    /**
     * 批量根据策略获取进货单价
     * 
     * @param organizationId 组织ID，不能为空
     * @param goodsStrategyMap 商品策略映射，key为organizationMedicineId，value为策略信息
     * @return 价格映射，key为organizationMedicineId，value为进货单价（分）
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    Map<Long, Integer> batchGetPurchasePriceByStrategy(Long organizationId, 
                                                      Map<Long, PriceStrategyInfo> goodsStrategyMap);
    
    // ==================== 具体策略方法 ====================
    
    /**
     * 策略1：按最低进价生成
     * 查询指定天数内该商品的最低进货价格
     * 
     * @param organizationId 组织ID，不能为空
     * @param organizationMedicineId 组织商品ID，不能为空
     * @param days 查询天数，如果为空则使用系统配置的默认天数
     * @return 最低进货价格（分），如果查询不到则返回null
     * @throws IllegalArgumentException 当必要参数为空时抛出
     */
    Integer getLowestPurchasePrice(Long organizationId, Long organizationMedicineId, Integer days);
    
    /**
     * 策略2：按最新供应商生成
     * 查询该商品最近一次采购记录的进货价格
     * 
     * @param organizationId 组织ID，不能为空
     * @param organizationMedicineId 组织商品ID，不能为空
     * @return 最新采购价格（分），如果查询不到则返回null
     * @throws IllegalArgumentException 当必要参数为空时抛出
     */
    Integer getLatestSupplierPrice(Long organizationId, Long organizationMedicineId);
    
    /**
     * 策略3：按上次进价生成
     * 查询该商品与指定供应商的上次采购价格
     * 
     * @param organizationId 组织ID，不能为空
     * @param organizationMedicineId 组织商品ID，不能为空
     * @param organizationSupplierId 供应商ID，不能为空
     * @return 上次进货价格（分），如果查询不到则返回null
     * @throws IllegalArgumentException 当必要参数为空时抛出
     */
    Integer getLastPurchasePrice(Long organizationId, Long organizationMedicineId, Long organizationSupplierId);
    
    // ==================== 供应商推荐方法 ====================
    
    /**
     * 获取商品最近使用的供应商
     * 根据最近的采购记录推荐供应商
     * 
     * @param organizationId 组织ID，不能为空
     * @param organizationMedicineId 组织商品ID，不能为空
     * @return 供应商ID，如果查询不到则返回null
     * @throws IllegalArgumentException 当必要参数为空时抛出
     */
    Long getRecentSupplier(Long organizationId, Long organizationMedicineId);
    
    /**
     * 获取商品的所有供应商及其最新价格
     * 
     * @param organizationId 组织ID，不能为空
     * @param organizationMedicineId 组织商品ID，不能为空
     * @return 供应商价格映射，key为供应商ID，value为最新价格（分）
     * @throws IllegalArgumentException 当必要参数为空时抛出
     */
    Map<Long, Integer> getSupplierPriceMap(Long organizationId, Long organizationMedicineId);
    
    /**
     * 批量获取商品的推荐供应商
     * 
     * @param organizationId 组织ID，不能为空
     * @param organizationMedicineIds 组织商品ID列表，不能为空
     * @return 供应商映射，key为organizationMedicineId，value为推荐的供应商ID
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    Map<Long, Long> batchGetRecentSupplier(Long organizationId, List<Long> organizationMedicineIds);
    
    // ==================== 价格历史查询方法 ====================
    
    /**
     * 获取商品的价格历史记录
     * 
     * @param organizationId 组织ID，不能为空
     * @param organizationMedicineId 组织商品ID，不能为空
     * @param days 查询天数，如果为空则查询所有历史
     * @return 价格历史列表，按时间倒序排列
     * @throws IllegalArgumentException 当必要参数为空时抛出
     */
    List<PurchasePriceHistory> getPriceHistory(Long organizationId, Long organizationMedicineId, Integer days);
    
    /**
     * 获取商品与特定供应商的价格历史
     * 
     * @param organizationId 组织ID，不能为空
     * @param organizationMedicineId 组织商品ID，不能为空
     * @param organizationSupplierId 供应商ID，不能为空
     * @param days 查询天数，如果为空则查询所有历史
     * @return 价格历史列表，按时间倒序排列
     * @throws IllegalArgumentException 当必要参数为空时抛出
     */
    List<PurchasePriceHistory> getPriceHistoryWithSupplier(Long organizationId, Long organizationMedicineId, 
                                                          Long organizationSupplierId, Integer days);
    
    // ==================== 价格统计方法 ====================
    
    /**
     * 获取商品价格统计信息
     * 
     * @param organizationId 组织ID，不能为空
     * @param organizationMedicineId 组织商品ID，不能为空
     * @param days 统计天数，如果为空则使用系统配置的默认天数
     * @return 价格统计信息
     * @throws IllegalArgumentException 当必要参数为空时抛出
     */
    PurchasePriceStatistics getPriceStatistics(Long organizationId, Long organizationMedicineId, Integer days);
    
    /**
     * 批量获取商品价格统计信息
     * 
     * @param organizationId 组织ID，不能为空
     * @param organizationMedicineIds 组织商品ID列表，不能为空
     * @param days 统计天数，如果为空则使用系统配置的默认天数
     * @return 价格统计映射，key为organizationMedicineId，value为价格统计信息
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    Map<Long, PurchasePriceStatistics> batchGetPriceStatistics(Long organizationId, 
                                                              List<Long> organizationMedicineIds, Integer days);
    
    // ==================== 价格验证方法 ====================
    
    /**
     * 验证价格是否合理
     * 根据历史价格判断当前价格是否在合理范围内
     * 
     * @param organizationId 组织ID，不能为空
     * @param organizationMedicineId 组织商品ID，不能为空
     * @param currentPrice 当前价格（分），不能为空
     * @param days 参考天数，如果为空则使用系统配置的默认天数
     * @return 价格验证结果
     * @throws IllegalArgumentException 当必要参数为空时抛出
     */
    PriceValidationResult validatePrice(Long organizationId, Long organizationMedicineId, 
                                       Integer currentPrice, Integer days);
    
    /**
     * 批量验证价格是否合理
     * 
     * @param organizationId 组织ID，不能为空
     * @param priceMap 价格映射，key为organizationMedicineId，value为当前价格（分）
     * @param days 参考天数，如果为空则使用系统配置的默认天数
     * @return 验证结果映射，key为organizationMedicineId，value为验证结果
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    Map<Long, PriceValidationResult> batchValidatePrice(Long organizationId, 
                                                       Map<Long, Integer> priceMap, Integer days);
    
    // ==================== 内部数据类定义 ====================
    
    /**
     * 价格策略信息
     */
    class PriceStrategyInfo {
        private Integer strategy;           // 价格策略
        private Long organizationSupplierId; // 供应商ID（策略3需要）
        
        public PriceStrategyInfo() {}
        
        public PriceStrategyInfo(Integer strategy, Long organizationSupplierId) {
            this.strategy = strategy;
            this.organizationSupplierId = organizationSupplierId;
        }
        
        // Getters and Setters
        public Integer getStrategy() { return strategy; }
        public void setStrategy(Integer strategy) { this.strategy = strategy; }
        public Long getOrganizationSupplierId() { return organizationSupplierId; }
        public void setOrganizationSupplierId(Long organizationSupplierId) { this.organizationSupplierId = organizationSupplierId; }
    }
    
    /**
     * 采购价格历史记录
     */
    class PurchasePriceHistory {
        private Long organizationMedicineId;    // 组织商品ID
        private Long organizationSupplierId;    // 供应商ID
        private Integer purchasePrice;          // 采购价格（分）
        private java.time.LocalDateTime purchaseDate; // 采购日期
        private String sourceType;             // 来源类型（采购订单、入库单等）
        private Long sourceId;                 // 来源ID
        
        // Getters and Setters
        public Long getOrganizationMedicineId() { return organizationMedicineId; }
        public void setOrganizationMedicineId(Long organizationMedicineId) { this.organizationMedicineId = organizationMedicineId; }
        public Long getOrganizationSupplierId() { return organizationSupplierId; }
        public void setOrganizationSupplierId(Long organizationSupplierId) { this.organizationSupplierId = organizationSupplierId; }
        public Integer getPurchasePrice() { return purchasePrice; }
        public void setPurchasePrice(Integer purchasePrice) { this.purchasePrice = purchasePrice; }
        public java.time.LocalDateTime getPurchaseDate() { return purchaseDate; }
        public void setPurchaseDate(java.time.LocalDateTime purchaseDate) { this.purchaseDate = purchaseDate; }
        public String getSourceType() { return sourceType; }
        public void setSourceType(String sourceType) { this.sourceType = sourceType; }
        public Long getSourceId() { return sourceId; }
        public void setSourceId(Long sourceId) { this.sourceId = sourceId; }
    }
    
    /**
     * 采购价格统计信息
     */
    class PurchasePriceStatistics {
        private Long organizationMedicineId;    // 组织商品ID
        private Integer minPrice;              // 最低价格（分）
        private Integer maxPrice;              // 最高价格（分）
        private Integer avgPrice;              // 平均价格（分）
        private Integer latestPrice;           // 最新价格（分）
        private Integer recordCount;           // 记录数量
        private java.time.LocalDateTime startDate; // 统计开始日期
        private java.time.LocalDateTime endDate;   // 统计结束日期
        
        // Getters and Setters
        public Long getOrganizationMedicineId() { return organizationMedicineId; }
        public void setOrganizationMedicineId(Long organizationMedicineId) { this.organizationMedicineId = organizationMedicineId; }
        public Integer getMinPrice() { return minPrice; }
        public void setMinPrice(Integer minPrice) { this.minPrice = minPrice; }
        public Integer getMaxPrice() { return maxPrice; }
        public void setMaxPrice(Integer maxPrice) { this.maxPrice = maxPrice; }
        public Integer getAvgPrice() { return avgPrice; }
        public void setAvgPrice(Integer avgPrice) { this.avgPrice = avgPrice; }
        public Integer getLatestPrice() { return latestPrice; }
        public void setLatestPrice(Integer latestPrice) { this.latestPrice = latestPrice; }
        public Integer getRecordCount() { return recordCount; }
        public void setRecordCount(Integer recordCount) { this.recordCount = recordCount; }
        public java.time.LocalDateTime getStartDate() { return startDate; }
        public void setStartDate(java.time.LocalDateTime startDate) { this.startDate = startDate; }
        public java.time.LocalDateTime getEndDate() { return endDate; }
        public void setEndDate(java.time.LocalDateTime endDate) { this.endDate = endDate; }
    }
    
    /**
     * 价格验证结果
     */
    class PriceValidationResult {
        private Boolean isValid;               // 是否有效
        private String message;                // 验证消息
        private Integer suggestedPrice;        // 建议价格（分）
        private Double deviationPercentage;    // 偏差百分比
        
        // Getters and Setters
        public Boolean getIsValid() { return isValid; }
        public void setIsValid(Boolean isValid) { this.isValid = isValid; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Integer getSuggestedPrice() { return suggestedPrice; }
        public void setSuggestedPrice(Integer suggestedPrice) { this.suggestedPrice = suggestedPrice; }
        public Double getDeviationPercentage() { return deviationPercentage; }
        public void setDeviationPercentage(Double deviationPercentage) { this.deviationPercentage = deviationPercentage; }
    }
}
