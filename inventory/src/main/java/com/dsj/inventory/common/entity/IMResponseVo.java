package com.dsj.inventory.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * IM 聊天请求响应内容
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class IMResponseVo {

    /**
     * 请求处理的结果，OK 表示处理成功，FAIL 表示失败
     */
    private String ActionStatus;

    /**
     * 错误码，0表示成功，非0表示失败
     */
    private Integer ErrorCode;

    /**
     * 错误信息
     */
    private String ErrorInfo;

    /**
     * 消息时间戳，UNIX 时间戳
     */
    private Integer MsgTime;

    /**
     * 消息唯一标识，用于撤回。长度不超过50个字符
     */
    private Integer MsgKey;
}
