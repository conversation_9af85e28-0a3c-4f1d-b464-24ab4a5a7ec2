package com.dsj.inventory.bussiness.inventory.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryOrganizationDictParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateOrganizationDictParam;
import com.dsj.inventory.bussiness.inventory.service.OrganizationDictService;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationDictVO;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 组织字典参数 Controller
 */
@Api(tags = "组织字典参数管理")
@RestController
@RequestMapping("/api/organization-dict")
@RequiredArgsConstructor
public class OrganizationDictController {

    private final OrganizationDictService organizationDictService;

    @ApiOperation(value = "保存或更新组织字典参数", notes = "创建或修改组织字典参数")
    @PostMapping("/save")
    public ResponseEntity<Long> saveOrUpdateOrganizationDict(@Valid @RequestBody SaveOrUpdateOrganizationDictParam param) {
        Long id = organizationDictService.saveOrUpdateOrganizationDict(param);
        return Res.success(id);
    }

    @ApiOperation(value = "删除组织字典参数", notes = "根据ID删除组织字典参数")
    @DeleteMapping("/{id}")
    public ResponseEntity<Boolean> deleteOrganizationDict(
            @ApiParam(value = "组织字典参数ID", required = true) @PathVariable Long id) {
        Boolean result = organizationDictService.deleteOrganizationDict(id);
        return Res.success(result);
    }

    @ApiOperation(value = "获取组织字典参数", notes = "根据ID获取组织字典参数详情")
    @GetMapping("/{id}")
    public ResponseEntity<OrganizationDictVO> getOrganizationDict(
            @ApiParam(value = "组织字典参数ID", required = true) @PathVariable Long id) {
        OrganizationDictVO vo = organizationDictService.getOrganizationDict(id);
        return Res.success(vo);
    }

    @ApiOperation(value = "分页查询组织字典参数", notes = "根据条件分页查询组织字典参数列表")
    @GetMapping("/page")
    public ResponseEntity<List<OrganizationDictVO>> queryOrganizationDictPage(QueryOrganizationDictParam param) {
        Page<OrganizationDictVO> page = organizationDictService.queryOrganizationDictPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    @ApiOperation(value = "根据组织ID和字典类型查询字典列表", notes = "获取指定组织和字典类型的所有字典项")
    @GetMapping("/list/{organizationId}/{dictType}")
    public ResponseEntity<List<OrganizationDictVO>> listByOrgAndType(
            @ApiParam(value = "组织ID", required = true) @PathVariable Long organizationId,
            @ApiParam(value = "字典类型", required = true) @PathVariable String dictType) {
        List<OrganizationDictVO> list = organizationDictService.listByOrgAndType(organizationId, dictType);
        return Res.success(list);
    }

    @ApiOperation(value = "根据组织ID、字典类型和编码查询字典", notes = "获取指定组织、字典类型和编码的字典项")
    @GetMapping("/get/{organizationId}/{dictType}/{dictCode}")
    public ResponseEntity<OrganizationDictVO> getByOrgTypeAndCode(
            @ApiParam(value = "组织ID", required = true) @PathVariable Long organizationId,
            @ApiParam(value = "字典类型", required = true) @PathVariable String dictType,
            @ApiParam(value = "字典编码", required = true) @PathVariable String dictCode) {
        OrganizationDictVO vo = organizationDictService.getByOrgTypeAndCode(organizationId, dictType, dictCode);
        return Res.success(vo);
    }
} 