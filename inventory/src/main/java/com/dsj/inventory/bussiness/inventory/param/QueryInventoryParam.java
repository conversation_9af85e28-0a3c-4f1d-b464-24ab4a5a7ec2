package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询商品库存
 * <AUTHOR>
 * @date 2023/04/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QueryInventoryParam",description = "查询商品库存参数")
public class QueryInventoryParam extends BasePage {

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "商品信息（商品编号/通用名称）")
    private String goodsLike;

    @ApiModelProperty(value = "传1库存大于0，传0库存等于0，不传获取全部")
    private Integer zeroState;

    @ApiModelProperty(value = "货位Id")
    private Long shelfId;

    @ApiModelProperty(value = "货位Ids，多个用逗号分割")
    private String shelfIds;

    @ApiModelProperty(value = "批次")
    private String batch;

}
