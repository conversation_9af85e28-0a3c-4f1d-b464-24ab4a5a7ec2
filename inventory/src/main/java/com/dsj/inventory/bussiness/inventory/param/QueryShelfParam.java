package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询货架
 * <AUTHOR>
 * @date 2023/04/03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QueryShelfParam",description = "查询货架参数")
public class QueryShelfParam extends BasePage {

    @ApiModelProperty(value = "组织id")
    private Long organizationId;

    @ApiModelProperty(value = "货架名称模糊搜索")
    private String nameLike;

    @ApiModelProperty(value = "父级id")
    private Long parentId;

    @ApiModelProperty(value = "货架类型 0普通货架1特殊货架")
    private Integer type;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "是否展示全部货架 0否1是")
    private Integer isAll = 0;

}
