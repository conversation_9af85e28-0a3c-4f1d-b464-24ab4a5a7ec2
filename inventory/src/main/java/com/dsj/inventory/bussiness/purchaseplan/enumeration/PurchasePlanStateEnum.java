package com.dsj.inventory.bussiness.purchaseplan.enumeration;

import lombok.Getter;


/**
 * 采购计划处理状态 0待执行 1已执行
 * <AUTHOR>
 */

@Getter
public enum PurchasePlanStateEnum {

    WAITING(0, "待执行"),
    EXECUTED(1, "已执行"),
    ;

    private Integer code;

    private String desc;

    PurchasePlanStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (PurchasePlanStateEnum a : PurchasePlanStateEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
