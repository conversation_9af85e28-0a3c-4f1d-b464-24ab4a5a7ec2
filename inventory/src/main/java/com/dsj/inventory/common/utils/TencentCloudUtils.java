package com.dsj.inventory.common.utils;

import com.pocky.transport.bussiness.tencent.param.TencentVodReq;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DozerUtils;
import com.dsj.inventory.common.utils.TencentIMUtils;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.trtc.v20190722.TrtcClient;
import com.tencentcloudapi.trtc.v20190722.models.*;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @ClassName : TencentCloudUtils
 * @Description : 腾讯云录制 工具类
 * @Date :
 */
@Slf4j
@Data
@Component
public class TencentCloudUtils {

    private Long SDKAPPID = 0L;

    private String SDKAPPSECRETKEY = "";

    private int EXPIRETIME = 604800;

    private String SECRETID = "";

    private String SECRETKEY = "";

    /** 云端录制URL */
    private static final String TRTC_ENDPOINT = "trtc.tencentcloudapi.com";

    private static final String VOD_ENDPOINT = "vod.tencentcloudapi.com";

    @Autowired
    private TencentIMUtils tencentIMUtils;
    @Autowired
    private DozerUtils dozerUtils;

    /**
     * 开始云端录制
     * 文档地址 https://cloud.tencent.com/document/product/647/73786
     * @param roomId 房间号
     * @param userId 录制机器人的userId
     * @param tencentVodReq 云点播参数
     * @return
     */
    public CreateCloudRecordingResponse createCloudRecording(String roomId,String userId,TencentVodReq tencentVodReq){
        try{
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(SECRETID, SECRETKEY);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(TRTC_ENDPOINT);
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            TrtcClient client = new TrtcClient(cred, "ap-beijing", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            CreateCloudRecordingRequest req = new CreateCloudRecordingRequest();
            req.setSdkAppId(SDKAPPID);
            req.setRoomId(roomId);
            req.setRoomIdType(0L);
            req.setUserId(userId);
            String userSig = tencentIMUtils.getUserSig(SDKAPPID,userId,EXPIRETIME,SDKAPPSECRETKEY);
            req.setUserSig(userSig);
            RecordParams recordParams1 = new RecordParams();
            recordParams1.setMaxIdleTime(30L);
            recordParams1.setStreamType(0L);
            recordParams1.setRecordMode(2L);
            req.setRecordParams(recordParams1);

            StorageParams storageParams1 = new StorageParams();
            CloudVod cloudVod1 = new CloudVod();
            TencentVod tencentVod1 = new TencentVod();
            if(CommonUtils.isNotEmpty(tencentVodReq)){
                tencentVod1 = dozerUtils.map(tencentVodReq,TencentVod.class);
            }
            tencentVod1.setExpireTime(0L);
            cloudVod1.setTencentVod(tencentVod1);
            storageParams1.setCloudVod(cloudVod1);
            req.setStorageParams(storageParams1);

            MixTranscodeParams mixTranscodeParams1 = new MixTranscodeParams();
            VideoParams videoParams1 = new VideoParams();
            videoParams1.setWidth(1280L);
            videoParams1.setHeight(720L);
            videoParams1.setFps(15L);
            videoParams1.setBitRate(550000L);
            videoParams1.setGop(10L);
            mixTranscodeParams1.setVideoParams(videoParams1);
            req.setMixTranscodeParams(mixTranscodeParams1);

            MixLayoutParams mixLayoutParams1 = new MixLayoutParams();
            mixLayoutParams1.setMixLayoutMode(3L);
            mixLayoutParams1.setMediaId(0L);
            mixLayoutParams1.setPlaceHolderMode(0L);
            req.setMixLayoutParams(mixLayoutParams1);
            // 返回的resp是一个CreateCloudRecordingResponse的实例，与请求对象对应
            log.info("=======开始云端录制《开始》======请求参数:{}",CreateCloudRecordingRequest.toJsonString(req));
            CreateCloudRecordingResponse resp = client.CreateCloudRecording(req);
            log.info("=======开始云端录制《成功》======result:{}",CreateCloudRecordingResponse.toJsonString(resp));
            return resp;
        } catch (TencentCloudSDKException e) {
            log.info("=======开始云端录制《失败》======请求参数:{}",e.toString());
        }
        return null;
    }

    /**
     * 开始云端录制、单流
     * 文档地址 https://cloud.tencent.com/document/product/647/73786
     * @param roomId 房间号
     * @param userId 录制机器人的userId
     * @param tencentVodReq 云点播参数
     * @return
     */
    public CreateCloudRecordingResponse createSingleCloudRecording(String roomId,String userId,TencentVodReq tencentVodReq){
        try{
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(SECRETID, SECRETKEY);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(TRTC_ENDPOINT);
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            TrtcClient client = new TrtcClient(cred, "ap-beijing", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            CreateCloudRecordingRequest req = new CreateCloudRecordingRequest();
            req.setSdkAppId(SDKAPPID);
            req.setRoomId(roomId);
            req.setRoomIdType(0L);
            req.setUserId(userId);
            String userSig = tencentIMUtils.getUserSig(SDKAPPID,userId,EXPIRETIME,SDKAPPSECRETKEY);
            req.setUserSig(userSig);
            RecordParams recordParams1 = new RecordParams();
            recordParams1.setRecordMode(1L);
            recordParams1.setMaxIdleTime(30L);
            recordParams1.setStreamType(0L);
            req.setRecordParams(recordParams1);

            StorageParams storageParams1 = new StorageParams();
            CloudVod cloudVod1 = new CloudVod();
            TencentVod tencentVod1 = new TencentVod();
            if(CommonUtils.isNotEmpty(tencentVodReq)){
                tencentVod1 = dozerUtils.map(tencentVodReq,TencentVod.class);
            }
            tencentVod1.setExpireTime(0L);
            cloudVod1.setTencentVod(tencentVod1);
            storageParams1.setCloudVod(cloudVod1);
            req.setStorageParams(storageParams1);

            // 返回的resp是一个CreateCloudRecordingResponse的实例，与请求对象对应
            log.info("=======开始云端录制《开始》======请求参数:{}",CreateCloudRecordingRequest.toJsonString(req));
            CreateCloudRecordingResponse resp = client.CreateCloudRecording(req);
            log.info("=======开始云端录制《成功》======result:{}",CreateCloudRecordingResponse.toJsonString(resp));
            return resp;
        } catch (TencentCloudSDKException e) {
            log.info("=======开始云端录制《失败》======请求参数:{}",e.toString());
        }
        return null;
    }

    /**
     * 结束云端录制
     * 文档地址 https://cloud.tencent.com/document/product/647/73786
     * taskId ：任务Id
     */
    public DeleteCloudRecordingResponse deleteCloudRecording(String taskId){
        try{
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(SECRETID, SECRETKEY);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(TRTC_ENDPOINT);
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            TrtcClient client = new TrtcClient(cred, "ap-beijing", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            DeleteCloudRecordingRequest req = new DeleteCloudRecordingRequest();
            req.setSdkAppId(SDKAPPID);
            req.setTaskId(taskId);
            // 返回的resp是一个DeleteCloudRecordingResponse的实例，与请求对象对应
            log.info("=======停止云端录制《开始》======请求参数:{}",DeleteCloudRecordingRequest.toJsonString(req));
            DeleteCloudRecordingResponse resp = client.DeleteCloudRecording(req);
            log.info("=======停止云端录制《成功》======result:{}",DeleteCloudRecordingResponse.toJsonString(resp));
            return resp;
        } catch (TencentCloudSDKException e) {
            log.info("=======停止云端录制《失败》======请求参数:{}",e.toString());
        }
        return null;
    }

    /**
     * 获取录制文件基本信息
     * 文档地址 https://cloud.tencent.com/document/product/266/31763
     * fileIdList ：文件IdList
     */
    public DescribeMediaInfosResponse describeBasicMediaInfos(String[] fileIds){
        try{
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            System.out.println(SECRETID);
            System.out.println(SECRETKEY);
            Credential cred = new Credential(SECRETID, SECRETKEY);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(VOD_ENDPOINT);
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            VodClient client = new VodClient(cred, "", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            DescribeMediaInfosRequest req = new DescribeMediaInfosRequest();
            req.setFileIds(fileIds);

            // 返回的resp是一个DescribeMediaInfosResponse的实例，与请求对象对应
            DescribeMediaInfosResponse resp = client.DescribeMediaInfos(req);
            // 输出json格式的字符串回包
            log.info(DescribeMediaInfosResponse.toJsonString(resp));
            return resp;
        } catch (TencentCloudSDKException e) {
            log.info(e.toString());
        }
        return null;

    }

    /**
     * 删除录制文件
     * 文档地址 https://cloud.tencent.com/document/product/266/31763
     * fileId ：文件Id
     */
    public void deleteMediaInfo(String fileId){
        try{
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(SECRETID, SECRETKEY);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(VOD_ENDPOINT);
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            VodClient client = new VodClient(cred, "", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            DeleteMediaRequest req = new DeleteMediaRequest();
            req.setFileId(fileId);
            log.info("=======删除云端录制文件《开始》======请求参数:{}",DeleteMediaRequest.toJsonString(req));
            // 返回的resp是一个DeleteMediaResponse的实例，与请求对象对应
            DeleteMediaResponse resp = client.DeleteMedia(req);
            // 输出json格式的字符串回包
            log.info("=======删除云端录制文件《成功》======result:{}",DeleteMediaResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            log.info("=======删除云端录制文件《失败》======错误信息:{}",e.toString());
        }
    }

    /**
     * 修改媒体文件存储类型
     * 文档地址 https://cloud.tencent.com/document/product/266/70862
     * fileId ：文件Id
     */
    public void modifyMediaStorageClass(String[] fileIds, String storageClass){
        try{
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(SECRETID, SECRETKEY);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(VOD_ENDPOINT);
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            VodClient client = new VodClient(cred, "", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            ModifyMediaStorageClassRequest req = new ModifyMediaStorageClassRequest();
            req.setFileIds(fileIds);

            req.setStorageClass(storageClass);
            // 返回的resp是一个ModifyMediaStorageClassResponse的实例，与请求对象对应
            log.info("=======修改媒体文件存储类型《开始》======请求参数:{}",ModifyMediaStorageClassRequest.toJsonString(req));
            ModifyMediaStorageClassResponse resp = client.ModifyMediaStorageClass(req);
            // 输出json格式的字符串回包
            log.info("=======修改媒体文件存储类型《结束》======result:{}",AbstractModel.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            log.info("=======修改媒体文件存储类型《失败》======错误信息:{}",e.toString());
        }
    }

    /**
     * 解冻媒体文件
     * 文档地址 https://cloud.tencent.com/document/product/266/75728
     * fileId ：文件Id
     */
    public void restoreMedia(String fileId,String restoreTier){
        try{
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(SECRETID, SECRETKEY);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(VOD_ENDPOINT);
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            VodClient client = new VodClient(cred, "", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            RestoreMediaRequest req = new RestoreMediaRequest();
            String[] fileIds1 = {fileId};
            req.setFileIds(fileIds1);

            req.setRestoreDay(7L);
            req.setRestoreTier(restoreTier);
            // 返回的resp是一个RestoreMediaResponse的实例，与请求对象对应
            log.info("=======解冻媒体文件《开始》======请求参数:{}",RestoreMediaRequest.toJsonString(req));
            RestoreMediaResponse resp = client.RestoreMedia(req);
            // 输出json格式的字符串回包
            log.info("=======解冻媒体文件《结束》======result:{}",AbstractModel.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            log.info("=======解冻媒体文件《失败》======错误信息:{}",e.toString());
        }
    }

    /**
     * 修改媒体文件分类
     * 文档地址 https://cloud.tencent.com/document/product/266/31762
     * fileId ：文件Id
     */
    public void modifyMediaClassId(String fileId, Long classId){
        try{
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(SECRETID, SECRETKEY);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(VOD_ENDPOINT);
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            VodClient client = new VodClient(cred, "", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            ModifyMediaInfoRequest req = new ModifyMediaInfoRequest();
            req.setFileId(fileId);
            req.setClassId(classId);
            log.info("=======修改媒体文件分类《开始》======请求参数:{}",ModifyMediaStorageClassRequest.toJsonString(req));
            ModifyMediaInfoResponse resp = client.ModifyMediaInfo(req);
            // 输出json格式的字符串回包
            log.info("=======修改媒体文件分类《结束》======result:{}",AbstractModel.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            log.info("=======修改媒体文件分类《失败》======错误信息:{}",e.toString());
        }
    }

}
