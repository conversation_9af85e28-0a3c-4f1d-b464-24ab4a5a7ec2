package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.AcceptOrderGoods;
import com.dsj.inventory.bussiness.inventory.vo.AcceptOrderGoodsVo;
import com.dsj.inventory.common.enumeration.TrueEnum;

import java.util.List;

/**
 * <p>
 * 进销存验收单商品明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface AcceptOrderGoodsMapper extends BaseMapper<AcceptOrderGoods> {

    /**
     * 获取验收单商品明细
     * @param orderId
     * @return
     */
    List<AcceptOrderGoodsVo> getAcceptOrderGoodsList(Long orderId);
    
    /**
     * 根据订单ID将商品明细标记为已删除
     * @param orderId 订单ID
     * @return 更新记录数
     */
    default int markAsDeletedByOrderId(Long orderId) {
        LambdaUpdateWrapper<AcceptOrderGoods> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AcceptOrderGoods::getOrderId, orderId)
                .set(AcceptOrderGoods::getIsDelete, TrueEnum.TRUE.getCode());
        return this.update(new AcceptOrderGoods(), updateWrapper);
    }

    default AcceptOrderGoods selectByOrderIdAndOrgMedicineId(Long orderId, Long organizationMedicineId){
        LambdaQueryWrapper<AcceptOrderGoods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AcceptOrderGoods::getOrderId, orderId)
                .eq(AcceptOrderGoods::getOrganizationMedicineId, organizationMedicineId);
        return this.selectOne(queryWrapper);
    }
}
