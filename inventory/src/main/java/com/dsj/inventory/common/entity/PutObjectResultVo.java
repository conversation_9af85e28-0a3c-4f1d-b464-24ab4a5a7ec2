package com.dsj.inventory.common.entity;

import com.obs.services.model.StorageClassEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @ClassName : PutObjectResultVo
 * @Description : OBS 自定义返回内容
 * @Date : 2022-07-26 16:12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PutObjectResultVo {

    private String bucketName;

    private String objectKey;

    private String etag;

    private String versionId;

    private StorageClassEnum storageClass;

    private String objectUrl;

}
