package com.dsj.inventory.common.entity;

import com.dsj.inventory.common.entity.TencentMessageDetailResponseVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * 腾讯云发送短信请求响应内容
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class TencentMessageResponseVo {

    private String RequestId;

    private List<TencentMessageDetailResponseVo> SendStatusSet;

}
