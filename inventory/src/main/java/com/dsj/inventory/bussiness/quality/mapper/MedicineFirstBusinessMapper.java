package com.dsj.inventory.bussiness.quality.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.quality.entity.MedicineFirstBusinessEntity;
import com.dsj.inventory.bussiness.quality.param.MedicineFirstBusinessQueryParam;
import com.dsj.inventory.bussiness.quality.vo.MedicineFirstBusinessVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 药品首营审批表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Mapper
public interface MedicineFirstBusinessMapper extends BaseMapper<MedicineFirstBusinessEntity> {

    /**
     * 获取最新的药品首营审批记录
     *
     * @param orgMedicineMappingId 药品私域ID
     * @param supplierId 供应商ID
     * @return 最新的审批记录
     */
    default MedicineFirstBusinessEntity getLatestMedicineFirstBusiness(Long orgMedicineMappingId, Long supplierId) {
        LambdaQueryWrapper<MedicineFirstBusinessEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MedicineFirstBusinessEntity::getOrgMedicineMappingId, orgMedicineMappingId)
                .eq(MedicineFirstBusinessEntity::getSupplierId, supplierId)
                .orderByDesc(MedicineFirstBusinessEntity::getApprovalSequence);
        queryWrapper.last("LIMIT 1");
        return this.selectOne(queryWrapper);
    }

    /**
     * 根据药品ID、供应商ID和状态获取首营审批记录
     *
     * @param orgMedicineMappingId 药品私域ID
     * @param supplierId 供应商ID
     * @param status 状态 (可为null)
     * @return 符合条件的审批记录
     */
    default MedicineFirstBusinessEntity getMedicineFirstBusinessByMedicineAndSupplierAndStatus(
            Long orgMedicineMappingId, Long supplierId, Integer status) {
        LambdaQueryWrapper<MedicineFirstBusinessEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MedicineFirstBusinessEntity::getOrgMedicineMappingId, orgMedicineMappingId)
                .eq(MedicineFirstBusinessEntity::getSupplierId, supplierId)
                .eq(ObjectUtil.isNotEmpty(status), MedicineFirstBusinessEntity::getStatus, status)
                .orderByDesc(MedicineFirstBusinessEntity::getApprovalSequence);
        queryWrapper.last("LIMIT 1");
        return this.selectOne(queryWrapper);
    }

    /**
     * 获取下一个审批序号
     *
     * @param orgMedicineMappingId 药品私域ID
     * @param supplierId 供应商ID
     * @return 下一个审批序号
     */
    default Integer getNextApprovalSequence(Long orgMedicineMappingId, Long supplierId) {
        LambdaQueryWrapper<MedicineFirstBusinessEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MedicineFirstBusinessEntity::getOrgMedicineMappingId, orgMedicineMappingId)
                .eq(MedicineFirstBusinessEntity::getSupplierId, supplierId)
                .orderByDesc(MedicineFirstBusinessEntity::getApprovalSequence);
        queryWrapper.last("LIMIT 1");
        
        MedicineFirstBusinessEntity entity = this.selectOne(queryWrapper);
        return entity != null ? entity.getApprovalSequence() + 1 : 1;
    }

    /**
     * 根据药品ID、供应商ID和审批类型获取最新的审批记录
     */
    default MedicineFirstBusinessEntity findLatestByMedicineAndSupplierAndType(
            Long orgMedicineMappingId, Long supplierId, Integer approvalType) {
        LambdaQueryWrapper<MedicineFirstBusinessEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MedicineFirstBusinessEntity::getOrgMedicineMappingId, orgMedicineMappingId)
                .eq(MedicineFirstBusinessEntity::getSupplierId, supplierId)
                .eq(MedicineFirstBusinessEntity::getApprovalType, approvalType)
                .orderByDesc(MedicineFirstBusinessEntity::getApprovalSequence)
                .last("LIMIT 1");
        return this.selectOne(queryWrapper);
    }
} 