package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 盘点计划完成参数
 * <AUTHOR>
 * @date 2023/04/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="CompleteCheckParam", description="盘点计划完成参数")
public class CompleteCheckParam {

    @ApiModelProperty(hidden = true,value = "盘点计划Id")
    private Long id;

    @ApiModelProperty(value = "盘点状态：0暂存 1盘点中 2已完成")
    private Integer checkState;

    @ApiModelProperty(value = "商品明细")
    private List<CompleteCheckGoodsParam> goodsList;

}
