package com.dsj.inventory.bussiness.purchaseplan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 采购计划商品明细
 * <AUTHOR>
 * @date 2023/04/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PurchasePlanGoodsVo", description="采购计划商品明细实体")
@ToString(callSuper = true)
public class PurchasePlanGoodsVo {

    private Long id;

    @ApiModelProperty(value = "计划Id")
    private Long planId;

    @ApiModelProperty(value = "私域供应商Id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "私域商品Id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "通用名")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    private String generalName;

    @ApiModelProperty(value = "商品编号")
    private String goodsCode;

    @ApiModelProperty(value = "包装单位")
    private String packUnit;

    @ApiModelProperty(value = "规格型号")
    private String specification;

    @ApiModelProperty(value = "剂型")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    private String manufacturer;

    @ApiModelProperty(value = "上市许可持有人")
    private String launchPermitHolder;

    @ApiModelProperty(value = "当前零售价（私域商品价格）")
    private Integer price;

    @ApiModelProperty(value = "当前库存")
    private Double repertory;

    @ApiModelProperty(value = "在途数量，已下单未入库的数量")
    private Double inTransitQuantity;

    @ApiModelProperty(value = "日均销量，用于计算库存上下限")
    private Double dailyAvgSales;

    @ApiModelProperty(value = "库存上限，上限天数×日均销量")
    private Double stockUpperLimit;

    @ApiModelProperty(value = "库存下限，下限天数×日均销量")
    private Double stockLowerLimit;

    @ApiModelProperty(value = "推荐采购数量，系统自动计算的建议采购量")
    private Double recommendQuantity;

    @ApiModelProperty(value = "价格策略：1-按最低进价，2-按最新供应商，3-按上次进价")
    private Integer priceStrategy;

    @ApiModelProperty(value = "含税价（进货价）")
    private Integer purchasePrice;

    @ApiModelProperty(value = "采购数量")
    private Double purchaseNumber;

    @ApiModelProperty(value = "含税金额（进货总金额）")
    private Integer totalPurchasePrice;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
