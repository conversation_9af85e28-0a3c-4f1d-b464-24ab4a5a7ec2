package com.dsj.inventory.bussiness.quality.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "拒收单商品明细视图对象")
public class RejectionOrderGoodsVO {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "商品ID")
    private Long goodsId;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "SKU ID")
    private Long skuId;

    @ApiModelProperty(value = "SKU编码")
    private String skuCode;
    
    @ApiModelProperty(value = "拒收数量")
    private BigDecimal rejectionQuantity;

    @ApiModelProperty(value = "拒收原因")
    private String rejectionReason;
} 