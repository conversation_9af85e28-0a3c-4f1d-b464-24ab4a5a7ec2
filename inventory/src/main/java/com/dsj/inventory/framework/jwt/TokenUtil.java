package com.dsj.inventory.framework.jwt;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pocky.transport.bussiness.auth.entity.User;
import com.pocky.transport.bussiness.auth.param.QueryUserMenuParam;
import com.pocky.transport.bussiness.auth.param.QueryUserROrganizationParam;
import com.pocky.transport.bussiness.auth.param.QueryUserRoleParam;
import com.pocky.transport.bussiness.auth.service.IUserMenuService;
import com.pocky.transport.bussiness.auth.service.IUserRoleService;
import com.pocky.transport.bussiness.auth.vo.UserMenuVo;
import com.pocky.transport.bussiness.auth.vo.UserROrganizationVo;
import com.pocky.transport.bussiness.auth.vo.UserRoleVo;
import com.pocky.transport.bussiness.diagnose.entity.DoctorDrugstore;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.service.DoctorDrugstoreService;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.diagnose.service.UserROrganizationService;
import com.pocky.transport.bussiness.extraApp.entity.OrganizationApply;
import com.dsj.inventory.common.cache.CacheRepository;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.TencentIMUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.jwt.model.JwtUserInfo;
import com.dsj.inventory.framework.jwt.utils.JwtUtil;
import com.pocky.transport.framework.jwt.model.AuthInfo;
import com.pocky.transport.framework.jwt.model.ExtraAuthInfo;
import com.pocky.transport.framework.jwt.model.Token;
import io.jsonwebtoken.Claims;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.dsj.inventory.common.context.BaseContextConstants.*;
import static com.dsj.inventory.framework.exception.code.ExceptionCode.JWT_PARSER_TOKEN_FAIL;

/**
 * 认证工具类
 *
 * <AUTHOR>
 * @date 2020年03月31日19:03:47
 */

@Component
public class TokenUtil {
    /**
     * 认证服务端使用，如 authority-server
     * 生成和 解析token
     */
    private JwtProperties authServerProperties;
    private CacheRepository cacheRepository;

    @Value("${pocky.mockLogin:false}")
    private Boolean mockLogin;

    @Autowired
    private IUserRoleService userRoleService;
    @Autowired
    private TencentIMUtils tencentIMUtils;
    @Autowired
    private UserROrganizationService userROrganizationService;
    @Autowired
    private IUserMenuService userMenuService;
    @Autowired
    private DoctorDrugstoreService doctorDrugstoreService;
    @Autowired
    private OrganizationService organizationService;

    public TokenUtil(JwtProperties authServerProperties,CacheRepository cacheRepository){
        this.authServerProperties = authServerProperties;
        this.cacheRepository = cacheRepository;
    }

    /**
     * 创建认证token
     * @return token
     */
    public AuthInfo createAuthInfo(User user, Long expireMillis) {
        if (expireMillis == null || expireMillis <= 0) {
            if (CommonUtils.isNotEmpty(user.getRoleVos()) && user.getRoleVos().stream().anyMatch(role->StrUtil.containsIgnoreCase(role.getRoleCode(),"admin"))){
                expireMillis = authServerProperties.getAdminExpire();
            }else {
                expireMillis = authServerProperties.getExpire();
            }
        }
        //微信和web 同时登陆，需要保存 同一token
        //存一份 token 和userId 的绑定关系
        Object tokenObject = cacheRepository.get(USER_ID_TOKEN_KEY + user.getId());
        if (CommonUtils.isNotEmpty(tokenObject)){
            Object authInfoObject = cacheRepository.get(USER_TOKEN_KEY + tokenObject.toString());
            if(CommonUtils.isEmpty(authInfoObject)){
                cacheRepository.del(BaseContextConstants.USER_ID_TOKEN_KEY + user.getId());
                throw new BizException("登录异常，请重新登录。");
            }
            AuthInfo authInfo = (AuthInfo) authInfoObject;
            //刷新 有效期
            cacheRepository.setExpire(USER_TOKEN_KEY + authInfo.getToken(),authInfo,expireMillis);
            cacheRepository.setExpire(USER_ID_TOKEN_KEY +user.getId(),authInfo.getToken(),expireMillis);
            return authInfo;
        }else {
            //设置jwt参数
            Map<String, String> param = new HashMap<>(16);
            param.put(JWT_KEY_TOKEN_TYPE, BEARER_HEADER_KEY);
            param.put(JWT_KEY_USER_ID, Convert.toStr(user.getId(), "0"));
            param.put(JWT_KEY_NAME, user.getName());
            Token token = JwtUtil.createJWT(param, expireMillis);

            AuthInfo authInfo = new AuthInfo();
            authInfo.setName(user.getName());
            authInfo.setNickName(user.getNickName());
            authInfo.setHeadImg(user.getHeadImg());
            authInfo.setGender(user.getGender());
            authInfo.setUserId(user.getId());
            authInfo.setToken(token.getToken());
            authInfo.setExpire(token.getExpire());
            authInfo.setExpiration(token.getExpiration());
            authInfo.setPhone(user.getPhone());
            authInfo.setSerialNo(user.getSerialNo());
            authInfo.setFingerprint(user.getFingerprint());
            authInfo.setFingerprintCheck(user.getFingerprintCheck());

            Page<UserRoleVo> userRoleVoPage = userRoleService.queryUserRolePage(QueryUserRoleParam.builder().userId(user.getId()).build());
            authInfo.setRoleVos(userRoleVoPage.getRecords());
            QueryUserROrganizationParam uroParam = new QueryUserROrganizationParam();
            uroParam.setNeedExport(true);
            uroParam.setUserId(user.getId());
            Page<UserROrganizationVo> userROrganizationVoPage = userROrganizationService.queryUserROrganizationPage(uroParam);
            authInfo.setUserROrganizationVos(userROrganizationVoPage.getRecords());
            if(CommonUtils.isNotEmpty(userROrganizationVoPage.getRecords()) ){
                Optional<UserROrganizationVo> first = userROrganizationVoPage.getRecords().stream().filter(uro -> uro.getOrgEnable().equals(TrueEnum.TRUE.getCode())).findFirst();
                if (first.isPresent()){
                    List<UserMenuVo> list = userMenuService.queryUserMenuPage(QueryUserMenuParam.builder().userId(user.getId()).organizationId(first.get().getOrganizationId()).build());
                    authInfo.setUserMenuVos(list);
                }
                for(UserROrganizationVo userROrganizationVo : userROrganizationVoPage.getRecords()){
                    if(TrueEnum.TRUE.getCode().equals(userROrganizationVo.getDoctorAppointDrugstore())){
                        List<DoctorDrugstore> doctorDrugstoreList = doctorDrugstoreService.lambdaQuery()
                                .eq(DoctorDrugstore::getDoctorId,userROrganizationVo.getUserId()).list();
                        if(CommonUtils.isNotEmpty(doctorDrugstoreList)){
                            List<Long> doctorDrugstoreIdList = doctorDrugstoreList.stream().map(d -> d.getDrugstoreId()).collect(Collectors.toList());
                            userROrganizationVo.setDoctorDrugstoreIdList(doctorDrugstoreIdList);
                            List<String> doctorDrugstoreNameList = organizationService.lambdaQuery()
                                    .in(Organization::getId,doctorDrugstoreIdList).list()
                                    .stream().map(o -> o.getName()).collect(Collectors.toList());
                            userROrganizationVo.setDoctorDrugstoreNameList(doctorDrugstoreNameList);
                        }
                    }
                }
            }
            //获取UserSig
            String userSig = tencentIMUtils.genTestUserSig(user.getId().toString());
            authInfo.setUserSig(userSig);
            authInfo.setSdkAppId(tencentIMUtils.getSDKAPPID());

            cacheRepository.setExpire(USER_TOKEN_KEY + token.getToken(),authInfo,expireMillis);
            //存一份 token 和userId 的绑定关系
            cacheRepository.setExpire(USER_ID_TOKEN_KEY +user.getId(),token.getToken(),expireMillis);
            return authInfo;
        }
    }

    /**
     * 创建外部认证token
     * @return token
     */
    public ExtraAuthInfo createExternalAuthInfo(OrganizationApply apply, Long expireMillis) {
        if (expireMillis == null || expireMillis <= 0) {
            expireMillis = authServerProperties.getExpire();
        }
        Object tokenObject = cacheRepository.get(CLIENT_ID_TOKEN_KEY + apply.getId());
        if (CommonUtils.isNotEmpty(tokenObject)){
            Object authInfoObject = cacheRepository.get(CLIENT_TOKEN_KEY + tokenObject.toString());
            ExtraAuthInfo authInfo = (ExtraAuthInfo) authInfoObject;
            //刷新 有效期
            cacheRepository.setExpire(CLIENT_TOKEN_KEY + authInfo.getToken(),authInfo,expireMillis);
            cacheRepository.setExpire(CLIENT_ID_TOKEN_KEY +apply.getId(),authInfo.getToken(),expireMillis);
            return authInfo;
        }else {
            //设置jwt参数
            Map<String, String> param = new HashMap<>(16);
            param.put(JWT_KEY_TOKEN_TYPE, EXTERNAL_BEARER_HEADER_KEY);
            param.put(JWT_KEY_CLIENT_ID, Convert.toStr(apply.getId(), "0"));
            param.put(JWT_KEY_CLIENT_NAME, apply.getAppName());
            Token token = JwtUtil.createJWT(param, expireMillis);

            ExtraAuthInfo extraAuthInfo = new ExtraAuthInfo();
            extraAuthInfo.setToken(token.getToken());
            extraAuthInfo.setExpire(token.getExpire());
            extraAuthInfo.setExpiration(token.getExpiration());
            extraAuthInfo.setAppId(apply.getId());
            extraAuthInfo.setAppName(apply.getAppName());
            extraAuthInfo.setSecretId(apply.getAppSecretId());
            extraAuthInfo.setSecretKey(apply.getAppSecretKey());
            // token:clientId:1535475812550049792  有效期 配置
            cacheRepository.setExpire(CLIENT_TOKEN_KEY + token.getToken(), extraAuthInfo, expireMillis);
            //存一份 token 和clientId 的绑定关系
            cacheRepository.setExpire(CLIENT_ID_TOKEN_KEY + apply.getId(), token.getToken(), expireMillis);
            return extraAuthInfo;
        }
    }

    /**
     * 创建refreshToken
     *
     * @param userInfo 用户信息
     * @return refreshToken
     */
    private Token createRefreshToken(JwtUserInfo userInfo) {
        Map<String, String> param = new HashMap<>(16);
        param.put(JWT_KEY_TOKEN_TYPE, REFRESH_TOKEN_KEY);
        param.put(JWT_KEY_USER_ID, Convert.toStr(userInfo.getUserId(), "0"));
        return JwtUtil.createJWT(param, authServerProperties.getRefreshExpire());
    }

    /**
     * 解析token
     *
     * @param token token
     * @return 用户信息
     */
    public AuthInfo getAuthInfo(String token) {
        if (token == null) {
            throw BizException.wrap(JWT_PARSER_TOKEN_FAIL);
        }
        if (token.startsWith(BaseContextConstants.BEARER_HEADER_PREFIX)) {
            token = StrUtil.subAfter(token, BaseContextConstants.BEARER_HEADER_PREFIX, false);
        }else {
            throw BizException.wrap(JWT_PARSER_TOKEN_FAIL);
        }
        AuthInfo authInfo = null;
        Object userToken = cacheRepository.get(USER_TOKEN_KEY + token);
        if(mockLogin && token.indexOf("test")==0){
            AuthInfo authInfo1 = new AuthInfo();
            authInfo1.setUserId(NumberUtil.parseLong(StrUtil.subAfter(token, "test", false)));
            authInfo1.setName("mockUser");
            userToken = authInfo1;
        }
        if(CommonUtils.isEmpty(userToken)) {
           return null;
        }else {
            authInfo = (AuthInfo)userToken;
            Long expireMillis = 0L;
            if (CommonUtils.isNotEmpty(authInfo.getRoleVos()) && authInfo.getRoleVos().stream().anyMatch(role->StrUtil.containsIgnoreCase(role.getRoleCode(),"admin"))){
                expireMillis = authServerProperties.getAdminExpire();
            }else {
                expireMillis = authServerProperties.getExpire();
            }
            cacheRepository.setExpire(USER_TOKEN_KEY + token,authInfo,expireMillis);
            cacheRepository.setExpire(USER_ID_TOKEN_KEY +authInfo.getUserId(),token,expireMillis);
        }
        return authInfo;
    }

    /**
     * 解析token
     *
     * @param token token
     * @return 用户信息
     */
    public ExtraAuthInfo getExtraTokenAuthInfo(String token) {
        if (token == null) {
            throw BizException.wrap(JWT_PARSER_TOKEN_FAIL);
        }
        if (token.startsWith(BaseContextConstants.BEARER_HEADER_PREFIX)) {
            token = StrUtil.subAfter(token, BaseContextConstants.BEARER_HEADER_PREFIX, false);
        }else {
            throw BizException.wrap(JWT_PARSER_TOKEN_FAIL);
        }
        ExtraAuthInfo authInfo = null;
        Object clientToken = cacheRepository.get(CLIENT_TOKEN_KEY + token);
        if(CommonUtils.isEmpty(clientToken)) {
            return null;
        }else {
            authInfo = (ExtraAuthInfo)clientToken;
            cacheRepository.setExpire(CLIENT_TOKEN_KEY + token,authInfo,authServerProperties.getExpire());
            cacheRepository.setExpire(CLIENT_ID_TOKEN_KEY + authInfo.getAppId(), token, authServerProperties.getExpire());
        }
        return authInfo;
    }
    

    /**
     * 解析刷新token
     *
     * @param token
     * @return
     */
    public AuthInfo parseJWT(String token) {
        Claims claims = JwtUtil.parseJWT(token);
        String tokenType = Convert.toStr(claims.get(JWT_KEY_TOKEN_TYPE));
        Long userId = Convert.toLong(claims.get(JWT_KEY_USER_ID));
        Date expiration = claims.getExpiration();
        return new AuthInfo().setToken(token)
                .setExpire(expiration != null ? expiration.getTime() : 0L)
                .setUserId(userId);
    }
}
