package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateMemberParam;
import com.dsj.inventory.bussiness.inventory.service.MemberService;
import com.dsj.inventory.bussiness.inventory.vo.MemberExportVo;
import com.dsj.inventory.bussiness.inventory.vo.MemberVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.EasyExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 进销存会员 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@RestController
@Api(value = "MemberController", tags = "进销存会员api")
public class MemberController {

    @Autowired
    private MemberService memberService;

    /*新增会员*/
    @PostMapping("/member")
    @ApiOperation(value = "新增会员", notes = "新增会员")
    public ResponseEntity<MemberVo> saveMember(@RequestBody SaveOrUpdateMemberParam param){
        MemberVo memberVo = memberService.saveOrUpdateMember(param);
        return Res.success(memberVo);
    }

    /*编辑会员*/
    @PutMapping("/member/{id}")
    @ApiOperation(value = "编辑会员", notes = "编辑会员")
    public ResponseEntity<MemberVo> updateMember(@PathVariable Long id, @RequestBody SaveOrUpdateMemberParam param){
        param.setId(id);
        MemberVo memberVo = memberService.saveOrUpdateMember(param);
        return Res.success(memberVo);
    }

    /*删除会员*/
    @DeleteMapping("/member/{id}")
    @ApiOperation(value = "删除会员", notes = "删除会员")
    public ResponseEntity<Void> deleteMember(@PathVariable Long id){
        SaveOrUpdateMemberParam param = SaveOrUpdateMemberParam.builder().isDelete(TrueEnum.TRUE.getCode()).id(id).build();
        memberService.saveOrUpdateMember(param);
        return Res.success();
    }

    /*获取会员列表*/
    @GetMapping("/member")
    @ApiOperation(value = "获取会员列表", notes = "获取会员列表")
    public ResponseEntity<List<MemberVo>> queryMemberList(QueryMemberParam param){
        Page<MemberVo> page = memberService.queryMemberPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取会员详情*/
    @GetMapping("/member/{id}")
    @ApiOperation(value = "获取会员详情", notes = "获取会员详情")
    public ResponseEntity<MemberVo> queryMember(@PathVariable Long id){
        return Res.success(memberService.getMemberById(id));
    }

    /*会员导出*/
    @GetMapping("/member/action-export")
    @ApiOperation(value = "导出会员", notes = "导出会员")
    public ResponseEntity<Void> exportMember(HttpServletResponse response, QueryMemberParam param) throws IOException {
        param.setNeedExport(true);
        List<MemberExportVo> list = memberService.queryMemberList(param);
        EasyExcelUtils.exportOrdinaryExcel(response,list,"会员导出列表","会员导出列表", MemberExportVo.class);
        return Res.success();
    }

}
