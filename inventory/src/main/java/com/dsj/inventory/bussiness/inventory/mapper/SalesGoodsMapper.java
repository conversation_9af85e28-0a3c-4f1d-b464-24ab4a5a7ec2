package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.SalesGoods;
import com.dsj.inventory.bussiness.inventory.vo.SalesGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存销售单商品明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface SalesGoodsMapper extends BaseMapper<SalesGoods> {

    /**
     * 获取销售单商品明细列表
     * @param orderId
     * @return
     */
    List<SalesGoodsVo> getSalesGoodsListByOrderId(Long orderId);

}
