package com.dsj.inventory.bussiness.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.PurchaseOrder;
import com.dsj.inventory.bussiness.inventory.entity.PurchaseOrderGoods;
import com.dsj.inventory.bussiness.inventory.entity.ReceiveOrder;
import com.dsj.inventory.bussiness.inventory.entity.ReceiveOrderGoods;
import com.dsj.inventory.bussiness.inventory.enumeration.PurchaseOrderStateEnum;
import com.dsj.inventory.bussiness.inventory.enumeration.ReceiveOrderStateEnum;
import com.dsj.inventory.bussiness.inventory.mapper.ReceiveOrderMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryReceiveOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateReceiveOrderGoodsParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateReceiveOrderParam;
import com.dsj.inventory.bussiness.inventory.service.*;
import com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderGoodsVo;
import com.dsj.inventory.bussiness.inventory.vo.ReceiveOrderVo;
import com.dsj.inventory.bussiness.quality.param.RejectedGoodsItem;
import com.dsj.inventory.bussiness.quality.param.RejectionCreateParam;
import com.dsj.inventory.bussiness.quality.service.RejectionOrderService;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.common.utils.MoneyUtil;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 进销存收货单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-07
 */
@Service
public class ReceiveOrderServiceImpl extends ServiceImpl<ReceiveOrderMapper, ReceiveOrder> implements ReceiveOrderService {

    private static final Logger log = LoggerFactory.getLogger(ReceiveOrderServiceImpl.class);

    @Autowired
    private RejectionOrderService rejectionOrderService;

    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @Autowired
    private ReceiveOrderGoodsService receiveOrderGoodsService;
    @Autowired
    private PurchaseOrderService purchaseOrderService;
    @Autowired
    private PurchaseOrderGoodsService purchaseOrderGoodsService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationSupplierService organizationSupplierService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReceiveOrderDetailVo saveOrUpdateReceiveOrder(SaveOrUpdateReceiveOrderParam param) {
        // 基础参数校验
        validateReceiveOrderParams(param);

        // 校验组织和进销存有效期
        Organization organization = validateOrganizationInventoryDate(param.getOrganizationId());

        // 处理现有订单或生成新的收货单号
        if (CommonUtils.isNotEmpty(param.getId())) {
            handleExistingReceiveOrder(param.getId());
        } else {
            generateReceiveOrderCode(param, organization);
        }

        // 校验采购单并设置相关信息
        PurchaseOrder purchaseOrder = validatePurchaseOrder(param);

        // 处理商品明细和金额计算
        processReceiveOrderGoods(param, purchaseOrder);

        // 设置默认接收者
        if (CommonUtils.isEmpty(param.getReceiverId())) {
            param.setReceiverId(BaseContextHandler.getUserId());
        }

        // 保存收货单主记录
        ReceiveOrder receiveOrder = new ReceiveOrder();
        BeanUtil.copyProperties(param, receiveOrder);
        this.saveOrUpdate(receiveOrder);

        // 更新采购订单商品明细中的已收货数量
        if (CommonUtils.isNotEmpty(param.getGoodsList())) {
            updatePurchaseOrderGoods(param);
        }

        // 更新采购订单的整体状态
        purchaseOrderService.updatePurchaseOrderStatusAfterReceipt(param.getOrderId());

        // 保存收货单的商品明细 (包含实收和拒收)
        List<ReceiveOrderGoods> savedGoods = saveReceiveOrderGoods(param, receiveOrder);

        // --- 开始处理拒收 ---
        // 1. 创建一个临时的信息组合对象列表，用于配对 "已保存的实体" 和 "原始的参数"
        List<TempRejectedInfo> tempRejectedInfos = new ArrayList<>();
        List<SaveOrUpdateReceiveOrderGoodsParam> originalParams = param.getGoodsList();

        for (int i = 0; i < savedGoods.size(); i++) {
            SaveOrUpdateReceiveOrderGoodsParam p = originalParams.get(i);
            // 2. 筛选出被拒收的商品
            if (p.getRefuseAmount() != null && p.getRefuseAmount() > 0) {
                // 3. 将实体和参数配对放入临时列表
                tempRejectedInfos.add(new TempRejectedInfo(savedGoods.get(i), p));
            }
        }

        // 如果存在被拒收的商品，则创建拒收单
        if (!tempRejectedInfos.isEmpty()) {
            createRejectionOrderFromSource(receiveOrder, purchaseOrder, tempRejectedInfos);
        }
        // --- 拒收处理结束 ---

        return this.getReceiveOrderById(receiveOrder.getId());
    }

    private void createRejectionOrderFromSource(ReceiveOrder receiveOrder, PurchaseOrder purchaseOrder, List<TempRejectedInfo> rejectedGoods) {
        RejectionCreateParam rejectionParam = new RejectionCreateParam();
        rejectionParam.setOrganizationId(receiveOrder.getOrganizationId());
        rejectionParam.setSourceId(receiveOrder.getId());
        rejectionParam.setSourceCode(receiveOrder.getCode());
        rejectionParam.setSourceType(com.dsj.inventory.bussiness.quality.enumeration.RejectionSourceTypeEnum.RECEIVE);
        rejectionParam.setSupplierId(purchaseOrder.getOrganizationSupplierId());
        rejectionParam.setSupplierName(organizationSupplierService.getOrganizationSupplierNameById(purchaseOrder.getOrganizationSupplierId()));
        rejectionParam.setRemark(receiveOrder.getRemark());

        List<RejectedGoodsItem> rejectedItems = rejectedGoods.stream().map(info -> {
            ReceiveOrderGoods entity = info.getEntity();
            SaveOrUpdateReceiveOrderGoodsParam p = info.getParam();

            RejectedGoodsItem rejectedItem = new RejectedGoodsItem();
            rejectedItem.setSourceOrderGoodsId(entity.getId());
            rejectedItem.setGoodsId(entity.getOrganizationMedicineId());
            rejectedItem.setGoodsName(entity.getGoodsName());
            if (entity.getPurchasePrice() != null) {
                rejectedItem.setPurchasePrice(new BigDecimal(entity.getPurchasePrice()));
            }
            if (entity.getRefuseAmount() != null) {
                rejectedItem.setRejectionQuantity(BigDecimal.valueOf(entity.getRefuseAmount()));
            }
            rejectedItem.setRejectionReason(p.getRefuseReason());
            return rejectedItem;
        }).collect(Collectors.toList());

        rejectionParam.setRejectedItems(rejectedItems);

        log.info("收货单[{}]创建关联拒收单...", receiveOrder.getCode());
        rejectionOrderService.createFromSource(rejectionParam);
    }

    /**
     * 基础参数校验
     *
     * @param param 收货单参数
     */
    private void validateReceiveOrderParams(SaveOrUpdateReceiveOrderParam param) {
        if (CommonUtils.isEmpty(param)) {
            throw new ParamException("参数异常");
        }
        // 增强校验：检查必要字段
        if (CommonUtils.isEmpty(param.getOrganizationId())) {
            throw new ParamException("组织ID不能为空");
        }
        // 在更新操作时，验证ID存在
        if (CommonUtils.isNotEmpty(param.getId()) && param.getOrderId() == null) {
            throw new ParamException("更新收货单时，采购订单ID不能为空");
        }
        log.debug("收货单参数基础校验通过: {}", param.getId() != null ? param.getId() : "新建收货单");
    }

    /**
     * 校验组织和进销存有效期
     *
     * @param organizationId 组织ID
     * @return 组织实体
     */
    private Organization validateOrganizationInventoryDate(Long organizationId) {
        if (organizationId == null) {
            throw new ParamException("组织ID不能为空");
        }

        Organization organization = organizationService.getById(organizationId);
        if (CommonUtils.isEmpty(organization)) {
            throw new BizException("组织不存在: ID=" + organizationId);
        }
        if (CommonUtils.isEmpty(organization.getInventoryDate())) {
            throw new BizException("进销存有效期未设置: 组织ID=" + organizationId);
        }

        boolean before = organization.getInventoryDate() != null && organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期: 组织ID=" + organizationId);
        }
        return organization;
    }

    /**
     * 处理现有收货单
     *
     * @param orderId 收货单ID
     */
    private void handleExistingReceiveOrder(Long orderId) {
        ReceiveOrder existingOrder = this.getById(orderId);
        if (CommonUtils.isEmpty(existingOrder)) {
            throw new BizException("操作失败，数据不存在");
        }
        // 删除原有商品明细
        receiveOrderGoodsService.markAsDeletedByOrderId(orderId);
    }

    /**
     * 生成收货单号
     *
     * @param param        收货单参数
     * @param organization 组织实体
     */
    private void generateReceiveOrderCode(SaveOrUpdateReceiveOrderParam param, Organization organization) {
        if (param == null) {
            throw new ParamException("收货单参数不能为空");
        }
        if (organization == null) {
            throw new ParamException("组织实体不能为空");
        }
        if (CommonUtils.isEmpty(organization.getSerialNo())) {
            throw new BizException("组织编号未设置，无法生成收货单号: 组织ID=" + organization.getId());
        }

        // 生成收货单号：FH+年后两位+月日四位+药房识别码六位+三位
        String day = DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_FORMAT);
        String dayStr = day.replaceAll("-", "").substring(2, 8);

        Integer count = this.getReceiveOrderCount(organization.getId(), day);
        // 防止count为null
        count = count == null ? 0 : count;
        count = count + 1;

        param.setCode("FH" + dayStr + organization.getSerialNo() + String.format("%03d", count));
        log.debug("生成收货单号: {}", param.getCode());
    }

    /**
     * 校验采购单并设置相关信息
     *
     * @param param 收货单参数
     * @return 采购单实体
     */
    private PurchaseOrder validatePurchaseOrder(SaveOrUpdateReceiveOrderParam param) {
        if (param == null) {
            throw new ParamException("收货单参数不能为空");
        }

        if (CommonUtils.isEmpty(param.getOrderId())) {
            throw new BizException("操作失败，未选择采购订单");
        }

        PurchaseOrder purchaseOrder = purchaseOrderService.getById(param.getOrderId());
        if (CommonUtils.isEmpty(purchaseOrder)) {
            throw new BizException("操作失败，采购订单不存在: ID=" + param.getOrderId());
        }

        // 检查采购订单状态已在PurchaseOrderService中进行

        // 支持分批收货：一个采购订单可以对应多个收货单
        // 但需要校验采购订单状态，确保可以继续收货
        if (purchaseOrder.getDealState() != null &&
            PurchaseOrderStateEnum.COMPLETED.getCode().equals(purchaseOrder.getDealState())) {
            throw new BizException("操作失败，采购订单已完成，无法继续收货: 订单ID=" + param.getOrderId());
        }

        // 设置采购订单编号
        if (CommonUtils.isNotEmpty(purchaseOrder.getCode())) {
            param.setOrderCode(purchaseOrder.getCode());
        } else {
            log.warn("采购订单编号为空: 订单ID={}", param.getOrderId());
            param.setOrderCode("未知编号");
        }

        return purchaseOrder;
    }

    /**
     * 处理商品明细和金额计算
     *
     * @param param         收货单参数
     * @param purchaseOrder 采购单实体
     */
    private void processReceiveOrderGoods(SaveOrUpdateReceiveOrderParam param, PurchaseOrder purchaseOrder) {
        if (CommonUtils.isEmpty(param.getGoodsList())) {
            return;
        }

        // 检查商品ID是否有重复
        List<Long> goodsIdList = param.getGoodsList().stream()
                .map(SaveOrUpdateReceiveOrderGoodsParam::getOrganizationMedicineId)
                .distinct()
                .collect(Collectors.toList());

        if (goodsIdList.size() != param.getGoodsList().size()) {
            throw new BizException("操作失败，存在重复商品");
        }

        BigDecimal totalPrice = MoneyUtil.toBigDecimal(0);
        BigDecimal totalDiscountPrice = MoneyUtil.toBigDecimal(0);
        BigDecimal totalRetailPrice = MoneyUtil.toBigDecimal(0);

        // 处理每个商品明细
        for (SaveOrUpdateReceiveOrderGoodsParam goodsParam : param.getGoodsList()) {
            // 校验商品并设置商品信息
            OrganizationMedicineMapping organizationMedicine = validateAndSetGoodsInfo(param, goodsParam);

            // 计算并校验金额
            BigDecimal amount = calculateEffectiveAmount(goodsParam);

            // 计算并校验采购金额
            BigDecimal totalPurchasePrice = calculateAndValidateTotalPurchasePrice(goodsParam, amount);
            totalPrice = MoneyUtil.add(totalPrice, goodsParam.getTotalPurchasePrice());

            // 计算并校验折扣金额
            BigDecimal goodsTotalDiscountPrice = calculateAndValidateDiscountPrice(goodsParam, amount);
            totalDiscountPrice = MoneyUtil.add(totalDiscountPrice, goodsTotalDiscountPrice);

            // 计算零售总价
            BigDecimal retailPrice = MoneyUtil.mul(goodsParam.getPrice(), goodsParam.getReceiveAmount());
            totalRetailPrice = MoneyUtil.add(totalRetailPrice, retailPrice);
        }

        // 设置采购内容和商品类型
        param.setPurchaseContent(purchaseOrder.getPurchaseContent());
        param.setGoodsTypes(purchaseOrder.getGoodsTypes());

        // 校验和设置总金额
        validateAndSetTotalPrice(param, totalPrice, totalDiscountPrice, totalRetailPrice);
    }

    /**
     * 校验商品并设置商品信息
     *
     * @param param      收货单参数
     * @param goodsParam 商品参数
     * @return 商品映射实体
     */
    private OrganizationMedicineMapping validateAndSetGoodsInfo(SaveOrUpdateReceiveOrderParam param,
                                                                SaveOrUpdateReceiveOrderGoodsParam goodsParam) {
        if (param == null || goodsParam == null) {
            throw new ParamException("收货单参数或商品参数不能为空");
        }

        if (goodsParam.getOrganizationMedicineId() == null) {
            throw new ParamException("商品ID不能为空");
        }

        OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(
                goodsParam.getOrganizationMedicineId());

        if (CommonUtils.isEmpty(organizationMedicine)) {
            throw new BizException("操作失败，商品Id" + goodsParam.getOrganizationMedicineId() + "不存在");
        }

        if (param.getOrderId() == null) {
            throw new BizException("操作失败，缺少采购订单ID");
        }

        PurchaseOrderGoods purchaseOrderGoods = purchaseOrderGoodsService.getByOrderIdAndOrgMedicineId(
                param.getOrderId(), organizationMedicine.getId());

        // 统一获取商品名用于异常信息 (修正了运算符优先级问题)
        String goodsName = "商品" + (CommonUtils.isNotEmpty(purchaseOrderGoods.getGoodsName()) ? purchaseOrderGoods.getGoodsName() : "ID=" + purchaseOrderGoods.getId());


        if (CommonUtils.isEmpty(purchaseOrderGoods)) {
            throw new BizException("操作失败，采购单不存在商品: " +
                    (CommonUtils.isNotEmpty(organizationMedicine.getName()) ? organizationMedicine.getName() : "ID=" + organizationMedicine.getId()));
        }

        // 新增校验：到货数量(receiveAmount)不能大于订货数量(purchaseNumber)
        Double receiveAmount = goodsParam.getReceiveAmount() != null ? goodsParam.getReceiveAmount() : 0.0; //到货数量
        // 到货数量必须大于零
        if (receiveAmount <= 0) {
            throw new BizException("操作失败，" + goodsName + "的到货数量必须大于零");
        }
        Double purchaseNumber = purchaseOrderGoods.getPurchaseNumber() != null ? purchaseOrderGoods.getPurchaseNumber() : 0.0;
        if (receiveAmount.compareTo(purchaseNumber) > 0) {
            throw new BizException("操作失败，" + goodsName + "的到货数量(" + receiveAmount + ")不能大于采购数量(" + purchaseNumber + ")");
        }

        // 新增校验：收货数量(到货数量-赠送数量)不能大于到货数量
        Double giftQuantity = goodsParam.getReceiveAmount() != null ? goodsParam.getReceiveAmount() : 0.0;
        // 收货数量必须大于零
        if (giftQuantity <= 0) {
            throw new BizException("操作失败，" + goodsName + "的收货数量必须大于零");
        }
        Double receivedQuantity = receiveAmount - giftQuantity;
        if (receivedQuantity.compareTo(receiveAmount) > 0) {
            throw new BizException("操作失败，" + goodsName + "的收货数量(" + receivedQuantity + ")不能大于到货数量(" + receiveAmount + ")");
        }

        // 设置商品基本信息
        goodsParam.setGoodsName(purchaseOrderGoods.getGoodsName());
        goodsParam.setGeneralName(purchaseOrderGoods.getGeneralName());
        goodsParam.setGoodsCode(purchaseOrderGoods.getGoodsCode());
        goodsParam.setPackUnit(purchaseOrderGoods.getPackUnit());
        goodsParam.setSpecification(purchaseOrderGoods.getSpecification());
        goodsParam.setDrugType(purchaseOrderGoods.getDrugType());
        goodsParam.setManufacturer(purchaseOrderGoods.getManufacturer());
        goodsParam.setLaunchPermitHolder(purchaseOrderGoods.getLaunchPermitHolder());

        // 设置组织关联的商品信息，增加空值处理
        goodsParam.setProducePlace(organizationMedicine.getProducePlace());
        // 处理可能为null的价格字段
        goodsParam.setPrice(organizationMedicine.getOfflinePrice() != null ?
                organizationMedicine.getOfflinePrice() : 0);
        goodsParam.setMemberPrice(organizationMedicine.getMemberPrice() != null ?
                organizationMedicine.getMemberPrice() : 0);
        goodsParam.setRepertory(organizationMedicine.getMedicineRepertory() != null ?
                organizationMedicine.getMedicineRepertory() : 0);

        return organizationMedicine;
    }

    /**
     * 计算有效收货数量（收货数量-赠送数量）
     *
     * @param goodsParam 商品参数
     * @return 有效收货数量
     */
    private BigDecimal calculateEffectiveAmount(SaveOrUpdateReceiveOrderGoodsParam goodsParam) {
        if (goodsParam == null) {
            throw new ParamException("商品参数不能为空");
        }

        // 处理可能为null的情况，注意receiveAmount和giveAmount是Double类型
        Double receiveAmount = goodsParam.getReceiveAmount() != null ? goodsParam.getReceiveAmount() : 0.0;
        Double giveAmount = goodsParam.getGiveAmount() != null ? goodsParam.getGiveAmount() : 0.0;

        // 使用MoneyUtil工具类计算
        return MoneyUtil.sub(receiveAmount, giveAmount);
    }

    /**
     * 计算并校验采购总价
     *
     * @param goodsParam 商品参数
     * @param amount     有效收货数量
     * @return 采购总价
     */
    private BigDecimal calculateAndValidateTotalPurchasePrice(SaveOrUpdateReceiveOrderGoodsParam goodsParam, BigDecimal amount) {
        if (goodsParam == null) {
            throw new ParamException("商品参数不能为空");
        }
        if (amount == null) {
            throw new ParamException("收货数量不能为空");
        }
        // 确保purchasePrice不为null
        Integer purchasePrice = goodsParam.getPurchasePrice() != null ? goodsParam.getPurchasePrice() : 0;

        BigDecimal totalPurchasePrice = MoneyUtil.mul(purchasePrice, amount);
        totalPurchasePrice = MoneyUtil.round(totalPurchasePrice, 0); // 使用整数精度

        // 安全比较：处理getTotalPurchasePrice可能为null的情况
        Integer goodsTotal = goodsParam.getTotalPurchasePrice();
        if (goodsTotal == null) {
            log.warn("商品总价未设置，将使用计算结果: {}", MoneyUtil.toInteger(totalPurchasePrice));
            goodsParam.setTotalPurchasePrice(MoneyUtil.toInteger(totalPurchasePrice));
        } else if (!MoneyUtil.equals(totalPurchasePrice, goodsTotal)) {
            String goodsName = goodsParam.getGoodsName() != null ? goodsParam.getGoodsName() : "未知商品";
            throw new BizException("操作失败，商品" + goodsName +
                    "小计金额计算不一致: 系统计算=" + MoneyUtil.toInteger(totalPurchasePrice) +
                    ", 传入值=" + goodsTotal);
        }

        return totalPurchasePrice;
    }

    /**
     * 计算并校验折扣总价
     *
     * @param goodsParam 商品参数
     * @param amount     有效收货数量
     * @return 折扣总价
     */
    private BigDecimal calculateAndValidateDiscountPrice(SaveOrUpdateReceiveOrderGoodsParam goodsParam, BigDecimal amount) {
        if (goodsParam == null) {
            throw new ParamException("商品参数不能为空");
        }
        if (amount == null) {
            throw new ParamException("收货数量不能为空");
        }
        // 确保discountPrice不为null
        Integer discountPrice = goodsParam.getDiscountPrice() != null ? goodsParam.getDiscountPrice() : 0;

        BigDecimal goodsTotalDiscountPrice = MoneyUtil.mul(discountPrice, amount);
        goodsTotalDiscountPrice = MoneyUtil.round(goodsTotalDiscountPrice, 0); // 使用整数精度

        // 安全比较：处理getTotalDiscountPrice可能为null的情况
        Integer totalDiscount = goodsParam.getTotalDiscountPrice();
        if (totalDiscount == null) {
            log.warn("商品折后金额未设置，将使用计算结果: {}", MoneyUtil.toInteger(goodsTotalDiscountPrice));
            goodsParam.setTotalDiscountPrice(MoneyUtil.toInteger(goodsTotalDiscountPrice));
        } else if (!MoneyUtil.equals(goodsTotalDiscountPrice, totalDiscount)) {
            String goodsName = goodsParam.getGoodsName() != null ? goodsParam.getGoodsName() : "未知商品";
            throw new BizException("操作失败，商品" + goodsName +
                    "折后金额计算不一致: 系统计算=" + MoneyUtil.toInteger(goodsTotalDiscountPrice) +
                    ", 传入值=" + totalDiscount);
        }

        return goodsTotalDiscountPrice;
    }

    /**
     * 校验和设置收货单总金额
     *
     * @param param              收货单参数
     * @param totalPrice         采购总价
     * @param totalDiscountPrice 折扣总价
     * @param totalRetailPrice   零售总价
     */
    private void validateAndSetTotalPrice(SaveOrUpdateReceiveOrderParam param,
                                          BigDecimal totalPrice,
                                          BigDecimal totalDiscountPrice,
                                          BigDecimal totalRetailPrice) {
        if (param == null) {
            throw new ParamException("收货单参数不能为空");
        }
        if (totalPrice == null || totalDiscountPrice == null || totalRetailPrice == null) {
            throw new ParamException("金额计算有误，存在空值");
        }

        // 将BigDecimal转为整数
        Integer calculatedTotalPrice = MoneyUtil.toInteger(totalPrice);
        Integer calculatedDiscountTotal = MoneyUtil.toInteger(totalDiscountPrice);
        Integer calculatedRetailPrice = MoneyUtil.toInteger(totalRetailPrice);

        // 处理param.getTotalPrice()可能为null的情况
        Integer paramTotalPrice = param.getTotalPrice();
        if (paramTotalPrice == null) {
            log.warn("收货单总金额未设置，将使用计算结果: {}", calculatedTotalPrice);
            param.setTotalPrice(calculatedTotalPrice);
        } else if (!MoneyUtil.equals(calculatedTotalPrice, paramTotalPrice)) {
            throw new BizException("操作失败，总金额计算不一致: 系统计算="
                    + calculatedTotalPrice + ", 传入值=" + paramTotalPrice);
        }

        // 处理param.getDiscountTotalPrice()可能为null的情况
        Integer paramDiscountTotal = param.getDiscountTotalPrice();
        if (paramDiscountTotal == null) {
            log.warn("收货单折后金额未设置，将使用计算结果: {}", calculatedDiscountTotal);
            param.setDiscountTotalPrice(calculatedDiscountTotal);
        } else if (!MoneyUtil.equals(calculatedDiscountTotal, paramDiscountTotal)) {
            throw new BizException("操作失败，折后金额计算不一致: 系统计算="
                    + calculatedDiscountTotal + ", 传入值=" + paramDiscountTotal);
        }

        // 设置零售总价
        param.setTotalRetailPrice(calculatedRetailPrice);

        log.debug("收货单金额验证通过: 总金额={}, 折后金额={}, 零售总价={}",
                param.getTotalPrice(), param.getDiscountTotalPrice(), param.getTotalRetailPrice());
    }

    /**
     * 更新采购订单商品明细已收货数量
     *
     * @param param 收货单参数
     */
    private void updatePurchaseOrderGoods(SaveOrUpdateReceiveOrderParam param) {
        if (param == null) {
            throw new ParamException("收货单参数不能为空");
        }

        if (CommonUtils.isEmpty(param.getGoodsList())) {
            log.warn("商品列表为空，无需更新采购订单商品明细");
            return;
        }

        if (param.getOrderId() == null) {
            throw new BizException("采购订单ID不能为空");
        }

        for (SaveOrUpdateReceiveOrderGoodsParam goodsParam : param.getGoodsList()) {
            if (goodsParam.getOrganizationMedicineId() == null) {
                log.warn("商品ID为空，跳过更新");
                continue;
            }

            PurchaseOrderGoods purchaseOrderGoods = purchaseOrderGoodsService.getByOrderIdAndOrgMedicineId(
                    param.getOrderId(), goodsParam.getOrganizationMedicineId());

            // 为防万一，或者如果之前的逻辑有变动，这里可以再加一次校验或日志
            if (CommonUtils.isEmpty(purchaseOrderGoods)) {
                log.error("Error: PurchaseOrderGoods not found for orderId {} and medicineId {} during received quantity update.",
                        param.getOrderId(), goodsParam.getOrganizationMedicineId());
                throw new BizException("更新采购订单商品已收数量失败：未找到对应的采购商品项");
            }

            BigDecimal effectiveReceiveAmount = calculateEffectiveAmount(goodsParam);

            if (MoneyUtil.isLessThanZero(effectiveReceiveAmount)) {
                throw new BizException("操作失败，有效收货数量不能为负数");
            }

            // 初始化 receivedQuantity 如果为 null
            Integer currentReceivedQuantity = purchaseOrderGoods.getReceivedQuantity() == null ? 0 : purchaseOrderGoods.getReceivedQuantity();
            int newlyReceivedQuantity = effectiveReceiveAmount.intValue(); // 本次有效收货

            // 安全验证：确保purchaseOrderGoods.getPurchaseNumber()不为null，并正确处理Double类型
            Double purchaseNumber = purchaseOrderGoods.getPurchaseNumber();
            if (purchaseNumber == null) {
                log.warn("采购数量为空，设置为0: 商品ID={}", purchaseOrderGoods.getId());
                purchaseNumber = 0.0;
            }

            // 将Double转换为int进行比较
            if (currentReceivedQuantity + newlyReceivedQuantity > purchaseNumber.intValue()) {
                throw new BizException("操作失败，商品" +
                        (purchaseOrderGoods.getGoodsName() != null ? purchaseOrderGoods.getGoodsName() : "ID=" + purchaseOrderGoods.getId()) +
                        "累计收货数量超出采购数量");
            }

            purchaseOrderGoods.setReceivedQuantity(currentReceivedQuantity + newlyReceivedQuantity);
            purchaseOrderGoodsService.updateById(purchaseOrderGoods);
        }
    }

    private List<ReceiveOrderGoods> saveReceiveOrderGoods(SaveOrUpdateReceiveOrderParam param, ReceiveOrder receiveOrder) {
        if (CommonUtils.isNotEmpty(param.getGoodsList())) {
            param.getGoodsList().forEach(g -> g.setOrderId(receiveOrder.getId()));
            List<ReceiveOrderGoods> goodsList = BeanUtil.copyToList(param.getGoodsList(), ReceiveOrderGoods.class);
            receiveOrderGoodsService.saveBatch(goodsList);
            return goodsList;
        }
        return Collections.emptyList();
    }

    @Override
    public void deleteReceiveOrder(Long id) {
        if (id == null) {
            throw new ParamException("收货单ID不能为空");
        }

        ReceiveOrder byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }

        // 验证订单状态
        if (byId.getDealState() == null) {
            throw new BizException("收货单状态异常: ID=" + id);
        }

        if (!ReceiveOrderStateEnum.TEMPORARY.getCode().equals(byId.getDealState())) {
            throw new BizException("操作失败，收货单当前状态不允许删除: 当前状态=" +
                    ReceiveOrderStateEnum.getDesc(byId.getDealState()) +
                    ", 只有暂存状态可删除");
        }

        // 删除商品明细，捕获可能的异常并记录
        try {
            receiveOrderGoodsService.deleteByOrderId(id);
        } catch (Exception e) {
            log.error("删除收货单商品明细失败: ID={}, 错误={}", id, e.getMessage());
            throw new BizException("删除收货单商品明细失败，请稍后重试");
        }

        // 删除收货单
        boolean removed = this.removeById(id);
        if (!removed) {
            log.warn("删除收货单失败: ID={}, 可能已被其他线程删除", id);
        } else {
            log.info("成功删除收货单: ID={}", id);
        }
    }

    @Override
    public Page<ReceiveOrderVo> queryReceiveOrderPage(QueryReceiveOrderParam param) {
        if (CommonUtils.isEmpty(param)) {
            throw new ParamException("参数异常");
        }

        // 安全获取用户组织类型
        String userOrgTypeStr = BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE);
        if (CommonUtils.isEmpty(userOrgTypeStr)) {
            throw new BizException("用户组织类型未设置");
        }

        Integer userOrgType;
        try {
            userOrgType = Integer.valueOf(userOrgTypeStr);
        } catch (NumberFormatException e) {
            log.error("用户组织类型格式错误: {}", userOrgTypeStr);
            throw new BizException("用户组织类型格式错误");
        }

        if (!OrganizationEnum.DRUGSTORE.getCode().equals(userOrgType)
                && !OrganizationEnum.PLATFORM.getCode().equals(userOrgType)) {
            throw new BizException("组织无权限");
        } else if (OrganizationEnum.DRUGSTORE.getCode().equals(userOrgType)) {
            // 安全获取用户组织ID
            String userOrgIdStr = BaseContextHandler.get(BaseContextConstants.USER_ORG_ID);
            if (CommonUtils.isEmpty(userOrgIdStr)) {
                throw new BizException("用户组织ID未设置");
            }

            try {
                param.setOrganizationId(Long.valueOf(userOrgIdStr));
            } catch (NumberFormatException e) {
                log.error("用户组织ID格式错误: {}", userOrgIdStr);
                throw new BizException("用户组织ID格式错误");
            }
        }

        // 确保分页参数有效
        int page = param.getPage() != null && param.getPage() > 0 ? param.getPage() : 1;
        int size = param.getSize() != null && param.getSize() > 0 ? param.getSize() : 10;

        Page<ReceiveOrderVo> pageObj = new Page<>(page, size);
        List<ReceiveOrderVo> list = this.baseMapper.queryReceiveOrderPage(pageObj, param);
        return pageObj.setRecords(list != null ? list : Collections.emptyList());
    }

    @Override
    public ReceiveOrderDetailVo getReceiveOrderById(Long id) {
        if (id == null) {
            log.warn("收货单ID为空");
            return null;
        }

        ReceiveOrderDetailVo vo = this.baseMapper.getReceiveOrderById(id);
        if (CommonUtils.isNotEmpty(vo)) {
            // 安全获取商品列表
            List<ReceiveOrderGoodsVo> goodsListVo = Collections.emptyList();
            List<ReceiveOrderGoods> goodsList = receiveOrderGoodsService.listByOrderId(vo.getId());

            if (CommonUtils.isNotEmpty(goodsList)) {
                goodsListVo = goodsList.stream()
                        .filter(sourceGoods -> sourceGoods != null) // 过滤null项
                        .map(sourceGoods -> {
                            ReceiveOrderGoodsVo targetVo = new ReceiveOrderGoodsVo();
                            BeanUtil.copyProperties(sourceGoods, targetVo);
                            return targetVo;
                        })
                        .collect(Collectors.toList());
            }

            vo.setGoodsList(goodsListVo);
        }
        return vo;
    }

    @Override
    public void submitReceiveOrder(Long id) {
        if (id == null) {
            throw new ParamException("收货单ID不能为空");
        }

        ReceiveOrder byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }

        // 验证订单状态
        if (byId.getDealState() == null) {
            throw new BizException("收货单状态异常: ID=" + id);
        }

        if (!ReceiveOrderStateEnum.TEMPORARY.getCode().equals(byId.getDealState())) {
            throw new BizException("操作失败，只有暂存状态的收货单才能提交: 当前状态=" +
                    ReceiveOrderStateEnum.getDesc(byId.getDealState()));
        }

        // 验证组织
        if (byId.getOrganizationId() == null) {
            throw new BizException("收货单组织ID不能为空");
        }

        Organization organization = organizationService.getById(byId.getOrganizationId());
        if (CommonUtils.isEmpty(organization)) {
            throw new BizException("组织不存在: ID=" + byId.getOrganizationId());
        }

        if (CommonUtils.isEmpty(organization.getInventoryDate())) {
            throw new BizException("进销存有效期未设置: 组织ID=" + byId.getOrganizationId());
        }

        boolean before = organization.getInventoryDate() != null &&
                organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期: 组织ID=" + byId.getOrganizationId());
        }

        // 更新为已审核状态
        this.baseMapper.updateStateById(id, ReceiveOrderStateEnum.REVIEWED.getCode());
        log.info("收货单状态已更新为已审核: ID={}", id);
    }

    /**
     * 更新订单状态
     *
     * @param id    订单ID
     * @param state 目标状态
     */
    private void updateOrderState(Long id, Integer state) {
        this.baseMapper.updateStateById(id, state);
    }

    @Override
    public List<ReceiveOrderExportVo> queryReceiveOrderList(QueryReceiveOrderParam param) {
        if (param == null) {
            throw new ParamException("查询参数不能为空");
        }

        // 安全获取用户组织类型
        String userOrgTypeStr = BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE);
        if (CommonUtils.isEmpty(userOrgTypeStr)) {
            throw new BizException("用户组织类型未设置");
        }

        Integer userOrgType;
        try {
            userOrgType = Integer.valueOf(userOrgTypeStr);
        } catch (NumberFormatException e) {
            log.error("用户组织类型格式错误: {}", userOrgTypeStr);
            throw new BizException("用户组织类型格式错误");
        }

        if (!OrganizationEnum.DRUGSTORE.getCode().equals(userOrgType)
                && !OrganizationEnum.PLATFORM.getCode().equals(userOrgType)) {
            throw new BizException("组织无权限");
        } else if (OrganizationEnum.DRUGSTORE.getCode().equals(userOrgType)) {
            // 安全获取用户组织ID
            String userOrgIdStr = BaseContextHandler.get(BaseContextConstants.USER_ORG_ID);
            if (CommonUtils.isEmpty(userOrgIdStr)) {
                throw new BizException("用户组织ID未设置");
            }

            try {
                param.setOrganizationId(Long.valueOf(userOrgIdStr));
            } catch (NumberFormatException e) {
                log.error("用户组织ID格式错误: {}", userOrgIdStr);
                throw new BizException("用户组织ID格式错误");
            }
        }

        List<ReceiveOrderExportVo> result = this.baseMapper.queryReceiveOrderList(param);
        return result != null ? result : Collections.emptyList();
    }

    @Override
    public void deleteReceiveOrderByOrganizationId(Long organizationId) {
        if (organizationId == null) {
            throw new ParamException("组织ID不能为空");
        }

        try {
            this.baseMapper.deleteReceiveOrderByOrganizationId(organizationId);
            log.info("已删除组织ID={}的收货单记录", organizationId);
        } catch (Exception e) {
            log.error("删除组织收货单失败: organizationId={}, 错误={}", organizationId, e.getMessage());
            throw new BizException("删除组织收货单失败: " + e.getMessage());
        }
    }

    @Override
    public Integer getReceiveOrderCount(Long organizationId, String day) {
        if (organizationId == null) {
            log.warn("获取收货单数量时组织ID为空");
            return 0;
        }
        if (CommonUtils.isEmpty(day)) {
            log.warn("获取收货单数量时日期为空");
            return 0;
        }

        Integer count = this.baseMapper.getReceiveOrderCount(organizationId, day);
        return count != null ? count : 0;
    }

    @Override
    public void completeReceiveOrder(Long orderId) {
        this.baseMapper.updateReviewedToCompleted(orderId);
    }

    /**
     * 用于临时存储已保存的收货明细实体及其对应的原始参数
     */
    @Getter
    @AllArgsConstructor
    private static class TempRejectedInfo {
        private final ReceiveOrderGoods entity;
        private final SaveOrUpdateReceiveOrderGoodsParam param;
    }

}
