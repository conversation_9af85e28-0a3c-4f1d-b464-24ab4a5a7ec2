
package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 进销存销售单商品明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_sales_goods")
@ApiModel(value="SalesGoods", description="进销存销售单商品明细")
public class SalesGoods extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "销售单Id")
    @TableField("order_id")
    private Long orderId;

    @ApiModelProperty(value = "销售员Id")
    @TableField("sale_id")
    private Long saleId;

    @ApiModelProperty(value = "私域商品Id")
    @TableField("organization_medicine_id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "通用名")
    @TableField("goods_name")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    @TableField("general_name")
    private String generalName;

    @ApiModelProperty(value = "商品编号")
    @TableField("goods_code")
    private String goodsCode;

    @ApiModelProperty(value = "包装单位")
    @TableField("pack_unit")
    private String packUnit;

    @ApiModelProperty(value = "规格型号")
    @TableField("specification")
    private String specification;

    @ApiModelProperty(value = "剂型")
    @TableField("drug_type")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    @TableField("manufacturer")
    private String manufacturer;

    @ApiModelProperty(value = "用法用量")
    @TableField("usage_dosage")
    private String usageDosage;

    @ApiModelProperty(value = "条形码")
    @TableField("bar_code")
    private String barCode;

    @ApiModelProperty(value = "批准文号")
    @TableField("approval_number")
    private String approvalNumber;

    @ApiModelProperty(value = "生产日期")
    @TableField("produce_date")
    private LocalDateTime produceDate;

    @ApiModelProperty(value = "有效期至")
    @TableField("user_date")
    private LocalDateTime userDate;

    @ApiModelProperty(value = "批号")
    @TableField("batch_number")
    private String batchNumber;

    @ApiModelProperty(value = "货位Id")
    @TableField("shelf_id")
    private Long shelfId;

    @ApiModelProperty(value = "销售数量")
    @TableField("sale_amount")
    private Double saleAmount;

    @ApiModelProperty(value = "赠送数量")
    @TableField("give_amount")
    private Double giveAmount;

    @ApiModelProperty(value = "单价")
    @TableField("price")
    private Integer price;

    @ApiModelProperty(value = "折后单价")
    @TableField("discount_price")
    private Integer discountPrice;

    @ApiModelProperty(value = "优惠")
    @TableField("preferential_price")
    private Integer preferentialPrice;

    @ApiModelProperty(value = "总计（销售数量*折后单价）")
    @TableField("total_price")
    private Integer totalPrice;

    @ApiModelProperty(value = "零售价（私域商品价格）")
    @TableField("sale_price")
    private Integer salePrice;

    @ApiModelProperty(value = "会员价")
    @TableField("member_price")
    private Integer memberPrice;

    @ApiModelProperty(value = "单品活动Id")
    @TableField("single_activity_id")
    private Long singleActivityId;

    @ApiModelProperty(value = "会员活动Id")
    @TableField("member_activity_id")
    private Long memberActivityId;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
