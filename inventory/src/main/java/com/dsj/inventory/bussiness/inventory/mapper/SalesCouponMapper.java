package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.SalesCoupon;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesCouponDetailParam;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesCouponParam;
import com.dsj.inventory.bussiness.inventory.vo.SalesCouponDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.SalesCouponVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存优惠券 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface SalesCouponMapper extends BaseMapper<SalesCoupon> {

    /**
     * 获取优惠券分页列表
     * @param page
     * @param param
     * @return
     */
    List<SalesCouponVo> querySalesCouponPage(@Param("page") Page page, @Param("param") QuerySalesCouponParam param);

    /**
     * 获取优惠券详情
     * @param id
     * @return
     */
    SalesCouponVo getSalesCouponById(Long id);

    /**
     * 获取优惠券会员明细分页列表
     * @param page
     * @param param
     * @return
     */
    List<SalesCouponDetailVo> querySalesCouponMemberDetailPage(@Param("page") Page page, @Param("param") QuerySalesCouponDetailParam param);

    /**
     * 获取优惠券用户明细分页列表
     * @param page
     * @param param
     * @return
     */
    List<SalesCouponDetailVo> querySalesCouponUserDetailPage(@Param("page") Page page, @Param("param") QuerySalesCouponDetailParam param);

}
