package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.*;
import com.dsj.inventory.bussiness.inventory.enumeration.CheckStateEnum;
import com.dsj.inventory.bussiness.inventory.enumeration.GainsLossesOrderStateEnum;
import com.dsj.inventory.bussiness.inventory.enumeration.GainsLossesOrderTypeEnum;
import com.dsj.inventory.bussiness.inventory.mapper.CheckMapper;
import com.dsj.inventory.bussiness.inventory.param.*;
import com.dsj.inventory.bussiness.inventory.service.*;
import com.dsj.inventory.bussiness.inventory.vo.CheckDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.CheckExportVo;
import com.dsj.inventory.bussiness.inventory.vo.CheckGoodsVo;
import com.dsj.inventory.bussiness.inventory.vo.CheckVo;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.common.utils.DozerUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.entity.UserROrganization;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.diagnose.service.UserROrganizationService;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import com.pocky.transport.bussiness.hospital.vo.OrganizationMedicineMappingVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 进销存盘点计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
@Service
public class CheckServiceImpl extends ServiceImpl<CheckMapper, Check> implements CheckService {

    @Autowired
    private DozerUtils dozerUtils;
    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @Autowired
    private CheckGoodsService checkGoodsService;
    @Autowired
    private InventoryService inventoryService;
    @Autowired
    private GainsLossesOrderService gainsLossesOrderService;
    @Autowired
    private GainsLossesGoodsService gainsLossesGoodsService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private InventoryChangeLogService inventoryChangeLogService;
    @Autowired
    private UserROrganizationService userROrganizationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckDetailVo saveOrUpdateCheck(SaveOrUpdateCheckParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if (CommonUtils.isNotEmpty(param.getCheckState()) && param.getCheckState().equals(CheckStateEnum.COMPLETED.getCode())) {
            throw new BizException("操作失败，盘点计划已完成");
        }
        Organization organization = organizationService.getById(param.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        if(CommonUtils.isNotEmpty(param.getId())) {
            Check byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            if (!byId.getCheckState().equals(CheckStateEnum.TEMPORARY.getCode())) {
                throw new BizException("操作失败，盘点计划状态不允许编辑");
            }
            //删除商品明细
            checkGoodsService.lambdaUpdate().eq(CheckGoods::getCheckId,param.getId())
                    .set(CheckGoods::getIsDelete, TrueEnum.TRUE.getCode()).update(new CheckGoods());
        }else {
            //生成盘点计划单号：PD+年后两位+月日四位+药房识别码六位+三位
            String day = DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_FORMAT);
            String dayStr = day.replaceAll("-", "").substring(2,8);
            Integer count = this.getCheckCount(organization.getId(),day);
            count = count + 1;
            param.setCode("PD" + dayStr + organization.getSerialNo() + String.format("%03d",count));
        }

        Set<String> shelfIdSet = new HashSet<>();
        if(CommonUtils.isNotEmpty(param.getGoodsList())){
            List<Long> inventoryIdList = param.getGoodsList().stream().map(g -> g.getInventoryId()).distinct().collect(Collectors.toList());
            if(inventoryIdList.size() != param.getGoodsList().size()){
                throw new BizException("操作失败，存在重复商品库存");
            }
            for(SaveOrUpdateCheckGoodsParam goodsParam : param.getGoodsList()){
                OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(goodsParam.getOrganizationMedicineId());
                if(CommonUtils.isEmpty(organizationMedicine)){
                    throw new BizException("操作失败，商品Id"+goodsParam.getOrganizationMedicineId()+"不存在");
                }
                Inventory inventory = inventoryService.getById(goodsParam.getInventoryId());
                if(CommonUtils.isEmpty(inventory)){
                    throw new BizException("操作失败，商品"+organizationMedicine.getName()+"无库存");
                }
                goodsParam.setGoodsName(organizationMedicine.getName());
                goodsParam.setGeneralName(organizationMedicine.getGeneralName());
                goodsParam.setGoodsCode(organizationMedicine.getGoodsCode());
                goodsParam.setPackUnit(organizationMedicine.getPackUnit());
                goodsParam.setSpecification(organizationMedicine.getSpecification());
                goodsParam.setDrugType(organizationMedicine.getDrugType());
                goodsParam.setManufacturer(organizationMedicine.getManufacturer());
                goodsParam.setBarCode(organizationMedicine.getBarCode());
                goodsParam.setApprovalNumber(organizationMedicine.getApprovalNumber());
                goodsParam.setPrice(organizationMedicine.getOfflinePrice());
                goodsParam.setProduceDate(inventory.getProduceDate());
                goodsParam.setUserDate(inventory.getUserDate());
                goodsParam.setShelfId(inventory.getShelfId());
                goodsParam.setBatchNumber(inventory.getBatchNumber());
                goodsParam.setBatch(inventory.getBatch());
                goodsParam.setInventoryAmount(inventory.getInventoryAmount());
                shelfIdSet.add(goodsParam.getShelfId().toString());
            }
            if(CommonUtils.isEmpty(param.getShelfIds()) || !param.getShelfIds().equals("1")){
                param.setShelfIds(String.join(",",shelfIdSet));
            }
        }
        Check check = dozerUtils.map(param,Check.class);
        this.saveOrUpdate(check);
        if(CommonUtils.isNotEmpty(param.getGoodsList())){
            param.getGoodsList().stream().map(g -> g.setCheckId(check.getId())).collect(Collectors.toList());
            List<CheckGoods> goodsList = dozerUtils.mapList(param.getGoodsList(),CheckGoods.class);
            checkGoodsService.saveBatch(goodsList);
        }
        return this.getCheckById(check.getId());
    }

    @Override
    public void deleteCheck(Long id) {
        Check byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        if(!CheckStateEnum.TEMPORARY.getCode().equals(byId.getCheckState())){
            throw new BizException("操作失败，盘点计划当前状态不允许删除");
        }
        //删除商品明细
        checkGoodsService.lambdaUpdate().eq(CheckGoods::getCheckId,id)
                .set(CheckGoods::getIsDelete, TrueEnum.TRUE.getCode()).update(new CheckGoods());
        this.baseMapper.deleteById(id);
    }

    @Override
    public Page<CheckVo> queryCheckPage(QueryCheckParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<CheckVo> list = this.baseMapper.queryCheckPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public CheckDetailVo getCheckById(Long id) {
        CheckDetailVo vo = this.baseMapper.getCheckById(id);
        if(CommonUtils.isNotEmpty(vo)){
            List<CheckGoodsVo> goodsList = checkGoodsService.getCheckGoodsListByCheckId(vo.getId());
            if(CommonUtils.isNotEmpty(goodsList)){
                vo.setGoodsList(goodsList);
            }
        }
        return vo;
    }

    @Override
    public void submitCheck(Long id) {
        Check byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        Organization organization = organizationService.getById(byId.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        this.lambdaUpdate().eq(Check::getId,id).set(Check::getCheckState,CheckStateEnum.CHECK.getCode()).update(new Check());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CheckDetailVo completeCheck(CompleteCheckParam param) {
        Check check = this.getById(param.getId());
        if(!CheckStateEnum.CHECK.getCode().equals(check.getCheckState())){
            throw new BizException("操作失败，该盘点计划状态不是盘点中");
        }
        Organization organization = organizationService.getById(check.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        //报溢
        List<GainsLossesGoods> gainsGoodsList = new ArrayList<>();
        Set<String> gainsGoodsNameSet = new HashSet<>();
        BigDecimal gainsTotalAmount = BigDecimal.valueOf(0.00);
        BigDecimal gainsCostTotalPrice = BigDecimal.valueOf(0);
        BigDecimal gainsAverageTotalCost = BigDecimal.valueOf(0);
        BigDecimal gainsTotalPrice = BigDecimal.valueOf(0);
        //报损
        List<GainsLossesGoods> lossesGoodsList = new ArrayList<>();
        Set<String> lossesGoodsNameSet = new HashSet<>();
        BigDecimal lossesTotalAmount = BigDecimal.valueOf(0.00);
        BigDecimal lossesCostTotalPrice = BigDecimal.valueOf(0);
        BigDecimal lossesAverageTotalCost = BigDecimal.valueOf(0);
        BigDecimal lossesTotalPrice = BigDecimal.valueOf(0);
        StringBuilder errorStr = new StringBuilder();
        for(CompleteCheckGoodsParam goodsParam : param.getGoodsList()){
            if(CommonUtils.isNotEmpty(goodsParam.getCheckAmount())){
                CheckGoods checkGoods = checkGoodsService.getById(goodsParam.getId());
                Inventory inventory = inventoryService.getById(checkGoods.getInventoryId());
                if(!inventory.getInventoryAmount().equals(goodsParam.getInventoryAmount())){
                    errorStr.append(checkGoods.getGoodsName() + "库存数已变更为" + inventory.getInventoryAmount() + "，");
                }
                checkGoodsService.lambdaUpdate().eq(CheckGoods::getId,checkGoods.getId())
                        .set(CheckGoods::getInventoryAmount,inventory.getInventoryAmount())
                        .set(CheckGoods::getCheckAmount,goodsParam.getCheckAmount()).update(new CheckGoods());
                if(CheckStateEnum.COMPLETED.getCode().equals(param.getCheckState())){
                    OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(checkGoods.getOrganizationMedicineId());
                    if(goodsParam.getCheckAmount() > inventory.getInventoryAmount()){
                        BigDecimal plugAmount = BigDecimal.valueOf(goodsParam.getCheckAmount())
                                .subtract(BigDecimal.valueOf(inventory.getInventoryAmount()));
                        GainsLossesGoods gainsGoods = dozerUtils.map(checkGoods,GainsLossesGoods.class);
                        gainsGoods.setId(null);
                        gainsGoods.setInventoryAmount(inventory.getInventoryAmount());
                        gainsGoods.setCostPrice(inventory.getCostPrice());
                        BigDecimal goodsCostTotalPrice = BigDecimal.valueOf(plugAmount.doubleValue()).multiply(BigDecimal.valueOf(inventory.getCostPrice()));
                        gainsGoods.setCostTotalPrice(goodsCostTotalPrice.intValue());
                        gainsGoods.setAverageCost(organizationMedicine.getAverageCost());
                        BigDecimal goodsAverageTotalCost = BigDecimal.valueOf(plugAmount.doubleValue()).multiply(BigDecimal.valueOf(organizationMedicine.getAverageCost()));
                        gainsGoods.setAverageTotalCost(goodsAverageTotalCost.intValue());
                        BigDecimal price = BigDecimal.valueOf(plugAmount.doubleValue()).multiply(BigDecimal.valueOf(organizationMedicine.getOfflinePrice()));
                        gainsTotalPrice = gainsTotalPrice.add(price);
                        gainsTotalAmount = gainsTotalAmount.add(BigDecimal.valueOf(plugAmount.doubleValue()));
                        gainsCostTotalPrice = gainsCostTotalPrice.add(goodsCostTotalPrice);
                        gainsAverageTotalCost = gainsAverageTotalCost.add(goodsAverageTotalCost);
                        gainsGoodsNameSet.add(organizationMedicine.getName());
                        gainsGoods.setPlugAmount(plugAmount.doubleValue());
                        gainsGoods.setRealAmount(goodsParam.getCheckAmount());
                        gainsGoodsList.add(gainsGoods);
                    }else if(goodsParam.getCheckAmount() < inventory.getInventoryAmount()){
                        BigDecimal plugAmount = BigDecimal.valueOf(inventory.getInventoryAmount())
                                .subtract(BigDecimal.valueOf(goodsParam.getCheckAmount()));
                        GainsLossesGoods lossesGoods = dozerUtils.map(checkGoods,GainsLossesGoods.class);
                        lossesGoods.setId(null);
                        lossesGoods.setInventoryAmount(inventory.getInventoryAmount());
                        lossesGoods.setCostPrice(inventory.getCostPrice());
                        BigDecimal goodsCostTotalPrice = BigDecimal.valueOf(plugAmount.doubleValue()).multiply(BigDecimal.valueOf(inventory.getCostPrice()));
                        lossesGoods.setCostTotalPrice(goodsCostTotalPrice.intValue());
                        lossesGoods.setAverageCost(organizationMedicine.getAverageCost());
                        BigDecimal goodsAverageTotalCost = BigDecimal.valueOf(plugAmount.doubleValue()).multiply(BigDecimal.valueOf(organizationMedicine.getAverageCost()));
                        lossesGoods.setAverageTotalCost(goodsAverageTotalCost.intValue());
                        BigDecimal price = BigDecimal.valueOf(plugAmount.doubleValue()).multiply(BigDecimal.valueOf(organizationMedicine.getOfflinePrice()));
                        lossesTotalPrice = lossesTotalPrice.add(price);
                        lossesTotalAmount = lossesTotalAmount.add(BigDecimal.valueOf(plugAmount.doubleValue()));
                        lossesCostTotalPrice = lossesCostTotalPrice.add(goodsCostTotalPrice);
                        lossesAverageTotalCost = lossesAverageTotalCost.add(goodsAverageTotalCost);
                        lossesGoodsNameSet.add(organizationMedicine.getName());
                        lossesGoods.setPlugAmount(plugAmount.doubleValue());
                        lossesGoods.setRealAmount(goodsParam.getCheckAmount());
                        lossesGoodsList.add(lossesGoods);
                    }

                }
            }
        }
        if(CheckStateEnum.COMPLETED.getCode().equals(param.getCheckState())){
            Check updateCheck = new Check();
            updateCheck.setId(check.getId());
            updateCheck.setCheckDate(LocalDateTime.now());
            if(CommonUtils.isNotEmpty(errorStr) && errorStr.length() > 0){
                param.setCheckState(CheckStateEnum.CHECK.getCode());
                this.updateById(updateCheck);
                throw new BizException(errorStr.append("请确认").toString());
            }
            updateCheck.setCheckState(CheckStateEnum.COMPLETED.getCode());
            //生成报损报溢单
            String day = DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_FORMAT);
            String dayStr = day.replaceAll("-", "").substring(2,8);
            if(CommonUtils.isNotEmpty(gainsGoodsList) && gainsGoodsList.size() > 0){
                GainsLossesOrder gainsOrder = GainsLossesOrder.builder().organizationId(check.getOrganizationId())
                        .dealState(GainsLossesOrderStateEnum.TEMPORARY.getCode()).type(GainsLossesOrderTypeEnum.Gains.getCode())
                        .goodsNames(String.join(",",gainsGoodsNameSet)).totalAmount(gainsTotalAmount.doubleValue())
                        .costTotalPrice(gainsCostTotalPrice.intValue()).averageTotalCost(gainsAverageTotalCost.intValue())
                        .totalPrice(gainsTotalPrice.intValue())
                        .build();
                //生成损溢单号：SY+年后两位+月日四位+药房识别码六位+三位
                Integer count = gainsLossesOrderService.getGainsLossesOrderCount(organization.getId(),day);
                count = count + 1;
                gainsOrder.setCode("SY" + dayStr + organization.getSerialNo() + String.format("%03d",count));
                gainsLossesOrderService.save(gainsOrder);
                gainsGoodsList.stream().map(g -> g.setOrderId(gainsOrder.getId())).collect(Collectors.toList());
                gainsLossesGoodsService.saveBatch(gainsGoodsList);
                updateCheck.setGainsId(gainsOrder.getId());
            }
            if(CommonUtils.isNotEmpty(lossesGoodsList) && lossesGoodsList.size() > 0){
                GainsLossesOrder lossesOrder = GainsLossesOrder.builder().organizationId(check.getOrganizationId())
                        .dealState(GainsLossesOrderStateEnum.TEMPORARY.getCode()).type(GainsLossesOrderTypeEnum.Losses.getCode())
                        .goodsNames(String.join(",",lossesGoodsNameSet)).totalAmount(lossesTotalAmount.doubleValue())
                        .costTotalPrice(lossesCostTotalPrice.intValue()).averageTotalCost(lossesAverageTotalCost.intValue())
                        .totalPrice(lossesTotalPrice.intValue())
                        .build();
                //生成损溢单号：SY+年后两位+月日四位+药房识别码六位+三位
                Integer count = gainsLossesOrderService.getGainsLossesOrderCount(organization.getId(),day);
                count = count + 1;
                lossesOrder.setCode("SY" + dayStr + organization.getSerialNo() + String.format("%03d",count));
                gainsLossesOrderService.save(lossesOrder);
                lossesGoodsList.stream().map(g -> g.setOrderId(lossesOrder.getId())).collect(Collectors.toList());
                gainsLossesGoodsService.saveBatch(lossesGoodsList);
                updateCheck.setLossesId(lossesOrder.getId());
            }
            this.updateById(updateCheck);
        }
        return this.getCheckById(param.getId());
    }

    @Override
    public List<CheckExportVo> queryCheckList(QueryCheckParam param) {
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        return this.baseMapper.queryCheckList(param);
    }

    @Override
    public void deleteCheckByOrganizationId(Long organizationId) {
        this.baseMapper.deleteCheckByOrganizationId(organizationId);
    }

    @Override
    public Integer getCheckCount(Long organizationId, String day) {
        return this.baseMapper.getCheckCount(organizationId, day);
    }

    @Override
    public CheckDetailVo saveDayCheck(Long organizationId) {
        Organization organization = organizationService.getById(organizationId);
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        //获取当天销售或退货的销售商品
        LocalDate dayDate = LocalDate.now();
        dayDate = LocalDate.of(2023, 7, 7);
        LocalDateTime startTime = LocalDateTime.of(dayDate, LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(dayDate, LocalTime.of(23,59,59));
        List<CheckGoodsVo> checkGoodsList = inventoryChangeLogService.getDayCheckGoods(organizationId,startTime,endTime);
        if(CommonUtils.isEmpty(checkGoodsList)){
            return null;
        }
        Check check = Check.builder().organizationId(organizationId).name(dayDate+"盘点计划")
                .checkState(CheckStateEnum.TEMPORARY.getCode()).build();
        //生成盘点计划单号：PD+年后两位+月日四位+药房识别码六位+三位
        String day = DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_FORMAT);
        String dayStr = day.replaceAll("-", "").substring(2,8);
        Integer count = this.getCheckCount(organization.getId(),day);
        count = count + 1;
        check.setCode("PD" + dayStr + organization.getSerialNo() + String.format("%03d",count));
        //获取盘点人
        List<UserROrganization> uroList = userROrganizationService.lambdaQuery()
                .eq(UserROrganization::getOrganizationId,organizationId).list();
        if(CommonUtils.isNotEmpty(uroList)){
            List<String> checkIdList = uroList.stream().map(u -> u.getUserId().toString()).distinct().collect(Collectors.toList());
            check.setCheckIds(String.join(",",checkIdList));
        }
        List<String> shelfIdList = checkGoodsList.stream().map(c -> c.getShelfId().toString()).distinct().collect(Collectors.toList());
        check.setShelfIds(String.join(",",shelfIdList));
        this.saveOrUpdate(check);
        checkGoodsList.stream().map(g -> g.setCheckId(check.getId())).collect(Collectors.toList());
        List<CheckGoods> goodsList = dozerUtils.mapList(checkGoodsList,CheckGoods.class);
        checkGoodsService.saveBatch(goodsList);
        return this.getCheckById(check.getId());
    }

}
