package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryOrganizationSupplierMemberParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateOrganizationSupplierMemberParam;
import com.dsj.inventory.bussiness.inventory.service.OrganizationSupplierMemberService;
import com.dsj.inventory.bussiness.inventory.vo.OrganizationSupplierMemberVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 供应商组织私域销售人员 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-24
 */
@RestController
@Api(value = "OrganizationSupplierMemberController", tags = "供应商组织私域销售人员api")
public class OrganizationSupplierMemberController {

    @Autowired
    private OrganizationSupplierMemberService organizationSupplierMemberService;

    /*新增供应商组织私域销售人员*/
    @PostMapping("/organization-supplier-member")
    @ApiOperation(value = "新增供应商组织私域销售人员", notes = "新增供应商组织私域销售人员")
    public ResponseEntity<OrganizationSupplierMemberVo> saveOrganizationSupplierMember(@RequestBody SaveOrUpdateOrganizationSupplierMemberParam param){
        OrganizationSupplierMemberVo memberVo = organizationSupplierMemberService.saveOrUpdateOrganizationSupplierMember(param);
        return Res.success(memberVo);
    }

    /*编辑供应商组织私域销售人员*/
    @PutMapping("/organization-supplier-member/{id}")
    @ApiOperation(value = "编辑供应商组织私域销售人员", notes = "编辑供应商组织私域销售人员")
    public ResponseEntity<OrganizationSupplierMemberVo> updateOrganizationSupplierMember(@PathVariable Long id, @RequestBody SaveOrUpdateOrganizationSupplierMemberParam param){
        param.setId(id);
        OrganizationSupplierMemberVo memberVo = organizationSupplierMemberService.saveOrUpdateOrganizationSupplierMember(param);
        return Res.success(memberVo);
    }

    /*删除供应商组织私域销售人员*/
    @DeleteMapping("/organization-supplier-member/{id}")
    @ApiOperation(value = "删除供应商组织私域销售人员", notes = "删除供应商组织私域销售人员")
    public ResponseEntity<Void> deleteOrganizationSupplierMember(@PathVariable Long id){
        SaveOrUpdateOrganizationSupplierMemberParam param = SaveOrUpdateOrganizationSupplierMemberParam.builder().isDelete(TrueEnum.TRUE.getCode()).id(id).build();
        organizationSupplierMemberService.saveOrUpdateOrganizationSupplierMember(param);
        return Res.success();
    }

    /*获取供应商组织私域销售人员列表*/
    @GetMapping("/organization-supplier-member")
    @ApiOperation(value = "获取供应商组织私域销售人员列表", notes = "获取供应商组织私域销售人员列表")
    public ResponseEntity<List<OrganizationSupplierMemberVo>> queryOrganizationSupplierMemberList(QueryOrganizationSupplierMemberParam param){
        Page<OrganizationSupplierMemberVo> page = organizationSupplierMemberService.queryOrganizationSupplierMemberPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*供应商组织私域批量处理销售人员 --新增、编辑、删除*/
    @PostMapping("/organization-supplier-member/{organizationSupplierId}/action-batch-change")
    @ApiOperation(value = "供应商组织私域批量处理销售人员", notes = "供应商组织私域批量处理销售人员 --新增、编辑、删除")
    public ResponseEntity<List<OrganizationSupplierMemberVo>> batchChangeOrganizationSupplierMember(@PathVariable Long organizationSupplierId, @RequestBody List<SaveOrUpdateOrganizationSupplierMemberParam> paramList){
        List<OrganizationSupplierMemberVo> list = organizationSupplierMemberService.batchChangeOrganizationSupplierMember(organizationSupplierId,paramList);
        return Res.success(list);
    }

}
