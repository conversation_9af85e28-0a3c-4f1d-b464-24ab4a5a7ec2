package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询优惠券明细
 * <AUTHOR>
 * @date 2023/04/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QuerySalesCouponDetailParam",description = "查询优惠券明细")
public class QuerySalesCouponDetailParam extends BasePage {

    @ApiModelProperty(hidden = true,value = "优惠券Id")
    private Long couponId;

}
