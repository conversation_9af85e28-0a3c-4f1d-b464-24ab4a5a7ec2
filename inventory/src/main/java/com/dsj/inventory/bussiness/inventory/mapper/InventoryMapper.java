package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.Inventory;
import com.dsj.inventory.bussiness.inventory.param.QueryInventoryParam;
import com.dsj.inventory.bussiness.inventory.param.QueryInventorySaleParam;
import com.dsj.inventory.bussiness.inventory.vo.InventoryExportVo;
import com.dsj.inventory.bussiness.inventory.vo.InventorySaleVo;
import com.dsj.inventory.bussiness.inventory.vo.InventoryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存商品库存 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
public interface InventoryMapper extends BaseMapper<Inventory> {

    /**
     * 获取商品库存分页列表
     * @param page
     * @param param
     * @return
     */
    List<InventoryVo> queryInventoryPage(@Param("page") Page page, @Param("param") QueryInventoryParam param);

    /**
     * 导出商品库存列表
     * @param param
     * @return
     */
    List<InventoryExportVo> queryInventoryList(@Param("param") QueryInventoryParam param);

    /**
     * 获取商品销售库存分页列表
     * @param page
     * @param param
     * @return
     */
    List<InventorySaleVo> queryInventorySalePage(@Param("page") Page page, @Param("param") QueryInventorySaleParam param);

    /**
     * 获取需要新增近效期预警的库存
     * @param organizationId
     * @param becomeDueDay
     * @return
     */
    List<InventoryVo> queryBecomeDueInventoryList(@Param("organizationId") Long organizationId, @Param("becomeDueDay") Integer becomeDueDay);

}
