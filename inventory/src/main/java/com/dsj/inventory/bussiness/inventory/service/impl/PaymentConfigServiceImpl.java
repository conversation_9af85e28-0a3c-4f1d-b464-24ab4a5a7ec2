package com.dsj.inventory.bussiness.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.PaymentConfig;
import com.dsj.inventory.bussiness.inventory.mapper.PaymentConfigMapper;
import com.dsj.inventory.bussiness.inventory.param.PaymentConfigParam;
import com.dsj.inventory.bussiness.inventory.service.PaymentConfigService;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.DeleteEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 付款方式配置 Service 实现类
 */
@Service
@RequiredArgsConstructor
public class PaymentConfigServiceImpl extends ServiceImpl<PaymentConfigMapper, PaymentConfig> implements PaymentConfigService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPaymentMethodId(Long paymentMethodId) {
        if (paymentMethodId == null) {
            return;
        }
        LambdaQueryWrapper<PaymentConfig> deleteWrapper = new LambdaQueryWrapper<PaymentConfig>()
                .eq(PaymentConfig::getPaymentMethodId, paymentMethodId);
        baseMapper.delete(deleteWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void replacePaymentConfigs(Long paymentMethodId, Long organizationId, List<PaymentConfigParam> configParams) {
        // 先删除旧的配置项
        this.deleteByPaymentMethodId(paymentMethodId);

        if (CollUtil.isEmpty(configParams)) {
            return;
        }

        Long userId = BaseContextHandler.getUserId();
        LocalDateTime now = LocalDateTime.now();
        List<PaymentConfig> newConfigs = new ArrayList<>();

        for (PaymentConfigParam configParam : configParams) {
            PaymentConfig config = new PaymentConfig();
            BeanUtil.copyProperties(configParam, config);
            config.setPaymentMethodId(paymentMethodId);
            config.setOrganizationId(organizationId);
            config.setCreateTime(now);
            config.setCreateUser(userId);
            config.setUpdateTime(now);
            config.setUpdateUser(userId);
            // 如果参数中没有 enabled 字段，确保默认为启用，或根据业务要求处理
            if (config.getEnabled() == null) {
                config.setEnabled(DeleteEnum.NOT_DELETE.getCode());
            }
            newConfigs.add(config);
        }
        this.saveBatch(newConfigs);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAllConfigs(List<PaymentConfig> configs) {
        if (CollUtil.isEmpty(configs)) {
            return;
        }
        this.saveBatch(configs);
    }

    @Override
    public List<PaymentConfig> listByPaymentMethodId(Long paymentMethodId) {
        if (paymentMethodId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PaymentConfig> queryWrapper = new LambdaQueryWrapper<PaymentConfig>()
                .eq(PaymentConfig::getPaymentMethodId, paymentMethodId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<PaymentConfig> listEnabledByPaymentMethodId(Long paymentMethodId) {
        if (paymentMethodId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<PaymentConfig> queryWrapper = new LambdaQueryWrapper<PaymentConfig>()
                .eq(PaymentConfig::getPaymentMethodId, paymentMethodId)
                .eq(PaymentConfig::getEnabled, DeleteEnum.NOT_DELETE.getCode());
        return baseMapper.selectList(queryWrapper);
    }
    
    @Override
    public Map<Long, List<PaymentConfig>> mapAllConfigsByPaymentMethodIds(Set<Long> paymentMethodIds) {
        if (CollUtil.isEmpty(paymentMethodIds)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<PaymentConfig> queryWrapper = new LambdaQueryWrapper<PaymentConfig>()
                .in(PaymentConfig::getPaymentMethodId, paymentMethodIds);
        List<PaymentConfig> configs = baseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(configs)) {
            return Collections.emptyMap();
        }
        return configs.stream().collect(Collectors.groupingBy(PaymentConfig::getPaymentMethodId));
    }

    @Override
    public Map<Long, List<PaymentConfig>> mapEnabledConfigsByPaymentMethodIds(Set<Long> paymentMethodIds) {
        if (CollUtil.isEmpty(paymentMethodIds)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<PaymentConfig> queryWrapper = new LambdaQueryWrapper<PaymentConfig>()
                .in(PaymentConfig::getPaymentMethodId, paymentMethodIds)
                .eq(PaymentConfig::getEnabled, DeleteEnum.NOT_DELETE.getCode());
        List<PaymentConfig> configs = baseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(configs)) {
            return Collections.emptyMap();
        }
        return configs.stream().collect(Collectors.groupingBy(PaymentConfig::getPaymentMethodId));
    }
} 