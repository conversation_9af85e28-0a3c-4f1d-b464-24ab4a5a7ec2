package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 销售活动任务关联信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_sales_activity_task_mapping")
@ApiModel(value="SalesActivityTaskMapping", description="销售活动任务关联信息")
public class SalesActivityTaskMapping extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务Id")
    @TableField("task_id")
    private Long taskId;

    @ApiModelProperty(value = "活动Id")
    @TableField("activity_id")
    private Long activityId;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}