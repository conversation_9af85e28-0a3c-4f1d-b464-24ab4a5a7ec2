package com.dsj.inventory.framework.config.easyExcel;


/**
 * 性别转换器
 */

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.pocky.transport.bussiness.auth.enumeration.GenderEnum;

public class GenderConverter implements Converter<Integer> {

    @Override
    public Class<Integer> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
                                           GlobalConfiguration globalConfiguration) {
        if (cellData.getStringValue().equals(GenderEnum.MAN.getDesc())){
            return GenderEnum.MAN.getCode();
        }else if (cellData.getStringValue().equals(GenderEnum.WOMAN.getDesc())){
            return GenderEnum.WOMAN.getCode();
        }else {
            return GenderEnum.CHILD.getCode();
        }
    }

    @Override
    public CellData<Integer> convertToExcelData(Integer value, ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        if (value.equals(GenderEnum.MAN.getCode())){
            return new CellData<>(GenderEnum.MAN.getDesc());
        }else if (value.equals(GenderEnum.WOMAN.getCode())){
            return new CellData<>(GenderEnum.WOMAN.getDesc());
        }else {
            return new CellData<>(GenderEnum.CHILD.getDesc());
        }
    }

}
