package com.dsj.inventory.framework.properties;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.util.AntPathMatcher;

import java.util.List;

/**
 * 忽略token 配置类
 * <p>
 *
 * <AUTHOR>
 * @date 2021年4月22日20:56:31
 */
@Data
@ConfigurationProperties(prefix = com.dsj.inventory.framework.properties.IgnoreTokenProperties.PREFIX)
public class IgnoreTokenProperties {
    public static final String PREFIX = "ignore.token";
    private static final AntPathMatcher ANT_PATH_MATCHER = new AntPathMatcher();
    private List<String> url = CollUtil.newArrayList(
            "/error",
            "/actuator/**",
            "/gate/**",
            "/static/**",
            "/anno/**",
            "/**/anno/**",
            "/**/swagger-ui.html",
            "/**/doc.html",
            "/**/webjars/**",
            "/**/v3/api-docs/**",
            "/**/v3/api-docs-ext/**",
            "/**/swagger-resources/**",
            "/**/free/**"
    );

    public boolean isIgnoreToken(String path) {
        return getUrl().stream().anyMatch((url) -> path.startsWith(url) || ANT_PATH_MATCHER.match(url, path));
    }
}
