-- =====================================================
-- 采购计划功能增强数据库迁移测试脚本
-- 版本: V1.1.0 Test
-- 描述: 测试采购计划模块增强功能的数据库迁移脚本
-- 作者: AI Assistant
-- 日期: 2025-06-23
-- =====================================================

-- =====================================================
-- 1. 测试前准备 - 创建测试数据
-- =====================================================

-- 插入测试采购计划数据
INSERT INTO jxc_purchase_plan (
    id, organization_id, code, bill_date, expect_receive_date, 
    remark, deal_state, organization_supplier_id, total_price, 
    goods_types, purchase_content, create_time, create_user, 
    update_time, update_user, is_delete
) VALUES (
    999999, 1, 'TEST_PLAN_001', NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY),
    '测试采购计划', 0, 1, 10000,
    '药品', '测试采购内容', NOW(), 1,
    NOW(), 1, 0
) ON DUPLICATE KEY UPDATE code = code;

-- 插入测试采购计划商品明细数据
INSERT INTO jxc_purchase_plan_goods (
    id, plan_id, organization_medicine_id, goods_name, general_name,
    goods_code, pack_unit, specification, drug_type, manufacturer,
    launch_permit_holder, price, repertory, purchase_price,
    purchase_number, total_purchase_price, create_time, create_user,
    update_time, update_user, is_delete
) VALUES (
    999999, 999999, 1, '测试药品', '测试通用名',
    'TEST_GOODS_001', '盒', '10mg*10片', '片剂', '测试厂家',
    '测试持有人', 1500, 100.0, 1200,
    50.0, 60000, NOW(), 1,
    NOW(), 1, 0
) ON DUPLICATE KEY UPDATE goods_name = goods_name;

-- =====================================================
-- 2. 执行迁移脚本测试
-- =====================================================

-- 测试字段添加
SELECT 'Testing field addition...' as test_phase;

-- 模拟执行迁移脚本的关键部分
-- 检查采购计划表新增字段
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default,
    column_comment
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan' 
  AND column_name IN ('auto_generated', 'gsp_check_status', 'check_message');

-- 检查采购计划商品明细表新增字段
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default,
    column_comment
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods' 
  AND column_name IN ('organization_supplier_id', 'in_transit_quantity', 'daily_avg_sales', 
                      'stock_upper_limit', 'stock_lower_limit', 'recommend_quantity', 'price_strategy');

-- =====================================================
-- 3. 测试数据操作
-- =====================================================

-- 测试新字段的数据插入和更新
SELECT 'Testing data operations...' as test_phase;

-- 更新测试采购计划的新字段
UPDATE jxc_purchase_plan 
SET auto_generated = 1,
    gsp_check_status = 1,
    check_message = '测试GSP校验通过'
WHERE id = 999999;

-- 更新测试商品明细的新字段
UPDATE jxc_purchase_plan_goods 
SET organization_supplier_id = 1,
    in_transit_quantity = 20.0,
    daily_avg_sales = 5.0,
    stock_upper_limit = 150.0,
    stock_lower_limit = 35.0,
    recommend_quantity = 30.0,
    price_strategy = 2
WHERE id = 999999;

-- 验证数据更新结果
SELECT 
    id, code, auto_generated, gsp_check_status, check_message
FROM jxc_purchase_plan 
WHERE id = 999999;

SELECT 
    id, goods_name, organization_supplier_id, in_transit_quantity,
    daily_avg_sales, stock_upper_limit, stock_lower_limit,
    recommend_quantity, price_strategy
FROM jxc_purchase_plan_goods 
WHERE id = 999999;

-- =====================================================
-- 4. 测试索引性能
-- =====================================================

-- 测试索引查询性能
SELECT 'Testing index performance...' as test_phase;

-- 测试自动生成标识索引
EXPLAIN SELECT * FROM jxc_purchase_plan 
WHERE auto_generated = 1 AND organization_id = 1;

-- 测试GSP校验状态索引
EXPLAIN SELECT * FROM jxc_purchase_plan 
WHERE gsp_check_status = 1 AND organization_id = 1;

-- 测试供应商ID索引
EXPLAIN SELECT * FROM jxc_purchase_plan_goods 
WHERE organization_supplier_id = 1;

-- 测试价格策略索引
EXPLAIN SELECT * FROM jxc_purchase_plan_goods 
WHERE price_strategy = 2 AND plan_id = 999999;

-- 测试库存分析索引
EXPLAIN SELECT * FROM jxc_purchase_plan_goods 
WHERE organization_medicine_id = 1 
  AND repertory < stock_lower_limit;

-- =====================================================
-- 5. 测试约束和数据完整性
-- =====================================================

-- 测试字段约束
SELECT 'Testing constraints and data integrity...' as test_phase;

-- 测试默认值
INSERT INTO jxc_purchase_plan (
    id, organization_id, code, bill_date, expect_receive_date,
    remark, deal_state, organization_supplier_id, total_price,
    goods_types, purchase_content, create_time, create_user,
    update_time, update_user, is_delete
) VALUES (
    999998, 1, 'TEST_PLAN_002', NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY),
    '测试默认值', 0, 1, 5000,
    '药品', '测试默认值内容', NOW(), 1,
    NOW(), 1, 0
);

-- 检查默认值是否正确设置
SELECT 
    id, code, auto_generated, gsp_check_status, check_message
FROM jxc_purchase_plan 
WHERE id = 999998;

-- 测试商品明细默认值
INSERT INTO jxc_purchase_plan_goods (
    plan_id, organization_medicine_id, goods_name, general_name,
    goods_code, pack_unit, specification, drug_type, manufacturer,
    launch_permit_holder, price, repertory, purchase_price,
    purchase_number, total_purchase_price, create_time, create_user,
    update_time, update_user, is_delete
) VALUES (
    999998, 2, '测试药品2', '测试通用名2',
    'TEST_GOODS_002', '盒', '20mg*10片', '片剂', '测试厂家2',
    '测试持有人2', 2000, 80.0, 1800,
    30.0, 54000, NOW(), 1,
    NOW(), 1, 0
);

-- 检查商品明细默认值
SELECT 
    id, goods_name, organization_supplier_id, in_transit_quantity,
    daily_avg_sales, stock_upper_limit, stock_lower_limit,
    recommend_quantity, price_strategy
FROM jxc_purchase_plan_goods 
WHERE plan_id = 999998;

-- =====================================================
-- 6. 测试边界值和异常情况
-- =====================================================

-- 测试边界值
SELECT 'Testing boundary values...' as test_phase;

-- 测试最大值
UPDATE jxc_purchase_plan_goods 
SET in_transit_quantity = 999999.99,
    daily_avg_sales = 999999.99,
    stock_upper_limit = 999999.99,
    stock_lower_limit = 999999.99,
    recommend_quantity = 999999.99
WHERE id = 999999;

-- 测试负值（应该被允许，因为可能有特殊业务场景）
UPDATE jxc_purchase_plan_goods 
SET in_transit_quantity = -10.0
WHERE id = 999999;

-- 验证边界值设置
SELECT 
    id, goods_name, in_transit_quantity, daily_avg_sales,
    stock_upper_limit, stock_lower_limit, recommend_quantity
FROM jxc_purchase_plan_goods 
WHERE id = 999999;

-- =====================================================
-- 7. 清理测试数据
-- =====================================================

-- 清理测试数据
SELECT 'Cleaning up test data...' as test_phase;

DELETE FROM jxc_purchase_plan_goods WHERE plan_id IN (999999, 999998);
DELETE FROM jxc_purchase_plan WHERE id IN (999999, 999998);

-- =====================================================
-- 8. 测试结果汇总
-- =====================================================

-- 汇总测试结果
SELECT 'Migration test completed successfully!' as test_result,
       NOW() as completion_time;

-- 显示最终的表结构
SELECT 
    'Final table structure verification' as verification_phase;

-- 采购计划表结构
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    column_comment
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan'
ORDER BY ordinal_position;

-- 采购计划商品明细表结构
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    column_comment
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'jxc_purchase_plan_goods'
ORDER BY ordinal_position;
