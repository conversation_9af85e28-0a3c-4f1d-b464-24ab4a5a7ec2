package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dsj.inventory.bussiness.inventory.entity.GainsLossesGoods;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存损溢单商品明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface GainsLossesGoodsMapper extends BaseMapper<GainsLossesGoods> {

    /**
     * 获取损溢单商品明细列表
     * @param id
     * @return
     */
    List<GainsLossesGoodsVo> getGainsLossesGoodById(Long id);

}
