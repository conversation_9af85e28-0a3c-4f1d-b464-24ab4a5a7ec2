package com.dsj.inventory.bussiness.timeTask;

import com.dsj.inventory.bussiness.inventory.service.SalesActivityService;
import com.dsj.inventory.bussiness.inventory.service.SalesCouponService;
import com.dsj.inventory.bussiness.inventory.service.SalesOrderService;
import com.dsj.inventory.bussiness.inventory.service.WarningLogService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/4/1 6:45
 */

@Slf4j
@Component
public class XxlJobTaskClient {

    @Autowired
    private SalesActivityService salesActivityService;
    @Autowired
    private SalesOrderService salesOrderService;
    @Autowired
    private SalesCouponService salesCouponService;
    @Autowired
    private WarningLogService warningLogService;


    @XxlJob("salesActivityStateHandler")
    public void salesActivityStateHandler() {
        try {
            log.info("开始执行===销售活动状态变更===的定时任务，线程{}", Thread.currentThread().getName());
            salesActivityService.batchHandleSalesActivityState();
        }catch (Exception e){
            log.error("销售活动状态变更错误", e);
        }
    }

    @XxlJob("salesCouponStateHandler")
    public void salesCouponStateHandler() {
        try {
            log.info("开始执行===优惠券状态变更及发放===的定时任务，线程{}", Thread.currentThread().getName());
            salesCouponService.batchHandleSalesCouponState();
        }catch (Exception e){
            log.error("优惠券状态变更及发放错误", e);
        }
    }

    @XxlJob("salesOrderStateHandler")
    public void salesOrderStateHandler() {
        try {
            log.info("开始执行===销售挂单清理===的定时任务，线程{}", Thread.currentThread().getName());
            salesOrderService.clearSalesOrder();
        }catch (Exception e){
            log.error("销售挂单清理错误", e);
        }
    }

    @XxlJob("warningLogHandler")
    public void warningLogHandler() {
        try {
            log.info("开始执行===库存预警记录===的定时任务，线程{}", Thread.currentThread().getName());
            warningLogService.batchHandleWarningLog();
        }catch (Exception e){
            log.error("库存预警记录错误", e);
        }
    }


}
