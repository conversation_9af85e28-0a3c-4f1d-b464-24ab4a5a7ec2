package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 活动类别，1单品活动 2会员日活动 3整单活动
 * <AUTHOR>
 */

@Getter
public enum SalesActivityCategoryEnum {

    SINGLE(1, "单品活动"),
    MEMBER(2, "会员日活动"),
    WHOLE(3, "整单活动"),
    ;

    private Integer code;

    private String desc;

    SalesActivityCategoryEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (SalesActivityCategoryEnum a : SalesActivityCategoryEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
