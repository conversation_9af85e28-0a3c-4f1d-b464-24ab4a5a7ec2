package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 进销存优惠券信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_sales_coupon")
@ApiModel(value="SalesCoupon", description="进销存优惠券信息")
public class SalesCoupon extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织id")
    @TableField("organization_id")
    private Long organizationId;

    @ApiModelProperty(value = "模块分类，1 云药快 ；2 外部；3 在线医院；4 商城；5进销存")
    @TableField("module_type")
    private Integer moduleType;

    @ApiModelProperty(value = "优惠券类型 1代金券 2折扣券")
    @TableField("coupon_type")
    private Integer couponType;

    @ApiModelProperty(value = "优惠券模式 1发放 2领取")
    @TableField("coupon_mode")
    private Integer couponMode;

    @ApiModelProperty(value = "优惠券状态 0未过期 1已过期")
    @TableField("coupon_state")
    private Integer couponState;

    @ApiModelProperty(value = "优惠券名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "使用条件")
    @TableField("coupon_condition")
    private String couponCondition;

    @ApiModelProperty(value = "减免金额")
    @TableField("reduce_price")
    private Integer reducePrice;

    @ApiModelProperty(value = "折扣数")
    @TableField("percentage")
    private Double percentage;

    @ApiModelProperty(value = "生效天数（发放后多少天生效）")
    @TableField("assert_day")
    private Integer assertDay;

    @ApiModelProperty(value = "有效天数")
    @TableField("use_day")
    private Integer useDay;

    @ApiModelProperty(value = "优惠券开始时间")
    @TableField("start_date")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "优惠券结束时间")
    @TableField("end_date")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "优惠券领取开始时间")
    @TableField("get_start_date")
    private LocalDateTime getStartDate;

    @ApiModelProperty(value = "优惠券领取结束时间")
    @TableField("get_end_date")
    private LocalDateTime getEndDate;

    @ApiModelProperty(value = "每人发放/领取数量限制")
    @TableField("amount_limit")
    private Integer amountLimit;

    @ApiModelProperty(value = "领取总量")
    @TableField("get_amount_limit")
    private Integer getAmountLimit;

    @ApiModelProperty(value = "领取对象 1所有买家 2已合作买家")
    @TableField("get_group")
    private Integer getGroup;

    @ApiModelProperty(value = "适用人群，会员等级")
    @TableField("apply_to")
    private Integer applyTo;

    @ApiModelProperty(value = "是否适用全部商品 0否1是")
    @TableField("goods_limit")
    private Integer goodsLimit;

    @ApiModelProperty(value = "发放数量")
    @TableField("grant_amount")
    private Integer grantAmount;

    @ApiModelProperty(value = "使用数量")
    @TableField("use_amount")
    private Integer useAmount;

    @ApiModelProperty(value = "过期数量")
    @TableField("expire_amount")
    private Integer expireAmount;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
