package com.dsj.inventory.bussiness.inventory.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 盘点计划完成商品明细
 * <AUTHOR>
 * @date 2023/04/11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value="CompleteCheckGoodsParam", description="盘点计划完成商品明细参数")
public class CompleteCheckGoodsParam {

    @ApiModelProperty(value = "商品明细Id")
    private Long id;

    @ApiModelProperty(value = "库存数量")
    private Double inventoryAmount;

    @ApiModelProperty(value = "盘点数量")
    private Double checkAmount;

}
