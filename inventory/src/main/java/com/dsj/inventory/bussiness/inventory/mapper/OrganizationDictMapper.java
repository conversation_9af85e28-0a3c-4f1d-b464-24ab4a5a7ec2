package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.OrganizationDict;
import com.dsj.inventory.bussiness.inventory.param.QueryOrganizationDictParam;
import cn.hutool.core.util.StrUtil;

import java.util.List;

/**
 * 组织字典参数表 Mapper 接口
 */
public interface OrganizationDictMapper extends BaseMapper<OrganizationDict> {

    /**
     * 检查是否存在相同组织下同一类型、同一编码的字典项
     */
    default Integer countDuplicateDict(Long organizationId, String dictType, String dictCode, Long excludeId) {
        LambdaQueryWrapper<OrganizationDict> wrapper = new LambdaQueryWrapper<OrganizationDict>()
                .eq(OrganizationDict::getOrganizationId, organizationId)
                .eq(OrganizationDict::getDictType, dictType)
                .eq(OrganizationDict::getDictCode, dictCode)
                .ne(excludeId != null, OrganizationDict::getId, excludeId);
        return selectCount(wrapper);
    }

    /**
     * 检查是否有子节点
     */
    default Integer countChildren(Long parentId) {
        LambdaQueryWrapper<OrganizationDict> wrapper = new LambdaQueryWrapper<OrganizationDict>()
                .eq(OrganizationDict::getParentId, parentId);
        return selectCount(wrapper);
    }

    /**
     * 分页查询组织字典
     */
    default Page<OrganizationDict> selectPageByParam(Page<OrganizationDict> page, QueryOrganizationDictParam param) {
        LambdaQueryWrapper<OrganizationDict> wrapper = new LambdaQueryWrapper<OrganizationDict>()
                .eq(param.getOrganizationId() != null, OrganizationDict::getOrganizationId, param.getOrganizationId())
                .like(StrUtil.isNotBlank(param.getDictName()), OrganizationDict::getDictName, param.getDictName())
                .eq(StrUtil.isNotBlank(param.getDictCode()), OrganizationDict::getDictCode, param.getDictCode())
                .eq(StrUtil.isNotBlank(param.getDictType()), OrganizationDict::getDictType, param.getDictType())
                .eq(param.getParentId() != null, OrganizationDict::getParentId, param.getParentId())
                .orderByDesc(OrganizationDict::getCreateTime);
        return selectPage(page, wrapper);
    }

    /**
     * 根据组织ID和字典类型查询字典列表
     */
    default List<OrganizationDict> selectByOrgAndType(Long organizationId, String dictType) {
        LambdaQueryWrapper<OrganizationDict> wrapper = new LambdaQueryWrapper<OrganizationDict>()
                .eq(OrganizationDict::getOrganizationId, organizationId)
                .eq(OrganizationDict::getDictType, dictType)
                .orderByAsc(OrganizationDict::getId);
        return selectList(wrapper);
    }

    /**
     * 根据组织ID、字典类型和编码查询字典项
     */
    default OrganizationDict selectByOrgTypeAndCode(Long organizationId, String dictType, String dictCode) {
        LambdaQueryWrapper<OrganizationDict> wrapper = new LambdaQueryWrapper<OrganizationDict>()
                .eq(OrganizationDict::getOrganizationId, organizationId)
                .eq(OrganizationDict::getDictType, dictType)
                .eq(OrganizationDict::getDictCode, dictCode);
        return selectOne(wrapper);
    }
}