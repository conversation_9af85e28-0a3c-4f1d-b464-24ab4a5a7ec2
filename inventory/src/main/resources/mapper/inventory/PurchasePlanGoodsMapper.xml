<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.purchaseplan.mapper.PurchasePlanGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.purchaseplan.entity.PurchasePlanGoods">
    <result column="id" property="id" />
        <result column="plan_id" property="planId" />
        <result column="organization_medicine_id" property="organizationMedicineId" />
        <result column="goods_name" property="goodsName" />
        <result column="goods_code" property="goodsCode" />
        <result column="pack_unit" property="packUnit" />
        <result column="specification" property="specification" />
        <result column="drug_type" property="drugType" />
        <result column="manufacturer" property="manufacturer" />
        <result column="launch_permit_holder" property="launchPermitHolder" />
        <result column="price" property="price" />
        <result column="repertory" property="repertory" />
        <result column="purchase_price" property="purchasePrice" />
        <result column="purchase_number" property="purchaseNumber" />
        <result column="total_purchase_price" property="totalPurchasePrice" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        plan_id, organization_medicine_id, goods_name, goods_code, pack_unit, specification, drug_type, manufacturer, launch_permit_holder, price, repertory, purchase_price, purchase_number, total_purchase_price, create_time, create_user, update_time, update_user, is_delete
    </sql>

</mapper>
