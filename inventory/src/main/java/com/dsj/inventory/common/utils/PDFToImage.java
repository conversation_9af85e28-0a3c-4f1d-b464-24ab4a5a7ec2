package com.dsj.inventory.common.utils;

import com.pocky.transport.bussiness.file.entity.File;
import org.icepdf.core.pobjects.Document;
import org.icepdf.core.pobjects.Page;
import org.icepdf.core.util.GraphicsRenderingHints;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.image.RenderedImage;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * PDF转图片
 *
 * <AUTHOR>
 * @date 2022/4/28 13:08
 */
public class PDFToImage {

    public static void pdf2Pic(File pdf,File file) throws Exception {
        Document document = new Document();
        java.io.File pdfFile = pdf.getSystemFile();
        FileInputStream fileInputStream = new FileInputStream(pdfFile);
        document.setInputStream(fileInputStream,pdf.getUrl());
        //缩放比例
        float scale = 2.5f;
        //旋转角度
        float rotation = 0f;
        for (int i = 0; i < document.getNumberOfPages(); i++) {
            BufferedImage image = (BufferedImage)
                    document.getPageImage(i, GraphicsRenderingHints.SCREEN, Page.BOUNDARY_CROPBOX, rotation, scale);
            RenderedImage rendImage = image;
            try {
                ImageIO.write(rendImage, file.getExt(), file.getSystemFile());
            } catch (IOException e) {
                e.printStackTrace();
            }
            image.flush();
        }
        document.dispose();
        fileInputStream.close();
    }
}
