package com.dsj.inventory.common.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @ClassName : AsyncResponseVo
 * @Description : 异步响应返回vo
 * @Date : 2022-06-30 14:42
 */
@Getter
@Setter
@Builder
@SuppressWarnings("ALL")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class AsyncResponseVo<T> {

    public static final int SUCCESS_CODE = 1;
    public static final int FAIL_CODE = -1;

    @ApiModelProperty(value = "响应编码:0/200-请求处理成功")
    private int code;

    /**
     * 调用结果
     */
    @ApiModelProperty(value = "响应数据")
    private T data;

    /**
     * 结果消息，如果调用成功，消息通常为空T
     */
    @ApiModelProperty(value = "提示消息")
    private String msg = "ok";
}
