package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 销售单类型 1线下 2线上
 * <AUTHOR>
 */

@Getter
public enum SalesOrderTypeEnum {

    OFF_LINE(1, "线下"),
    ON_LINE(2, "线上"),
    ;

    private Integer code;

    private String desc;

    SalesOrderTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (SalesOrderTypeEnum a : SalesOrderTypeEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
