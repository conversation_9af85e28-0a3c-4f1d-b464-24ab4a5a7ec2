package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.Check;
import com.dsj.inventory.bussiness.inventory.param.QueryCheckParam;
import com.dsj.inventory.bussiness.inventory.vo.CheckDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.CheckExportVo;
import com.dsj.inventory.bussiness.inventory.vo.CheckVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存盘点计划 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface CheckMapper extends BaseMapper<Check> {


    /**
     * 获取盘点计划分页列表
     * @param page
     * @param param
     * @return
     */
    List<CheckVo> queryCheckPage(@Param("page") Page page, @Param("param") QueryCheckParam param);

    /**
     * 获取盘点计划详情
     * @param id
     * @return
     */
    CheckDetailVo getCheckById(Long id);

    /**
     * 导出盘点计划列表
     * @param param
     * @return
     */
    List<CheckExportVo> queryCheckList(@Param("param") QueryCheckParam param);

    /**
     * 删除组织的盘点计划
     * @param organizationId
     */
    void deleteCheckByOrganizationId(Long organizationId);

    /**
     * 统计组织当天盘点计划数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getCheckCount(@Param("organizationId") Long organizationId, @Param("day") String day);

}
