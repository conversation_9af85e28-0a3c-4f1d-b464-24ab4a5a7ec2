package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.DeliveryOrder;
import com.dsj.inventory.bussiness.inventory.param.QueryDeliveryOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateDeliveryOrderParam;
import com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存退货单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface DeliveryOrderService extends IService<DeliveryOrder> {

    /**
     * 新增、编辑 退货单
     * @param param
     * @return
     */
    DeliveryOrderDetailVo saveOrUpdateDeliveryOrder(SaveOrUpdateDeliveryOrderParam param);

    /**
     * 删除 退货单
     * @param id
     */
    void deleteDeliveryOrder(Long id);

    /**
     * 获取退货单分页列表
     * @param param
     * @return
     */
    Page<DeliveryOrderVo> queryDeliveryOrderPage(QueryDeliveryOrderParam param);

    /**
     * 获取退货单列表详情
     * @param id
     * @return
     */
    DeliveryOrderDetailVo getDeliveryOrderById(Long id);

    /**
     * 提交退货单
     * @param id
     */
    void submitDeliveryOrder(Long id);

    /**
     * 导出退货单列表
     * @param param
     * @return
     */
    List<DeliveryOrderExportVo> queryDeliveryOrderList(QueryDeliveryOrderParam param);

    /**
     * 删除组织的退货单
     * @param organizationId
     */
    void deleteDeliveryOrderByOrganizationId(Long organizationId);

    /**
     * 统计组织当天退货单数量
     * @param organizationId
     * @param day
     * @return
     */
    Integer getDeliveryOrderCount(@Param("organizationId") Long organizationId, @Param("day") String day);

}
