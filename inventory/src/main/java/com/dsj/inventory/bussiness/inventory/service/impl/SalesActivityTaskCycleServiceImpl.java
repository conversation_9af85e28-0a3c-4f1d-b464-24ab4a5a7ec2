package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityTaskCycle;
import com.dsj.inventory.bussiness.inventory.mapper.SalesActivityTaskCycleMapper;
import com.dsj.inventory.bussiness.inventory.service.SalesActivityTaskCycleService;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskCycleVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 销售活动任务周期信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
@Service
public class SalesActivityTaskCycleServiceImpl extends ServiceImpl<SalesActivityTaskCycleMapper, SalesActivityTaskCycle> implements SalesActivityTaskCycleService {

    @Override
    public List<SalesActivityTaskCycleVo> queryCycleListByActivityId(Long activityId, Long taskId, Integer executeState) {
        return this.baseMapper.queryCycleListByActivityId(activityId,taskId,executeState);
    }
}
