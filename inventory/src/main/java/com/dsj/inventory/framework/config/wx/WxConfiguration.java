package com.dsj.inventory.framework.config.wx;

import cn.binarywang.wx.miniapp.api.WxMaMsgService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.WxMaUserService;
import cn.binarywang.wx.miniapp.api.impl.WxMaMsgServiceImpl;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.api.impl.WxMaUserServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import me.chanjar.weixin.common.service.WxOAuth2Service;
import me.chanjar.weixin.open.api.WxOpenService;
import me.chanjar.weixin.open.api.impl.WxOpenInMemoryConfigStorage;
import me.chanjar.weixin.open.api.impl.WxOpenOAuth2ServiceImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Created by kellen on 2020/5/3. 微信小程序配置
 */
@Configuration
public class WxConfiguration {

    /**  微信公众号相关配置  */
    @Value("${pocky.wx.mp.appId}")
    private String mpAppId;

    @Value("${pocky.wx.mp.appSecret}")
    private String mpAppSecret;

    /**  微信小程序相关配置  */
    @Value("${pocky.wx.miniapp.appId}")
    private String miniappAppId;

    @Value("${pocky.wx.miniapp.appSecret}")
    private String miniappSecret;

//    /**  微信一体机相关配置  */
//    @Value("${pocky.wx.all-in-one.appId}")
//    private String allInOneAppId;
//
//    @Value("${pocky.wx.all-in-one.appSecret}")
//    private String allInOneAppSecret;

    /**  微信开放平台app相关配置  */
    @Value("${pocky.wx.open-app.appId}")
    private String openAppAppId;

    @Value("${pocky.wx.open-app.appSecret}")
    private String openAppAppSecret;

    /**  微信支付相关配置  */
//    @Value("${pocky.wx.pay.mch-id}")
//    private String payMchId;
//
//    @Value("${pocky.wx.pay.mch-key}")
//    private String payMchKey;
//
//    @Value("${pocky.wx.pay.notify-url}")
//    private String payNotifyUrl;
//
//    @Value("${pocky.wx.pay.refund-notify-url}")
//    private String payRefundNotifyUrl;
//
//    @Value("${pocky.wx.pay.api-V3-key}")
//    private String payApiV3Key;
//
//    @Value("${pocky.wx.pay.private-key-path}")
//    private String payPrivateKeyPath;
//
//    @Value("${pocky.wx.pay.private-cert-path}")
//    private String payPrivateCertPath;


    /** 小程序的service 开始*/
    @Bean
    public WxMaService wxMaService() {
        WxMaService wxMaService = new WxMaServiceImpl();
        WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
        config.setAppid(miniappAppId);
        config.setSecret(miniappSecret);
        wxMaService.setWxMaConfig(config);
        return wxMaService;
    }

    @Bean
    public WxMaMsgService wxMaMsgService() {
        WxMaMsgService wxMaMsgService = new WxMaMsgServiceImpl(wxMaService());
        return wxMaMsgService;
    }

    @Bean
    public WxMaUserService waUserService() {
        WxMaUserService WxMaUserService = new WxMaUserServiceImpl(wxMaService());
        return WxMaUserService;
    }

//    @Bean
//    public WxPayConfig wxMaPayConfig() {
//        WxPayConfig payConfig = new WxPayConfig();
//        payConfig.setAppId(miniappAppId);
//        payConfig.setMchId(payMchId);
//        payConfig.setNotifyUrl(payNotifyUrl);
//        payConfig.setTradeType("JSAPI");
//        payConfig.setApiV3Key(payApiV3Key);
//        payConfig.setPrivateKeyPath(payPrivateKeyPath);
//        payConfig.setPrivateCertPath(payPrivateCertPath);
//        return payConfig;
//    }

//    @Bean
//    @Primary
//    public WxPayService wxMaPayService(WxPayConfig wxMaPayConfig) {
//        WxPayService wxPayService = new WxPayServiceImpl();
//        wxPayService.setConfig(wxMaPayConfig);
//        return wxPayService;
//    }
    /** 小程序的service 结束*/


    /** 微信 网站一体机 service 开始*/
//    @Bean
//    public WxOAuth2Service wxAllInOneService() {
//        WxOpenService wxOAuth2Service = new WxOpenOAuth2ServiceImpl(allInOneAppId,allInOneAppSecret);
//        wxOAuth2Service.setWxOpenConfigStorage(new WxOpenInMemoryConfigStorage());
//        return (WxOAuth2Service)wxOAuth2Service;
//    }
//    @Bean
//    public WxPayConfig wxAllInOnePayConfig() {
//        WxPayConfig payConfig = new WxPayConfig();
//        payConfig.setAppId(mpAppId);
//        payConfig.setMchId(payMchId);
//        payConfig.setNotifyUrl(payNotifyUrl);
//        payConfig.setTradeType("NATIVE");
//        payConfig.setApiV3Key(payApiV3Key);
//        payConfig.setPrivateKeyPath(payPrivateKeyPath);
//        payConfig.setPrivateCertPath(payPrivateCertPath);
//        return payConfig;
//    }

//    @Bean()
//    public WxPayService wxAllInOnePayService(WxPayConfig wxAllInOnePayConfig) {
//        WxPayService wxPayService = new WxPayServiceImpl();
//        wxPayService.setConfig(wxAllInOnePayConfig);
//        return wxPayService;
//    }
    /** 微信 网站一体机 service 结束*/

    /** 微信 公众平台app service 开始*/
    @Bean
    public WxOAuth2Service wxOpenAppService() {
        WxOpenService wxOAuth2Service = new WxOpenOAuth2ServiceImpl(openAppAppId,openAppAppSecret);
        wxOAuth2Service.setWxOpenConfigStorage(new WxOpenInMemoryConfigStorage());
        return (WxOAuth2Service)wxOAuth2Service;
    }
    /** 微信 公众平台app service 开始*/
}
