package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 销售活动任务关联
 * <AUTHOR>
 * @date 2023/04/07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SalesActivityTaskMappingVo", description="销售活动任务关联实体")
@ToString(callSuper = true)
public class SalesActivityTaskMappingVo {

    private Long id;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "活动Id")
    private Integer activityId;

    @ApiModelProperty(value = "活动类别，1单品活动 2会员日活动 3整单活动")
    private Integer activityCategory;

    @ApiModelProperty(value = "活动类型 1逢倍折扣 2满件减 3单品特价 4满元减元")
    private Integer activityType;

    @ApiModelProperty(value = "活动状态 0未开始 1进行中 2已暂停 3已结束")
    private Integer activityState;

    @ApiModelProperty(value = "生效状态 0未生效 1生效中")
    private Integer assertState;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "活动开始时间")
    private LocalDateTime startDate;

    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime endDate;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}

