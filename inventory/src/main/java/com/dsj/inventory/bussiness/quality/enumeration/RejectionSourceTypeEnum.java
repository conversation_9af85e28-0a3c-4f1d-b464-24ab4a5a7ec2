package com.dsj.inventory.bussiness.quality.enumeration;

import lombok.Getter;

/**
 * 拒收来源单据类型枚举
 */
@Getter
public enum RejectionSourceTypeEnum {
    RECEIVE(1, "收货单"),
    ACCEPTANCE(2, "验收单"),
    INCOMING(3, "入库单");

    private final Integer code;
    private final String desc;

    RejectionSourceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RejectionSourceTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RejectionSourceTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 