<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dsj.inventory.bussiness.inventory.mapper.SalesGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dsj.inventory.bussiness.inventory.entity.SalesGoods">
    <result column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="organization_medicine_id" property="organizationMedicineId" />
        <result column="goods_name" property="goodsName" />
        <result column="goods_code" property="goodsCode" />
        <result column="pack_unit" property="packUnit" />
        <result column="specification" property="specification" />
        <result column="drug_type" property="drugType" />
        <result column="manufacturer" property="manufacturer" />
        <result column="price" property="price" />
        <result column="member_price" property="memberPrice" />
        <result column="batch_number" property="batchNumber" />
        <result column="produce_date" property="produceDate" />
        <result column="user_date" property="userDate" />
        <result column="shelf_id" property="shelfId" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        order_id, organization_medicine_id, goods_name, goods_code, pack_unit, specification, drug_type, manufacturer, price, member_price, batch_number, produce_date, user_date, shelf_id, create_time, create_user, update_time, update_user, is_delete
    </sql>

    <select id="getSalesGoodsListByOrderId" resultType="com.dsj.inventory.bussiness.inventory.vo.SalesGoodsVo">
        SELECT distinct jsg.id
            ,jsg.order_id orderId
            ,jsg.sale_id saleId
            ,su.name saleName
            ,jsg.organization_medicine_id organizationMedicineId
            ,jsg.goods_name goodsName
            ,jsg.general_name generalName
            ,jsg.goods_code goodsCode
            ,jsg.pack_unit packUnit
            ,jsg.specification
            ,jsg.drug_type drugType
            ,jsg.manufacturer
            ,jsg.usage_dosage usageDosage
            ,jsg.bar_code barCode
            ,jsg.approval_number approvalNumber
            ,jsg.produce_date produceDate
            ,jsg.user_date userDate
            ,jsg.batch_number batchNumber
            ,jsg.shelf_id shelfId
            ,js.name shelfName
            ,jsg.sale_amount saleAmount
            ,jsg.give_amount giveAmount
            ,jsg.price
            ,jsg.discount_price discountPrice
            ,jsg.total_price totalPrice
            ,jsg.sale_price salePrice
            ,jsg.member_price memberPrice
            ,SUM(ji.inventory_amount) inventoryAmount
            ,(SELECT hommf.file_path FROM hos_organization_medicine_mapping_file hommf WHERE hommf.mapping_id = jsg.organization_medicine_id AND hommf.is_delete = 0 ORDER BY hommf.create_time LIMIT 1 ) goodsFilePath
        FROM jxc_sales_goods jsg
        LEFT JOIN jxc_shelf js ON js.id = jsg.shelf_id AND js.is_delete = 0
        LEFT JOIN jxc_inventory ji ON ji.organization_medicine_id = jsg.organization_medicine_id
            AND ji.batch_number = jsg.batch_number AND ji.shelf_id = jsg.shelf_id AND ji.is_delete = 0
        LEFT JOIN sys_user su ON su.id = jsg.sale_id
        WHERE
            jsg.is_delete = 0
        AND jsg.order_id = #{orderId}
        GROUP BY jsg.id
    </select>

</mapper>
