package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.GainsLossesGoods;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesGoodsVo;

import java.util.List;

/**
 * <p>
 * 进销存损溢单商品明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface GainsLossesGoodsService extends IService<GainsLossesGoods> {

    /**
     * 获取损溢单商品明细列表
     * @param id
     * @return
     */
    List<GainsLossesGoodsVo> getGainsLossesGoodById(Long id);

}
