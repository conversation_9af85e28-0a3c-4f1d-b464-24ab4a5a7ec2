package com.dsj.inventory.bussiness.inventory.param;

import com.dsj.inventory.common.entity.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 查询优惠券
 * <AUTHOR>
 * @date 2023/04/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
@EqualsAndHashCode
@ApiModel(value = "QuerySalesCouponParam",description = "查询优惠券参数")
public class QuerySalesCouponParam extends BasePage {

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "模块分类，1 云药快 ；2 外部；3 在线医院；4 商城；5进销存")
    private Integer moduleType;

    @ApiModelProperty(value = "优惠券类型 1代金券 2折扣券")
    private Integer couponType;

    @ApiModelProperty(value = "优惠券模式 1发放 2领取")
    private Integer couponMode;

    @ApiModelProperty(value = "优惠券状态 0未过期 1已过期")
    private Integer couponState;

    @ApiModelProperty(value = "是否筛选可领取，默认不筛选")
    private Boolean getScreen = false;

    @ApiModelProperty(value = "领取对象 1所有买家 2已合作买家")
    private Integer getGroup;

    @ApiModelProperty(value = "活动名称模糊搜索")
    private String nameLike;

    @ApiModelProperty(value = "创建人名称模糊搜索")
    private String createUserNameLike;

    @ApiModelProperty(value = "创建时间，查询条件--起始时间")
    private String createStart;

    @ApiModelProperty(value = "创建时间，查询条件--结束时间")
    private String createEnd;

    @ApiModelProperty(value = "优惠券领取时间，查询条件--起始时间")
    private String getDateStart;

    @ApiModelProperty(value = "优惠券领取时间，查询条件--结束时间")
    private String getDateEnd;

    @ApiModelProperty(value = "商品goodsIds")
    private String goodsIds;

}
