package com.dsj.inventory.bussiness.inventory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 盘点计划
 * <AUTHOR>
 * @date 2023/04/06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="CheckVo", description="盘点计划实体")
@ToString(callSuper = true)
public class CheckVo {

    private Long id;

    @ApiModelProperty(value = "组织Id")
    private Long organizationId;

    @ApiModelProperty(value = "盘点单号")
    private String code;

    @ApiModelProperty(value = "盘点计划名称")
    private String name;

    @ApiModelProperty(value = "盘点状态：0暂存 1盘点中 2已完成")
    private Integer checkState;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "盘点货位Id，多个用逗号分割，值为1时是全部")
    private String shelfIds;

    @ApiModelProperty(value = "盘点货位名称")
    private String shelfNames;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "盘点人Id，多个用逗号分割")
    private String checkIds;

    @ApiModelProperty(value = "盘点人名称")
    private String checkNames;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "盘点时间")
    private LocalDateTime checkDate;

    @ApiModelProperty(hidden = true,value = "删除标志,0：未删除  1：已删除")
    private Integer isDelete;
}
