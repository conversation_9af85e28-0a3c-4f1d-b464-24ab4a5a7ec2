package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.*;
import com.dsj.inventory.bussiness.inventory.enumeration.*;
import com.dsj.inventory.bussiness.inventory.mapper.GainsLossesOrderMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryGainsLossesOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateGainsLossesGoodsParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateGainsLossesOrderParam;
import com.dsj.inventory.bussiness.inventory.service.*;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesGoodsVo;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.GainsLossesOrderVo;
import com.dsj.inventory.common.constant.BaseConstant;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.common.utils.DozerUtils;
import com.dsj.inventory.common.utils.TencentIMUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.entity.UserROrganization;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.diagnose.service.UserROrganizationService;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import com.pocky.transport.bussiness.hospital.vo.OrganizationMedicineMappingVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 进销存损溢单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Slf4j
@Service
public class GainsLossesOrderServiceImpl extends ServiceImpl<GainsLossesOrderMapper, GainsLossesOrder> implements GainsLossesOrderService {

    @Autowired
    private DozerUtils dozerUtils;
    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @Autowired
    private GainsLossesGoodsService gainsLossesGoodsService;
    @Autowired
    private InventoryService inventoryService;
    @Autowired
    private InventoryChangeLogService inventoryChangeLogService;
    @Autowired
    private WarningLogService warningLogService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private TencentIMUtils tencentIMUtils;
    @Autowired
    private UserROrganizationService userROrganizationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GainsLossesOrderDetailVo saveOrUpdateGainsLossesOrder(SaveOrUpdateGainsLossesOrderParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        Organization organization = organizationService.getById(param.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        if(CommonUtils.isNotEmpty(param.getId())) {
            GainsLossesOrder byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            //删除商品明细
            gainsLossesGoodsService.lambdaUpdate().eq(GainsLossesGoods::getOrderId,param.getId())
                    .set(GainsLossesGoods::getIsDelete, TrueEnum.TRUE.getCode()).update(new GainsLossesGoods());
        }else {
            //生成损溢单号：SY+年后两位+月日四位+药房识别码六位+三位
            String day = DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_FORMAT);
            String dayStr = day.replaceAll("-", "").substring(2,8);
            Integer count = this.getGainsLossesOrderCount(organization.getId(),day);
            count = count + 1;
            param.setCode("SY" + dayStr + organization.getSerialNo() + String.format("%03d",count));
        }

        Set<String> goodsNameSet = new HashSet<>();
        BigDecimal totalAmount = BigDecimal.valueOf(0.00);
        BigDecimal costTotalPrice = BigDecimal.valueOf(0);
        BigDecimal averageTotalCost = BigDecimal.valueOf(0);
        BigDecimal totalPrice = BigDecimal.valueOf(0);
        if(CommonUtils.isNotEmpty(param.getGoodsList())){
            List<Long> goodsIdList = param.getGoodsList().stream().map(g -> g.getInventoryId()).distinct().collect(Collectors.toList());
            if(goodsIdList.size() != param.getGoodsList().size()){
                throw new BizException("操作失败，存在重复库存");
            }
            for(SaveOrUpdateGainsLossesGoodsParam goodsParam : param.getGoodsList()){
                OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(goodsParam.getOrganizationMedicineId());
                if(CommonUtils.isEmpty(organizationMedicine)){
                    throw new BizException("操作失败，商品Id"+goodsParam.getOrganizationMedicineId()+"不存在");
                }
                Inventory inventory = inventoryService.getById(goodsParam.getInventoryId());
                if(CommonUtils.isEmpty(inventory)){
                    throw new BizException("操作失败，"+organizationMedicine.getName()+"库存不存在");
                }
                goodsParam.setGoodsName(organizationMedicine.getName());
                goodsParam.setGeneralName(organizationMedicine.getGeneralName());
                goodsParam.setPackUnit(organizationMedicine.getPackUnit());
                goodsParam.setSpecification(organizationMedicine.getSpecification());
                goodsParam.setDrugType(organizationMedicine.getDrugType());
                goodsParam.setManufacturer(organizationMedicine.getManufacturer());
                goodsParam.setApprovalNumber(organizationMedicine.getApprovalNumber());
                goodsParam.setInventoryAmount(inventory.getInventoryAmount());
                goodsParam.setBatchNumber(inventory.getBatchNumber());
                goodsParam.setShelfId(inventory.getShelfId());
                goodsParam.setBatch(inventory.getBatchNumber());
                goodsParam.setCostPrice(inventory.getCostPrice());
                BigDecimal goodsCostTotalPrice = BigDecimal.valueOf(goodsParam.getPlugAmount()).multiply(BigDecimal.valueOf(inventory.getCostPrice()));
                goodsParam.setCostTotalPrice(goodsCostTotalPrice.intValue());
                goodsParam.setAverageCost(organizationMedicine.getAverageCost());
                BigDecimal goodsAverageTotalCost = BigDecimal.valueOf(goodsParam.getPlugAmount()).multiply(BigDecimal.valueOf(organizationMedicine.getAverageCost()));
                goodsParam.setAverageTotalCost(goodsAverageTotalCost.intValue());
                BigDecimal price = BigDecimal.valueOf(goodsParam.getPlugAmount()).multiply(BigDecimal.valueOf(organizationMedicine.getOfflinePrice()));
                totalPrice = totalPrice.add(price);
                totalAmount = totalAmount.add(BigDecimal.valueOf(goodsParam.getPlugAmount()));
                costTotalPrice = costTotalPrice.add(goodsCostTotalPrice);
                averageTotalCost = averageTotalCost.add(goodsAverageTotalCost);
                goodsNameSet.add(organizationMedicine.getName());
            }
            String goodsNames = String.join(",",goodsNameSet);
            param.setGoodsNames(goodsNames);
            param.setTotalPrice(totalPrice.intValue());
            param.setTotalAmount(totalAmount.doubleValue());
            param.setCostTotalPrice(costTotalPrice.intValue());
            param.setAverageTotalCost(averageTotalCost.intValue());
        }
        GainsLossesOrder gainsLossesOrder = dozerUtils.map(param,GainsLossesOrder.class);
        this.saveOrUpdate(gainsLossesOrder);
        if(CommonUtils.isNotEmpty(param.getGoodsList())){
            param.getGoodsList().stream().map(g -> g.setOrderId(gainsLossesOrder.getId())).collect(Collectors.toList());
            List<GainsLossesGoods> goodsList = dozerUtils.mapList(param.getGoodsList(),GainsLossesGoods.class);
            gainsLossesGoodsService.saveBatch(goodsList);
        }
        if(GainsLossesOrderStateEnum.COMPLETED.getCode().equals(param.getDealState())){
            this.submitGainsLossesOrder(gainsLossesOrder.getId());
        }
        return this.getGainsLossesOrderById(gainsLossesOrder.getId());
    }

    @Override
    public void deleteGainsLossesOrder(Long id) {
        GainsLossesOrder byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        if(!byId.getDealState().equals(GainsLossesOrderStateEnum.TEMPORARY.getCode())){
            throw new BizException("操作失败，当前状态不可删除");
        }
        //删除商品明细
        gainsLossesGoodsService.lambdaUpdate().eq(GainsLossesGoods::getOrderId,id)
                .set(GainsLossesGoods::getIsDelete, TrueEnum.TRUE.getCode()).update(new GainsLossesGoods());
        this.baseMapper.deleteById(id);
    }

    @Override
    public Page<GainsLossesOrderVo> queryGainsLossesOrderPage(QueryGainsLossesOrderParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<GainsLossesOrderVo> list = this.baseMapper.queryGainsLossesOrderPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public GainsLossesOrderDetailVo getGainsLossesOrderById(Long id) {
        GainsLossesOrderDetailVo vo = this.baseMapper.getGainsLossesOrderById(id);
        if(CommonUtils.isNotEmpty(vo)){
            List<GainsLossesGoodsVo> goodsList = gainsLossesGoodsService.getGainsLossesGoodById(vo.getId());
            if(CommonUtils.isNotEmpty(goodsList)){
                vo.setGoodsList(goodsList);
            }
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitGainsLossesOrder(Long id) {
        GainsLossesOrderDetailVo gainsLossesOrderDetailVo = this.getGainsLossesOrderById(id);
        if(CommonUtils.isEmpty(gainsLossesOrderDetailVo)){
            throw new BizException("操作失败，损溢单不存在");
        }
        Organization organization = organizationService.getById(gainsLossesOrderDetailVo.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        this.lambdaUpdate().eq(GainsLossesOrder::getId,id).eq(GainsLossesOrder::getDealState, GainsLossesOrderStateEnum.TEMPORARY.getCode())
                .set(GainsLossesOrder::getDealState, GainsLossesOrderStateEnum.COMPLETED.getCode()).update(new GainsLossesOrder());
        //是否发送缺货预警消息
        boolean lackFlag = false;
        //是否发送积货预警消息
        boolean exceedFlag = false;
        //更改库存
        Integer type = gainsLossesOrderDetailVo.getType();
        for(GainsLossesGoodsVo goods : gainsLossesOrderDetailVo.getGoodsList()){
            Integer changeType = 0;
            OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(goods.getOrganizationMedicineId());
            BigDecimal medicineRepertory = BigDecimal.valueOf(organizationMedicine.getMedicineRepertory());
            Inventory inventory = inventoryService.getById(goods.getInventoryId());
            BigDecimal inventoryAmount = BigDecimal.valueOf(inventory.getInventoryAmount());
            //缺货
            WarningLog lowerWarningLog = warningLogService.lambdaQuery()
                    .eq(WarningLog::getType, WarningTypeEnum.LACK.getCode())
                    .eq(WarningLog::getOrganizationId,organizationMedicine.getOrganizationId())
                    .eq(WarningLog::getOrganizationMedicineId,organizationMedicine.getId()).one();
            //库存下限
            Integer lowerLimit = organizationMedicine.getRepertoryLowerLimit();
            //积货
            WarningLog upperWarningLog = warningLogService.lambdaQuery()
                    .eq(WarningLog::getType, WarningTypeEnum.EXCEED.getCode())
                    .eq(WarningLog::getOrganizationId,organizationMedicine.getOrganizationId())
                    .eq(WarningLog::getOrganizationMedicineId,organizationMedicine.getId()).one();
            //库存上限
            Integer upperLimit = organizationMedicine.getRepertoryUpperLimit();
            if(GainsLossesOrderTypeEnum.Gains.getCode().equals(type)){
                //加库存
                changeType = InventoryChangeTypeEnum.INCREASE.getCode();
                medicineRepertory = medicineRepertory.add(BigDecimal.valueOf(goods.getPlugAmount()));
                inventoryAmount = inventoryAmount.add(BigDecimal.valueOf(goods.getPlugAmount()));
                //处理库存预警
                if(CommonUtils.isNotEmpty(lowerLimit) && medicineRepertory.doubleValue() > lowerLimit && CommonUtils.isNotEmpty(lowerWarningLog)){
                    //删除预警记录
                    warningLogService.lambdaUpdate().eq(WarningLog::getId,lowerWarningLog.getId())
                            .set(WarningLog::getIsDelete,TrueEnum.TRUE.getCode()).update(new WarningLog());
                }
                if(CommonUtils.isNotEmpty(upperLimit) && medicineRepertory.doubleValue() > upperLimit && CommonUtils.isEmpty(upperWarningLog)){
                    //新增积货预警
                    WarningLog warningLog = WarningLog.builder().organizationId(organizationMedicine.getOrganizationId())
                            .organizationMedicineId(organizationMedicine.getId())
                            .type(WarningTypeEnum.EXCEED.getCode()).build();
                    warningLogService.save(warningLog);
                    exceedFlag = true;
                }
            }else if(GainsLossesOrderTypeEnum.Losses.getCode().equals(type)){
                //减库存
                changeType = InventoryChangeTypeEnum.DECREASE.getCode();
                if(organizationMedicine.getMedicineRepertory() < goods.getPlugAmount()){
                    throw new BizException("操作失败，"+goods.getGoodsName() +"商品库存数量小于报损数量");
                }
                medicineRepertory = medicineRepertory.subtract(BigDecimal.valueOf(goods.getPlugAmount()));
                if(inventory.getInventoryAmount() < goods.getPlugAmount()){
                    throw new BizException("操作失败，"+goods.getGoodsName() +"库存数量小于报损数量");
                }
                inventoryAmount = inventoryAmount.subtract(BigDecimal.valueOf(goods.getPlugAmount()));
                //处理库存预警
                if(CommonUtils.isNotEmpty(lowerLimit) && medicineRepertory.doubleValue() < lowerLimit && CommonUtils.isEmpty(lowerWarningLog)){
                    //新增缺货预警
                    WarningLog warningLog = WarningLog.builder().organizationId(organizationMedicine.getOrganizationId())
                            .organizationMedicineId(organizationMedicine.getId())
                            .type(WarningTypeEnum.LACK.getCode()).build();
                    warningLogService.save(warningLog);
                    lackFlag = true;
                }
                if(CommonUtils.isNotEmpty(upperLimit) && medicineRepertory.doubleValue() < upperLimit && CommonUtils.isNotEmpty(upperWarningLog)){
                    //删除预警记录
                    warningLogService.lambdaUpdate().eq(WarningLog::getId,upperWarningLog.getId())
                            .set(WarningLog::getIsDelete,TrueEnum.TRUE.getCode()).update(new WarningLog());
                }
            }
            //库存变动记录
            InventoryChangeLog inventoryChangeLog = InventoryChangeLog.builder().organizationId(gainsLossesOrderDetailVo.getOrganizationId())
                    .organizationMedicineId(goods.getOrganizationMedicineId()).inventoryId(gainsLossesOrderDetailVo.getId())
                    .orderId(gainsLossesOrderDetailVo.getId()).orderType(InventoryOrderTypeEnum.GAINS_LOSSES.getCode())
                    .changeType(changeType).changeAmount(goods.getPlugAmount()).batchNumber(goods.getBatchNumber())
                    .shelfId(goods.getShelfId()).batch(goods.getBatch()).costPrice(goods.getCostPrice())
                    .executeId(BaseContextHandler.getUserId()).build();
            inventoryChangeLogService.save(inventoryChangeLog);
            inventoryService.lambdaUpdate().eq(Inventory::getId,goods.getInventoryId())
                    .set(Inventory::getInventoryAmount,inventoryAmount.doubleValue()).update(new Inventory());
            organizationMedicineMappingService.lambdaUpdate().eq(OrganizationMedicineMapping::getId,goods.getOrganizationMedicineId())
                    .set(OrganizationMedicineMapping::getMedicineRepertory,medicineRepertory.doubleValue())
                    .update(new OrganizationMedicineMapping());
        }
        //发送预警消息
        if(exceedFlag){
            log.info("============向商家发送积货预警消息============");
            List<UserROrganization> uroList = userROrganizationService.lambdaQuery()
                    .eq(UserROrganization::getOrganizationId,gainsLossesOrderDetailVo.getOrganizationId()).list();
            if (CommonUtils.isNotEmpty(uroList)){
                List<String> userIdList = uroList.stream().map(s -> s.getUserId().toString()).collect(Collectors.toList());
                tencentIMUtils.adminSendBatchSingleMsg(userIdList, BaseConstant.IM_DATE_INVENTORY_WARNING,BaseConstant.IM_DESC_INVENTORY_EXCEED,"");
            }
        }
        if(lackFlag){
            log.info("============向商家发送缺货预警消息============");
            List<UserROrganization> uroList = userROrganizationService.lambdaQuery()
                    .eq(UserROrganization::getOrganizationId,gainsLossesOrderDetailVo.getOrganizationId()).list();
            if (CommonUtils.isNotEmpty(uroList)){
                List<String> userIdList = uroList.stream().map(s -> s.getUserId().toString()).collect(Collectors.toList());
                tencentIMUtils.adminSendBatchSingleMsg(userIdList, BaseConstant.IM_DATE_INVENTORY_WARNING,BaseConstant.IM_DESC_INVENTORY_LACK,"");
            }
        }
    }

    @Override
    public List<GainsLossesOrderExportVo> queryGainsLossesOrderList(QueryGainsLossesOrderParam param) {
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        return this.baseMapper.queryGainsLossesOrderList(param);
    }

    @Override
    public void deleteGainsLossesOrderByOrganizationId(Long organizationId) {
        this.baseMapper.deleteGainsLossesOrderByOrganizationId(organizationId);
    }

    @Override
    public Integer getGainsLossesOrderCount(Long organizationId, String day) {
        return this.baseMapper.getGainsLossesOrderCount(organizationId, day);
    }

}
