package com.dsj.inventory.framework.properties;


import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * 腾讯短信平台配置
 */
@Setter
@Getter
@ConfigurationProperties(prefix = "pocky.ms")
@Configuration
public class TencentMSProperties {
    /**
     * 腾讯短信配置
     */
    private String secretId = "12345678";

    private String secretKey = "12345678";

    private String sdkAppId = "12345678";

    private String signName = "12345678";

    private String templateId = "12345678";


}
