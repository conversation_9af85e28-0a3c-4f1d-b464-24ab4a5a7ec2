package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 进销存损溢单商品明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_gains_losses_goods")
@ApiModel(value="GainsLossesGoods", description="进销存损溢单商品明细")
public class GainsLossesGoods extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "损溢单Id")
    @TableField("order_id")
    private Long orderId;

    @ApiModelProperty(value = "私域商品Id")
    @TableField("organization_medicine_id")
    private Long organizationMedicineId;

    @ApiModelProperty(value = "库存Id")
    @TableField("inventory_id")
    private Long inventoryId;

    @ApiModelProperty(value = "通用名")
    @TableField("goods_name")
    private String goodsName;

    @ApiModelProperty(value = "商品名称")
    @TableField("general_name")
    private String generalName;

    @ApiModelProperty(value = "包装单位")
    @TableField("pack_unit")
    private String packUnit;

    @ApiModelProperty(value = "规格型号")
    @TableField("specification")
    private String specification;

    @ApiModelProperty(value = "剂型")
    @TableField("drug_type")
    private String drugType;

    @ApiModelProperty(value = "生产厂家")
    @TableField("manufacturer")
    private String manufacturer;

    @ApiModelProperty(value = "批准文号")
    @TableField("approval_number")
    private String approvalNumber;

    @ApiModelProperty(value = "批号")
    @TableField("batch_number")
    private String batchNumber;

    @ApiModelProperty(value = "货位Id")
    @TableField("shelf_id")
    private Long shelfId;

    @ApiModelProperty(value = "批次（入库单号）")
    @TableField("batch")
    private String batch;

    @ApiModelProperty(value = "库存数量")
    @TableField("inventory_amount")
    private Double inventoryAmount;

    @ApiModelProperty(value = "实际数")
    @TableField("real_amount")
    private Double realAmount;

    @ApiModelProperty(value = "损益数")
    @TableField("plug_amount")
    private Double plugAmount;

    @ApiModelProperty(value = "成本均价")
    @TableField("cost_price")
    private Integer costPrice;

    @ApiModelProperty(value = "损溢金额")
    @TableField("cost_total_price")
    private Integer costTotalPrice;

    @ApiModelProperty(value = "移动加权平均成本价")
    @TableField("average_cost")
    private Integer averageCost;

    @ApiModelProperty(value = "移动加权平均成本价总额")
    @TableField("average_total_cost")
    private Integer averageTotalCost;

    @ApiModelProperty(value = "损溢原因")
    @TableField("reason")
    private String reason;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
