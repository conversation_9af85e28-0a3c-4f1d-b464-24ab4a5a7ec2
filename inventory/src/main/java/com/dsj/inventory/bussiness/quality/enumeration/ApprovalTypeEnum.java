package com.dsj.inventory.bussiness.quality.enumeration;

import lombok.Getter;

/**
 * 审批类型枚举
 */
@Getter
public enum ApprovalTypeEnum {

    FIRST_BUSINESS(1, "首营审批"),
    MODIFICATION(2, "修改审批");

    private final Integer code;
    private final String desc;

    ApprovalTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        if (code == null) {
            return "";
        }
        for (ApprovalTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return "";
    }
} 