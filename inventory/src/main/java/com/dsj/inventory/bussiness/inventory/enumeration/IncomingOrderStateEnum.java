package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 入库单处理状态 0暂存 1已完成
 * <AUTHOR>
 */

@Getter
public enum IncomingOrderStateEnum {

    TEMPORARY(0, "暂存"),
    COMPLETED(1, "已完成"),
    RETURNED(2, "已退货"),
    ;

    private Integer code;

    private String desc;

    IncomingOrderStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (IncomingOrderStateEnum a : IncomingOrderStateEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
