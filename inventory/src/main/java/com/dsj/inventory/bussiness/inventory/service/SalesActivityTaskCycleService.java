package com.dsj.inventory.bussiness.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivityTaskCycle;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityTaskCycleVo;

import java.util.List;

/**
 * <p>
 * 销售活动任务周期信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-04
 */
public interface SalesActivityTaskCycleService extends IService<SalesActivityTaskCycle> {

    /**
     * 获取任务周期
     * @param activityId
     * @param taskId
     * @param executeState
     * @return
     */
    List<SalesActivityTaskCycleVo> queryCycleListByActivityId(Long activityId, Long taskId, Integer executeState);

}
