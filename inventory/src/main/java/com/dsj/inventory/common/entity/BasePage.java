package com.dsj.inventory.common.entity;

import com.dsj.inventory.common.constant.BaseConstant;
import com.dsj.inventory.common.utils.CommonUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version v1.0
 * @ProjectName: transport-server
 * @ClassName: BasePage
 * @Description:
 * @Author: ch
 * @Date: 2021-04-26 14:32
 */
@Data
@ApiModel(value="BasePage", description="基础分页实体")
public class BasePage {

    @ApiModelProperty(value = "页码")
    private Integer page;

    @ApiModelProperty(value = "每页数据条数")
    private Integer size;

    @ApiModelProperty(hidden = true,value = "是否需要导出")
    private Boolean needExport = false;

    public Integer getPage() {
        if (CommonUtils.isEmpty(page)){
            return 0;
        }else {
            return page;
        }
    }

    public Integer getSize() {
        if (CommonUtils.isNotEmpty(needExport) && needExport == true){
            if (CommonUtils.isEmpty(size)){
                return Integer.MAX_VALUE;
            }else {
                return size;
            }
        }else {
            if (CommonUtils.isEmpty(size) || size > BaseConstant.PAGE_SIZE_MAX){
                return BaseConstant.PAGE_SIZE_MAX;
            }else {
                return size;
            }
        }
    }
}
