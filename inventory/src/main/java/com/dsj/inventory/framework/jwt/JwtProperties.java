package com.dsj.inventory.framework.jwt;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import static com.dsj.inventory.framework.jwt.JwtProperties.PREFIX;

/**
 * 认证服务端,此处记得看配置文件中的结果
 *
 * <AUTHOR>
 * @date 2018/11/20
 */
@Data
@NoArgsConstructor
@ConfigurationProperties(prefix = PREFIX)
@Component
public class JwtProperties {
    public static final String PREFIX = "authentication";

    /**
     * 过期时间 30天
     */
    private Long expire = 60*60*24*30L;

    /**
     * 管理员过期时间 30天
     */
    private Long adminExpire = 60*60*24*30L;
    /**
     * 刷新token的过期时间 30天
     */
    private Long refreshExpire = 60*60*24*30L;

    /**
     * 刷新token的过期时间 7天
     */
    private Long refreshExpire7Day = 60*60*24*7L;

}
