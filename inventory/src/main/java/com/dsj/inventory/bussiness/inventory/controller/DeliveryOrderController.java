package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.QueryDeliveryOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateDeliveryOrderParam;
import com.dsj.inventory.bussiness.inventory.service.DeliveryOrderService;
import com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderDetailVo;
import com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderExportVo;
import com.dsj.inventory.bussiness.inventory.vo.DeliveryOrderVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.EasyExcelUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 进销存退货单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@RestController
@Api(value = "DeliveryOrderController", tags = "进销存退货单api")
public class DeliveryOrderController {

    @Autowired
    private DeliveryOrderService deliveryOrderService;

    /*新增退货单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-return"})
    @PostMapping("/delivery-order")
    @ApiOperation(value = "新增退货单", notes = "新增退货单")
    public ResponseEntity<DeliveryOrderDetailVo> saveDeliveryOrder(@RequestBody SaveOrUpdateDeliveryOrderParam param){
        DeliveryOrderDetailVo deliveryVo = deliveryOrderService.saveOrUpdateDeliveryOrder(param);
        return Res.success(deliveryVo);
    }

    /*编辑退货单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-return"})
    @PutMapping("/delivery-order/{id}")
    @ApiOperation(value = "编辑退货单", notes = "编辑退货单")
    public ResponseEntity<DeliveryOrderDetailVo> updateDeliveryOrder(@PathVariable Long id, @RequestBody SaveOrUpdateDeliveryOrderParam param){
        param.setId(id);
        DeliveryOrderDetailVo deliveryVo = deliveryOrderService.saveOrUpdateDeliveryOrder(param);
        return Res.success(deliveryVo);
    }

    /*删除退货单*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-return"})
    @DeleteMapping("/delivery-order/{id}")
    @ApiOperation(value = "删除退货单", notes = "删除退货单")
    public ResponseEntity<Void> deleteDeliveryOrder(@PathVariable Long id){
        deliveryOrderService.deleteDeliveryOrder(id);
        return Res.success();
    }

    /*获取退货单列表*/
    @OrgAuthMode(strict = false,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-return"})
    @GetMapping("/delivery-order")
    @ApiOperation(value = "获取退货单列表", notes = "获取退货单列表")
    public ResponseEntity<List<DeliveryOrderVo>> queryDeliveryOrderList(QueryDeliveryOrderParam param){
        Page<DeliveryOrderVo> page = deliveryOrderService.queryDeliveryOrderPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*获取退货单详情*/
    @GetMapping("/delivery-order/{id}")
    @ApiOperation(value = "获取退货单详情", notes = "获取退货单详情")
    public ResponseEntity<DeliveryOrderDetailVo> queryDeliveryOrder(@PathVariable Long id){
        return Res.success(deliveryOrderService.getDeliveryOrderById(id));
    }

    /*提交退货单*/
    @OrgAuthMode(strict = true,strictUserMenus = true,menuCodeArray = {"drugstore-erp-stock-return"})
    @PostMapping("/delivery-order/{id}/action-submit")
    @ApiOperation(value = "提交退货单", notes = "提交退货单")
    public ResponseEntity<Void> submitDeliveryOrder(@PathVariable Long id){
        deliveryOrderService.submitDeliveryOrder(id);
        return Res.success();
    }
    /*退货单导出*/
    @GetMapping("/delivery-order/action-export")
    @ApiOperation(value = "导出退货单", notes = "导出退货单")
    public ResponseEntity<Void> exportDeliveryOrder(HttpServletResponse response, QueryDeliveryOrderParam param) throws IOException {
        param.setNeedExport(true);
        List<DeliveryOrderExportVo> list = deliveryOrderService.queryDeliveryOrderList(param);
        EasyExcelUtils.exportOrdinaryExcel(response,list,"退货单导出列表","退货单导出列表", DeliveryOrderExportVo.class);
        return Res.success();
    }

}
