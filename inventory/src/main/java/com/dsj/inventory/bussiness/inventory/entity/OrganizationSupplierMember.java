package com.dsj.inventory.bussiness.inventory.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dsj.inventory.common.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 供应商组织私域销售人员
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("jxc_organization_supplier_member")
@ApiModel(value="OrganizationSupplierMember", description="供应商组织私域销售人员")
public class OrganizationSupplierMember extends Entity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "私域供应商Id")
    @TableField("organization_supplier_id")
    private Long organizationSupplierId;

    @ApiModelProperty(value = "姓名")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "身份证号码")
    @TableField("card")
    private String card;

    @ApiModelProperty(value = "身份证有效期")
    @TableField("card_use_date")
    private LocalDateTime cardUseDate;

    @ApiModelProperty(value = "手机号码")
    @TableField("phone")
    private String phone;

    @ApiModelProperty(value = "授权区域")
    @TableField("authorize_area")
    private String authorizeArea;

    @ApiModelProperty(value = "授权书号")
    @TableField("authorize_code")
    private String authorizeCode;

    @ApiModelProperty(value = "授权书号有效期")
    @TableField("authorize_code_date")
    private LocalDateTime authorizeCodeDate;

    @ApiModelProperty(value = "授权品种，字典")
    @TableField("authorize_scope")
    private String authorizeScope;

    @ApiModelProperty(value = "身份证附件")
    @TableField("card_front_path")
    private String cardFrontPath;

    @ApiModelProperty(value = "授权书附件")
    @TableField("authorize_path")
    private String authorizePath;

    @ApiModelProperty(value = "删除标志,0：未删除  1：已删除")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;


}
