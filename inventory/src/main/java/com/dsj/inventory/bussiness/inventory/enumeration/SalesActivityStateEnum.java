package com.dsj.inventory.bussiness.inventory.enumeration;

import lombok.Getter;


/**
 * 销售活动状态 0未开始 1进行中 2已暂停 3已结束
 * <AUTHOR>
 */

@Getter
public enum SalesActivityStateEnum {

    NOT_STARTED(0, "未开始"),
    IN_PROGRESS(1, "进行中"),
    PAUSE(2, "已暂停"),
    FINISH(3, "已结束"),
    ;

    private Integer code;

    private String desc;

    SalesActivityStateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for (SalesActivityStateEnum a : SalesActivityStateEnum.values()) {
            if (a.getCode().equals(code)) {
                return a.desc;
            }
        }
        return null;
    }
}
