package com.dsj.inventory.common.utils.ca;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.SmUtil;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;

/**
 * @Title: 签名验签工具类
 * @ClassName: com.yitong.uap.framework.util.SignUtils.java
 * @Description:
 *
 * @Copyright 2016-2022 公司名称 - Powered By 研发中心
 * @author: 陈延龙
 * @date:  2022-08-25 10:20
 * @version V1.0
 */
public class SignUtils {

    public static String createSign(String appKey, String appSecret, SortedMap<Object, Object> parameters,String type){
        StringBuffer sb = new StringBuffer();
        Set es = parameters.entrySet();
        Iterator it = es.iterator();
        while(it.hasNext()) {
            Map.Entry entry = (Map.Entry)it.next();
            String k = (String)entry.getKey();
            Object v = entry.getValue();
            if (!"sign".equals(k) && null != v && !"".equals(v)) {
                sb.append(k + "=" + v + "&");
            }
        }
        sb.append("appKey=" + appKey+"&appSecret="+appSecret);
        String sign="";
        if(StrUtil.isNotBlank(type)){
            if(type.equalsIgnoreCase("sm3")){
                sign = SmUtil.sm3(sb.toString()).toLowerCase();
            }else{
                sign =SecureUtil.md5(sb.toString()).toLowerCase();
            }
        }else{
            sign =SecureUtil.md5(sb.toString()).toLowerCase();
        }
        return sign;
    }

    public static boolean isValidSign(String appKey, String appSecret, SortedMap<Object, Object> sortedMap,String type) {
        StringBuffer sb = new StringBuffer();
        Set es = sortedMap.entrySet();
        Iterator it = es.iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            // String v = (String)entry.getValue();
            Object v = entry.getValue();
            if (!"sign".equals(k) && null != v && !"".equals(v)) {
                sb.append(k + "=" + v + "&");
            }
        }

        sb.append("appKey=" + appKey + "&appSecret=" + appSecret);

        // 算出摘要
        String mysign = "";
        if(StrUtil.isNotBlank(type)){
            if(type.equalsIgnoreCase("sm3")){
                mysign = SmUtil.sm3(sb.toString()).toLowerCase();
            }else{
                mysign =SecureUtil.md5(sb.toString()).toLowerCase();
            }
        }else{
            mysign =SecureUtil.md5(sb.toString()).toLowerCase();
        }
        String sign = ((String) sortedMap.get("sign")).toLowerCase();

        return sign.equals(mysign);
    }

}
