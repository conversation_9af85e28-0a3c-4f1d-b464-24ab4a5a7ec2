package com.dsj.inventory.common.generator;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 编号服务类
 * 提供业务层调用的编号生成服务
 */
@Service
public class NumberService {

    private final NumberGeneratorFactory generatorFactory;

    @Autowired
    public NumberService(NumberGeneratorFactory generatorFactory) {
        this.generatorFactory = generatorFactory;
    }

    /**
     * 生成供应商首营单据编号
     * @return 供应商首营单据编号
     */
    public String generateSupplierFirstDocumentNumber() {
        return generatorFactory.getGenerator(NumberType.SUPPLIER_FIRST_DOCUMENT).generate();
    }

    /**
     * 生成供应商编号
     * @return 供应商编号
     */
    public String generateSupplierCode() {
        return generatorFactory.getGenerator(NumberType.SUPPLIER_CODE).generate();
    }

    /**
     * 生成采购订单编号
     * @return 采购订单编号
     */
    public String generatePurchaseOrderNumber() {
        return generatorFactory.getGenerator(NumberType.PURCHASE_ORDER).generate();
    }

    /**
     * 生成采购计划编号
     * @return 采购计划编号
     */
    public String generatePurchasePlanNumber() {
        return generatorFactory.getGenerator(NumberType.PURCHASE_PLAN).generate();
    }

    /**
     * 生成退货编号
     * @return 退货编号
     */
    public String generateReturnOrderNumber() {
        return generatorFactory.getGenerator(NumberType.RETURN_ORDER).generate();
    }

    /**
     * 通用编号生成方法
     * @param type 编号类型
     * @return 生成的编号
     */
    public String generateNumber(NumberType type) {
        return generatorFactory.getGenerator(type).generate();
    }
} 