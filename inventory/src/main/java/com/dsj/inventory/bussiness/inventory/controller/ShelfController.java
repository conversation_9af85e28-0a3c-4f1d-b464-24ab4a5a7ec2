package com.dsj.inventory.bussiness.inventory.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.param.MoveGoodsParam;
import com.dsj.inventory.bussiness.inventory.param.QueryShelfParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateShelfParam;
import com.dsj.inventory.bussiness.inventory.service.ShelfService;
import com.dsj.inventory.bussiness.inventory.vo.ShelfVo;
import com.dsj.inventory.common.entity.Res;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.framework.annotations.OrgAuthMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 进销存货架信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
@RestController
@Api(value = "ShelfController", tags = "进销存货架api")
public class ShelfController {

    @Autowired
    private ShelfService shelfService;

    /*新增货架*/
    @OrgAuthMode(strict = false,strictUserMenus = false)
    @PostMapping("/shelf")
    @ApiOperation(value = "新增货架", notes = "新增货架")
    public ResponseEntity<ShelfVo> saveShelf(@RequestBody SaveOrUpdateShelfParam param){
        ShelfVo shelfVo = shelfService.saveOrUpdateShelf(param);
        return Res.success(shelfVo);
    }

    /*编辑货架*/
    @PutMapping("/shelf/{id}")
    @ApiOperation(value = "编辑货架", notes = "编辑货架")
    public ResponseEntity<ShelfVo> updateShelf(@PathVariable Long id, @RequestBody SaveOrUpdateShelfParam param){
        param.setId(id);
        ShelfVo shelfVo = shelfService.saveOrUpdateShelf(param);
        return Res.success(shelfVo);
    }

    /*删除货架*/
    @DeleteMapping("/shelf/{id}")
    @ApiOperation(value = "删除货架", notes = "删除货架")
    public ResponseEntity<Void> deleteShelf(@PathVariable Long id){
        shelfService.deleteShelf(id);
        return Res.success();
    }

    /*获取货架列表*/
    @GetMapping("/shelf")
    @ApiOperation(value = "获取货架列表", notes = "获取货架列表")
    public ResponseEntity<List<ShelfVo>> queryShelfList(QueryShelfParam param){
        Page<ShelfVo> page = shelfService.queryShelfPage(param);
        if (CommonUtils.isNotEmpty(param.getPage()) && CommonUtils.isNotEmpty(param.getSize())){
            return Res.successPage(page);
        }else {
            return Res.success(page.getRecords());
        }
    }

    /*货架批量移动商品*/
    @OrgAuthMode(strict = true,strictUserMenus = false)
    @PostMapping("/shelf/{id}/action-batch-move")
    @ApiOperation(value = "货架批量移动商品", notes = "货架批量移动商品")
    public ResponseEntity<Void> batchMoveShelf(@PathVariable Long id, @RequestBody @Validated List<MoveGoodsParam> params){
        shelfService.batchMoveShelf(id,params);
        return Res.success();
    }

    /*初始化货架*/
    @PostMapping("/shelf/action-initialize")
    @ApiOperation(value = "初始化货架", notes = "初始化货架")
    public ResponseEntity<Void> initializeShelf(){
        shelfService.initializeShelf();
        return Res.success();
    }

}
