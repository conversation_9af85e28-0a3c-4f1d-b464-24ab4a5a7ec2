package com.dsj.inventory.common.entity;

import com.dsj.inventory.common.entity.IMMemberInfoVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * IM 群主成员信息
 *  自定义字段没有列列举，需要时再加
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class IMGroupInfoVo {

    /*** 群组的唯一标识*/
    private String GroupId;

    /**群组类型*/
    private String Type;

    /**群组名称*/
    private String Name;

    /**群组简介*/
    private String Introduction;

    /**群组公告*/
    private String Notification;

    /**群组头像 URL*/
    private String FaceUrl;

    /**群组头像 URL*/
    private long CreateTime;

    /*** 群成员 ID*/
    private List<IMMemberInfoVo> MemberList;
}
