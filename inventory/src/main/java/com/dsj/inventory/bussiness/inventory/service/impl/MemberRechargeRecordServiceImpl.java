package com.dsj.inventory.bussiness.inventory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pocky.transport.bussiness.auth.enumeration.RoleEnum;
import com.dsj.inventory.bussiness.inventory.entity.CashierWorking;
import com.dsj.inventory.bussiness.inventory.entity.Member;
import com.dsj.inventory.bussiness.inventory.entity.MemberRechargeRecord;
import com.dsj.inventory.bussiness.inventory.mapper.MemberRechargeRecordMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryMemberRechargeRecordParam;
import com.dsj.inventory.bussiness.inventory.param.SaveMemberRechargeRecordParam;
import com.dsj.inventory.bussiness.inventory.service.CashierWorkingService;
import com.dsj.inventory.bussiness.inventory.service.MemberRechargeRecordService;
import com.dsj.inventory.bussiness.inventory.service.MemberService;
import com.dsj.inventory.bussiness.inventory.vo.MemberRechargeRecordVo;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <p>
 * 进销存会员充值记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@Service
public class MemberRechargeRecordServiceImpl extends ServiceImpl<MemberRechargeRecordMapper, MemberRechargeRecord> implements MemberRechargeRecordService {

    @Autowired
    private MemberService memberService;
    @Autowired
    private CashierWorkingService cashierWorkingService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberRechargeRecordVo saveMemberRechargeRecord(SaveMemberRechargeRecordParam param) {
        Member member = memberService.getById(param.getMemberId());
        if(CommonUtils.isEmpty(member)){
            throw new BizException("操作失败，会员不存在");
        }
        MemberRechargeRecord memberRechargeRecord = BeanUtil.toBean(param, MemberRechargeRecord.class);
        this.save(memberRechargeRecord);
        //更改会员储值金额
        BigDecimal rechargePrice = BigDecimal.valueOf(memberRechargeRecord.getRechargePrice())
                .add(BigDecimal.valueOf(memberRechargeRecord.getGivePrice()));
        BigDecimal balance = BigDecimal.valueOf(member.getBalance()).add(rechargePrice);
        memberService.lambdaUpdate().eq(Member::getId,member.getId()).set(Member::getBalance,balance.intValue()).update(new Member());
        //更改营业员交班记录
        Long cashierId = BaseContextHandler.getUserId();
        LocalDateTime startDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime endDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(23,59,59));
        CashierWorking cashierWorking = cashierWorkingService.lambdaQuery().eq(CashierWorking::getCashierId,cashierId)
                .eq(CashierWorking::getOrganizationId,param.getOrganizationId())
                .ge(CashierWorking::getCreateTime, startDateTime)
                .le(CashierWorking::getCreateTime,endDateTime).one();
        if(CommonUtils.isEmpty(cashierWorking)){
            CashierWorking newWorking = CashierWorking.builder().cashierId(cashierId)
                    .startDate(LocalDateTime.now()).endDate(LocalDateTime.now())
                    .organizationId(param.getOrganizationId()).rechargePrice(rechargePrice.intValue())
                    .rechargeRealPrice(memberRechargeRecord.getRechargePrice())
                    .rechargeGivePrice(memberRechargeRecord.getGivePrice()).build();
            cashierWorkingService.save(newWorking);
        }else {
            cashierWorkingService.lambdaUpdate().eq(CashierWorking::getId,cashierWorking.getId())
                    .set(CashierWorking::getEndDate,LocalDateTime.now())
                    .set(CashierWorking::getRechargePrice,BigDecimal.valueOf(cashierWorking.getRechargePrice()).add(rechargePrice))
                    .set(CashierWorking::getRechargeRealPrice,BigDecimal.valueOf(cashierWorking.getRechargeRealPrice()).add(BigDecimal.valueOf(memberRechargeRecord.getRechargePrice())))
                    .set(CashierWorking::getRechargeGivePrice,BigDecimal.valueOf(cashierWorking.getRechargeGivePrice()).add(BigDecimal.valueOf(memberRechargeRecord.getGivePrice())))
                    .update(new CashierWorking());
        }
        return BeanUtil.toBean(this.getById(memberRechargeRecord.getId()), MemberRechargeRecordVo.class);
    }

    @Override
    public Page<MemberRechargeRecordVo> queryMemberRechargeRecordPage(QueryMemberRechargeRecordParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(RoleEnum.USER.getCode().equals(BaseContextHandler.get(BaseContextConstants.USER_ROLE))){
            param.setUserId(BaseContextHandler.getUserId());
        }else if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<MemberRechargeRecordVo> list = this.baseMapper.queryMemberRechargeRecordPage(page, param);
        return page.setRecords(list);
    }
}
