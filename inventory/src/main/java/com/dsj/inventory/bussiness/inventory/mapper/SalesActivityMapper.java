package com.dsj.inventory.bussiness.inventory.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dsj.inventory.bussiness.inventory.entity.SalesActivity;
import com.dsj.inventory.bussiness.inventory.param.QuerySalesActivityParam;
import com.dsj.inventory.bussiness.inventory.vo.SalesActivityVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 进销存销售活动 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface SalesActivityMapper extends BaseMapper<SalesActivity> {

    /**
     * 获取销售活动分页列表
     * @param page
     * @param param
     * @return
     */
    List<SalesActivityVo> querySalesActivityPage(@Param("page") Page page, @Param("param") QuerySalesActivityParam param);

    /**
     * 获取销售活动详情
     * @param id
     * @return
     */
    SalesActivityVo getSalesActivityById(Long id);

}
