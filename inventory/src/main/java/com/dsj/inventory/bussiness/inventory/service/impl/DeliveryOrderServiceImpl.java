package com.dsj.inventory.bussiness.inventory.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dsj.inventory.bussiness.inventory.entity.*;
import com.dsj.inventory.bussiness.inventory.enumeration.*;
import com.dsj.inventory.bussiness.inventory.mapper.DeliveryOrderMapper;
import com.dsj.inventory.bussiness.inventory.param.QueryDeliveryOrderParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateDeliveryOrderGoodsParam;
import com.dsj.inventory.bussiness.inventory.param.SaveOrUpdateDeliveryOrderParam;
import com.dsj.inventory.bussiness.inventory.service.*;
import com.dsj.inventory.bussiness.inventory.vo.*;
import com.dsj.inventory.common.constant.BaseConstant;
import com.dsj.inventory.common.context.BaseContextConstants;
import com.dsj.inventory.common.context.BaseContextHandler;
import com.dsj.inventory.common.enumeration.TrueEnum;
import com.dsj.inventory.common.utils.CommonUtils;
import com.dsj.inventory.common.utils.DateUtils;
import com.dsj.inventory.common.utils.DozerUtils;
import com.dsj.inventory.common.utils.TencentIMUtils;
import com.dsj.inventory.framework.exception.BizException;
import com.dsj.inventory.framework.exception.ParamException;
import com.pocky.transport.bussiness.diagnose.entity.Organization;
import com.pocky.transport.bussiness.diagnose.entity.UserROrganization;
import com.pocky.transport.bussiness.diagnose.enumeration.OrganizationEnum;
import com.pocky.transport.bussiness.diagnose.service.OrganizationService;
import com.pocky.transport.bussiness.diagnose.service.UserROrganizationService;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineExpand;
import com.pocky.transport.bussiness.hospital.entity.OrganizationMedicineMapping;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineExpandService;
import com.pocky.transport.bussiness.hospital.service.OrganizationMedicineMappingService;
import com.pocky.transport.bussiness.hospital.vo.OrganizationMedicineMappingVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 进销存退货单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Slf4j
@Service
public class DeliveryOrderServiceImpl extends ServiceImpl<DeliveryOrderMapper, DeliveryOrder> implements DeliveryOrderService {

    @Autowired
    private DozerUtils dozerUtils;
    @Autowired
    private OrganizationMedicineMappingService organizationMedicineMappingService;
    @Autowired
    private IncomingOrderService incomingOrderService;
    @Autowired
    private IncomingOrderGoodsService incomingOrderGoodsService;
    @Autowired
    private DeliveryOrderGoodsService deliveryOrderGoodsService;
    @Autowired
    private InventoryService inventoryService;
    @Autowired
    private InventoryChangeLogService inventoryChangeLogService;
    @Autowired
    private WarningLogService warningLogService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private TencentIMUtils tencentIMUtils;
    @Autowired
    private UserROrganizationService userROrganizationService;
    @Autowired
    private OrganizationMedicineExpandService organizationMedicineExpandService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeliveryOrderDetailVo saveOrUpdateDeliveryOrder(SaveOrUpdateDeliveryOrderParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        Organization organization = organizationService.getById(param.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        if(CommonUtils.isNotEmpty(param.getOrderId())){
            IncomingOrder incomingOrder = incomingOrderService.getById(param.getOrderId());
            if(CommonUtils.isEmpty(incomingOrder)){
                throw new BizException("操作失败，入库单不存在");
            }
            param.setOrderCode(incomingOrder.getCode());
        }
        if(CommonUtils.isNotEmpty(param.getId())) {
            DeliveryOrder byId = this.getById(param.getId());
            if (CommonUtils.isEmpty(byId)) {
                throw new BizException("操作失败，数据不存在");
            }
            //删除商品明细
            deliveryOrderGoodsService.lambdaUpdate().eq(DeliveryOrderGoods::getOrderId,param.getId())
                    .set(DeliveryOrderGoods::getIsDelete, TrueEnum.TRUE.getCode()).update(new DeliveryOrderGoods());
        }else {
            //生成退货单号：TC+年后两位+月日四位+药房识别码六位+三位
            String day = DateUtils.format(LocalDateTime.now(),DateUtils.DEFAULT_DATE_FORMAT);
            String dayStr = day.replaceAll("-", "").substring(2,8);
            Integer count = this.getDeliveryOrderCount(organization.getId(),day);
            count = count + 1;
            param.setCode("TC" + dayStr + organization.getSerialNo() + String.format("%03d",count));
        }

        StringBuilder reason = new StringBuilder();
        if(CommonUtils.isNotEmpty(param.getGoodsList())){
            List<Long> goodsIdList = param.getGoodsList().stream().map(g -> g.getInventoryId()).distinct().collect(Collectors.toList());
            if(goodsIdList.size() != param.getGoodsList().size()){
                throw new BizException("操作失败，存在重复商品库存");
            }
            List<Long> orderIdList = param.getGoodsList().stream().map(g -> g.getOrderId()).distinct().collect(Collectors.toList());
            if(orderIdList.size() > 1){
                throw new BizException("操作失败，商品不属于同一退货单");
            }
            BigDecimal returnTotalPrice = BigDecimal.valueOf(0);
            BigDecimal returnCostTotalPrice = BigDecimal.valueOf(0);
            for(SaveOrUpdateDeliveryOrderGoodsParam goodsParam : param.getGoodsList()){
                OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(goodsParam.getOrganizationMedicineId());
                if(CommonUtils.isEmpty(organizationMedicine)){
                    throw new BizException("操作失败，商品Id"+goodsParam.getOrganizationMedicineId()+"不存在");
                }
                Inventory inventory = inventoryService.getById(goodsParam.getInventoryId());
                if(inventory.getInventoryAmount() < goodsParam.getReturnAmount()){
                    throw new BizException("操作失败，"+organizationMedicine.getName() +"库存数量小于退货数量");
                }
                IncomingOrderGoods incomingGoods = incomingOrderGoodsService.getById(inventory.getIncomingGoodsId());
                goodsParam.setGoodsName(organizationMedicine.getName());
                goodsParam.setGeneralName(organizationMedicine.getGeneralName());
                goodsParam.setGoodsCode(organizationMedicine.getGoodsCode());
                goodsParam.setPackUnit(organizationMedicine.getPackUnit());
                goodsParam.setSpecification(organizationMedicine.getSpecification());
                goodsParam.setDrugType(organizationMedicine.getDrugType());
                goodsParam.setManufacturer(organizationMedicine.getManufacturer());
                OrganizationMedicineExpand medicineExpand = organizationMedicineExpandService.getById(organizationMedicine.getId());
                if(CommonUtils.isNotEmpty(medicineExpand)) {
                    goodsParam.setLaunchPermitHolder(medicineExpand.getLaunchPermitHolder());
                }
                goodsParam.setProducePlace(organizationMedicine.getProducePlace());
                goodsParam.setApprovalNumber(organizationMedicine.getApprovalNumber());
                goodsParam.setProduceDate(incomingGoods.getProduceDate());
                goodsParam.setUserDate(incomingGoods.getUserDate());
                goodsParam.setBatchNumber(inventory.getBatchNumber());
                goodsParam.setShelfId(inventory.getShelfId());
                goodsParam.setBatch(inventory.getBatch());
                goodsParam.setIncomingGoodsId(incomingGoods.getId());
                goodsParam.setIncomingAmount(incomingGoods.getIncomingAmount());
                goodsParam.setReturnedAmount(incomingGoods.getReturnedAmount());
                goodsParam.setInventoryAmount(inventory.getInventoryAmount());
                BigDecimal amount  = BigDecimal.valueOf(goodsParam.getReturnAmount())
                        .subtract(BigDecimal.valueOf(goodsParam.getGiveAmount()));
                BigDecimal goodsReturnTotalPrice = BigDecimal.valueOf(goodsParam.getReturnPrice()).multiply(amount);
                goodsReturnTotalPrice = goodsReturnTotalPrice.setScale(0,BigDecimal.ROUND_HALF_UP);
                if(goodsReturnTotalPrice.intValue() != goodsParam.getReturnTotalPrice()){
                    throw new BizException("操作失败，商品"+goodsParam.getGoodsName()+"小计金额计算不一致");
                }
                goodsParam.setCostPrice(incomingGoods.getDiscountPrice());
                BigDecimal goodsReturnCostTotalPrice = BigDecimal.valueOf(goodsParam.getCostPrice()).multiply(amount);
                goodsReturnCostTotalPrice = goodsReturnCostTotalPrice.setScale(0,BigDecimal.ROUND_HALF_UP);
                goodsParam.setReturnCostTotalPrice(goodsReturnCostTotalPrice.intValue());
                BigDecimal goodsDisparityPrice = goodsReturnCostTotalPrice.subtract(goodsReturnTotalPrice);
                goodsParam.setDisparityPrice(goodsDisparityPrice.intValue());
                returnTotalPrice = returnTotalPrice.add(BigDecimal.valueOf(goodsParam.getReturnTotalPrice()));
                returnCostTotalPrice = returnCostTotalPrice.add(BigDecimal.valueOf(goodsParam.getReturnCostTotalPrice()));
                reason.append(goodsParam.getGoodsName()+":"+goodsParam.getReason()+"；");
            }
            if(returnTotalPrice.intValue() != param.getReturnTotalPrice()){
                throw new BizException("操作失败，总金额计算不一致");
            }
            param.setReturnTotalPrice(returnTotalPrice.intValue());
            param.setReturnCostTotalPrice(returnCostTotalPrice.intValue());
            param.setDisparityPrice(returnCostTotalPrice.subtract(returnTotalPrice).intValue());
            param.setReason(reason.substring(0,reason.length()-1));
        }
        DeliveryOrder deliveryOrder = dozerUtils.map(param,DeliveryOrder.class);
        this.saveOrUpdate(deliveryOrder);
        if(CommonUtils.isNotEmpty(param.getGoodsList())){
            param.getGoodsList().stream().map(g -> g.setOrderId(deliveryOrder.getId())).collect(Collectors.toList());
            List<DeliveryOrderGoods> goodsList = dozerUtils.mapList(param.getGoodsList(),DeliveryOrderGoods.class);
            deliveryOrderGoodsService.saveBatch(goodsList);
            if(DeliveryOrderStateEnum.RETURNED.getCode().equals(param.getDealState())){
                this.dealSubmitDeliveryOrder(this.getDeliveryOrderById(deliveryOrder.getId()));
            }
        }
        return this.getDeliveryOrderById(deliveryOrder.getId());
    }

    @Override
    public void deleteDeliveryOrder(Long id) {
        DeliveryOrder byId = this.getById(id);
        if (CommonUtils.isEmpty(byId)) {
            throw new BizException("操作失败，数据不存在");
        }
        if(!byId.getDealState().equals(DeliveryOrderStateEnum.TEMPORARY.getCode())){
            throw new BizException("操作失败，退货单当前状态不允许删除");
        }
        //删除商品明细
        deliveryOrderGoodsService.lambdaUpdate().eq(DeliveryOrderGoods::getOrderId,id)
                .set(DeliveryOrderGoods::getIsDelete, TrueEnum.TRUE.getCode()).update(new DeliveryOrderGoods());
        this.baseMapper.deleteById(id);
    }

    @Override
    public Page<DeliveryOrderVo> queryDeliveryOrderPage(QueryDeliveryOrderParam param) {
        if (CommonUtils.isEmpty(param)){
            throw new ParamException("参数异常");
        }
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        Page page = new Page<>(param.getPage(),param.getSize());
        List<DeliveryOrderVo> list = this.baseMapper.queryDeliveryOrderPage(page, param);
        return page.setRecords(list);
    }

    @Override
    public DeliveryOrderDetailVo getDeliveryOrderById(Long id) {
        DeliveryOrderDetailVo vo = this.baseMapper.getDeliveryOrderById(id);
        if(CommonUtils.isNotEmpty(vo)){
            List<DeliveryOrderGoodsVo> goodsList = deliveryOrderGoodsService.getDeliveryOrderGoodsByOrderId(vo.getId());
            if(CommonUtils.isNotEmpty(goodsList)){
                vo.setGoodsList(goodsList);
            }
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitDeliveryOrder(Long id) {
        DeliveryOrderDetailVo deliveryOrder = this.getDeliveryOrderById(id);
        if(CommonUtils.isEmpty(deliveryOrder)){
            throw new BizException("操作失败，退货单不存在");
        }
        Organization organization = organizationService.getById(deliveryOrder.getOrganizationId());
        if(CommonUtils.isEmpty(organization)){
            throw new BizException("组织不存在");
        }
        if(CommonUtils.isEmpty(organization.getInventoryDate())){
            throw new BizException("进销存有效期未设置");
        }
        boolean before = organization.getInventoryDate().isBefore(LocalDate.now());
        if (before) {
            throw new BizException("进销存有效期已过期");
        }
        this.lambdaUpdate().eq(DeliveryOrder::getId,id)
                .set(DeliveryOrder::getDealState,DeliveryOrderStateEnum.RETURNED.getCode()).update(new DeliveryOrder());
        this.dealSubmitDeliveryOrder(deliveryOrder);
    }

    private void dealSubmitDeliveryOrder(DeliveryOrderDetailVo deliveryOrder){
        //修改已退货数量
        for(DeliveryOrderGoodsVo goods : deliveryOrder.getGoodsList()){
            Double returnedAmount = this.baseMapper.countReturnedAmount(goods.getIncomingGoodsId());
            incomingOrderGoodsService.lambdaUpdate().eq(IncomingOrderGoods::getId,goods.getIncomingGoodsId())
                    .set(IncomingOrderGoods::getReturnedAmount,returnedAmount).update(new IncomingOrderGoods());
        }
        //判断入库单是否全部退货
        List<IncomingOrderGoods> incomingOrderGoodsList = incomingOrderGoodsService.lambdaQuery()
                .eq(IncomingOrderGoods::getOrderId,deliveryOrder.getOrderId()).list();
        boolean flag = true;
        for(IncomingOrderGoods goods : incomingOrderGoodsList){
            if(!goods.getReturnedAmount().equals(goods.getIncomingAmount())){
                flag = false;
                break;
            }
        }
        if(flag){
            //入库单变更状态--已退货
            incomingOrderService.lambdaUpdate().eq(IncomingOrder::getId,deliveryOrder.getOrderId())
                    .set(IncomingOrder::getDealState,IncomingOrderStateEnum.RETURNED.getCode())
                    .update(new IncomingOrder());
        }
        //是否发送缺货预警消息
        boolean lackFlag = false;
        //扣库存
        for(DeliveryOrderGoodsVo goods : deliveryOrder.getGoodsList()){
            Inventory inventory = inventoryService.getById(goods.getInventoryId());
            if(inventory.getInventoryAmount() < goods.getReturnAmount()){
                throw new BizException("操作失败，"+goods.getGoodsName() +"库存数量小于退货数量");
            }
            BigDecimal inventoryAmount = BigDecimal.valueOf(inventory.getInventoryAmount())
                    .subtract(BigDecimal.valueOf(goods.getReturnAmount()));
            inventoryService.lambdaUpdate().eq(Inventory::getId,inventory.getId())
                    .set(Inventory::getInventoryAmount,inventoryAmount.doubleValue()).update(new Inventory());
            OrganizationMedicineMapping organizationMedicine = organizationMedicineMappingService.getById(goods.getOrganizationMedicineId());
            BigDecimal medicineRepertory = BigDecimal.valueOf(organizationMedicine.getMedicineRepertory())
                    .subtract(BigDecimal.valueOf(goods.getReturnAmount()));
            organizationMedicineMappingService.lambdaUpdate().eq(OrganizationMedicineMapping::getId,goods.getOrganizationMedicineId())
                    .set(OrganizationMedicineMapping::getMedicineRepertory,medicineRepertory.doubleValue())
                    .update(new OrganizationMedicineMapping());
            //处理库存预警
            if(CommonUtils.isNotEmpty(organizationMedicine.getRepertoryLowerLimit())){
                //缺货
                WarningLog lowerWarningLog = warningLogService.lambdaQuery()
                        .eq(WarningLog::getType, WarningTypeEnum.LACK.getCode())
                        .eq(WarningLog::getOrganizationId,deliveryOrder.getOrganizationId())
                        .eq(WarningLog::getOrganizationMedicineId,organizationMedicine.getId()).one();
                //库存下限
                Integer lowerLimit = organizationMedicine.getRepertoryLowerLimit();
                if(medicineRepertory.doubleValue() < lowerLimit && CommonUtils.isEmpty(lowerWarningLog)){
                    //新增缺货预警
                    WarningLog warningLog = WarningLog.builder().organizationId(deliveryOrder.getOrganizationId())
                            .organizationMedicineId(organizationMedicine.getId())
                            .type(WarningTypeEnum.LACK.getCode()).build();
                    warningLogService.save(warningLog);
                    lackFlag = true;
                }
            }
            if(CommonUtils.isNotEmpty(organizationMedicine.getRepertoryUpperLimit())){
                //积货
                WarningLog upperWarningLog = warningLogService.lambdaQuery()
                        .eq(WarningLog::getType,WarningTypeEnum.EXCEED.getCode())
                        .eq(WarningLog::getOrganizationId,deliveryOrder.getOrganizationId())
                        .eq(WarningLog::getOrganizationMedicineId,organizationMedicine.getId()).one();
                //库存上限
                Integer upperLimit = organizationMedicine.getRepertoryUpperLimit();
                if(medicineRepertory.doubleValue() < upperLimit && CommonUtils.isNotEmpty(upperWarningLog)){
                    //删除预警记录
                    warningLogService.lambdaUpdate().eq(WarningLog::getId,upperWarningLog.getId())
                            .set(WarningLog::getIsDelete,TrueEnum.TRUE.getCode()).update(new WarningLog());
                }
            }
            //库存变动记录
            InventoryChangeLog inventoryChangeLog = InventoryChangeLog.builder().organizationId(deliveryOrder.getOrganizationId())
                    .organizationMedicineId(goods.getOrganizationMedicineId()).inventoryId(inventory.getId())
                    .orderId(deliveryOrder.getId()).orderType(InventoryOrderTypeEnum.DELIVERY.getCode())
                    .changeType(InventoryChangeTypeEnum.DECREASE.getCode()).changeAmount(goods.getReturnAmount())
                    .batchNumber(goods.getBatchNumber()).shelfId(goods.getShelfId()).batch(goods.getBatch())
                    .costPrice(inventory.getCostPrice()).executeId(BaseContextHandler.getUserId())
                    .build();
            inventoryChangeLogService.save(inventoryChangeLog);
            //重新计算成本加权平均价
            List<IncomingOrderGoodsVo> incomingGoodsList = incomingOrderGoodsService.countIncomingOrderGoods(goods.getOrganizationMedicineId());
            BigDecimal amount = BigDecimal.valueOf(0.00);
            BigDecimal costPrice = BigDecimal.valueOf(0);
            for(IncomingOrderGoodsVo incomingGoods : incomingGoodsList){
                BigDecimal goodsAmount = BigDecimal.valueOf(incomingGoods.getIncomingAmount())
                        .subtract(BigDecimal.valueOf(incomingGoods.getReturnedAmount()));
                BigDecimal goodsPrice = goodsAmount.multiply(BigDecimal.valueOf(incomingGoods.getCostPrice()));
                costPrice = costPrice.add(goodsPrice);
                amount = amount.add(goodsAmount);
            }
            BigDecimal averageCost = BigDecimal.valueOf(0.00);
            if(amount.doubleValue() > 0){
                costPrice.divide(amount,0,BigDecimal.ROUND_HALF_UP);
            }
            organizationMedicineMappingService.lambdaUpdate().eq(OrganizationMedicineMapping::getId,goods.getOrganizationMedicineId())
                    .set(OrganizationMedicineMapping::getAverageCost,averageCost)
                    .update(new OrganizationMedicineMapping());
        }
        //发送预警消息
        if(lackFlag){
            log.info("============向商家发送缺货预警消息============");
            List<UserROrganization> uroList = userROrganizationService.lambdaQuery()
                    .eq(UserROrganization::getOrganizationId,deliveryOrder.getOrganizationId()).list();
            if (CommonUtils.isNotEmpty(uroList)){
                List<String> userIdList = uroList.stream().map(s -> s.getUserId().toString()).collect(Collectors.toList());
                tencentIMUtils.adminSendBatchSingleMsg(userIdList,BaseConstant.IM_DATE_INVENTORY_WARNING,BaseConstant.IM_DESC_INVENTORY_LACK,"");
            }
        }
    }

    @Override
    public List<DeliveryOrderExportVo> queryDeliveryOrderList(QueryDeliveryOrderParam param) {
        if(!OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))
                && !OrganizationEnum.PLATFORM.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            throw new BizException("组织无权限");
        }else if(OrganizationEnum.DRUGSTORE.getCode().equals(Integer.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_TYPE)))){
            param.setOrganizationId(Long.valueOf(BaseContextHandler.get(BaseContextConstants.USER_ORG_ID)));
        }
        return this.baseMapper.queryDeliveryOrderList(param);
    }

    @Override
    public void deleteDeliveryOrderByOrganizationId(Long organizationId) {
        this.baseMapper.deleteDeliveryOrderByOrganizationId(organizationId);
    }

    @Override
    public Integer getDeliveryOrderCount(Long organizationId, String day) {
        return this.baseMapper.getDeliveryOrderCount(organizationId, day);
    }


}
